package com.lge.d2x.domain.svdCategory.v1.repository.pdsmgr;

import com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv1CategoryResponseVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv2CategoryResponseVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv3CategoryResponseVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SvdCategoryRepository {

    /************************************
     * /api/category/retrieveCategoryList
     ************************************/
    /*Select lv1 category list*/
    List<SvdLv1CategoryResponseVO> selectSvdLv1CategoryList(SvdCategoryRequestVO categoryRequestVO);

    List<SvdLv1CategoryResponseVO> selectSvdLv1CategoryListTypeT(
            SvdCategoryRequestVO categoryRequestVO);

    List<SvdLv1CategoryResponseVO> selectSvdLv1CategoryListV2TypeT(
            SvdCategoryRequestVO categoryRequestVO);
    /*Select lv2 category list*/
    List<SvdLv2CategoryResponseVO> selectSvdLv2CategoryListTypeT(
            SvdCategoryRequestVO categoryRequestVO);

    List<SvdLv2CategoryResponseVO> selectSvdLv2CategoryListV2TypeT(
            SvdCategoryRequestVO categoryRequestVO);

    List<SvdLv2CategoryResponseVO> selectSvdLv2CategoryListTypeE(
            SvdCategoryRequestVO categoryRequestVO);

    List<SvdLv2CategoryResponseVO> selectSvdLv2CategoryListTypeMW(
            SvdCategoryRequestVO categoryRequestVO);
    /*Select lv3 category list*/
    List<SvdLv3CategoryResponseVO> selectSvdLv3CategoryList(SvdCategoryRequestVO categoryRequestVO);

    List<SvdLv3CategoryResponseVO> selectSvdLv3CategoryListV2(
            SvdCategoryRequestVO categoryRequestVO);

    List<SvdLv3CategoryResponseVO> selectSvdLv3CategoryListTypeE(
            SvdCategoryRequestVO categoryRequestVO);

    /**********************************************
     * todo: /api/category/retrieveGsfsCategoryList
     **********************************************/
}
