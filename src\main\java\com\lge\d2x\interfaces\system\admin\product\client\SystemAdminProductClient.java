package com.lge.d2x.interfaces.system.admin.product.client;

import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminCatalogueListRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminCatalogueListResponseVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminDowntimeInfoVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminFilterCountryListRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminFilterCountryListResponseVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminLatsRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminLatsResponseVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminProductBizTypeGetRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminProductModelIdGetRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminSeoKeySpecRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminSeoKeySpecResponseVO;
import com.lge.d2x.interfaces.system.admin.product.url.SystemAdminProductUrlConstants;
import com.lge.d2xfrm.client.configuration.D2xFeignClientConfiguration;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(
        name = "SystemAdminProductClient",
        url = "${client.url.system.admin}",
        configuration = D2xFeignClientConfiguration.class)
public interface SystemAdminProductClient {
    @GetMapping(value = SystemAdminProductUrlConstants.GET_PRODUCT_V1_MODEL_ID)
    String getModelBySku(@SpringQueryMap SystemAdminProductModelIdGetRequestVO requestVO);

    @GetMapping(value = SystemAdminProductUrlConstants.GET_PRODUCT_V1_BIZ_TYPE)
    String getBizTypeByModelId(@SpringQueryMap SystemAdminProductBizTypeGetRequestVO requestVO);

    // @ClientCaching(name = "getProductCatalogue", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemAdminProductUrlConstants.GET_PRODUCT_V1_CATALOGUE)
    SystemAdminCatalogueListResponseVO getProductCatalogue(
            @SpringQueryMap SystemAdminCatalogueListRequestVO requestVO);

    // @ClientCaching(name = "getDowntimeInfo", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemAdminProductUrlConstants.GET_PRODUCT_V1_DOWNTIME_INFO)
    SystemAdminDowntimeInfoVO getDowntimeInfo(@SpringQueryMap SystemAdminDowntimeInfoVO requestVO);

    @GetMapping(value = SystemAdminProductUrlConstants.GET_PRODUCT_V1_FILTER_COUNTRY_LIST)
    List<SystemAdminFilterCountryListResponseVO> getFilterCountryList(
            @SpringQueryMap SystemAdminFilterCountryListRequestVO requestVO);

    @GetMapping(value = SystemAdminProductUrlConstants.GET_PRODUCT_V1_SEO_KEYSPEC)
    SystemAdminSeoKeySpecResponseVO getSeoKeySpec(
            @SpringQueryMap SystemAdminSeoKeySpecRequestVO requestVO);

    @GetMapping(value = SystemAdminProductUrlConstants.GET_PRODUCT_V1_SEO_ENERGY_MIN_MAX)
    List<String> getSeoEnergyMinMax(@SpringQueryMap SystemAdminSeoKeySpecRequestVO requestVO);

    @GetMapping(value = SystemAdminProductUrlConstants.GET_PRODUCT_V1_LATS_INFO)
    SystemAdminLatsResponseVO getLatsInfo(@SpringQueryMap SystemAdminLatsRequestVO requestVO);
}
