package com.lge.d2x.domain.product.v1.model;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PdpSiblingItemVO {
    private String target;
    private String pdpTitle;
    private String siblingType;
    private String sku;
    private String modelId;
    private String siblingCode;
    private String pdpDefaultSiblingType;
    private int pdpLimitedNumber;
    private List<PdpSiblingModelResponseVO> siblingModels;
}
