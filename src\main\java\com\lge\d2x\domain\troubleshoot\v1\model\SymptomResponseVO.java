package com.lge.d2x.domain.troubleshoot.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SymptomResponseVO {
    @Schema(description = "sympId")
    private String sympId;

    @Schema(description = "sympName")
    private String sympName;

    @Schema(description = "symptomDisplayOrderNo")
    private String symptomDisplayOrderNo;

    @Schema(description = "symptomDepthSetup")
    private String symptomDepthSetup;
}
