package com.lge.d2x.domain.product.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductSummaryRequestVO {
    @NotBlank(message = "Missing required parameters")
    @Schema(description = ".COM 에서 사용하는 SKU", example = "OLED42C44LA.AEK.EEUK.UK.C")
    private String sku;

    @Schema(description = "Login Group")
    private String group;

    @Schema(description = "Shop Code")
    private String multishopCode;

    @Schema(description = "Customer Group")
    private String customerGroup;

    @Schema(description = "PDP 아이디")
    private String pdpId;

    @Schema(description = "에너지라벨명")
    private String energyLabelName;

    @Schema(description = "제품명")
    private String modelName;
}
