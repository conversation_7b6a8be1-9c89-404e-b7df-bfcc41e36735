package com.lge.d2x.domain.product.v1.model;

import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductSummarySpecResponseVO {
    private String sku;
    private String pimSku;
    private String bundleComponentYn;
    private String bizType;
    private String modelId;
    private String modelName;
    private String salesModelCode;
    private String userFriendlyName;
    private String largeImageAddr;
    private String mediumImageAddr;
    private String smallImageAddr;
    private String wtbUseFlag;
    private String wtbExternalLinkUseFlag;
    private String wtbExternalLinkName;
    private String wtbExternalLinkUrl;
    private String wtbExternalLinkSelfFlag;
    private String pdrUseFlag;
    private String inquiryToBuyFlag;
    private String buyNowFlag;
    private String buyNowURL;
    private String productSupportFlag;
    private String productSupportUrl;
    private String fileType;
    private String docFileName;
    private String docId;
    private String originalFileName;
    private String linkAddr;
    private String linkTargetName;
    private String specDownloadAddr;
    private String ecoBtnUrl;
    private String downTime;
    private String opentime;
    private String enegyLabelLocalizationFlag;
    private String findTheDealerFlag;
    private String obsSellFlag;
    private String productEnquiryFlag;
    private String productEnquiryUrl;
    private String retailerPricingFlag;
    private String rsUseFlag;
    private String seoSpecId;
    private Map<String, Object> dimensionMap;
    private List<Map<String, Object>> keyFeatures;
    private List<Map<String, Object>> gsri;
    private List<Map<String, Object>> keySpec;
    private List<Map<String, Object>> techSpecs;
}
