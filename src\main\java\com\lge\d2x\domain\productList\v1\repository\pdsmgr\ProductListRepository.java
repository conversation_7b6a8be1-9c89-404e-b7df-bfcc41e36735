package com.lge.d2x.domain.productList.v1.repository.pdsmgr;

import com.lge.d2x.domain.productList.v1.model.OrderProductListRequestVO;
import com.lge.d2x.domain.productList.v1.model.OrderProductListResponseVO;
import com.lge.d2x.domain.productList.v1.model.PdpIdListBySkuRequestVO;
import com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoResponseVO;
import com.lge.d2x.domain.productList.v1.model.ProductListPromotionInfoRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListPromotionInfoResponseVO;
import com.lge.d2x.domain.productList.v1.model.ProductListSelfSiblingDefaultRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListSiblingInfoRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListSiblingInfoResponseVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ProductListRepository {

    List<String> selectPdpIdBySkuId(PdpIdListBySkuRequestVO pdpIdBySkuIdRequestVO);

    List<String> selectPdpIdBySkuIdNotStandard(PdpIdListBySkuRequestVO pdpIdBySkuIdRequestVO);

    List<String> selectNewestPdpIdList(PdpIdListRequestVO newestPdpIdListRequestVO);

    List<String> selectNewestPdpIdListNotStandard(PdpIdListRequestVO newestPdpIdListRequestVO);

    List<String> selectHighlyRatedPdpIdList(PdpIdListRequestVO highlyRatedPdpIdListRequestVO);

    List<String> selectHighlyRatedPdpIdListNotStandard(
            PdpIdListRequestVO highlyRatedPdpIdListRequestVO);

    List<String> selectMostPopularPdpIdList(PdpIdListRequestVO mostPopularPdpIdListRequestVO);

    List<String> selectMostPopularPdpIdListNotStandard(
            PdpIdListRequestVO mostPopularPdpIdListRequestVO);

    List<String> selectAccessoryPdpIdList(PdpIdListRequestVO accessoryPdpIdListRequestVO);

    List<ProductListPdpInfoResponseVO> selectProductListPdpInfo(
            ProductListPdpInfoRequestVO productListPdpInfoRequestVO);

    List<ProductListPdpInfoResponseVO> selectProductListPdpInfoNotStandard(
            ProductListPdpInfoRequestVO productListPdpInfoRequestVO);

    List<ProductListSiblingInfoResponseVO> selectProductListSiblingList(
            ProductListSiblingInfoRequestVO productListSiblingInfoRequestVO);

    ProductListPromotionInfoResponseVO selectProductListPromotionInfo(
            ProductListPromotionInfoRequestVO productListPromotionInfoRequestVO);

    List<String> selectMtsPdpIdBySkuId(PdpIdListBySkuRequestVO pdpIdBySkuIdRequestVO);

    List<String> selectMtsPdpIdBySkuIdNotStandard(PdpIdListBySkuRequestVO pdpIdBySkuIdRequestVO);

    List<String> selectMtsNewestPdpIdList(PdpIdListRequestVO newestPdpIdListRequestVO);

    List<String> selectMtsNewestPdpIdListNotStandard(PdpIdListRequestVO newestPdpIdListRequestVO);

    List<String> selectMtsHighlyRatedPdpIdList(PdpIdListRequestVO highlyRatedPdpIdListRequestVO);

    List<String> selectMtsHighlyRatedPdpIdListNotStandard(
            PdpIdListRequestVO highlyRatedPdpIdListRequestVO);

    List<String> selectMtsMostPopularPdpIdList(PdpIdListRequestVO mostPopularPdpIdListRequestVO);

    List<String> selectMtsMostPopularPdpIdListNotStandard(
            PdpIdListRequestVO mostPopularPdpIdListRequestVO);

    List<String> selectMtsAccessoryPdpIdList(PdpIdListRequestVO accessoryPdpIdListRequestVO);

    String selectSelfSiblingDefaultUrl(
            ProductListSelfSiblingDefaultRequestVO selfSiblingDefaultRequestVO);

    List<ProductListPdpInfoResponseVO> selectMtsProductListPdpInfo(
            ProductListPdpInfoRequestVO productListPdpInfoRequestVO);

    List<ProductListPdpInfoResponseVO> selectMtsProductListPdpInfoNotStandard(
            ProductListPdpInfoRequestVO productListPdpInfoRequestVO);

    List<OrderProductListResponseVO> selectMtsOrderProductList(
            OrderProductListRequestVO orderProductListRequestVO);

    List<OrderProductListResponseVO> selectMtsOrderProductListNotStandard(
            OrderProductListRequestVO orderProductListRequestVO);

    List<String> selectLgPickPdpIdList(PdpIdListRequestVO accessoryPdpIdListRequestVO);
}
