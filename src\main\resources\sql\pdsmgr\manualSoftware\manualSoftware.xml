<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.manualSoftware.v1.repository.pdsmgr.ManualSoftwareRepository">
    
	<select id="softwareOsCombo" resultType="com.lge.d2x.domain.manualSoftware.v1.model.SoftwareOsResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.SoftwareOsRequestVO">
 			SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.softwareOsCombo */
 			       DISTINCT OS_NM AS OS_CODE
			     , OS_NM AS OS_NAME
			FROM SVD_SOFTWARE_OS_D
		<if test="docIdList != null">
			<foreach collection="docIdList" item="c" index="idx" open="WHERE DOC_ID IN (" separator="," close=")">
			   #{c}
			</foreach>
		</if>
    </select>  
    
	<select id="additionalSwOs" resultType="com.lge.d2x.domain.manualSoftware.v1.model.SoftwareOsResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.SoftwareOsRequestVO">
		 	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.additionalSwOs */
		 	       AA.MODEL_TYPE
                  ,AA.LINK_TITLE
                  ,AA.LINK_NAME
                  ,AA.LINK_ADDR
                  ,AA.FILE_SIZE
                  ,AA.RELEASE_DATE
                  ,AA.OS_UPGRADE_DESC
                  ,AA.LAST_UPDATE_DATE
                  ,AA.SELECT_NUM
                  ,AA.SELECT_MAX_NUM
        	FROM (
	        	SELECT A.MODEL_TYPE
				      ,A.LINK_TITLE
				      ,A.LINK_NAME
				      ,A.LINK_ADDR
				      ,A.FILE_SIZE
				      ,NVL(DATE_FORMAT(A.LAST_UPDATE_DATE, #{dateFormat}),'N/A') AS RELEASE_DATE
				      ,A.OS_UPGRADE_DESC
				      ,A.LAST_UPDATE_DATE
				      ,ROW_NUMBER() OVER(PARTITION BY a.model_type ORDER BY @rownum desc) AS SELECT_NUM
				      ,A.SELECT_MAX_NUM
	        	FROM (
		        	  SELECT  'SW' AS MODEL_TYPE
		        	        , B.MODEL_FILE_NM AS LINK_TITLE
			                , REVERSE(SUBSTR(REVERSE(LINK_URL), 1, INSTR(REVERSE(LINK_URL), '/') - 1)) AS LINK_NAME
			                , B.LINK_URL AS LINK_ADDR
			                , B.FILE_SIZE AS FILE_SIZE
			                , B.LAST_UPDATE_DATE			                
			                , '' AS OS_UPGRADE_DESC
			                , COUNT(1) OVER() AS SELECT_MAX_NUM
			                , @rownum:=0
			          FROM SVD_MODEL_FILE_R B 
			          WHERE B.USE_FLAG = 'Y'
			            AND B.SITE_CODE = #{siteCode}
			            AND B.CUST_MODEL_CODE  = (SELECT DISTINCT BUYER_MODEL_CODE 
			            						FROM PDM_PRODUCT_SVC_D A
			            						JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
												WHERE 1=1
												AND (B.SALES_CODE = UPPER(#{modelNum}) OR A.BUYER_MODEL_CODE = UPPER(#{modelNum}))
												AND A.USE_FLAG = 'Y'
												AND B.USE_FLAG = 'Y'
												LIMIT 1
												)
				 ) A
				ORDER BY  A.LAST_UPDATE_DATE DESC
			) AA
			ORDER BY AA.MODEL_TYPE DESC, AA.SELECT_NUM ASC
    </select>  
    
	<select id="gpSoftwareListAll" resultType="com.lge.d2x.domain.manualSoftware.v1.model.GpSoftwareResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.GpSoftwareRequestVO">
	    WITH /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.gpSoftwareListAll */
				   SWMDL AS (
				      SELECT DISTINCT C.BUYER_MODEL_CODE , C.DOC_ID , Z.SITE_CODE
				      FROM SVD_SOFTWARE_MODEL_R C
				        JOIN  SVD_SOFTWARE_M A 
				        ON (C.DOC_ID = A.DOC_ID )
				        JOIN SVD_SOFTWARE_BUYER_R B 
				          ON (A.DOC_ID = B.DOC_ID)
				        JOIN COM_SVD_PREFERENCE_M X 
				          ON (B.BUYER_CODE = X.BUYER_CODE)
				        JOIN COM_LOCALE_M Y 
				          ON (X.COUNTRY_CODE = Y.COUNTRY_CODE)
				         JOIN COM_SITE_M Z 
				         on (Y.locale_code = Z.locale_code) 
				        WHERE 1=1
				        AND A.DEL_FLAG = 'N' 
				        AND A.OPEN_STATE_CODE IN ('ONSERVICE','TEMP') 
				        AND A.BUYER_CODE = '00000000'
				        AND Z.SITE_CODE =#{siteCode}
				        AND (C.AFFILIATE_CODE , C.FACTORY_MODEL_CODE , C.FACTORY_SUFFIX_CODE,C.OBU_CODE) IN 
									  (
										SELECT GG.AFFILIATE_CODE, GG.FACTORY_MODEL_CODE, GG.FACTORY_SUFFIX_CODE, GG.OBU_CODE
										FROM SVD_GSCS_MODEL_M GG
										JOIN (SELECT
												 CC.AFFILIATE_CODE, CC.FACTORY_MODEL_CODE, CC.FACTORY_SUFFIX_CODE, CC.OBU_CODE
										  FROM PDM_PRODUCT_SVC_D AA
										  JOIN SVD_PRODUCT_SVC_MODEL_D BB
										  	ON (AA.SVD_SKU_ID = BB.SVD_SKU_ID AND BB.SITE_CODE = #{siteCode})
										  JOIN SVD_CUSTOMER_MODEL_FACTORY_R CC
											ON (AA.BUYER_MODEL_CODE = CC.CUST_MODEL_CODE)
											WHERE BB.SALES_CODE = #{modelNum}
										   AND CC.BUYER_CODE =  #{buyerCode}) DD
											ON (
												GG.FACTORY_MODEL_CODE = DD.FACTORY_MODEL_CODE
											AND GG.FACTORY_SUFFIX_CODE = DD.FACTORY_SUFFIX_CODE
											AND GG.AFFILIATE_CODE = DD.AFFILIATE_CODE
											AND GG.OBU_CODE = DD.OBU_CODE
											  )
										WHERE GG.BUYER_CODE =  #{buyerCode}
									  )
						),
				  SWLIST  AS (
					 SELECT AA1.CUST_MODEL_CODE ,
							AA1.DOC_ID,
							AA1.GLOBAL_DOC_ID,
							AA1.TITLE_NAME,
							CASE WHEN INSTR(TRIM(AA1.DETAIL_CONTENT),<![CDATA['<') = 1 THEN REPLACE(NVL(AA1.DETAIL_CONTENT,AA1.DETAIL_CONTENT2),'\n','<br>') ELSE REPLACE(REPLACE(NVL(AA1.DETAIL_CONTENT,AA1.DETAIL_CONTENT2),'\n','<br>'),' ','&nbsp;') ]]>END AS DETAIL_CONTENT ,
							AA1.HIGH_DOC_ID,
							AA1.IS_SW_DRV_LOCAL
					  FROM (
							SELECT A1.CUST_MODEL_CODE ,
								   A1.DOC_ID,
								   A1.GLOBAL_DOC_ID,
								   A1.TITLE_NAME,
								   (
									SELECT GROUP_CONCAT<![CDATA[('<h4>',A.CONTENT_TITLE,'</h4><p>', A.CONTENT_CNTS,'</p><br>')]]>
									  FROM SVD_SOFTWARE_CONTENT_D A  
									 WHERE A.USE_FLAG = 'Y'
									   AND  A.SW_CONTENT_DOC_ID = A1.GLOBAL_DOC_ID
									   GROUP BY  A.SW_CONTENT_DOC_ID
									)
								   AS DETAIL_CONTENT,
								   CASE WHEN A1.IS_SW_DRV_LOCAL = 'Y' THEN '' ELSE A1.DETAIL_CONTENT END DETAIL_CONTENT2,
								   A1.HIGH_DOC_ID,
								   A1.IS_SW_DRV_LOCAL
							FROM  (
								   SELECT DISTINCT C.BUYER_MODEL_CODE AS CUST_MODEL_CODE ,
										  A.SW_CONTENT_DOC_ID as DOC_ID ,
										  A.SW_CONTENT_DOC_ID AS GLOBAL_DOC_ID,
										  E.SUBJECT_CNTS AS TITLE_NAME,
										  <![CDATA[CONCAT('<h4>',A.CONTENT_TITLE,'</h4><p>', A.CONTENT_CNTS,'</p><br>') AS DETAIL_CONTENT,]]>
										  E.HIGH_DOC_ID,
										  'N' AS  IS_SW_DRV_LOCAL
								   FROM SVD_SOFTWARE_CONTENT_D A 
								   JOIN SWMDL C ON ( A.SW_CONTENT_DOC_ID = C.DOC_ID)
								   JOIN SVD_SOFTWARE_M E ON ( A.SW_CONTENT_DOC_ID = E.DOC_ID)
							WHERE    C.SITE_CODE =  #{siteCode}
							  AND    A.USE_FLAG = 'Y'
							  AND    E.DEL_FLAG ='N'
							  AND    E.OPEN_STATE_CODE IN ( 'ONSERVICE','TEMP')
							  AND    E.LANG_CODE IN (SELECT LANG_CODE
														   FROM   SVD_COUNTRY_LANG_D F
														  WHERE  COUNTRY_CODE =  #{countryCode})
					  ) A1 
				  ) AA1 
				  )
				  SELECT SWLIST.CUST_MODEL_CODE ,
					 SWLIST.DOC_ID,
					 SWLIST.GLOBAL_DOC_ID,
					 SWLIST.TITLE_NAME AS TITLE,
					 SWLIST.DETAIL_CONTENT,
					 OCCR_DATE,
					 '' AS FILE_SIZE,
					 IS_SW_DRV_LOCAL,
					 OS.OS_ID as OS_CODE ,
					 OS.OS_NM,
					 F.F_FILE_NM,
					 F.F_ORIGINAL_FILE_NM,
					 F.F_FILE_SIZE,
					 F.F_FILE_PATH,
					 IFNULL(F.F_FILE_SYSTEM_CODE, '') AS F_FILE_SYSTEM_CODE,
					 <if test='siteCode eq "ES"'> 
					IFNULL((SELECT DATE_FORMAT(Q.OCCR_DATE,'%Y%m%d') 
					 FROM SVD_SOFTWARE_M C  
					 JOIN SVD_SOFTWARE_M T ON (C.HIGH_DOC_ID = T.HIGH_DOC_ID) 
					 JOIN SVD_SOFTWARE_FILE_D S ON (C.HIGH_DOC_ID = S.SW_FILE_DOC_ID)
					 JOIN SVD_SOFTWARE_WRITER_D Q ON (Q.SW_AUTHOR_DOC_ID = T.DOC_ID)
					WHERE C.HIGH_DOC_ID =  SWLIST.GLOBAL_DOC_ID
					  AND C.BUYER_CODE <![CDATA[<>]]> '00000000'
					  AND T.BUYER_CODE = '00000000'
					  AND C.DOC_ID = SWLIST.DOC_ID
					  AND S.FILE_NM = F.F_FILE_NM
					  AND Q.SW_AUTHOR_SEQ = ( SELECT MAX(SW_AUTHOR_SEQ)
											 FROM SVD_SOFTWARE_WRITER_D Z
							     		    WHERE Z.SW_AUTHOR_DOC_ID = T.DOC_ID
											  AND Z.OPERATION_NM <![CDATA[<>]]> 'DELETE')),DATE_FORMAT(B.OCCR_DATE ,'%Y%m%d')) F_RELEASE_DATE,
					</if>
					<if test='siteCode neq "ES"'>
						 DATE_FORMAT(B.OCCR_DATE , #{dateFormat}) AS F_RELEASE_DATE,
					 </if>
					 F.FILE_SEQ AS F_FILE_SEQ,
					 ROW_NUMBER() OVER(PARTITION BY SWLIST.CUST_MODEL_CODE ,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ORDER BY SWLIST.CUST_MODEL_CODE ,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ) AS DOC_DISPLAY,
					 ROW_NUMBER() OVER(PARTITION BY SWLIST.CUST_MODEL_CODE ,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, OS.OS_ID ORDER BY SWLIST.CUST_MODEL_CODE ,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, OS.OS_ID) AS OS_DISPLAY,
					 ROW_NUMBER() OVER(PARTITION BY SWLIST.CUST_MODEL_CODE ,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, F.FILE_SEQ ORDER BY SWLIST.CUST_MODEL_CODE ,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, F.FILE_SEQ) AS FILE_DISPLAY,
					 ROW_NUMBER() OVER(PARTITION BY SWLIST.CUST_MODEL_CODE ,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ORDER BY SWLIST.CUST_MODEL_CODE ,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID) AS SW_DISPLAY,
					 ROW_NUMBER() OVER(PARTITION BY OS.OS_ID ORDER BY OS.OS_ID) AS OS_ROW,
					 ROW_NUMBER() OVER(PARTITION BY F.FILE_ROW ORDER BY F.FILE_ROW) AS FILE_ROW,
					 CONCAT(
					   MAX(DATE_FORMAT( #{dateFormat} ,'%Y%m%d')) OVER(PARTITION BY SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ORDER BY SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID) ,
					   MAX(CONCAT(LPAD(SWLIST.DOC_ID, 25, '0'),SWLIST.GLOBAL_DOC_ID)) OVER(PARTITION BY SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ORDER BY SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID)
					 ) AS DOC_ORD
					 <if test='siteCode eq "ES"'>
		          ,IFNULL((SELECT DATE_FORMAT(Q.OCCR_DATE,'%Y%m%d') 
					 FROM SVD_SOFTWARE_M C 
					 JOIN SVD_SOFTWARE_M T ON (C.HIGH_DOC_ID = T.HIGH_DOC_ID)
					 JOIN SVD_SOFTWARE_FILE_D S ON (C.HIGH_DOC_ID = S.SW_FILE_DOC_ID)
					 JOIN SVD_SOFTWARE_WRITER_D Q ON(T.DOC_ID = Q.SW_AUTHOR_DOC_ID)
					WHERE C.HIGH_DOC_ID =  SWLIST.GLOBAL_DOC_ID
					  AND C.BUYER_CODE <![CDATA[<>]]> '00000000'
					  AND T.BUYER_CODE = '00000000'
					  AND C.DOC_ID = SWLIST.DOC_ID
					  AND S.FILE_NM = F.F_FILE_NM
					  AND Q.SW_AUTHOR_SEQ = ( SELECT MAX(SW_AUTHOR_SEQ)
											 FROM SVD_SOFTWARE_WRITER_D Z
										    WHERE Z.SW_AUTHOR_DOC_ID = T.DOC_ID
											  AND Z.OPERATION_NM <![CDATA[<>]]> 'DELETE' )),NVL(DATE_FORMAT(B.OCCR_DATE ,'%Y%M%D'),'0')) F_ORDER_RELEASEDATE
		            ,MAX(IFNULL((SELECT DATE_FORMAT(Q.OCCR_DATE,'%Y%m%d') 
					 FROM SVD_SOFTWARE_M C 
					 JOIN SVD_SOFTWARE_M T ON (C.HIGH_DOC_ID = T.HIGH_DOC_ID) 
					 JOIN SVD_SOFTWARE_FILE_D S ON (C.HIGH_DOC_ID = S.SW_FILE_DOC_ID)
					 JOIN SVD_SOFTWARE_WRITER_D Q ON(T.DOC_ID = Q.SW_AUTHOR_DOC_ID )
					WHERE C.HIGH_DOC_ID =  SWLIST.GLOBAL_DOC_ID
					  AND C.BUYER_CODE <![CDATA[<>]]> '00000000'
					  AND T.BUYER_CODE = '00000000'
					  AND C.DOC_ID = SWLIST.DOC_ID
					  AND S.FILE_NM = F.F_FILE_NM
					  AND Q.SW_AUTHOR_SEQ = ( SELECT MAX(SW_AUTHOR_SEQ)
											 FROM SVD_SOFTWARE_WRITER_D Z
										    WHERE Z.SW_AUTHOR_DOC_ID = T.DOC_ID
											  AND Z.OPERATION_NM <![CDATA[<>]]> 'DELETE' )),NVL(DATE_FORMAT(B.OCCR_DATE ,'%Y%m%d'),'0'))) OVER(PARTITION BY ( SWLIST.DOC_ID ) ) F_ORDER_CONDITION
					 </if>
					,'' AS F_ORDER_CONDITION
				   FROM
						 SWLIST LEFT OUTER JOIN
						 (
							SELECT  @ROWID:=@ROWID+1 AS FILE_ROW,
								SW_FILE_DOC_ID AS DOC_ID,
							   SW_FILE_SEQ as  FILE_SEQ,
							   SW_FILE_DOC_ID AS F_DOC_ID,
							   SW_FILE_DOC_ID AS F_GLOBAL_DOC_ID,
							   FILE_NM AS F_FILE_NM,
							   REPLACE(ORIGINAL_FILE_NM, ' ', '_') AS F_ORIGINAL_FILE_NM,
							   FILE_SIZE AS F_FILE_SIZE,
							   FILE_PATH AS F_FILE_PATH,
							   'GCSC' AS F_FILE_SYSTEM_CODE,
							   LIST_EXP_FLAG
							FROM SVD_SOFTWARE_FILE_D
							WHERE (SW_FILE_DOC_ID,'N') IN ((SELECT DISTINCT DOC_ID,IS_SW_DRV_LOCAL FROM SWLIST))
						   ) F  ON SWLIST.DOC_ID = F.DOC_ID
						  AND F.LIST_EXP_FLAG = '1'
						  LEFT OUTER JOIN
						   (
								SELECT A1.SW_AUTHOR_DOC_ID,
									   A1.OCCR_DATE
								FROM (
									SELECT A.SW_AUTHOR_DOC_ID,
										   A.OCCR_DATE,
										   A.SW_AUTHOR_SEQ,
										   MAX(A.SW_AUTHOR_SEQ) OVER(PARTITION BY A.SW_AUTHOR_DOC_ID ORDER BY A.SW_AUTHOR_DOC_ID) AS MX_WRITER_SEQ
									FROM   SVD_SOFTWARE_WRITER_D A
									WHERE  A.SW_AUTHOR_DOC_ID  IN ((SELECT DOC_ID FROM SWLIST))
								) A1
								WHERE A1.SW_AUTHOR_SEQ = A1.MX_WRITER_SEQ
						   ) B ON F.DOC_ID = B.SW_AUTHOR_DOC_ID
						   LEFT OUTER JOIN SVD_SOFTWARE_OS_D AS OS ON SWLIST.HIGH_DOC_ID = OS.DOC_ID
		<if test="searchOs != null and searchOs != 'All'">
			<![CDATA[
				AND EXISTS (SELECT 1
				              FROM SVD_SOFTWARE_OS_D X
				              JOIN SVD_SOFTWARE_M Y ON (X.DOC_ID = Y.HIGH_DOC_ID)
				               WHERE Y.DOC_ID = SWLIST.DOC_ID
						       AND X.OS_NM = #{searchOs}
		           )
		      WHERE OS.OS_NM = #{searchOs}
			]]>
			</if>
		<if test='siteCode eq "ES"'>
          ORDER BY CONCAT(F_ORDER_CONDITION,SWLIST.DOC_ID) DESC
		</if>
		<if test='siteCode neq "ES"'>
          ORDER BY OCCR_DATE DESC, DOC_ORD DESC
		</if>
    </select>
    
    
    
        <select id="gpProductSupportDetail" resultType="com.lge.d2x.domain.manualSoftware.v1.model.GpProductSupportDetailResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.GpProductSupportDetailRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.gpProductSupportDetail */
			       I.CS_SALES_CODE
			     , I.SALES_MODEL_CODE
			     , I.SALES_SUFFIX_CODE
			     , I.CS_CUSTOMER_MAPPING_MODEL
			     , I.CS_MATCHED_MODEL_CODE
			     , I.CS_MDMS_PRODUCT_CODE
			     , I.CS_SUPER_CATEGORY_ID
			     , I.CS_SUPER_CATEGORY_NAME
			     , I.SUPER_STICKY_IMAGE_ADDR
				 , I.CS_CATEGORY_ID
				 , I.CS_CATEGORY_NAME
				 , I.STICKY_IMAGE_ADDR
			     , I.CS_SUB_CATEGORY_ID
			     , I.CS_SUB_CATEGORY_NAME
			     , I.SUB_STICKY_IMAGE_ADDR
				 , I.CS_MODEL_IMAGE_PATH
				 , I.CS_PRODUCT_PAGE_LINK_STATUS
				 , I.CS_PRODUCT_PAGE_LINK
			     , I.CS_MOBILE_MODEL_FLAG
			     , I.CUSTOMER_MODEL_CODE
				 , I.CS_LINK_CUST_MODEL_CODE
				 , I.CS_MATCHED_MODEL_DISPLAY
			     , I.GSFS_FLAG
				 , I.MKT_PAGE_LINK
			     , I.SW_TAB_OPTION
			     , I.CS_CUSTOMER_MODEL
			     , I.CS_SALES_MODEL_CODE
			     , I.ORDER_NUMBER
		         , I.MODEL_RELEASE_DATE
				 , I.SALES_MODEL_FLAG
			     , I.OBU_CODE
		         , I.USER_FRIENDLY_NAME
			     , (
               		SELECT O.LBR_WTY_PRODUCT_TEXT_CNTS
          		        FROM SVD_WTY_INFO_M O
          		        JOIN SVD_WTY_INFO_MODEL_R P
          		                 ON O.DOC_ID = P.DOC_ID
          		        WHERE  O.SITE_CODE =  #{siteCode}
  		                AND    O.COUNTRY_CODE = #{countryCode}
  		                AND    Nvl(O.USE_FLAG, 'Y') = 'Y'
  		                AND    O.OPEN_STATE_CODE = 'ONSERVICE'
  		                AND   (P.CUST_MODEL_CODE = I.CS_SALES_CODE or P.CUST_MODEL_CODE = I.CS_CUSTOMER_MODEL)
          		        UNION ALL
          		        SELECT Q.LBR_WTY_PRODUCT_TEXT_CNTS
          		        FROM   SVD_WTY_INFO_M Q
          		        WHERE  Q.SITE_CODE =  #{siteCode}
  		                AND    Q.COUNTRY_CODE = #{countryCode}
  		                AND    Nvl(Q.USE_FLAG, 'Y') = 'Y'
  		                AND    Q.OPEN_STATE_CODE = 'ONSERVICE'
  			            AND    Q.LV1_CATEGORY_CODE = I.CS_SUPER_CATEGORY_ID
  			            AND    Q.LV2_CATEGORY_CODE = I.CS_CATEGORY_ID
  		                AND    Q.LV3_CATEGORY_CODE = I.CS_SUB_CATEGORY_ID
  		                AND    NOT EXISTS (SELECT 'Y'
  		                                     FROM SVD_WTY_INFO_MODEL_R R
  		                                    WHERE R.SITE_CODE =  #{siteCode}
  		                                      AND R.DOC_ID = Q.DOC_ID)
          		        UNION ALL
          		        SELECT S.LBR_WTY_PRODUCT_TEXT_CNTS
          		        FROM   SVD_WTY_INFO_M S
          		        WHERE  S.SITE_CODE =  #{siteCode}
          		               AND S.COUNTRY_CODE = #{countryCode}
          		               AND Nvl(S.USE_FLAG, 'Y') = 'Y'
          		               AND S.OPEN_STATE_CODE = 'ONSERVICE'
          		               AND S.LV1_CATEGORY_CODE = I.CS_SUPER_CATEGORY_ID
          			           AND S.LV2_CATEGORY_CODE = I.CS_CATEGORY_ID
          		               AND NOT EXISTS (SELECT 'Y'
          		                               FROM   SVD_WTY_INFO_MODEL_R T
          		                               WHERE  T.SITE_CODE =  #{siteCode}
          		                               AND T.DOC_ID = S.DOC_ID)
          		        UNION ALL
          		        SELECT U.LBR_WTY_PRODUCT_TEXT_CNTS
          		        FROM   SVD_WTY_INFO_M U
          		        WHERE  U.SITE_CODE = #{siteCode}
  		                AND    U.COUNTRY_CODE =#{countryCode}
  		                AND    Nvl(U.USE_FLAG, 'Y') = 'Y'
  		                AND    U.OPEN_STATE_CODE = 'ONSERVICE'
  		                AND    U.LV1_CATEGORY_CODE = I.CS_SUPER_CATEGORY_ID
  		                AND    NOT EXISTS (SELECT 'Y'
  		                                     FROM  SVD_WTY_INFO_MODEL_R V
  		                                    WHERE  V.SITE_CODE =  #{siteCode}
  		                                      AND  V.DOC_ID = U.DOC_ID)
               		LIMIT  1
              	  ) AS LBR_WTY_PRD_TEXT
              	, (
               		SELECT W.PART_WTY_PRODUCT_TEXT_CNTS
          		        FROM   SVD_WTY_INFO_M W
          		        JOIN   SVD_WTY_INFO_MODEL_R X
          		        ON     W.DOC_ID = X.DOC_ID
          		        WHERE  W.SITE_CODE = #{siteCode}
  		                AND W.COUNTRY_CODE = #{countryCode}
  		                AND Nvl(W.USE_FLAG, 'Y') = 'Y'
  		                AND W.OPEN_STATE_CODE = 'ONSERVICE'
  		                AND (X.CUST_MODEL_CODE = I.CS_SALES_CODE or X.CUST_MODEL_CODE = I.CS_CUSTOMER_MODEL)
          		        UNION ALL
          		        SELECT Y.PART_WTY_PRODUCT_TEXT_CNTS
          		        FROM   SVD_WTY_INFO_M Y
          		        WHERE  Y.SITE_CODE =  #{siteCode}
  		                AND    Y.COUNTRY_CODE = #{countryCode}
  		                AND    Nvl(Y.USE_FLAG, 'Y') = 'Y'
  		                AND    Y.OPEN_STATE_CODE = 'ONSERVICE'
  			            AND    Y.LV1_CATEGORY_CODE = I.CS_SUPER_CATEGORY_ID
  			            AND    Y.LV2_CATEGORY_CODE = I.CS_CATEGORY_ID
  		                AND    Y.LV3_CATEGORY_CODE = I.CS_SUB_CATEGORY_ID
  		                AND    NOT EXISTS (SELECT 'Y'
          		                             FROM  SVD_WTY_INFO_MODEL_R Z
          		                            WHERE  Z.SITE_CODE =  #{siteCode}
          		                              AND  Z.DOC_ID = Y.DOC_ID)
          		        UNION ALL
          		        SELECT AA.PART_WTY_PRODUCT_TEXT_CNTS
          		        FROM   SVD_WTY_INFO_M AA
          		        WHERE  AA.SITE_CODE =  #{siteCode}
  		                AND    AA.COUNTRY_CODE = #{countryCode}
  		                AND    Nvl(AA.USE_FLAG, 'Y') = 'Y'
  		                AND    AA.OPEN_STATE_CODE = 'ONSERVICE'
  		                AND    AA.LV1_CATEGORY_CODE = I.CS_SUPER_CATEGORY_ID
  			            AND    AA.LV2_CATEGORY_CODE = I.CS_CATEGORY_ID
  		                AND    NOT EXISTS (SELECT 'Y'
   		                                 FROM SVD_WTY_INFO_MODEL_R BB
   		                                WHERE BB.SITE_CODE = #{siteCode}
  		                                      AND BB.DOC_ID = AA.DOC_ID)
          		        UNION ALL
          		        SELECT CC.PART_WTY_PRODUCT_TEXT_CNTS
          		        FROM   SVD_WTY_INFO_M CC
          		        WHERE  CC.SITE_CODE =  #{siteCode}
  		                AND    CC.COUNTRY_CODE = #{countryCode}
  		                AND    Nvl(CC.USE_FLAG, 'Y') = 'Y'
  		                AND    CC.OPEN_STATE_CODE = 'ONSERVICE'
  		                AND    CC.LV1_CATEGORY_CODE = I.CS_SUPER_CATEGORY_ID
  		                AND    NOT EXISTS (SELECT 'Y'
  		                                     FROM  SVD_WTY_INFO_MODEL_R DD
  		                                    WHERE  DD.SITE_CODE =  #{siteCode}
  		                                      AND  DD.DOC_ID = CC.DOC_ID)
               		LIMIT  1
              	  ) AS PART_WTY_PRD_TEXT
				 , I.LOCALE_CODE
		  FROM  (
				SELECT IFNULL(B.SALES_CODE , '') AS CS_SALES_CODE
					, IFNULL(C.SALES_MODEL_CODE , '') AS CS_SALES_MODEL_CODE
					, IFNULL(C.SALES_MODEL_CODE , '') AS SALES_MODEL_CODE
					, IFNULL(C.SALES_MODEL_SUFFIX_CODE , '') AS SALES_SUFFIX_CODE
					, C.MDMS_PRDGRP_CODE AS CS_MDMS_PRODUCT_CODE
					, CASE IFNULL(F.PDP_ID, 'NOT_MATCHED') WHEN 'NOT_MATCHED' THEN E.CUST_MODEL_CODE ELSE IFNULL(G.PRODUCT_NM, '') END AS CS_CUSTOMER_MAPPING_MODEL
					, A.LV1_CATEGORY_CODE         AS CS_SUPER_CATEGORY_ID
					, SUPER.SITE_CATEGORY_NM      AS CS_SUPER_CATEGORY_NAME
					, ( SELECT IFNULL(J.STICKY_IMG_URL,'') FROM DSP_DISPLAY_CATEGORY_D J
						WHERE STICKY_USE_FLAG = 'Y'
							AND J.SITE_CODE = A.SITE_CODE
							AND J.CATEGORY_CODE = A.LV1_CATEGORY_CODE
							AND J.SHOP_CODE = 'D2C'
						) AS SUPER_STICKY_IMAGE_ADDR
					, A.LV2_CATEGORY_CODE         AS CS_CATEGORY_ID
					, D.SITE_CATEGORY_NM          AS CS_CATEGORY_NAME
					, ( SELECT IFNULL(K.STICKY_IMG_URL,'') FROM DSP_DISPLAY_CATEGORY_D K
						WHERE STICKY_USE_FLAG = 'Y'
							AND K.SITE_CODE = A.SITE_CODE
							AND K.CATEGORY_CODE = A.LV2_CATEGORY_CODE
							AND K.SHOP_CODE = 'D2C'
						) AS STICKY_IMAGE_ADDR
					, IF(trim(UPPER(A.LV3_CATEGORY_CODE))='OTHERS', 'Others',A.LV3_CATEGORY_CODE)     AS CS_SUB_CATEGORY_ID
					, IF(trim(UPPER(A.LV3_CATEGORY_CODE))='OTHERS', 'Others',SUB.SITE_CATEGORY_NM)    AS CS_SUB_CATEGORY_NAME
					, ( SELECT IFNULL(L.STICKY_IMG_URL,'') FROM DSP_DISPLAY_CATEGORY_D L
						WHERE STICKY_USE_FLAG = 'Y'
							AND L.SITE_CODE = A.SITE_CODE
							AND L.CATEGORY_CODE = A.LV3_CATEGORY_CODE
							AND L.SHOP_CODE = 'D2C'
						) AS SUB_STICKY_IMAGE_ADDR
					, IFNULL(DATE_FORMAT(G.PRODUCT_RELES_DD,'%Y-%m-%d'), '') AS MODEL_RELEASE_DATE
					, IFNULL(IFNULL(NULLIF(PPD.NEW_MKT_PRODUCT_NM, ''), G.USER_FRNDY_PRODUCT_NM ), '') AS USER_FRIENDLY_NAME
					, IFNULL(B.MODEL_IMG_PATH, '') AS CS_MODEL_IMAGE_PATH
					, B.MOBL_MODEL_FLAG   		 AS CS_MOBILE_MODEL_FLAG
					, C.BUYER_MODEL_CODE			 AS CUSTOMER_MODEL_CODE
					, NVL(B.GSFS_USE_FLAG,'Y')	 AS GSFS_FLAG
					, B.SALES_MODEL_FLAG
					, C.OBU_CODE
					,(SELECT SSTD.OPT_TYPE_CODE
						FROM SVD_SOFTWARE_TAB_D SSTD
						WHERE SSTD.CUST_MODEL_CODE  = C.BUYER_MODEL_CODE
						AND SSTD.SITE_CODE = #{siteCode}
						LIMIT 1			       ) AS SW_TAB_OPTION
					, C.BUYER_MODEL_CODE			   AS CS_CUSTOMER_MODEL
					, IFNULL(F.PDP_ID, 'NOT_MATCHED') AS CS_MATCHED_MODEL_CODE
					, '1' ORDER_NUMBER
					, A.SITE_CODE				      AS LOCALE_CODE
					, IFNULL(F.PRODUCT_STATE_CODE, 'NOT_MATCHED') AS CS_PRODUCT_PAGE_LINK_STATUS
					, F.PDP_URL AS CS_PRODUCT_PAGE_LINK
					, IFNULL((SELECT CONCAT(K.SALES_MODEL_CODE,'.',K.SALES_MODEL_SUFFIX_CODE)
								FROM PDM_PRODUCT_M K
								WHERE K.SKU_ID = G.SKU_ID
								AND K.SALES_MODEL_CODE IS NOT NULL
								LIMIT 1
							), G.PRODUCT_NM) AS CS_MATCHED_MODEL_DISPLAY
					, IFNULL(B.SALES_CODE , '') AS CS_LINK_CUST_MODEL_CODE
					, (CASE (F.PRODUCT_STATE_CODE)
							WHEN 'ACTIVE' THEN REPLACE(F.PDP_URL,'.csp','')
							WHEN 'DISCONTINUED' THEN REPLACE(F.PDP_URL,'.csp','')
							ELSE NULL
							END )  AS MKT_PAGE_LINK
					FROM SVD_PRODUCT_SVC_MODEL_CATEGORY_R A
				INNER JOIN SVD_PRODUCT_SVC_MODEL_D B  
					ON A.SITE_CODE = B.SITE_CODE
					AND A.SVD_SKU_ID = B.SVD_SKU_ID
				INNER JOIN PDM_PRODUCT_SVC_D C
					ON A.SVD_SKU_ID = C.SVD_SKU_ID
				INNER JOIN DSP_DISPLAY_CATEGORY_M D
					ON A.SITE_CODE = D.SITE_CODE
					AND A.LV2_CATEGORY_CODE = D.CATEGORY_CODE
				LEFT JOIN DSP_DISPLAY_CATEGORY_M SUPER
					ON A.SITE_CODE = SUPER.SITE_CODE
					AND A.LV1_CATEGORY_CODE = SUPER.CATEGORY_CODE
				LEFT JOIN DSP_DISPLAY_CATEGORY_M SUB
					ON A.SITE_CODE = SUB.SITE_CODE
					AND A.LV3_CATEGORY_CODE = SUB.CATEGORY_CODE
					LEFT JOIN SVD_CUSTOMER_MODEL_M E
					ON A.SITE_CODE = E.SITE_CODE
					AND C.BUYER_MODEL_CODE = E.CUST_MODEL_CODE
					AND E.USE_FLAG='Y'
					LEFT JOIN DSP_PDP_D F
					ON E.PDP_ID=F.PDP_ID
					AND F.USE_FLAG = 'Y'
					AND F.AEM_PUBL_FLAG = 'Y'
					AND F.SITE_CODE = A.SITE_CODE
					AND F.SHOP_CODE  = 'D2C'
				LEFT JOIN DSP_PDP_M G 
					ON F.PDP_ID = G.PDP_ID
					AND F.SITE_CODE = G.SITE_CODE 
				LEFT JOIN PDM_PRODUCT_D PPD
					ON G.SKU_ID = PPD.SKU_ID
				AND F.BIZ_TYPE_CODE = PPD.BIZ_TYPE_CODE
				WHERE A.USE_FLAG = 'Y'
					AND A.SITE_CODE = #{siteCode}
					AND D.SUPP_USE_FLAG = 'Y'
					AND (B.SALES_CODE = UPPER(#{csSalesCode}) OR C.BUYER_MODEL_CODE = UPPER(#{csSalesCode}))
			UNION ALL
				SELECT IFNULL(B.SALES_CODE , '') AS CS_SALES_CODE
					, IFNULL(C.SALES_MODEL_CODE , '') AS CS_SALES_MODEL_CODE
					, IFNULL(C.SALES_MODEL_CODE , '') AS SALES_MODEL_CODE
					, IFNULL(C.SALES_MODEL_SUFFIX_CODE , '') AS SALES_SUFFIX_CODE
					, C.MDMS_PRDGRP_CODE AS CS_MDMS_PRODUCT_CODE
					, CASE IFNULL(F.PDP_ID, 'NOT_MATCHED') WHEN 'NOT_MATCHED' THEN E.CUST_MODEL_CODE ELSE IFNULL(G.PRODUCT_NM, '') END AS CS_CUSTOMER_MAPPING_MODEL
					, A.LV1_CATEGORY_CODE         AS CS_SUPER_CATEGORY_ID
					, SUPER.SITE_CATEGORY_NM      AS CS_SUPER_CATEGORY_NAME
					, ( SELECT IFNULL(J.STICKY_IMG_URL,'') FROM DSP_DISPLAY_CATEGORY_D J
						WHERE STICKY_USE_FLAG = 'Y'
							AND J.SITE_CODE = A.SITE_CODE
							AND J.CATEGORY_CODE = A.LV1_CATEGORY_CODE
							AND J.SHOP_CODE = 'D2C'
						) AS SUPER_STICKY_IMAGE_ADDR
					, A.LV2_CATEGORY_CODE         AS CS_CATEGORY_ID
					, D.SITE_CATEGORY_NM          AS CS_CATEGORY_NAME
					, ( SELECT IFNULL(K.STICKY_IMG_URL,'') FROM DSP_DISPLAY_CATEGORY_D K
						WHERE STICKY_USE_FLAG = 'Y'
							AND K.SITE_CODE = A.SITE_CODE
							AND K.CATEGORY_CODE = A.LV2_CATEGORY_CODE
							AND K.SHOP_CODE = 'D2C'
						) AS STICKY_IMAGE_ADDR
					, IF(trim(UPPER(A.LV3_CATEGORY_CODE))='OTHERS', 'Others',A.LV3_CATEGORY_CODE)     AS CS_SUB_CATEGORY_ID
					, IF(trim(UPPER(A.LV3_CATEGORY_CODE))='OTHERS', 'Others',SUB.SITE_CATEGORY_NM)    AS CS_SUB_CATEGORY_NAME
					, ( SELECT IFNULL(L.STICKY_IMG_URL,'') FROM DSP_DISPLAY_CATEGORY_D L
						WHERE STICKY_USE_FLAG = 'Y'
							AND L.SITE_CODE = A.SITE_CODE
							AND L.CATEGORY_CODE = A.LV3_CATEGORY_CODE
							AND L.SHOP_CODE = 'D2C'
						) AS SUB_STICKY_IMAGE_ADDR
					, IFNULL(DATE_FORMAT(G.PRODUCT_RELES_DD,'%Y-%m-%d'), '') AS MODEL_RELEASE_DATE
					, IFNULL(IFNULL(NULLIF(PPD.NEW_MKT_PRODUCT_NM, ''), G.USER_FRNDY_PRODUCT_NM ), '') AS USER_FRIENDLY_NAME
					, IFNULL(B.MODEL_IMG_PATH, '') AS CS_MODEL_IMAGE_PATH
					, B.MOBL_MODEL_FLAG   		 AS CS_MOBILE_MODEL_FLAG
					, C.BUYER_MODEL_CODE			 AS CUSTOMER_MODEL_CODE
					, NVL(B.GSFS_USE_FLAG,'Y')	 AS GSFS_FLAG
					, B.SALES_MODEL_FLAG
					, C.OBU_CODE
					,(SELECT SSTD.OPT_TYPE_CODE
						FROM SVD_SOFTWARE_TAB_D SSTD
						WHERE SSTD.CUST_MODEL_CODE  = C.BUYER_MODEL_CODE
						AND SSTD.SITE_CODE = #{siteCode}
						LIMIT 1			       ) AS SW_TAB_OPTION
					, C.BUYER_MODEL_CODE			   AS CS_CUSTOMER_MODEL
					, IFNULL(F.PDP_ID, 'NOT_MATCHED') AS CS_MATCHED_MODEL_CODE
					, '2' ORDER_NUMBER
					, A.SITE_CODE				      AS LOCALE_CODE
					, IFNULL(F.PRODUCT_STATE_CODE, 'NOT_MATCHED') AS CS_PRODUCT_PAGE_LINK_STATUS
					, F.PDP_URL AS CS_PRODUCT_PAGE_LINK
					, IFNULL((SELECT CONCAT(K.SALES_MODEL_CODE,'.',K.SALES_MODEL_SUFFIX_CODE)
								FROM PDM_PRODUCT_M K
								WHERE K.SKU_ID = G.SKU_ID
								AND K.SALES_MODEL_CODE IS NOT NULL
								LIMIT 1
							), G.PRODUCT_NM) AS CS_MATCHED_MODEL_DISPLAY
					, IFNULL(B.SALES_CODE , '') AS CS_LINK_CUST_MODEL_CODE
					, (CASE (F.PRODUCT_STATE_CODE)
							WHEN 'ACTIVE' THEN REPLACE(F.PDP_URL,'.csp','')
							WHEN 'DISCONTINUED' THEN REPLACE(F.PDP_URL,'.csp','')
							ELSE NULL
							END )  AS MKT_PAGE_LINK
					FROM SVD_PRODUCT_SVC_MODEL_CATEGORY_R A
				INNER JOIN SVD_PRODUCT_SVC_MODEL_D B  
					ON A.SITE_CODE = B.SITE_CODE
					AND A.SVD_SKU_ID = B.SVD_SKU_ID
				INNER JOIN PDM_PRODUCT_SVC_D C
					ON A.SVD_SKU_ID = C.SVD_SKU_ID
				INNER JOIN DSP_DISPLAY_CATEGORY_M D
					ON A.SITE_CODE = D.SITE_CODE
					AND A.LV2_CATEGORY_CODE = D.CATEGORY_CODE
				LEFT JOIN DSP_DISPLAY_CATEGORY_M SUPER
					ON A.SITE_CODE = SUPER.SITE_CODE
					AND A.LV1_CATEGORY_CODE = SUPER.CATEGORY_CODE
				LEFT JOIN DSP_DISPLAY_CATEGORY_M SUB
					ON A.SITE_CODE = SUB.SITE_CODE
					AND A.LV3_CATEGORY_CODE = SUB.CATEGORY_CODE
					LEFT JOIN SVD_CUSTOMER_MODEL_M E
					ON A.SITE_CODE = E.SITE_CODE
					AND C.BUYER_MODEL_CODE = E.CUST_MODEL_CODE
					AND E.USE_FLAG='Y'
					LEFT JOIN DSP_PDP_D F
					ON E.PDP_ID=F.PDP_ID
					AND F.USE_FLAG = 'Y'
					AND F.AEM_PUBL_FLAG = 'Y'
					AND F.SITE_CODE = A.SITE_CODE
					AND F.SHOP_CODE  = 'D2C'
				LEFT JOIN DSP_PDP_M G 
					ON F.PDP_ID = G.PDP_ID
					AND F.SITE_CODE = G.SITE_CODE 
				LEFT JOIN PDM_PRODUCT_D PPD
					ON G.SKU_ID = PPD.SKU_ID
				AND F.BIZ_TYPE_CODE = PPD.BIZ_TYPE_CODE
				WHERE A.USE_FLAG = 'Y'
					AND A.SITE_CODE = #{siteCode}
					AND D.SUPP_USE_FLAG = 'Y'
					AND (C.SALES_MODEL_CODE = UPPER(#{csSalesCode}) OR C.BUYER_MODEL_CODE = UPPER(#{csSalesCode}))
		 ) I
		 ORDER BY I.ORDER_NUMBER ASC ,I.CS_SALES_CODE ASC
		 LIMIT 1
    </select>

	<select id="gpMcSupportManualsDocsAllList" resultType="com.lge.d2x.domain.manualSoftware.v1.model.GpMcSupportManualDocResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.GpMcSupportManualDocRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.gpMcSupportManualsDocsAllList */
			       DISTINCT (CASE LENGTH(SUFFIX) WHEN '5' THEN SUBSTR(SUFFIX,1,3) WHEN '6' THEN SUBSTR(SUFFIX,2,3) WHEN '7' THEN SUBSTR(SUFFIX,2,3) ELSE NULL END) AS SUFFIX_AFTER
				 , TELECOM
			     , DOCUMENT_TYPE
			     , DOC_ID
			     , LANG_CODE
			     , PDF_AVAILABLE_FLAG
			     , PDF_FILE_NAME
			     , PDF_ORIGINAL_FILE_NAME
			     , PDF_FILE_NAME_PRINT
			     , PDF_SIZE
			     , SELECTED_MANUAL
			     , RELEASE_DATE
                 , COUNT(*) OVER() AS MANUAL_COUNT
            FROM (
                  SELECT DOCUMENT_TYPE
                       , DOC_ID
                       , LANG_CODE
                       , SUFFIX
					<if test='countryCode neq "JO"'>
                       , (CASE LENGTH(SUFFIX) WHEN '5' THEN (SELECT SUFFIX_NM  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,1,3) AND USE_FLAG='Y') WHEN '6' THEN (SELECT SUFFIX_NM  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,2,3) AND USE_FLAG='Y') WHEN '7' THEN (SELECT SUFFIX_NM  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,2,3) AND USE_FLAG='Y') ELSE NULL END) AS TELECOM
					</if>
					<if test='countryCode eq "JO"'>
                       , (CASE LENGTH(SUFFIX) WHEN '5' THEN (SELECT SUFFIX_NM  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,1,3) AND USE_FLAG='Y' AND COUNTRY_CODE NOT IN ('ISR')) WHEN '6' THEN (SELECT SUFFIX_NM  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,2,3) AND USE_FLAG='Y' AND COUNTRY_CODE NOT IN ('ISR')) WHEN '7' THEN (SELECT SUFFIX_NM  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,2,3) AND USE_FLAG='Y' AND COUNTRY_CODE NOT IN ('ISR')) ELSE NULL END) AS TELECOM
					</if>
            		   , (CASE LENGTH(SUFFIX) WHEN'5' THEN (SELECT SUFFIX_NM  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,1,3) AND USE_FLAG='Y') WHEN'6' THEN (SELECT COUNTRY_CODE  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,2,3) AND USE_FLAG='Y') WHEN '7' THEN (SELECT COUNTRY_CODE  FROM SVD_MDMS_MC_SUFFIX_D  WHERE SUFFIX_CODE =SUBSTR(SUFFIX,2,3) AND USE_FLAG='Y') ELSE NULL END) AS COUNTRY_CODE
                       , PDF_AVAILABLE_FLAG
                       , PDF_FILE_NAME
                       , REPLACE(PDF_ORIGINAL_FILE_NAME,' ','_') AS PDF_ORIGINAL_FILE_NAME
                       , PDF_FILE_NAME_PRINT
                       , PDF_SIZE
                       , SELECTED_MANUAL
                       , RELEASE_DATE
                  FROM (
                        SELECT DOCUMENT_TYPE
                             , DOC_ID
                             , LANG_CODE
                             , SUFFIX
                             , PDF_AVAILABLE_FLAG
                             , PDF_FILE_NAME
                             , PDF_ORIGINAL_FILE_NAME
                             , PDF_FILE_NAME_PRINT
                             , PDF_SIZE
                             , SELECTED_MANUAL
                             , RELEASE_DATE
                        FROM (
                              SELECT 'OWNERS_MANUAL' AS DOCUMENT_TYPE
                                    , B.MANUAL_ID AS DOC_ID
                                    , C.LANG_LIST_CNTS AS LANG_CODE
                                    , B.FACTORY_SUFFIX_CODE AS SUFFIX
                                    , (CASE IFNULL(D.DEL_FLAG,'N') WHEN 'Y' THEN 'N' WHEN 'N' THEN 'Y' END) AS PDF_AVAILABLE_FLAG
                                    , C.MANUAL_FILE_ID AS PDF_FILE_NAME
                                    , C.ORIGINAL_FILE_NM AS PDF_ORIGINAL_FILE_NAME
									, C.FILE_SIZE AS PDF_SIZE
									, IFNULL(DATE_FORMAT(C.ISSU_DD, #{dateFormat}),'N/A') AS RELEASE_DATE
									 <if test='countryCode neq "CA"'>
									, RANK() OVER(PARTITION BY (CASE LENGTH(B.FACTORY_SUFFIX_CODE) WHEN '5' THEN SUBSTR(B.FACTORY_SUFFIX_CODE,1,3) WHEN '6' THEN SUBSTR(B.FACTORY_SUFFIX_CODE,2,3) WHEN '7' THEN SUBSTR(B.FACTORY_SUFFIX_CODE,2,3) ELSE NULL END),C.LANG_LIST_CNTS  ORDER BY C.ISSU_DD DESC) AS SELECTED_MANUAL
									 </if>
									 <if test='countryCode eq "CA"'>
									, RANK() OVER(PARTITION BY B.FACTORY_MODEL_CODE,C.LANG_LIST_CNTS  ORDER BY C.ISSU_DD DESC) AS SELECTED_MANUAL
									, ROW_NUMBER() OVER(PARTITION BY C.ORIGINAL_FILE_NM ORDER BY C.ISSU_DD DESC) AS SELECTED_MANUAL_NUM
									 </if>
									,(SELECT I.MSG_CNTS 
										 FROM COM_COMMON_CODE_M	G
										 JOIN COM_COMMON_CODE_D H ON G.COMMON_CODE = H.COMMON_CODE 
										 JOIN COM_MESSAGE_M I ON H.CODE_VAL_NM = I.MSG_CODE
										 WHERE 1=1
										 AND G.COMMON_CODE_NM = 'CS05'
										 AND H.COMMON_CODE_VAL = LOWER(C.LANG_LIST_CNTS)
										 AND I.USE_FLAG = 'Y'
										 AND I.DSP_SVC_MSG_SP_CODE = 'SVC'
										 AND I.LOCALE_CODE = #{javaLocaleCode}
									)AS PDF_FILE_NAME_PRINT
                              FROM SVD_MANUAL_MODEL_D B 
                              JOIN SVD_MANUAL_FILE_D C ON (B.MANUAL_ID = C.MANUAL_ID)
                              LEFT OUTER JOIN SVD_GSCS_DELETE_PDF_D D ON ( B.MANUAL_ID = D.DOC_ID AND ((FIND_IN_SET('en',C.LANG_LIST_CNTS) <![CDATA[>]]> 0 ) or (FIND_IN_SET('es',C.LANG_LIST_CNTS) <![CDATA[>]]> 0 )))
                              WHERE (B.AFFILIATE_CODE,B.FACTORY_MODEL_CODE,B.FACTORY_SUFFIX_CODE,B.OBU_CODE) IN
                                    (
                                     SELECT A.AFFILIATE_CODE , A.FACTORY_MODEL_CODE , A.FACTORY_SUFFIX_CODE , A.OBU_CODE
                                       FROM SVD_CUSTOMER_MODEL_FACTORY_R A
                                      WHERE A.CUST_MODEL_CODE = (SELECT 
																			E.BUYER_MODEL_CODE
																		FROM PDM_PRODUCT_SVC_D E 
																		JOIN SVD_PRODUCT_SVC_MODEL_D F ON E.SVD_SKU_ID = F.SVD_SKU_ID AND F.SITE_CODE = #{siteCode}
																		WHERE 1=1
																		AND (F.SALES_CODE = UPPER(#{customerModelCode}) OR E.BUYER_MODEL_CODE = UPPER(#{customerModelCode}))
																		AND F.USE_FLAG = 'Y'
																		AND E.USE_FLAG = 'Y'
																		LIMIT 1
																	)
										<if test='siteCode eq "TW"'>
                                      	AND A.BUYER_CODE = #{buyerCode}
										</if>
                                    )
                             ) T3
                        WHERE SELECTED_MANUAL = 1
						<if test='countryCode eq "CA"'>
		                AND SELECTED_MANUAL_NUM = 1
						</if>
                        ) T2
                 WHERE (PDF_AVAILABLE_FLAG) <![CDATA[<>]]> 'N'
                 ) T1
            WHERE TELECOM IS NOT NULL
			ORDER BY TELECOM
	</select>
	<select id="productDocuments" resultType="com.lge.d2x.domain.manualSoftware.v1.model.ProductDocumentsInfoResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.ProductDocumentsInfoRequestVO">
			SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.productDocuments */
			(SELECT I.MSG_CNTS 
			 FROM COM_COMMON_CODE_M	G
			 JOIN COM_COMMON_CODE_D H ON G.COMMON_CODE = H.COMMON_CODE 
			 JOIN COM_MESSAGE_M I ON H.CODE_VAL_NM = I.MSG_CODE
			 WHERE 1=1
			 AND G.COMMON_CODE_NM = 'DOCU000000'
			 AND H.COMMON_CODE_VAL = A.MODEL_FILE_TYPE_CODE
			 AND I.USE_FLAG = 'Y'
			 AND I.DSP_SVC_MSG_SP_CODE = 'SVC'
			 AND I.LOCALE_CODE = #{javaLocaleCode}
			 LIMIT 1
			 )AS DOCNAME
	               ,(SELECT L.MSG_CNTS 
			 FROM COM_COMMON_CODE_M	J
			 JOIN COM_COMMON_CODE_D K ON J.COMMON_CODE = K.COMMON_CODE 
			 JOIN COM_MESSAGE_M L ON k.CODE_VAL_NM = L.MSG_CODE
			 WHERE 1=1
			 AND J.COMMON_CODE_NM = 'CS05'
			 AND K.COMMON_CODE_VAL = LOWER(A.MODEL_DOC_LANG_CODE)
			 AND L.USE_FLAG = 'Y'
			 AND L.DSP_SVC_MSG_SP_CODE = 'SVC'
			 AND L.LOCALE_CODE = #{javaLocaleCode}
			 LIMIT 1
			 )AS LANG
	         , A.FILE_PATH AS LINK_ADDR
	         , A.FILE_SIZE AS FILE_SIZE
	         , IFNULL(DATE_FORMAT(A.LAST_UPDATE_DATE, #{dateFormat}),'N/A') AS RELEASE_DATE
	    FROM SVD_MODEL_DOC_D A 
		WHERE A.CUST_MODEL_CODE = (SELECT 
											E.BUYER_MODEL_CODE
										FROM PDM_PRODUCT_SVC_D E 
										JOIN SVD_PRODUCT_SVC_MODEL_D F ON E.SVD_SKU_ID = F.SVD_SKU_ID AND F.SITE_CODE = #{siteCode}
										WHERE 1=1
										AND (F.SALES_CODE = #{customerModelCode} OR E.BUYER_MODEL_CODE = #{customerModelCode})
										AND F.USE_FLAG = 'Y'
										AND E.USE_FLAG = 'Y'
										LIMIT 1
									)
		      AND A.SITE_CODE =   #{siteCode}
		      AND A.MODEL_FILE_TYPE_CODE IN ('DU03')
		      AND A.FILE_PATH IS NOT NULL
		    ORDER BY LANG ASC, RELEASE_DATE DESC
	</select>
	
	
	<select id="gpGCSCManualsDocsListAll" resultType="com.lge.d2x.domain.manualSoftware.v1.model.GpGscsManualDocsInfoResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.GpGscsManualDocsInfoRequestVO">
		SELECT	  /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.gpGCSCManualsDocsListAll */
			   M.MANUAL_TYPE
			 , UPPER(M.FILE_TYPE) AS FILE_TYPE
			 , M.MANUAL_CODE
			 , M.DOC_ID
			 , M.AVAILABLE_FLAG AS AVAIVABLE_FLAG
			 , M.FILE_NAME
			 , M.ORIGINAL_FILE_NAME
			 , M.FILE_SIZE
			 , DATE_FORMAT(M.RELEASE_DATE,#{dateFormat}) AS RELEASE_DATE
			 , GROUP_CONCAT(M.LANGUAGE_NAME ORDER BY M.LANGUAGE_NAME, M.FILE_TYPE, M.DOC_ID,', ') AS  FILE_NAME_PRINT
			 , M.SELECTED_NUM
			 , M.ORDER_NUM
			 , M.FILE_URL
		FROM (SELECT DISTINCT CASE WHEN C.MANUAL_CODE ='OLM' THEN 'HTML2'
								ELSE C.FILE_EXETENSION_NM
								END FILE_TYPE
					, CASE WHEN C.MANUAL_CODE = 'OWM' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manual-type-MANUAL' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Owner’s Manual')
                         WHEN C.MANUAL_CODE = 'QSG' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manual-type-quick' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Quick Setup Guide')
                         WHEN C.MANUAL_CODE = 'USG' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manual-type-USER' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'User Guide')
                         WHEN C.MANUAL_CODE = 'OLM' AND C.FILE_URL IS NOT null AND C.FILE_URL <![CDATA[<>]]> '' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manual-type-OM' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Online Manual')
                         WHEN C.MANUAL_CODE = 'OSU' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manual-type-OSU' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'OS Upgrade')
                         WHEN C.MANUAL_CODE = 'ISM' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manual-type-installation' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Installation')
                    END AS MANUAL_TYPE
					, C.MANUAL_CODE
				   , B.MANUAL_ID AS DOC_ID
				   , (CASE C.DEL_FLAG WHEN 'Y' THEN 'N' ELSE 'Y' END) AS AVAILABLE_FLAG
				   , C.MANUAL_FILE_ID AS FILE_NAME
				   , REPLACE(C.ORIGINAL_FILE_NM,' ',<![CDATA['_']]>) AS ORIGINAL_FILE_NAME
				   , IFNULL(C.FILE_SIZE,'') AS FILE_SIZE
				   , RANK() OVER(PARTITION BY C.MANUAL_CODE, E.LANG_CODE ORDER BY C.ISSU_DD DESC) AS SELECTED_NUM
				   , IFNULL(C.ISSU_DD,<![CDATA['N/A']]>) AS RELEASE_DATE
					,(SELECT T.MSG_CNTS 
						 FROM COM_COMMON_CODE_M	Q
						 JOIN COM_COMMON_CODE_D R ON Q.COMMON_CODE = R.COMMON_CODE 
						 JOIN COM_MESSAGE_M T ON R.CODE_VAL_NM = T.MSG_CODE
						 WHERE 1=1
						 AND Q.COMMON_CODE_NM = 'CS05'
						 AND R.COMMON_CODE_VAL = LOWER(E.LANG_CODE)
						 AND T.USE_FLAG = 'Y'
						 AND T.DSP_SVC_MSG_SP_CODE = 'SVC'
						 AND T.LOCALE_CODE = #{javaLocaleCode}
						 )AS LANGUAGE_NAME
					, CASE WHEN C.MANUAL_CODE = 'OWM' THEN '1'
							 WHEN C.MANUAL_CODE = 'USG' THEN '3'
							 WHEN C.MANUAL_CODE = 'OLM' THEN '2'
							 WHEN C.MANUAL_CODE = 'OSU' THEN '6'
							 WHEN C.MANUAL_CODE = 'ISM' THEN '5'
							 WHEN C.MANUAL_CODE = 'QSG' THEN '4'
						END AS ORDER_NUM
					, IFNULL(IF(C.MANUAL_CODE = 'OLM',C.FILE_URL,<![CDATA['N/A'),'N/A']]>) AS FILE_URL
			  FROM SVD_MANUAL_MODEL_D B 
			  INNER JOIN SVD_MANUAL_FILE_D C ON (B.MANUAL_ID = C.MANUAL_ID)
			  INNER JOIN SVD_COUNTRY_LANG_D E ON (  FIND_IN_SET(E.LANG_CODE,C.LANG_LIST_CNTS) <![CDATA[>]]> 0 )
					 AND E.COUNTRY_CODE = #{countryCode}
			  WHERE (B.AFFILIATE_CODE, B.FACTORY_MODEL_CODE, B.FACTORY_SUFFIX_CODE, B.OBU_CODE) IN					   
					   (
						   SELECT DISTINCT AFFILIATE_CODE,
									MODEL_CODE,
									SUFFIX_CODE,
									OBU_CODE
					FROM   (SELECT DISTINCT F.AFFILIATE_CODE,
											F.MODEL_CODE,
											F.SUFFIX_CODE,
											F.OBU_CODE
							FROM   SVD_MDMS_MODEL_M S
								JOIN SVD_MDMS_MODEL_M F
									ON ( F.MAP_MODEL_CODE = S.MAP_MODEL_CODE
										AND F.MAP_SUFFIX_CODE = S.MAP_SUFFIX_CODE
										AND F.SALES_MODEL_FLAG <![CDATA[<>]]> 'S' )
								JOIN (SELECT A2.AFFILIATE_CODE
										FROM   (SELECT A1.AFFILIATE_CODE
												FROM   SVD_SITE_AFFILIATE_R A1
												WHERE  A1.SITE_CODE = #{siteCode}
														AND A1.USE_FLAG = 'Y'
												ORDER  BY A1.DEFAULT_FLAG DESC) A2) A
									ON ( S.AFFILIATE_CODE = A.AFFILIATE_CODE )
								JOIN PDM_PRODUCT_SVC_D A
										JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
										ON A.SVD_SKU_ID = B.SVD_SKU_ID
											AND B.SITE_CODE = #{siteCode}
										JOIN SVD_PRODUCT_SVC_MODEL_D C
										ON A.SVD_SKU_ID = C.SVD_SKU_ID
											AND C.SITE_CODE = #{siteCode}
											AND C.SALES_CODE = #{csSalesCode}
									ON ( S.MODEL_CODE = A.SALES_MODEL_CODE
										AND S.SUFFIX_CODE = A.SALES_MODEL_SUFFIX_CODE )
							WHERE  C.SALES_CODE = #{csSalesCode}
								AND C.SITE_CODE = #{siteCode}
							UNION
							SELECT D.AFFILIATE_CODE,
								D.FACTORY_MODEL_CODE  AS MODEL_CODE,
								D.FACTORY_SUFFIX_CODE AS SUFFIX_CODE,
								D.OBU_CODE
							FROM   PDM_PRODUCT_SVC_D A
								JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
									ON A.SVD_SKU_ID = B.SVD_SKU_ID
										AND B.SITE_CODE = #{siteCode}
								JOIN SVD_PRODUCT_SVC_MODEL_D C
									ON A.SVD_SKU_ID = C.SVD_SKU_ID
										AND C.SITE_CODE = #{siteCode}
										AND C.SALES_CODE = #{csSalesCode}
								JOIN SVD_GSCS_MODEL_M D
									ON A.BUYER_MODEL_CODE = D.BUYER_MODEL_CODE
										AND D.BUYER_CODE = #{buyerCode}
							UNION
							SELECT A.AFFILIATE_CODE,
								A.FACTORY_MODEL_CODE  AS MODEL_CODE,
								A.FACTORY_SUFFIX_CODE AS SUFFIX_CODE,
								A.OBU_CODE
							FROM   SVD_CUSTOMER_MODEL_FACTORY_R A
							WHERE  A.CUST_MODEL_CODE = #{customerModelCode}
								AND A.BUYER_CODE = #{buyerCode}) A
					   )
			   ) M	
		   where M.AVAILABLE_FLAG <![CDATA[<>]]> 'N'
		   and M.SELECTED_NUM = '1'
		   and M.MANUAL_TYPE is not NULL
		  GROUP BY M.FILE_TYPE
				 , M.DOC_ID
				 , M.AVAILABLE_FLAG
				 , M.FILE_NAME
				 , M.ORIGINAL_FILE_NAME
				 , M.FILE_SIZE
		   ORDER BY ORDER_NUM, LANGUAGE_NAME ASC, RELEASE_DATE desc
	</select>
	
	
	
	<select id="productDocumentsAll" resultType="com.lge.d2x.domain.manualSoftware.v1.model.ProductDocumentsInfoResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.ProductDocumentsInfoRequestVO">
			SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.productDocumentsAll */
				T.DOCNAME
				,T.MODEL_FILE_TYPE_CODE
				,T.LANG
				,T.LANGUAGE_CODE AS LANGUAGE
				,T.LINK_ADDR
				,T.FILE_SIZE
				,T.RELEASE_DATE
			FROM(
				SELECT MODEL_FILE_TYPE_CODE
				      , CASE WHEN MODEL_FILE_TYPE_CODE = 'DU01' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manuals-main-00034' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Brochure')
                             WHEN MODEL_FILE_TYPE_CODE = 'DU02' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manuals-main-00035' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Data Sheet')
                             WHEN MODEL_FILE_TYPE_CODE = 'DU04' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manual-quickType' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Quick Start Guide')
                             WHEN MODEL_FILE_TYPE_CODE = 'DU05' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manuals-main-00040' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Warranty')
                             WHEN MODEL_FILE_TYPE_CODE = 'DU06' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'cst-manual-specification' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Specification')
                             WHEN MODEL_FILE_TYPE_CODE = 'DU07' THEN IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'psp-manual-type-FEATURE' AND a.LOCALE_CODE = #{javaLocaleCode} AND a.USE_FLAG = 'Y'),'Feature Guide')
                         END AS DOCNAME
					  , (SELECT L.MSG_CNTS 
					       FROM COM_COMMON_CODE_M	J
					       JOIN COM_COMMON_CODE_D K ON J.COMMON_CODE = K.COMMON_CODE 
					       JOIN COM_MESSAGE_M L ON k.CODE_VAL_NM = L.MSG_CODE
					      WHERE 1=1
					        AND J.COMMON_CODE_NM = 'CS05'
					        AND K.COMMON_CODE_VAL = LOWER(A.MODEL_DOC_LANG_CODE)
					        AND L.USE_FLAG = 'Y'
					        AND L.DSP_SVC_MSG_SP_CODE = 'SVC'
					        AND L.LOCALE_CODE = #{javaLocaleCode}
					      LIMIT 1
					    ) AS LANG	
					  , A.MODEL_DOC_LANG_CODE AS LANGUAGE_CODE
					  , A.FILE_PATH AS LINK_ADDR
					  , A.FILE_SIZE
					  , IFNULL(DATE_FORMAT(A.LAST_UPDATE_DATE, #{dateFormat}),'N/A') AS RELEASE_DATE
				  FROM SVD_MODEL_DOC_D A 
				 WHERE A.CUST_MODEL_CODE = (SELECT E.BUYER_MODEL_CODE
											  FROM PDM_PRODUCT_SVC_D E 
											  JOIN SVD_PRODUCT_SVC_MODEL_D F ON E.SVD_SKU_ID = F.SVD_SKU_ID AND F.SITE_CODE = #{siteCode}
											 WHERE 1=1
											   AND (F.SALES_CODE = #{customerModelCode} OR E.BUYER_MODEL_CODE = #{customerModelCode})
											   AND F.USE_FLAG = 'Y'
											   AND E.USE_FLAG = 'Y'
											LIMIT 1
										   )
					AND A.SITE_CODE =   #{siteCode}
				<if test='csMobileModelFlag == "N"'>
					AND A.MODEL_FILE_TYPE_CODE <![CDATA[<>]]>'DU03'
				</if>
					AND A.FILE_PATH IS NOT NULL
		    )T
		    ORDER BY T.LANG ASC, T.RELEASE_DATE DESC
	</select>
	
	<select id="additionaLinks" resultType="com.lge.d2x.domain.manualSoftware.v1.model.ModelRinkRInfoResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.ModelRinkRInfoRequestVO">
			SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.additionaLinks */
			       LINK_LABEL_NM AS LINK_LABEL_NAME
			      ,CASE WHEN SUBSTR(LOWER(LINK_URL),1,4) = 'http' THEN LINK_URL
                   ELSE REPLACE(LINK_URL,'.jsp','')
                   END AS LINK_ADDR
			      ,LINK_TYPE_NM AS LINK_TYPE_NAME
			      ,TARGET_TYPE_NM AS TARGET_TYPE_NAME
			      ,IFNULL(DATE_FORMAT(LAST_UPDATE_DATE, #{dateFormat}),'N/A') AS RELEASE_DATE
			FROM SVD_MODEL_LINK_R A 
			WHERE A.USE_FLAG = 'Y'
			  AND A.SITE_CODE = #{siteCode}
			  AND A.CUST_MODEL_CODE = (SELECT 
											E.BUYER_MODEL_CODE
										FROM PDM_PRODUCT_SVC_D E 
										JOIN SVD_PRODUCT_SVC_MODEL_D F ON E.SVD_SKU_ID = F.SVD_SKU_ID AND F.SITE_CODE = #{siteCode}
										WHERE 1=1
										AND (F.SALES_CODE = #{customerModelCode} OR E.BUYER_MODEL_CODE = #{customerModelCode})
										AND F.USE_FLAG = 'Y'
										AND E.USE_FLAG = 'Y'
										LIMIT 1
									)
			ORDER BY LAST_UPDATE_DATE DESC
    </select>
    
    
    <select id="mcSupportDown" resultType="com.lge.d2x.domain.manualSoftware.v1.model.McSupportDownResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.McSupportDownRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.mcSupportDown */
			   SITE_CODE
   		      ,CUST_MODEL_CODE
   		      ,OS_CODE
   		      ,DOWNLOAD_TYPE_CODE
   		      ,DOWNLOAD_URL
   		      ,(CASE DOWNLOAD_VER
				 	  WHEN 'LG Bridge Windows' THEN 'BRIDGE'
					  WHEN 'LG Bridge Mac' THEN 'BRIDGE'
					  WHEN 'LGPCSuite' THEN 'ONE'
					  WHEN 'LGPCSuiteIV' THEN 'FOUR'
					  WHEN 'Google Phone' THEN 'Google'
					  WHEN 'EUT Mac' THEN 'EUT'
					  WHEN 'EUT Windows' THEN 'EUT'
					  ELSE 'ETC' END
					  ) AS VERSION_NO
   		      ,USE_FLAG
		         ,(CASE
					WHEN INSTR(DOWNLOAD_VER , 'LG Bridge Windows') <![CDATA[>]]> 0 THEN 11
                       WHEN INSTR(DOWNLOAD_VER , 'LG Bridge Mac') <![CDATA[>]]> 0 THEN 12
                    ELSE 0 END
					 ) AS VERSION_ORDER
        FROM  SVD_GSCS_MC_DOWNLOAD_D 
        WHERE SITE_CODE = #{siteCode}
        AND  CUST_MODEL_CODE  = (SELECT DISTINCT BUYER_MODEL_CODE 
			            						FROM PDM_PRODUCT_SVC_D A
			            						JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
												WHERE 1=1
												AND (B.SALES_CODE = UPPER(#{customerModelCode}) OR A.BUYER_MODEL_CODE = UPPER(#{customerModelCode}))
												AND A.USE_FLAG = 'Y'
												AND B.USE_FLAG = 'Y'
												LIMIT 1
												)
        ORDER BY VERSION_ORDER, LAST_UPDATE_DATE

    </select>
	<select id="mcDownLoadSever" resultType="com.lge.d2x.domain.manualSoftware.v1.model.McSupportDownResponseVO" parameterType="com.lge.d2x.domain.manualSoftware.v1.model.McSupportDownRequestVO">
		 	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.manualSoftware.v1.service.ManualSoftwareService.mcDownLoadSever */
		 	       DOWNLOAD_URL AS URL_ADDR
		    FROM SVD_MC_DOWNLOAD_SERVER_D
		    WHERE SITE_CODE = #{siteCode}
    </select>
    
</mapper>

