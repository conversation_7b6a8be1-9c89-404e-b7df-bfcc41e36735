package com.lge.d2x.interfaces.system.pds.whereToBuy.model;

import com.lge.d2x.domain.whereToBuy.v1.model.WtbDistributorResponseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemPdsWtbDistributorResponseVO {
    @Schema(description = "유통업자로고URL")
    private String distributorLogoUrl;

    @Schema(description = "유통업자아이디", example = "DS00000001")
    private String distributorId;

    @Schema(description = "유통업자명", example = "Tottenham Store")
    private String distributorNm;

    @Schema(description = "유통업자유형코드", example = "STORE")
    private String distributorTypeCode;

    @Schema(description = "사이트코드", example = "UK")
    private String siteCode;

    @Schema(description = "위도값", example = "51.5892808000")
    private Double wtbLatitudeValue;

    @Schema(description = "경도값", example = "-0.0610505000")
    private Double wtbLongitudeValue;

    @Schema(description = "거리")
    private Double distance;

    @Schema(description = "주소")
    private String address;

    @Schema(description = "우편핀번호", example = "N17")
    private String pinNo;

    @Schema(description = "연락처지역번호", example = "01277")
    private String contactLocalNo;

    @Schema(description = "지역", example = "London,England")
    private String region;

    @Schema(description = "주요지점명")
    private String majorBranchNm;

    @Schema(description = "BrandShop여부", example = "true")
    private String brand;

    @Schema(description = "WTB전화번호")
    private String wtbPhoneNo;

    @Schema(description = "WTB사이트URL")
    private String wtbUrl;

    @Schema(description = "WTB운영시간")
    private String wtbTime;

    @Schema(description = "사이트URL")
    private String siteUrl;

    @Schema(description = "위치아이디", example = "ChIJRc1jry0bdkgRQoKOyQDcfqM")
    private String positId;

    @Schema(description = "우선순서")
    private String prioritySeq;

    @Schema(description = "브랜드샵관리자성명")
    private String brandshopManagerPnm;

    @Schema(description = "법인코드")
    private String corporationCode;

    @Schema(description = "방향")
    private String direction;

    @Schema(description = "3레벨카테고리코드", example = "CT00008334")
    private String lv3CategoryCode;

    @Schema(description = "도시명", example = "Essex")
    private String cityNm;

    @Schema(description = "도시주명")
    private String cityStatenm;

    @Schema(description = "국가명", example = "England")
    private String countryNm;

    @Schema(description = "주소라인내용1", example = "Unit 7, Beckton Triangle Retail Park")
    private String addrlineCnts1;

    @Schema(description = "주소라인내용2", example = "5 Clapsgate Lane")
    private String addrlineCnts2;

    @Schema(description = "주소라인내용3", example = "Beckton")
    private String addrlineCnts3;

    @Schema(description = "주소라인내용4")
    private String addrlineCnts4;

    @Schema(description = "우편번호", example = "Vatan")
    private String postalNo;

    @Schema(description = "핸드폰번호1", example = "0333 900 0085")
    private String cellpNo1;

    @Schema(description = "핸드폰번호2")
    private String cellpNo2;

    @Schema(description = "연락처번호1")
    private String contactNo1;

    @Schema(description = "연락처번호2")
    private String contactNo2;

    @Schema(description = "이메일주소")
    private String emailAddr;

    @Schema(description = "핸드폰번호")
    private String phoneNo;

    @Schema(description = "팩스번호")
    private String faxNo;

    @Schema(description = "개장시간")
    private String openingTime;

    @Schema(description = "폐장시간")
    private String closingTime;

    @Schema(description = "정기휴무명")
    private String perdDayoffNm;

    @Schema(description = "WTB명순서", example = "2")
    private String wtbNameOrder;

    @Schema(description = "주소검색체크", example = "AC001")
    private String srchAddressChecked;

    @Schema(description = "카테고리", example = "Fridge Freezers,Laundry,TVs & Soundbars")
    private String category;

    @Schema(description = "슈퍼카테고리", example = "Appliances,TV/Audio/Video")
    private String superCategory;

    @Schema(description = "담당자성명")
    private String psnPnm;

    @Schema(description = "PDP 아이디")
    private String pdpId;

    @Schema(description = "사용자 친화 제품명")
    private String userFrndyProductNm;

    @Schema(description = "썸네일")
    private String thumbnail;

    @Schema(description = "요일별 오픈시간 설정 여부")
    private String weeklyOpenTimeSettFlag;

    @Schema(description = "토요일 오픈 여부")
    private String satOpenFlag;

    @Schema(description = "일요일 오픈 여부")
    private String sunOpenFlag;

    @Schema(description = "공휴일 운영어부")
    private String holidayOpenFlag;

    public WtbDistributorResponseVO toWtbDistributorVO() {

        return WtbDistributorResponseVO.builder()
                .distributorLogoUrl(this.distributorLogoUrl)
                .distributorId(this.distributorId)
                .distributorNm(this.distributorNm)
                .distributorTypeCode(this.distributorTypeCode)
                .siteCode(this.siteCode)
                .wtbLatitudeValue(this.wtbLatitudeValue)
                .wtbLongitudeValue(this.wtbLongitudeValue)
                .distance(this.distance)
                .address(this.address)
                .pinNo(this.pinNo)
                .contactLocalNo(this.contactLocalNo)
                .region(this.region)
                .majorBranchNm(this.majorBranchNm)
                .brand(this.brand)
                .wtbPhoneNo(this.wtbPhoneNo)
                .wtbUrl(this.wtbUrl)
                .wtbTime(this.wtbTime)
                .siteUrl(this.siteUrl)
                .positId(this.positId)
                .prioritySeq(this.prioritySeq)
                .brandshopManagerPnm(this.brandshopManagerPnm)
                .corporationCode(this.corporationCode)
                .direction(this.direction)
                .lv3CategoryCode(this.lv3CategoryCode)
                .cityNm(this.cityNm)
                .cityStatenm(this.cityStatenm)
                .countryNm(this.countryNm)
                .addrlineCnts1(this.addrlineCnts1)
                .addrlineCnts2(this.addrlineCnts2)
                .addrlineCnts3(this.addrlineCnts3)
                .addrlineCnts4(this.addrlineCnts4)
                .postalNo(this.postalNo)
                .cellpNo1(this.cellpNo1)
                .cellpNo2(this.cellpNo2)
                .contactNo1(this.contactNo1)
                .contactNo2(this.contactNo2)
                .emailAddr(this.emailAddr)
                .phoneNo(this.phoneNo)
                .faxNo(this.faxNo)
                .openingTime(this.openingTime)
                .closingTime(this.closingTime)
                .perdDayoffNm(this.perdDayoffNm)
                .wtbNameOrder(this.wtbNameOrder)
                .srchAddressChecked(this.srchAddressChecked)
                .category(this.category)
                .superCategory(this.superCategory)
                .psnPnm(this.psnPnm)
                .pdpId(this.pdpId)
                .userFrndyProductNm(this.userFrndyProductNm)
                .thumbnail(this.thumbnail)
                .weeklyOpenTimeSettFlag(this.weeklyOpenTimeSettFlag)
                .satOpenFlag(this.satOpenFlag)
                .sunOpenFlag(this.sunOpenFlag)
                .holidayOpenFlag(this.holidayOpenFlag)
                .build();
    }
}
