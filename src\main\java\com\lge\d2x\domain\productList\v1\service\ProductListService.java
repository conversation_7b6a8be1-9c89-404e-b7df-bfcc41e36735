package com.lge.d2x.domain.productList.v1.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lge.d2x.core.constants.CommonConstants;
import com.lge.d2x.domain.common.v1.model.ProductSupportRequestVO;
import com.lge.d2x.domain.common.v1.service.CommonService;
import com.lge.d2x.domain.product.v1.model.ProductIconListResponseVO;
import com.lge.d2x.domain.productList.v1.model.ProductListModelVO;
import com.lge.d2x.domain.productList.v1.model.ProductListRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListResponseVO;
import com.lge.d2x.interfaces.system.admin.wishlist.client.SystemAdminWishlistClient;
import com.lge.d2x.interfaces.system.admin.wishlist.model.SystemAdminWishlistRequestVO;
import com.lge.d2x.interfaces.system.pds.category.client.SystemPdsCategoryClient;
import com.lge.d2x.interfaces.system.pds.category.model.SystemPdsProductListCategoryInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.category.model.SystemPdsProductListCategoryInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.category.model.SystemPdsProductListHRCategoryInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.category.model.SystemPdsProductListHRCategoryInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.client.SystemPdsPdpInfoClient;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductEpsInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductEpsInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListResponseVO;
import com.lge.d2x.interfaces.system.pds.productList.client.SystemPdsProductListClient;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsOrderProductListRequestVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsOrderProductListResponseVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsPdpIdListBySkuRequestVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsPdpIdListRequestVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsProductListPdpInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsProductListPdpInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsProductListSelfSiblingDefaultRequestVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsProductListSiblingInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsProductListSiblingInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.support.client.SystemPdsSupportClient;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoResponseVO;
import com.lge.d2xfrm.constants.CommonCodes;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.CachedCodeRequestVO;
import com.lge.d2xfrm.util.common.CachedDataUtil;
import com.lge.d2xfrm.util.common.DateUtil;
import com.lge.d2xfrm.util.common.RequestHeaderUtil;
import com.lge.d2xfrm.util.sso.SessionUserUtil;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductListService {
    private final SystemPdsProductListClient systemPdsProductListClient;
    private final SystemPdsCategoryClient systemPdsCategoryClient;
    private final SystemPdsPdpInfoClient systemPdsPdpInfoClient;
    private final SystemPdsSupportClient systemPdsSupportClient;
    private final SystemAdminWishlistClient systemAdminWishlistClient;
    private final CachedDataUtil cachedDataUtil;
    private final CommonService commonService;

    public static final int NBAA_PRODUCT_LIST_LIMIT = 10;
    public static final int NBAA_PRODUCT_LIST_LIMIT_ADDON_SA = 20;
    public static final int NBAA_PRODUCT_LIST_LIMIT_SP = 12;
    public static final int NBAA_PRODUCT_LIST_LIMIT_EV = 24;
    public static final int NBAA_PRODUCT_LIST_LIMIT_PLP = 12;
    public static final String EXTERNAL_WTB_PREFIX = "EXTERNAL_WTB_";

    @SuppressWarnings("unchecked")
    public List<ProductListResponseVO> getProductList(ProductListRequestVO requestVO) {
        List<ProductListResponseVO> productListRespVO = new ArrayList<>();
        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String SHOP_CODE = CommonConstants.SHOP_CODE_D2C;
        String CUSTOMER_GROUP = "";

        if (StringUtils.isEmpty(SITE_CODE)) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        /** 멀티샵 여부 */
        boolean mtsFlag = false;
        if (StringUtils.isNotBlank(requestVO.getMultishopCode())
                && !CommonConstants.SHOP_CODE_D2C.equals(requestVO.getMultishopCode())) {
            mtsFlag = true;
            SHOP_CODE = requestVO.getMultishopCode();
            CUSTOMER_GROUP = requestVO.getCustomerGroup();
        }

        /** 카테고리 표준화 국가 여부 조회 */
        String standardFlag = commonService.getCategoryStandardFlag();

        String newestOnSaleUse =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("newest_on_sale_use")),
                        CommonConstants.NO_FLAG);
        String mostOnSaleUse =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("most_popular_on_sale_use")),
                        CommonConstants.NO_FLAG);

        String obsBuynowFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("obs_buy_now_change_flag")),
                        CommonConstants.NO_FLAG);
        String energyLabelFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("Energy_Label_Flag")),
                        CommonConstants.NO_FLAG);

        String reviewType =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("REVIEW_TYPE")),
                        CommonConstants.NO_FLAG);

        String obsLoginFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("OBS_LOGIN_FLAG")),
                        CommonConstants.NO_FLAG);

        String buyNowUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("BUY_NOW_FLAG")),
                        CommonConstants.NO_FLAG);

        String hiddenModelSearchFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("HIDDEN_MODEL_SEARCH_FLAG")),
                        CommonConstants.NO_FLAG);

        /** 타임존 조회 */
        String TIMEZONE =
                cachedDataUtil
                        .getComLocaleCode(CachedCodeRequestVO.builder().siteCode(SITE_CODE).build())
                        .getTimezoneNm();

        List<Map<String, Object>> tabTypes = requestVO.getProductList();

        Map<String, Object> input = new HashMap<>();
        input.put("mtsFlag", mtsFlag);
        input.put("siteCode", SITE_CODE);
        input.put("shopCode", SHOP_CODE);
        input.put("customerGroup", CUSTOMER_GROUP);
        input.put("standardFlag", standardFlag);
        input.put("reviewType", reviewType);
        input.put("hiddenModelSearchFlag", hiddenModelSearchFlag);
        input.put("buyNowUseFlag", buyNowUseFlag);
        input.put("categoryId", requestVO.getCategoryId());
        input.put("bizType", requestVO.getBizType());
        input.put("pdpIds", requestVO.getModelIds());
        input.put("promoProductFlag", requestVO.getPromoProductFlag());
        input.put("promotionId", requestVO.getPromotionId());
        input.put("componentId", requestVO.getComponentId());
        input.put("filterModelList", requestVO.getFilterModelList());
        input.put("bundlesOnly", requestVO.getBundlesOnly());
        input.put("promotionsOnly", requestVO.getPromotionsOnly());
        input.put("customerNo", requestVO.getCustomerNo());
        input.put("orderListFlag", requestVO.getOrderListFlag());
        input.put("timezone", TIMEZONE);

        LocalDateTime todayTime = LocalDateTime.now();
        String pattern = "yyyyMMdd HH:mm:ss";
        String today = "";
        try {
            todayTime = DateUtil.getCurrentTime(TIMEZONE, pattern);
            today = todayTime.toLocalDate().toString().replaceAll("-", "");
        } catch (Exception e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            today = todayTime.atOffset(ZoneOffset.of("+09:00")).format(formatter);
        }

        input.put("today", today);

        for (Map<String, Object> data : tabTypes) {

            ProductListResponseVO productListResp = new ProductListResponseVO();

            List<String> models = new ArrayList<>();
            List<String> limitCntModels = null;

            String listType = Objects.toString(data.get("listType"), "");
            String tabTitle = Objects.toString(data.get("tabTitle"), "");
            String siblingGroupFlag =
                    Objects.toString(data.get("siblingGroupFlag"), CommonConstants.NO_FLAG);

            input.put("listType", listType);

            if ("NEW".equalsIgnoreCase(listType)
                    || ("NO".equalsIgnoreCase(listType) && newestOnSaleUse.equals("Y"))) { // NEWEST
                input.put("listType", "NEW");
                models = retrieveNewestModels(input);
            } else if ("NEWEST_ACTIVE_COMMERCE".equalsIgnoreCase(listType)) {
                String skuIds = Objects.toString(data.get("skuList"), "");
                if (StringUtils.isNotEmpty(skuIds)) {
                    models =
                            systemPdsProductListClient.getPdpIdListBySkuId(
                                    SystemPdsPdpIdListBySkuRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .skuIds(skuIds)
                                            .standardFlag(standardFlag)
                                            .build());
                }

                input.put("cateId", Objects.toString(data, "categoryId"));

                String cateId = Objects.toString(data.get("categoryId"), "");
                String[] categoryArr = cateId.trim().split(",");

                input.put("categoryFlag", CommonConstants.YES_FLAG);
                input.put("activeCommerce", CommonConstants.YES_FLAG);
                input.put("listType", "NEW");

                List<String> tempModels = new ArrayList<>();
                if (categoryArr != null && categoryArr.length > 0) {
                    for (int i = 0; i < categoryArr.length; i++) {
                        input.put("cateId", categoryArr[i]);
                        tempModels = retrieveNewestModels(input);
                        if (ObjectUtils.isNotEmpty(tempModels)) {
                            models.addAll(i, tempModels);
                        }
                    }
                }
            } else if ("HR".equalsIgnoreCase(listType)) { // HIGHLY RATED
                models = retrieveHighlyRatedModels(input);
            } else if ("AC".equalsIgnoreCase(listType)) { // Accessory (Home 사용 불가)

                // AC일때 sku필수
                String sku = Objects.toString(data.get("sku"), "");
                if (StringUtils.isEmpty(sku)) {
                    throw new D2xBusinessException(
                            CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
                }

                if (!sku.contains(",")) {
                    List<String> pdpIdList = new ArrayList<>();

                    if (mtsFlag) {
                        pdpIdList =
                                systemPdsProductListClient.getMtsPdpIdListBySkuId(
                                        SystemPdsPdpIdListBySkuRequestVO.builder()
                                                .siteCode(SITE_CODE)
                                                .standardFlag(standardFlag)
                                                .shopCode(SHOP_CODE)
                                                .skuIds(sku)
                                                .build());
                    } else {
                        pdpIdList =
                                systemPdsProductListClient.getPdpIdListBySkuId(
                                        SystemPdsPdpIdListBySkuRequestVO.builder()
                                                .siteCode(SITE_CODE)
                                                .standardFlag(standardFlag)
                                                .skuIds(sku)
                                                .build());
                    }

                    if (ObjectUtils.isNotEmpty(pdpIdList)) {
                        input.put("pdpId", pdpIdList.get(0));

                        models = retrieveAccessoryModels(input);
                    }
                }

            } else if ("MP".equalsIgnoreCase(listType)
                    || ("MO".equalsIgnoreCase(listType)
                            && CommonConstants.YES_FLAG.equals(mostOnSaleUse))) { // Most Popular
                input.put("listType", "MP");
                models = retrieveMostPopularModels(input);
            } else if ("WISHLIST".equalsIgnoreCase(listType)) {
                String customerNo = Objects.toString(input.get("customerNo"), "");

                if (StringUtils.isNotBlank(customerNo)) {
                    models =
                            systemAdminWishlistClient.getWishlistModelIdList(
                                    SystemAdminWishlistRequestVO.builder()
                                            .localeCode(SITE_CODE)
                                            .customerNo(customerNo)
                                            .build());
                }

            } else if ("LGPICK".equalsIgnoreCase(listType)) {
                List<String> modelList =
                        systemPdsProductListClient.getPdpIdListByListType(
                                SystemPdsPdpIdListRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .listType(listType)
                                        .standardFlag(standardFlag)
                                        .build());
                models.addAll(modelList);
            } else {
                log.debug("#### tabTitle : " + tabTitle);
                String skuIds = Objects.toString(data.get("skuList"), "");
                input.put("skuListFlag", CommonConstants.YES_FLAG);
                if (StringUtils.isNotEmpty(skuIds)) {
                    models =
                            systemPdsProductListClient.getPdpIdListBySkuId(
                                    SystemPdsPdpIdListBySkuRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .skuIds(skuIds)
                                            .standardFlag(standardFlag)
                                            .build());
                }
            }

            /** NBA제품목록 조회 */
            List<Map<String, Object>> productListMapList = new ArrayList<>();

            if (0 < models.size()) {
                input.put("listType", listType);
                input.put("siblingGroupFlag", siblingGroupFlag);
                limitCntModels = setLimitCntModels(models, listType, mtsFlag);

                input.put("modelsInfo", limitCntModels);
                input.put("pageType", requestVO.getPageType());
                productListMapList = getProductListInfo(input);

                if (CommonConstants.YES_FLAG.equals(
                        Objects.toString(input.get("orderListFlag"), ""))) {

                    List<Map<String, Object>> shopInfoList =
                            commonService.retrieveShopInfoList(SHOP_CODE);

                    if (shopInfoList != null && shopInfoList.size() > 0) {
                        for (Map<String, Object> shopInfoMap : shopInfoList) {
                            input.put("orderMtsCode", shopInfoMap.get("shopCode").toString());

                            List<Map<String, Object>> orderProductList = new ArrayList<>();
                            List<SystemPdsOrderProductListResponseVO> sysPdsOrderList =
                                    systemPdsProductListClient.getMtsOrderProductList(
                                            SystemPdsOrderProductListRequestVO.builder()
                                                    .siteCode(SITE_CODE)
                                                    .listType(listType)
                                                    .standardFlag(standardFlag)
                                                    .today(today)
                                                    .shopCode(SHOP_CODE)
                                                    .pdpIdList(limitCntModels)
                                                    .bizTypeCode(
                                                            Objects.toString(
                                                                    input.get("bizType"), ""))
                                                    .build());

                            if (ObjectUtils.isNotEmpty(sysPdsOrderList)) {
                                for (SystemPdsOrderProductListResponseVO resp : sysPdsOrderList) {
                                    ObjectMapper mapper = new ObjectMapper();
                                    Map<String, Object> orderMap =
                                            mapper.convertValue(resp, Map.class);
                                    orderProductList.add(orderMap);
                                }
                            }

                            shopInfoMap.put("productList", orderProductList);
                        }
                    }

                    productListResp.setShopInfoList(shopInfoList);
                }
            }

            productListResp.setObsBuynowFlag(obsBuynowFlag);
            productListResp.setEnergyLabelFlag(energyLabelFlag);
            productListResp.setProductListType(listType);
            productListResp.setProductListTitle(tabTitle);
            productListResp.setProductList(productListMapList);
            productListResp.setObsLoginFlag(obsLoginFlag);
            productListResp.setBuyNowUseFlag(buyNowUseFlag);
            productListResp.setNewestOnSaleUse(newestOnSaleUse);
            productListResp.setMostOnSaleUse(mostOnSaleUse);
            productListResp.setComponentId(Objects.toString(input.get("componentId"), ""));
            productListResp.setReviewType(Objects.toString(input.get("reviewType"), ""));

            productListRespVO.add(productListResp);
        }

        return productListRespVO;
    }

    private List<String> retrieveNewestModels(Map<String, Object> input) {
        List<String> models = null;

        String categoryId = Objects.toString(input.get("categoryId"), "");
        String cateId = Objects.toString(input.get("cateId"), "");
        String siteCode = Objects.toString(input.get("siteCode"), "");
        String shopCode = Objects.toString(input.get("shopCode"), "");
        String customerGroup = Objects.toString(input.get("customerGroup"), "");
        String superCategoryCode = "";
        String categoryCode = "";
        String subCategoryCode = "";

        try {
            if (StringUtils.isNotBlank(categoryId)
                    && !CommonConstants.YES_FLAG.equals(
                            Objects.toString(input.get("categoryFlag")))) {

                SystemPdsProductListCategoryInfoResponseVO categoryInfo =
                        systemPdsCategoryClient.getProductListCategoryInfo(
                                SystemPdsProductListCategoryInfoRequestVO.builder()
                                        .categoryCode(categoryId)
                                        .siteCode(siteCode)
                                        .shopCode(shopCode)
                                        .bizTypeCode(Objects.toString(input.get("bizType"), ""))
                                        .build());

                if (ObjectUtils.isNotEmpty(categoryInfo)) {
                    superCategoryCode = categoryInfo.getSuperCategoryCode();
                    categoryCode = categoryInfo.getCategoryCode();
                    subCategoryCode = categoryInfo.getSubCategoryCode();
                }
            }

            SystemPdsPdpIdListRequestVO sysPdsPdpIdListReq = new SystemPdsPdpIdListRequestVO();
            sysPdsPdpIdListReq.setSiteCode(siteCode);
            sysPdsPdpIdListReq.setStandardFlag(Objects.toString(input.get("standardFlag"), ""));
            sysPdsPdpIdListReq.setListType(Objects.toString(input.get("listType"), ""));
            sysPdsPdpIdListReq.setShopCode(shopCode);
            sysPdsPdpIdListReq.setCustomerGroup(customerGroup);
            sysPdsPdpIdListReq.setLv2CategoryCode(superCategoryCode);
            sysPdsPdpIdListReq.setLv3CategoryCode(
                    StringUtils.isBlank(cateId) ? categoryCode : cateId);
            sysPdsPdpIdListReq.setLv4CategoryCode(subCategoryCode);
            sysPdsPdpIdListReq.setLv3CategoryCdFlag(
                    Objects.toString(input.get("categoryFlag"), ""));
            sysPdsPdpIdListReq.setActiveCommerceFlag(
                    Objects.toString(input.get("activeCommerce"), ""));
            sysPdsPdpIdListReq.setBizTypeCode(Objects.toString(input.get("bizType"), ""));

            boolean mtsFlag = Boolean.parseBoolean(Objects.toString(input.get("mtsFlag")));
            models = callSystemPdsPlpPdpIdList(sysPdsPdpIdListReq, mtsFlag);
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }

        return models;
    }

    private List<String> retrieveHighlyRatedModels(Map<String, Object> input) {
        List<String> models = new ArrayList<>();
        List<String> hrModels = new ArrayList<>();

        try {
            List<SystemPdsProductListHRCategoryInfoResponseVO> hrCategoryList =
                    systemPdsCategoryClient.getProductListHRCategoryInfo(
                            SystemPdsProductListHRCategoryInfoRequestVO.builder()
                                    .shopCode(Objects.toString(input.get("shopCode"), ""))
                                    .siteCode(Objects.toString(input.get("siteCode"), ""))
                                    .bizTypeCode(Objects.toString(input.get("bizType"), ""))
                                    .build());

            int hrLimit = 10;

            SystemPdsPdpIdListRequestVO sysPdsPdpIdListReq = new SystemPdsPdpIdListRequestVO();
            sysPdsPdpIdListReq.setSiteCode(Objects.toString(input.get("siteCode"), ""));
            sysPdsPdpIdListReq.setStandardFlag(Objects.toString(input.get("standardFlag"), ""));
            sysPdsPdpIdListReq.setListType(Objects.toString(input.get("listType"), ""));
            sysPdsPdpIdListReq.setShopCode(Objects.toString(input.get("shopCode"), ""));
            sysPdsPdpIdListReq.setCustomerGroup(Objects.toString(input.get("customerGroup"), ""));
            sysPdsPdpIdListReq.setActiveCommerceFlag(
                    Objects.toString(input.get("activeCommerce"), ""));
            sysPdsPdpIdListReq.setHrLimit(hrLimit);

            boolean mtsFlag = Boolean.parseBoolean(Objects.toString(input.get("mtsFlag")));
            if (ObjectUtils.isNotEmpty(hrCategoryList)) {

                for (SystemPdsProductListHRCategoryInfoResponseVO sysPdsHrCategory :
                        hrCategoryList) {
                    // hrCategoryList의 superCategoryCode로 세팅
                    sysPdsPdpIdListReq.setLv2CategoryCode(sysPdsHrCategory.getSuperCategoryCode());
                    sysPdsPdpIdListReq.setHrLimit(sysPdsHrCategory.getHrLimit());

                    hrModels = callSystemPdsPlpPdpIdList(sysPdsPdpIdListReq, mtsFlag);

                    models.addAll(hrModels);
                }
            } else {
                models = callSystemPdsPlpPdpIdList(sysPdsPdpIdListReq, mtsFlag);
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }

        return models;
    }

    private List<String> retrieveAccessoryModels(Map<String, Object> input) {
        List<String> models = new ArrayList<>();
        try {
            SystemPdsPdpIdListRequestVO sysPdsPdpIdListReq = new SystemPdsPdpIdListRequestVO();
            sysPdsPdpIdListReq.setSiteCode(Objects.toString(input.get("siteCode"), ""));
            sysPdsPdpIdListReq.setStandardFlag(Objects.toString(input.get("standardFlag"), ""));
            sysPdsPdpIdListReq.setListType(Objects.toString(input.get("listType"), ""));
            sysPdsPdpIdListReq.setShopCode(Objects.toString(input.get("shopCode"), ""));
            sysPdsPdpIdListReq.setPdpId(Objects.toString(input.get("pdpId"), ""));

            boolean mtsFlag = Boolean.parseBoolean(Objects.toString(input.get("mtsFlag")));
            models = callSystemPdsPlpPdpIdList(sysPdsPdpIdListReq, mtsFlag);
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }
        return models;
    }

    @SuppressWarnings("unchecked")
    private List<String> retrieveMostPopularModels(Map<String, Object> input) {
        List<String> models = new ArrayList<String>();

        String categoryId = Objects.toString(input.get("categoryId"), "");
        String siteCode = Objects.toString(input.get("siteCode"), "");
        String shopCode = Objects.toString(input.get("shopCode"), "");
        String customerGroup = Objects.toString(input.get("customerGroup"), "");
        String superCategoryCode = "";
        String categoryCode = "";
        String subCategoryCode = "";

        try {
            if (StringUtils.isNotBlank(categoryId)) {
                SystemPdsProductListCategoryInfoResponseVO categoryInfo =
                        systemPdsCategoryClient.getProductListCategoryInfo(
                                SystemPdsProductListCategoryInfoRequestVO.builder()
                                        .categoryCode(categoryId)
                                        .siteCode(siteCode)
                                        .shopCode(shopCode)
                                        .bizTypeCode(Objects.toString(input.get("bizType"), ""))
                                        .build());

                if (ObjectUtils.isNotEmpty(categoryInfo)) {
                    superCategoryCode = categoryInfo.getSuperCategoryCode();
                    categoryCode = categoryInfo.getCategoryCode();
                    subCategoryCode = categoryInfo.getSubCategoryCode();
                    input.put("subCategoryId", subCategoryCode);
                }
            }

            SystemPdsPdpIdListRequestVO sysPdsPdpIdListReq = new SystemPdsPdpIdListRequestVO();
            sysPdsPdpIdListReq.setSiteCode(siteCode);
            sysPdsPdpIdListReq.setStandardFlag(Objects.toString(input.get("standardFlag"), ""));
            sysPdsPdpIdListReq.setListType(Objects.toString(input.get("listType"), ""));
            sysPdsPdpIdListReq.setBizTypeCode(Objects.toString(input.get("bizType"), ""));
            sysPdsPdpIdListReq.setPdpIds((List<String>) input.get("pdpIds"));
            sysPdsPdpIdListReq.setToday(Objects.toString(input.get("today"), ""));
            sysPdsPdpIdListReq.setTimezone(Objects.toString(input.get("timezone"), ""));
            sysPdsPdpIdListReq.setShopCode(shopCode);
            sysPdsPdpIdListReq.setCustomerGroup(customerGroup);
            sysPdsPdpIdListReq.setLv2CategoryCode(superCategoryCode);
            sysPdsPdpIdListReq.setLv3CategoryCode(categoryCode);
            sysPdsPdpIdListReq.setLv4CategoryCode(subCategoryCode);

            boolean mtsFlag = Boolean.parseBoolean(Objects.toString(input.get("mtsFlag")));
            models = callSystemPdsPlpPdpIdList(sysPdsPdpIdListReq, mtsFlag);

        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }
        return models;
    }

    private List<String> callSystemPdsPlpPdpIdList(
            SystemPdsPdpIdListRequestVO sysPdsPdpIdListReq, boolean mtsFlag) {
        List<String> models = new ArrayList<>();

        try {
            if (mtsFlag) {
                models = systemPdsProductListClient.getMtsPdpIdListByListType(sysPdsPdpIdListReq);
            } else {
                models = systemPdsProductListClient.getPdpIdListByListType(sysPdsPdpIdListReq);
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }
        return models;
    }

    private List<String> setLimitCntModels(List<String> models, String listType, boolean mtsFlag) {
        String siteCode = RequestHeaderUtil.getSiteCode();
        List<String> limitModel = new ArrayList<>();
        int productListLimit = NBAA_PRODUCT_LIST_LIMIT;
        if ("EVENT".equals(listType)) {
            productListLimit = NBAA_PRODUCT_LIST_LIMIT_EV;
        } else if ("SP".equals(listType)) {
            productListLimit = NBAA_PRODUCT_LIST_LIMIT_SP;
        } else if ("ADDON".equals(listType) && !mtsFlag) { // LGCOMSA-599
            if ("SA".equals(siteCode) || "SA_EN".equals(siteCode)) {
                productListLimit = NBAA_PRODUCT_LIST_LIMIT_ADDON_SA;
            }

            String pdpAddOnListLimit =
                    cachedDataUtil.getComMessageCode(
                            CachedCodeRequestVO.ofDspTypeComMessage(
                                    "pbp_addon_list_limit", CommonConstants.SHOP_CODE_D2C));

            if (!pdpAddOnListLimit.isEmpty()) {
                try {
                    productListLimit = Integer.parseInt(pdpAddOnListLimit);
                } catch (NumberFormatException e) {
                    log.error(
                            "ProductListServiceNoTxImpl^^setModelsInfo^^Exception : "
                                    + e.getMessage());
                }
            }
        } else if ("PAGINATION".equals(listType)
                || "ALL".equals(listType)
                || "HR".equals(listType)
                || "WISHLIST".equals(listType)
                || "NEWEST_ACTIVE_COMMERCE".equals(listType)) {
            productListLimit = models.size();
        }

        for (int i = 0; i < models.size(); i++) {
            String pdpId = models.get(i).toString();
            limitModel.add(pdpId);
            if (productListLimit <= limitModel.size()) {
                break;
            }
        }

        return limitModel;
    }

    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getProductListInfo(Map<String, Object> input) {

        List<Map<String, Object>> productListMapList = new ArrayList<>();
        String siteCode = Objects.toString(input.get("siteCode"));
        String standardFlag = Objects.toString(input.get("standardFlag"));
        String shopCode = Objects.toString(input.get("shopCode"));
        String customerGroup = Objects.toString(input.get("customerGroup"));
        String bundlesOnly = Objects.toString(input.get("bundlesOnly"));
        List<String> filterModelList = (List<String>) input.get("filterModelList");
        String localeCode =
                cachedDataUtil.getComLocaleCode(CachedCodeRequestVO.ofComLocale()).getLocaleCode();
        try {
            String defaultDummyImg =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.ofComSysConfig("dummy_image_url")),
                            "");

            String group = Objects.toString(input.get("group"));
            if (StringUtils.isBlank(group)) {
                input.put("group", SessionUserUtil.getUserGroup());
            }

            /** 제품 목록 조회 */
            String listType = Objects.toString(input.get("listType"));
            String pageType = Objects.toString(input.get("pageType"));
            if ("EVENT".equals(listType)) {
                input.put("productListLimit", NBAA_PRODUCT_LIST_LIMIT_EV);
            } else if ("SP".equals(listType)) {
                input.put("productListLimit", NBAA_PRODUCT_LIST_LIMIT_SP);
            } else if ("PLP".equals(pageType)) {
                input.put("productListLimit", NBAA_PRODUCT_LIST_LIMIT_PLP);
            } else {
                input.put("productListLimit", NBAA_PRODUCT_LIST_LIMIT);
            }

            List<String> pdpIdList = (List<String>) input.get("modelsInfo");
            List<SystemPdsProductListPdpInfoResponseVO> sysPdsProductListPdpInfoList = null;
            boolean mtsFlag = Boolean.parseBoolean(Objects.toString(input.get("mtsFlag")));

            if (!mtsFlag) {
                sysPdsProductListPdpInfoList =
                        systemPdsProductListClient.getProductListPdpInfo(
                                SystemPdsProductListPdpInfoRequestVO.builder()
                                        .standardFlag(standardFlag)
                                        .pdpIdList(pdpIdList)
                                        .listType(listType)
                                        .dummyImageUrl(defaultDummyImg)
                                        .bizTypeCode(Objects.toString(input.get("bizType"), ""))
                                        .siblingGroupFlag(
                                                Objects.toString(input.get("siblingGroupFlag"), ""))
                                        .today(Objects.toString(input.get("today"), ""))
                                        .siteCode(siteCode)
                                        .localeCode(localeCode)
                                        .promotionId(Objects.toString(input.get("promotionId"), ""))
                                        .skuListFlag(Objects.toString(input.get("skuListFlag"), ""))
                                        .hiddenModelSearchFlag(
                                                Objects.toString(
                                                        input.get("hiddenModelSearchFlag"), ""))
                                        .pageType(pageType)
                                        .reviewType(Objects.toString(input.get("reviewType"), ""))
                                        .build());
            } else {
                // 멀티샵일때, OrderList 여부에 따른 조회 분기
                if (!CommonConstants.YES_FLAG.equals(
                        Objects.toString(input.get("orderListFlag"), ""))) {
                    sysPdsProductListPdpInfoList =
                            systemPdsProductListClient.getMtsProductListPdpInfo(
                                    SystemPdsProductListPdpInfoRequestVO.builder()
                                            .standardFlag(standardFlag)
                                            .pdpIdList(pdpIdList)
                                            .listType(listType)
                                            .dummyImageUrl(defaultDummyImg)
                                            .shopCode(shopCode)
                                            .customerGroup(customerGroup)
                                            .bizTypeCode(Objects.toString(input.get("bizType"), ""))
                                            .siblingGroupFlag(
                                                    Objects.toString(
                                                            input.get("siblingGroupFlag"), ""))
                                            .today(Objects.toString(input.get("today"), ""))
                                            .siteCode(siteCode)
                                            .localeCode(localeCode)
                                            .promotionId(
                                                    Objects.toString(input.get("promotionId"), ""))
                                            .skuListFlag(
                                                    Objects.toString(input.get("skuListFlag"), ""))
                                            .hiddenModelSearchFlag(
                                                    Objects.toString(
                                                            input.get("hiddenModelSearchFlag"), ""))
                                            .build());
                } else {
                    sysPdsProductListPdpInfoList =
                            systemPdsProductListClient.getProductListPdpInfo(
                                    SystemPdsProductListPdpInfoRequestVO.builder()
                                            .standardFlag(standardFlag)
                                            .pdpIdList(pdpIdList)
                                            .listType(listType)
                                            .dummyImageUrl(defaultDummyImg)
                                            .bizTypeCode(Objects.toString(input.get("bizType"), ""))
                                            .siblingGroupFlag(
                                                    Objects.toString(
                                                            input.get("siblingGroupFlag"), ""))
                                            .today(Objects.toString(input.get("today"), ""))
                                            .siteCode(siteCode)
                                            .promotionId(
                                                    Objects.toString(input.get("promotionId"), ""))
                                            .skuListFlag(
                                                    Objects.toString(input.get("skuListFlag"), ""))
                                            .hiddenModelSearchFlag(
                                                    Objects.toString(
                                                            input.get("hiddenModelSearchFlag"), ""))
                                            .orderListFlag(CommonConstants.YES_FLAG)
                                            .build());
                }
            }

            String limitSaleUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference("LIMIT_SALE_USE_FLAG")),
                            "N");
            String labelUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference("LABEL_USE_FLAG")),
                            "N");
            String repairabilityIndexFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.ofComSysConfig(
                                            "repairability_index_use_flag")),
                            "N");

            for (int i = 0; i < sysPdsProductListPdpInfoList.size(); i++) {
                SystemPdsProductListPdpInfoResponseVO nbaVo = sysPdsProductListPdpInfoList.get(i);
                ProductListModelVO productListResp = nbaVo.toVO();

                if (CommonConstants.YES_FLAG.equals(standardFlag)) {
                    productListResp.setSuperCategoryId(nbaVo.getLv2CategoryCode());
                    productListResp.setCategoryId(nbaVo.getLv3CategoryCode());
                    productListResp.setSubCategoryId(nbaVo.getLv4CategoryCode());
                } else {
                    productListResp.setSuperCategoryId(nbaVo.getLv1CategoryId());
                    productListResp.setCategoryId(nbaVo.getLv2CategoryId());
                    productListResp.setSubCategoryId(nbaVo.getLv3CategoryId());
                }

                /** MSRP SETTING */
                productListResp.setMsrp(
                        cachedDataUtil.getMsrp(siteCode, nbaVo.getMsrpSalesPrice()));

                /** siblingList */
                List<Map<String, Object>> siblingList = new ArrayList<>();
                String siblingGroupCode = "";
                String bizTypeCode = "";
                String superCategoryId = "";
                if (StringUtils.isNotBlank(productListResp.getSiblingGroupCode())) {
                    siblingGroupCode = productListResp.getSiblingGroupCode();
                    bizTypeCode = productListResp.getBizType();
                    if (CommonConstants.YES_FLAG.equals(standardFlag)) {
                        superCategoryId = nbaVo.getLv2CategoryCode();
                    } else {
                        superCategoryId = nbaVo.getLv1CategoryId();
                    }

                    String siblingLimitCntSt =
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.ofComSysConfig("sibling_limit_cnt"));

                    if (StringUtils.isBlank(siblingLimitCntSt)) {
                        siblingLimitCntSt = "6";
                    }

                    List<SystemPdsProductListSiblingInfoResponseVO> sysPdsSiblingList =
                            systemPdsProductListClient.getProductListSiblingList(
                                    SystemPdsProductListSiblingInfoRequestVO.builder()
                                            .siteCode(siteCode)
                                            .standardFlag(standardFlag)
                                            .siblingGroupCode(siblingGroupCode)
                                            .bundlesOnly(bundlesOnly)
                                            .filterModelList(filterModelList)
                                            .productStateCode(productListResp.getModelStatusCode())
                                            .bizTypeCode(bizTypeCode)
                                            .promotionsOnly(
                                                    Objects.toString(input.get("promotionsOnly")))
                                            .today(Objects.toString(input.get("today")))
                                            .superCategoryId(superCategoryId)
                                            .categoryId(
                                                    Objects.toString(input.get("categoryId"), ""))
                                            .subCategoryId(
                                                    Objects.toString(
                                                            input.get("subCategoryId"), ""))
                                            .siblingLimitCnt(Integer.parseInt(siblingLimitCntSt))
                                            .pageType(pageType)
                                            .build());

                    if (ObjectUtils.isNotEmpty(sysPdsSiblingList)) {
                        for (SystemPdsProductListSiblingInfoResponseVO
                                sysPdsProductListSiblingResp : sysPdsSiblingList) {
                            Map<String, Object> siblingMap = new HashMap<>();
                            siblingMap.put(
                                    "siblingCode", sysPdsProductListSiblingResp.getSiblingCode());
                            siblingMap.put(
                                    "siblingValue", sysPdsProductListSiblingResp.getSiblingValue());
                            siblingMap.put("sku", sysPdsProductListSiblingResp.getLgcomSkuId());
                            siblingMap.put(
                                    "promotionText",
                                    sysPdsProductListSiblingResp.getPromotionText());
                            siblingMap.put(
                                    "promotionLinkUrl",
                                    sysPdsProductListSiblingResp.getPromotionLinkUrl());
                            siblingList.add(siblingMap);
                        }
                    }

                    if ("SELF".equals(productListResp.getTarget())
                            && CommonConstants.NO_FLAG.equals(
                                    productListResp.getDefaultSiblingModelFlag())) {
                        String modelUrlPath =
                                systemPdsProductListClient.getSelfSiblingDefaultUrl(
                                        SystemPdsProductListSelfSiblingDefaultRequestVO.builder()
                                                .siteCode(siteCode)
                                                .bizTypeCode(bizTypeCode)
                                                .standardFlag(standardFlag)
                                                .pdpId(productListResp.getModelId())
                                                .superCategoryId(superCategoryId)
                                                .categoryId(
                                                        Objects.toString(
                                                                input.get("categoryId"), ""))
                                                .subCategoryId(
                                                        Objects.toString(
                                                                input.get("subCategoryId"), ""))
                                                .build());

                        productListResp.setModelUrlPath(modelUrlPath);
                    }

                    productListResp.setSiblingModels(siblingList);
                }

                if (null == productListResp.getBuyNowUrl()
                        || "".equals(productListResp.getBuyNowUrl())) {
                    productListResp.setBuyNowUrl(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.ofComSysConfig("default_buy_now_url")));
                }

                if ("Y".equals(productListResp.getProductSupportFlag())) {
                    ProductSupportRequestVO tmpInput = new ProductSupportRequestVO();
                    tmpInput.setSalesModelCode(productListResp.getSalesModelCode());
                    tmpInput.setSalesSuffixCode(productListResp.getSalesSuffixCode());
                    tmpInput.setModelId(productListResp.getModelId());
                    productListResp.setProductSupportUrl(commonService.getSupportLinkUrl(tmpInput));
                }

                String bizReviewUseFlag =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemPreferenceCode(
                                        CachedCodeRequestVO.ofComSysPreference(
                                                "BUSINESS_REVIEW_USE_FLAG")),
                                CommonConstants.NO_FLAG);
                String reviewType = Objects.toString(input.get("reviewType"));
                if (CommonConstants.BIZ_TYPE_B2B.equals(productListResp.getBizType())
                        && CommonConstants.NO_FLAG.equals(bizReviewUseFlag)) {
                    reviewType = CommonConstants.NO_FLAG;
                }

                productListResp.setReviewType(reviewType);
                productListResp.setLocaleCode(Objects.toString(input.get("siteCode")));

                setGsriEnergyLabelVO(productListResp, shopCode);

                productListResp.setLimitSaleUseFlag(limitSaleUseFlag);
                productListResp.setBuyNowUseFlag(Objects.toString(input.get("buyNowUseFlag")));

                String inquiryToBuyFlag =
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("INQUIRY_TO_BUY_BTN_FLAG"));
                inquiryToBuyFlag =
                        StringUtils.isBlank(inquiryToBuyFlag)
                                ? CommonConstants.NO_FLAG
                                : inquiryToBuyFlag;
                String findTheDealerFlag =
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("FIND_A_DEALER_BTN_FLAG"));
                findTheDealerFlag =
                        StringUtils.isBlank(findTheDealerFlag)
                                ? CommonConstants.NO_FLAG
                                : findTheDealerFlag;

                productListResp.setInquiryToBuyFlag(mtsFlag ? null : inquiryToBuyFlag);
                productListResp.setFindTheDealerFlag(mtsFlag ? null : findTheDealerFlag);

                List<Map<String, Object>> labelRepairMapList = new ArrayList<Map<String, Object>>();

                if (CommonConstants.YES_FLAG.equals(labelUseFlag)
                        && CommonConstants.YES_FLAG.equals(repairabilityIndexFlag)) {
                    Map<String, Object> labelRepairMap = null;

                    List<SystemPdsProductIconListResponseVO> sysPdsProductIconListVO =
                            systemPdsPdpInfoClient.getProductIconList(
                                    SystemPdsProductIconListRequestVO.builder()
                                            .siteCode(siteCode)
                                            .bizTypeCode(bizTypeCode)
                                            .today(Objects.toString(input.get("today")))
                                            .pdpIdList(Arrays.asList(productListResp.getModelId()))
                                            .build());

                    if (ObjectUtils.isNotEmpty(sysPdsProductIconListVO)) {
                        for (SystemPdsProductIconListResponseVO sysPdsPdpIcon :
                                sysPdsProductIconListVO) {
                            ProductIconListResponseVO pdpIconRespVO = sysPdsPdpIcon.toVO();
                            ObjectMapper mapper = new ObjectMapper();

                            if ("REPAIRABILITY INDEX".equals(pdpIconRespVO.getShortDesc())) {
                                if (labelRepairMapList.size() == 0) {
                                    labelRepairMap = mapper.convertValue(pdpIconRespVO, Map.class);
                                    labelRepairMapList.add(labelRepairMap);
                                }
                            }
                        }
                    }

                    productListResp.setLabelUseFlag(labelUseFlag);
                    productListResp.setRepairabilityIndexFlag(repairabilityIndexFlag);
                }
                productListResp.setLabelRepairMap(labelRepairMapList);

                String taxInformationUseFlag = CommonConstants.NO_FLAG;
                String taxInfoProductLvl1Code =
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("tax_info_product_level_code"));

                List<String> list = new ArrayList<String>();
                Collections.addAll(list, taxInfoProductLvl1Code.split(","));
                if (list.indexOf(productListResp.getProductLevel1Code()) > -1) {
                    taxInformationUseFlag = CommonConstants.YES_FLAG;
                }
                productListResp.setTaxInformationUseFlag(taxInformationUseFlag);

                setWtbFlag(productListResp);

                SystemPdsProductEpsInfoResponseVO sysPdsProductEpsInfo =
                        systemPdsPdpInfoClient.getProductEpsInfo(
                                SystemPdsProductEpsInfoRequestVO.builder()
                                        .pdpId(productListResp.getModelId())
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsProductEpsInfo)) {
                    productListResp.setEpsIncludeCharger(sysPdsProductEpsInfo.getEpsUseFlag());
                    productListResp.setEpsMinPower(sysPdsProductEpsInfo.getEpsMinVoltage());
                    productListResp.setEpsMaxPower(sysPdsProductEpsInfo.getEpsMaxVoltage());
                    productListResp.setEpsUsbPd(sysPdsProductEpsInfo.getEpsUsbPdNm());
                }

                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> productListMap =
                        mapper.convertValue(productListResp, Map.class);

                productListMapList.add(productListMap);
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }

        return productListMapList;
    }

    private void setGsriEnergyLabelVO(ProductListModelVO productListModel, String shopCode) {
        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        try {
            String pdpId = productListModel.getModelId();
            String energyLabel = productListModel.getEnergyLabel();
            String elTypeCode = productListModel.getElType();
            String secondELTypeCode = productListModel.getSecondElType();
            String secondELabel = productListModel.getSecondEnergyLabel();
            String washTowerFlag = productListModel.getWashTowerFlag();
            String bizTypeCode = productListModel.getBizType();

            /** 제품 GSRI(환경규제 관련 모델) 정보 조회 */
            String docTypeCode = "";
            String energyLabelDocId = "";
            String fEnergyLabelDocId = "";
            String fEnergyLabelFileName = "";
            String fEnergyLabelOriginalName = "";
            String fEnergyLabelproductLeve1Code = "";
            String productFichelDocId = "";
            String productFicheFileName = "";
            String productFicheOriginalName = "";
            String productFicheproductLeve1Code = "";
            String energyLabelFileName = "";
            String energyLabelOriginalName = "";
            String energyLabelproductLeve1Code = "";

            docTypeCode = productListModel.getEnergyLabelCategory();
            SystemPdsProductGsriFileInfoResponseVO sysPdsProductGsriFileResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductGsriFileResp.getResultMap();

                energyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                energyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                energyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                energyLabelproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            productListModel.setEnergyLabelDocId(energyLabelDocId);
            productListModel.setEnergyLabelFileName(energyLabelFileName);
            productListModel.setEnergyLabelOriginalName(energyLabelOriginalName);
            productListModel.setEnergyLabelproductLeve1Code(energyLabelproductLeve1Code);

            docTypeCode = productListModel.getPisDocType();
            SystemPdsProductGsriFileInfoResponseVO sysPdsProductFicheResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductFicheResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductFicheResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductFicheResp.getResultMap();

                productFichelDocId = Objects.toString(resultMap.get("docId"), "");
                productFicheFileName = Objects.toString(resultMap.get("fileName"), "");
                productFicheOriginalName = Objects.toString(resultMap.get("originalName"), "");
                productFicheproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            productListModel.setProductFicheDocId(productFichelDocId);
            productListModel.setProductFicheFileName(productFicheFileName);
            productListModel.setProductFicheOriginalName(productFicheOriginalName);
            productListModel.setProductFicheproductLeve1Code(productFicheproductLeve1Code);

            if ("EL".equals(productListModel.getEnergyLabelCategory())) {
                docTypeCode = "FL";
            } else {
                docTypeCode = "FE";
            }
            SystemPdsProductGsriFileInfoResponseVO sysPdsFEnergyLabelResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp)
                    && ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsFEnergyLabelResp.getResultMap();

                fEnergyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                fEnergyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                fEnergyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                fEnergyLabelproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            productListModel.setFEnergyLabelDocId(fEnergyLabelDocId);
            productListModel.setFEnergyLabelFileName(fEnergyLabelFileName);
            productListModel.setFEnergyLabelOriginalName(fEnergyLabelOriginalName);
            productListModel.setFenergyLabelproductLeve1Code(fEnergyLabelproductLeve1Code);

            /** 제품 에너지 라벨 조회 */
            SystemPdsPdpEnergyLabelInfoResponseVO eLabelInfoResponseVO =
                    systemPdsPdpInfoClient.getEnergyLabelInfo(
                            SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .bizTypeCode(bizTypeCode)
                                    .elabelClsCode(elTypeCode)
                                    .elabelGrdCode(energyLabel)
                                    .build());

            String eLabelImageAddr = "";
            String eLabelName = "";
            if (ObjectUtils.isNotEmpty(eLabelInfoResponseVO)) {
                eLabelImageAddr =
                        StringUtils.defaultIfBlank(eLabelInfoResponseVO.getElabelImgPath(), "");
                eLabelName =
                        StringUtils.defaultIfBlank(eLabelInfoResponseVO.getEnergyLabelName(), "");
            }
            productListModel.setEnergyLabelImageAddr(eLabelImageAddr);
            productListModel.setEnergyLabelName(eLabelName);

            if (CommonConstants.YES_FLAG.equals(washTowerFlag)) {
                docTypeCode = productListModel.getSecondEnergyLabelCategory();
                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductGsriFileResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondProductGsriFileResp.getResultMap();

                    energyLabelDocId = Objects.toString(resultMap.get("docId"), "");

                    productListModel.setSecondEnergyLabelDocId(
                            Objects.toString(resultMap.get("docId"), ""));
                    productListModel.setSecondEnergyLabelFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    productListModel.setSecondEnergyLabelOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    productListModel.setSecondEnergyLabelproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                docTypeCode = productListModel.getSecondPisDocType();
                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductFicheResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondProductFicheResp.getResultMap();

                    productFichelDocId = Objects.toString(resultMap.get("docId"), "");

                    productListModel.setSecondProductFicheDocId(productFichelDocId);
                    productListModel.setSecondProductFicheFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    productListModel.setSecondProductFicheOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    productListModel.setSecondProductFicheproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                if ("EN".equals(productListModel.getEnergyLabelCategory())) {
                    docTypeCode = "FL";
                } else {
                    docTypeCode = "FE";
                }
                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondFEnergyLabelResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondFEnergyLabelResp.getResultMap();

                    productListModel.setSecondFEnergyLabelDocId(
                            Objects.toString(resultMap.get("docId"), ""));
                    productListModel.setSecondFEnergyLabelFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    productListModel.setSecondFEnergyLabelOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    productListModel.setSecondFEnergyLabelproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                SystemPdsPdpEnergyLabelInfoResponseVO secondELabelInfoResponseVO =
                        systemPdsPdpInfoClient.getEnergyLabelInfo(
                                SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .bizTypeCode(bizTypeCode)
                                        .elabelClsCode(secondELTypeCode)
                                        .elabelGrdCode(secondELabel)
                                        .build());

                String secondELabelImageAddr = "";
                String secondELabelName = "";
                if (ObjectUtils.isNotEmpty(secondELabelInfoResponseVO)) {
                    secondELabelImageAddr =
                            StringUtils.defaultIfBlank(
                                    secondELabelInfoResponseVO.getElabelImgPath(), "");
                    secondELabelName =
                            StringUtils.defaultIfBlank(
                                    secondELabelInfoResponseVO.getEnergyLabelName(), "");
                }
                productListModel.setSecondEnergyLabelImageAddr(secondELabelImageAddr);
                productListModel.setSecondEnergyLabelName(secondELabelName);
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }
    }

    private void setWtbFlag(ProductListModelVO productInfo) {

        String externalLinkUseFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference(
                                        "WTB_EXTERNAL_LINK_USE_FLAG")),
                        CommonConstants.NO_FLAG);

        if (CommonConstants.NO_FLAG.equals(externalLinkUseFlag)) {
            productInfo.setWtbExternalLinkName("");
            productInfo.setWtbExternalLinkUrl("");
            productInfo.setWtbExternalLinkUseFlag(CommonConstants.NO_FLAG);
            productInfo.setWtbExternalLinkSelfFlag(CommonConstants.NO_FLAG);
        } else {
            boolean wtbExternalConditon =
                    CommonConstants.YES_FLAG.equals(productInfo.getWtbExternalLinkUseFlag())
                            && StringUtils.isNotBlank(productInfo.getWtbExternalLinkName());

            if (wtbExternalConditon) {
                String defaultExternalLinkName =
                        productInfo.getWtbExternalLinkName().replaceAll("_", " ");
                StringBuffer externalBuffer = new StringBuffer();
                externalBuffer.append(EXTERNAL_WTB_PREFIX).append(defaultExternalLinkName);
                String msgExternalLinkName = externalBuffer.toString();

                String localeCode =
                        cachedDataUtil
                                .getComLocaleCode(CachedCodeRequestVO.ofComLocale())
                                .getLocaleCode();
                String wtbExtlLinkNm =
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        msgExternalLinkName,
                                        CommonConstants.SHOP_CODE_D2C,
                                        localeCode));
                productInfo.setWtbExternalLinkName(wtbExtlLinkNm);
            }
        }
    }
}
