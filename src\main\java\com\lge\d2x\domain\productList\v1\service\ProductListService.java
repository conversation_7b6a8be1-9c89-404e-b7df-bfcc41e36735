package com.lge.d2x.domain.productList.v1.service;

import com.lge.d2x.domain.productList.v1.model.OrderProductListRequestVO;
import com.lge.d2x.domain.productList.v1.model.OrderProductListResponseVO;
import com.lge.d2x.domain.productList.v1.model.PdpIdListBySkuRequestVO;
import com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoResponseVO;
import com.lge.d2x.domain.productList.v1.model.ProductListPromotionInfoRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListPromotionInfoResponseVO;
import com.lge.d2x.domain.productList.v1.model.ProductListSelfSiblingDefaultRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListSiblingInfoRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListSiblingInfoResponseVO;
import com.lge.d2x.domain.productList.v1.repository.pdsmgr.ProductListRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Aspect
@Service
@RequiredArgsConstructor
public class ProductListService {

    private final ProductListRepository productListRepository;

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<String> selectPdpIdBySkuId(PdpIdListBySkuRequestVO requestVO) {

        List<String> result = null;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = productListRepository.selectPdpIdBySkuId(requestVO);
        } else {
            result = productListRepository.selectPdpIdBySkuIdNotStandard(requestVO);
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<String> selectPdpIdListByListType(PdpIdListRequestVO requestVO) {

        List<String> result = null;

        String listType = requestVO.getListType();

        if ("Y".equals(requestVO.getStandardFlag())) {
            switch (listType) {
                case "NEW" -> result = productListRepository.selectNewestPdpIdList(requestVO);
                case "HR" -> result = productListRepository.selectHighlyRatedPdpIdList(requestVO);
                case "MP" -> result = productListRepository.selectMostPopularPdpIdList(requestVO);
                case "AC" -> result = productListRepository.selectAccessoryPdpIdList(requestVO);
                case "LGPICK" -> result = productListRepository.selectLgPickPdpIdList(requestVO);
            }
        } else {
            switch (listType) {
                case "NEW" -> result =
                        productListRepository.selectNewestPdpIdListNotStandard(requestVO);
                case "HR" -> result =
                        productListRepository.selectHighlyRatedPdpIdListNotStandard(requestVO);
                case "MP" -> result =
                        productListRepository.selectMostPopularPdpIdListNotStandard(requestVO);
                case "AC" -> result = productListRepository.selectAccessoryPdpIdList(requestVO);
                case "LGPICK" -> result = productListRepository.selectLgPickPdpIdList(requestVO);
            }
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductListPdpInfoResponseVO> selectProductListPdpInfo(
            ProductListPdpInfoRequestVO requestVO) {

        List<ProductListPdpInfoResponseVO> result = null;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = productListRepository.selectProductListPdpInfo(requestVO);
        } else {
            result = productListRepository.selectProductListPdpInfoNotStandard(requestVO);
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductListSiblingInfoResponseVO> selectProductListSiblingList(
            ProductListSiblingInfoRequestVO requestVO) {

        return productListRepository.selectProductListSiblingList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public ProductListPromotionInfoResponseVO selectProductListPromotionInfo(
            ProductListPromotionInfoRequestVO requestVO) {

        return productListRepository.selectProductListPromotionInfo(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<String> selectMtsPdpIdBySkuId(PdpIdListBySkuRequestVO requestVO) {

        List<String> result = null;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = productListRepository.selectMtsPdpIdBySkuId(requestVO);
        } else {
            result = productListRepository.selectMtsPdpIdBySkuIdNotStandard(requestVO);
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<String> selectMtsPdpIdListByListType(PdpIdListRequestVO requestVO) {

        List<String> result = null;

        String listType = requestVO.getListType();

        if ("Y".equals(requestVO.getStandardFlag())) {
            switch (listType) {
                case "NEW" -> result = productListRepository.selectMtsNewestPdpIdList(requestVO);
                case "HR" -> result =
                        productListRepository.selectMtsHighlyRatedPdpIdList(requestVO);
                case "MP" -> result =
                        productListRepository.selectMtsMostPopularPdpIdList(requestVO);
                case "AC" -> result = productListRepository.selectMtsAccessoryPdpIdList(requestVO);
            }
        } else {
            switch (listType) {
                case "NEW" -> result =
                        productListRepository.selectMtsNewestPdpIdListNotStandard(requestVO);
                case "HR" -> result =
                        productListRepository.selectMtsHighlyRatedPdpIdListNotStandard(requestVO);
                case "MP" -> result =
                        productListRepository.selectMtsMostPopularPdpIdListNotStandard(requestVO);
                case "AC" -> result = productListRepository.selectMtsAccessoryPdpIdList(requestVO);
            }
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public String selectSelfSiblingDefaultUrl(ProductListSelfSiblingDefaultRequestVO requestVO) {
        String pdpUrl = "";

        pdpUrl = productListRepository.selectSelfSiblingDefaultUrl(requestVO);

        return pdpUrl;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductListPdpInfoResponseVO> selectMtsProductListPdpInfo(
            ProductListPdpInfoRequestVO requestVO) {

        List<ProductListPdpInfoResponseVO> result = null;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = productListRepository.selectMtsProductListPdpInfo(requestVO);
        } else {
            result = productListRepository.selectMtsProductListPdpInfoNotStandard(requestVO);
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<OrderProductListResponseVO> selectMtsOrderProductList(
            OrderProductListRequestVO requestVO) {

        List<OrderProductListResponseVO> result = null;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = productListRepository.selectMtsOrderProductList(requestVO);
        } else {
            result = productListRepository.selectMtsOrderProductListNotStandard(requestVO);
        }

        return result;
    }
}
