package com.lge.d2x.domain.support.v1.model;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class GpCateInfoRequestVO {
    private String dateFormatInfo;
    private String customerNo;
    private String localeCode;
    private String countryCode;
    private String euEcoCategoryDt;
    private String ecoCategoryFlag;
    private List<String> categoryIdList;
    private List<String> mobileCategoryList;
    private List<String> hiddenCategoryList;
    private String b2bUseFlag;
    private String b2bDivisionUseFlag;
    private String pageType;
    private String javaLocaleCode;
    private String categoryId;
    private String pageFlag;
    private String callSubCateId;
    private String superCategoryId;
    private String divisionBizType;
    private String tabType;
    private String[] objetModelList;
}
