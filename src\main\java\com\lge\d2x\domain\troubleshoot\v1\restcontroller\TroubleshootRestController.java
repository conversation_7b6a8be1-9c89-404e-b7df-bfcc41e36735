package com.lge.d2x.domain.troubleshoot.v1.restcontroller;

import com.lge.d2x.domain.troubleshoot.v1.model.CategoryNameRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.CategoryRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.CategoryResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptomResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SuperCategoryRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SuperCategoryResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.service.TroubleshootService;
import com.lge.d2xfrm.model.common.D2xCommonResponseVO;
import com.lge.d2xfrm.util.common.D2xResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/troubleshoot/v1")
@RequiredArgsConstructor
@Tag(name = "TroubleshootRestController", description = "Troubleshoot API")
public class TroubleshootRestController {
    private final TroubleshootService troubleshootService;

    @Operation(summary = "HelpfulArticlesList 정보", description = "HelpfulArticlesList 정보를 조회한다.")
    @GetMapping(path = "/helpful-articles-list")
    public ResponseEntity<D2xCommonResponseVO<List<HelpfulArticlesResponseVO>>> helpfulArticlesList(
            HelpfulArticlesRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getHelpfulArticlesList(requestVO));
    }

    @Operation(
            summary = "HelpfulArticlesList 3depth 정보",
            description = "HelpfulArticlesList 3depth 정보를 조회한다.")
    @GetMapping(path = "/helpful-articles-list-3depth")
    public ResponseEntity<D2xCommonResponseVO<List<HelpfulArticlesResponseVO>>>
            helpfulArticlesList3Depth(HelpfulArticlesRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getHelpfulArticlesList3Depth(requestVO));
    }

    @Operation(summary = "Super Category List 정보", description = "Super Category List 정보를 조회한다.")
    @GetMapping(path = "/super-category-list")
    public ResponseEntity<D2xCommonResponseVO<List<SuperCategoryResponseVO>>> gpSuperCategoryList(
            SuperCategoryRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getGpSuperCategory(requestVO));
    }

    @Operation(summary = "Category List 정보", description = "Category List 정보를 조회한다.")
    @GetMapping(path = "/category-list")
    public ResponseEntity<D2xCommonResponseVO<List<CategoryResponseVO>>> gpCategoryList(
            CategoryRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(troubleshootService.getGpCategory(requestVO));
    }

    @Operation(summary = "Symptom List 정보", description = "Symptom List 정보를 조회한다.")
    @GetMapping(path = "/symptom-list")
    public ResponseEntity<D2xCommonResponseVO<List<SymptomResponseVO>>> symptomList(
            SymptomRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(troubleshootService.getSymptom(requestVO));
    }

    @Operation(
            summary = "Symptom T 1Depth List 정보",
            description = "Symptom T 1Depth List 정보를 조회한다.")
    @GetMapping(path = "/symptom-t-1depth-list")
    public ResponseEntity<D2xCommonResponseVO<List<SymptomResponseVO>>> symptomT1DepthList(
            SymptomRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSymptomT1Depth(requestVO));
    }

    @Operation(summary = "Sub Symptom List 정보", description = "Sub Symptom List 정보를 조회한다.")
    @GetMapping(path = "/sub-symptom-list")
    public ResponseEntity<D2xCommonResponseVO<List<SubSymptomResponseVO>>> subSymptomList(
            SubSymptomRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(troubleshootService.getSubsymptom(requestVO));
    }

    @Operation(
            summary = "Sub Symptom T 2Depth List 정보",
            description = "Sub Symptom T 2Depth List 정보를 조회한다.")
    @GetMapping(path = "/sub-symptom-t-2depth-list")
    public ResponseEntity<D2xCommonResponseVO<List<SubSymptomResponseVO>>> subSymptomT2DepthList(
            SubSymptomRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSubSymptomT2DepthList(requestVO));
    }

    @Operation(
            summary = "Sub Symptom 3Depth List 정보",
            description = "Sub Symptom 3Depth List 정보를 조회한다.")
    @GetMapping(path = "/sub-symptom-3depth-ttype-list")
    public ResponseEntity<D2xCommonResponseVO<List<DetailSymptomResponseVO>>>
            subsymptom3DepthTtypeList(SubSymptomRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSubsymptom3DepthTtype(requestVO));
    }

    @Operation(summary = "Category Name 정보", description = "Category Name 정보를 조회한다.")
    @GetMapping(path = "/category-name")
    public ResponseEntity<D2xCommonResponseVO<String>> categoryName(
            CategoryNameRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getCategoryName(requestVO));
    }

    @Operation(
            summary = "Sub Symptom CW Type List",
            description = "Sub Symptom CW Type List 정보를 조회한다.")
    @GetMapping(path = "/sub-sympton-cw-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<SubSymptCwTypeResponseVO>>> subsymptomCWtypeList(
            SubSymptCwTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSubsymptomCWtypeList(requestVO));
    }

    @Operation(
            summary = "Sub Symptom CW 2Depth Type List",
            description = "Sub Symptom CW 2Depth Type List 정보를 조회한다.")
    @GetMapping(path = "/sub-sympton-cw-2depth-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<SubSymptCwTypeResponseVO>>>
            subSymptomCw2DepthTypeList(SubSymptCwTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSubSymptomCw2DepthTypeList(requestVO));
    }

    @Operation(
            summary = "Sub Symptom CW 3Depth Type List",
            description = "Sub Symptom CW 3Depth Type List 정보를 조회한다.")
    @GetMapping(path = "/sub-sympton-cw-3depth-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<DetailSymptCwTypeResponseVO>>>
            subsymptomCW3DepthTypeList(SubSymptCwTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSubsymptomCW3DepthTypeList(requestVO));
    }

    @Operation(summary = "Symptom CW Type List", description = "Symptom CW Type List 정보를 조회한다.")
    @GetMapping(path = "/sympton-cw-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<SymptCwTypeResponseVO>>> symptomCWtypeList(
            SymptCwTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSymptomCWtypeList(requestVO));
    }

    @Operation(
            summary = "Symptom CW 1Depth Type List",
            description = "Symptom CW 1Depth Type List 정보를 조회한다.")
    @GetMapping(path = "/sympton-cw-1depth-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<SymptCwTypeResponseVO>>> symptomCW1DepthTypeList(
            SymptCwTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSymptomCW1DepthTypeList(requestVO));
    }

    @Operation(summary = "Symptom R Type List", description = "Symptom R Type List 정보를 조회한다.")
    @GetMapping(path = "/sympton-r-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<SymptomRTypeResponseVO>>> symptomRtypeList(
            SymptomRTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSymptomRtypeList(requestVO));
    }

    @Operation(
            summary = "Symptom R 1Depth Type List",
            description = "Symptom R 1Depth Type List 정보를 조회한다.")
    @GetMapping(path = "/sympton-r-1depth-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<SymptomRTypeResponseVO>>> symptomR1DepthTypeList(
            SymptomRTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSymptomR1DepthTypeList(requestVO));
    }

    @Operation(
            summary = "Sub Symptom R Type List",
            description = "Sub Symptom R Type List 정보를 조회한다.")
    @GetMapping(path = "/sub-sympton-r-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<SubSymptRTypeResponseVO>>> subSymptomRtypeList(
            SubSymptRTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSubSymptomRtypeList(requestVO));
    }

    @Operation(
            summary = "Sub Symptom R 2Depth Type List",
            description = "Sub Symptom R 2Depth Type List 정보를 조회한다.")
    @GetMapping(path = "/sub-sympton-r-2depth-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<SubSymptRTypeResponseVO>>>
            subSymptomR2DepthTypeList(SubSymptRTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSubSymptomR2DepthTypeList(requestVO));
    }

    @Operation(
            summary = "Sub Symptom R 3Depth Type List",
            description = "Sub Symptom R 3Depth Type List 정보를 조회한다.")
    @GetMapping(path = "/sub-sympton-r-3depth-type-list")
    public ResponseEntity<D2xCommonResponseVO<List<DetailSymptRTypeResponseVO>>>
            subSymptomR3DepthTypeList(SubSymptRTypeRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                troubleshootService.getSubSymptomR3DepthTypeList(requestVO));
    }
}
