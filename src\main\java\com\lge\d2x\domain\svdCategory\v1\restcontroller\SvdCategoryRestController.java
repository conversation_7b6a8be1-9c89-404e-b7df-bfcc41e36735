package com.lge.d2x.domain.svdCategory.v1.restcontroller;

import com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv1CategoryResponseVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv2CategoryResponseVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv3CategoryResponseVO;
import com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService;
import com.lge.d2xfrm.model.common.D2xCommonResponseVO;
import com.lge.d2xfrm.util.common.D2xResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/svd-category/v1")
@Tag(name = "[Support]CategoryRestController", description = "서포트 카테고리 정보를 조회하는 컨트롤러이다")
public class SvdCategoryRestController {
    private final SvdCategoryService svdCategoryService;

    @GetMapping("/lv1-category-list")
    @Operation(summary = "서포트 Lv1 카테고리 정보", description = "서포트 Lv1 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv1CategoryResponseVO>>> lv1CategoryList(
            @Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv1CategoryList(req));
    }

    @GetMapping("/lv1-category-list-type-t")
    @Operation(summary = "서포트 Lv1 카테고리(Type T) 정보", description = "서포트 Lv1 카테고리(Type T) 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv1CategoryResponseVO>>> lv1CategoryListTypeT(
            @Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv1CategoryListTypeT(req));
    }

    @GetMapping("/lv1-category-list-v2-type-t")
    @Operation(
            summary = "서포트 Lv1 카테고리(Type T) V2 정보",
            description = "서포트 Lv1 카테고리(Type T) V2 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv1CategoryResponseVO>>>
            lv1CategoryListV2TypeT(@Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv1CategoryListV2TypeT(req));
    }

    @GetMapping("/lv2-category-list-type-t")
    @Operation(summary = "서포트 Lv2 카테고리(Type T) 정보", description = "서포트 Lv2 카테고리(Type T) 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv2CategoryResponseVO>>> lv2CategoryListTypeT(
            @Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv2CategoryListTypeT(req));
    }

    @GetMapping("/lv2-category-list-v2-type-t")
    @Operation(
            summary = "서포트 Lv2 카테고리(Type T) V2 정보",
            description = "서포트 Lv2 카테고리(Type T) V2 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv2CategoryResponseVO>>>
            lv2CategoryListV2TypeT(@Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv2CategoryListV2TypeT(req));
    }

    @GetMapping("/lv2-category-list-type-e")
    @Operation(summary = "서포트 Lv2 카테고리(Type E) 정보", description = "서포트 Lv2 카테고리(Type E) 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv2CategoryResponseVO>>> lv2CategoryListTypeE(
            @Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv2CategoryListTypeE(req));
    }

    @GetMapping("/lv2-category-list-type-mw")
    @Operation(
            summary = "서포트 Lv2 카테고리(Type M or W) 정보",
            description = "서포트 Lv2 카테고리(Type M or W) 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv2CategoryResponseVO>>>
            lv2CategoryListTypeMW(@Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv2CategoryListTypeMW(req));
    }

    @GetMapping("/lv3-category-list")
    @Operation(summary = "서포트 Lv3 카테고리 정보", description = "서포트 Lv3 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv3CategoryResponseVO>>> lv3CategoryList(
            @Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv3CategoryList(req));
    }

    @GetMapping("/lv3-category-list-v2")
    @Operation(summary = "서포트 Lv3 카테고리 V2 정보", description = "서포트 Lv3 카테고리 V2 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv3CategoryResponseVO>>> lv3CategoryListV2(
            @Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv3CategoryListV2(req));
    }

    @GetMapping("/lv3-category-list-type-e")
    @Operation(summary = "서포트 Lv3 카테고리(Type E) 정보", description = "서포트 Lv3 카테고리(Type E) 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SvdLv3CategoryResponseVO>>> lv3CategoryListTypeE(
            @Valid SvdCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                svdCategoryService.selectSvdLv3CategoryListTypeE(req));
    }
}
