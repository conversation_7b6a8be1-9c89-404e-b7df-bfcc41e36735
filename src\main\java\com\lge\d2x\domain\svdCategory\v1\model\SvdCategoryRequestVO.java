package com.lge.d2x.domain.svdCategory.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SvdCategoryRequestVO {

    @Schema(description = "사이트 코드", example = "UK")
    private String siteCode;

    @Schema(description = "자바 로케일 코드", example = "en_GB")
    private String localeCode;

    @Schema(description = "국가 코드", example = "GB")
    private String countryCode;

    @Schema(description = "탭 타입", example = "W")
    private String tabType;

    @Schema(description = "페이지 플래그", example = "Y")
    private String pageFlag;

    @Schema(description = "LV1 카테고리 코드", example = "CT00008336")
    private String lv1CategoryCode;

    @Schema(description = "LV2 카테고리 코드", example = "CT00008337")
    private String lv2CategoryCode;

    @Schema(description = "LV2 카테고리 코드 리스트", example = "[\"CT00008333\", \"CT00008334\"]")
    private List<String> lv2CategoryCodeList;

    @Schema(description = "LV3 카테고리 코드 호출", example = "CT00008335")
    private String callLv3CategoryCode;

    @Schema(description = "B2B 사용 플래그", example = "Y")
    private String b2bUseFlag;

    @Schema(description = "B2B 부서 사용 플래그", example = "Y")
    private String b2bDivisionUseFlag;

    @Schema(description = "비즈니스 타입", example = "B2C")
    private String divisionBizType;

    @Schema(description = "EU 에코 카테고리 날짜", example = "20240708")
    private String euEcoCategoryDt;

    @Schema(description = "에코 카테고리 플래그", example = "Y")
    private String ecoCategoryFlag;

    @Schema(description = "오브제 모델 리스트", example = "[\"105UC9V.AEU\", \"10SE3E-B.AEU\"]")
    private String[] objetModelList;
}
