<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.whereToBuy.v1.repository.pdsmgr.WtbRepository">
	<select id="selectWtbOnlineStore" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.selectWtbOnlineStore */
		       PDP_ID
		     , DSP_SEQ
		     , BIZ_TYPE_CODE
		     , SITE_CODE
		     , RETAILER_NM
		     , RETAILER_HOME_LINK_URL
		     , RETAILER_IMG_PATH
		     , RETAILER_IMG_ALT_TEXT_CNTS
		     , PRODUCT_LINK_URL
		  FROM DSP_PRODUCT_WTB_R
		 WHERE PDP_ID = #{pdpId}
		<choose>
		   <when test='siteCode == "NL"'>
		   AND SITE_CODE IN ('NL', 'NL_BE', 'NLB')
		  </when>
		   <otherwise>
		   AND SITE_CODE = #{siteCode}
		   </otherwise>
		</choose>
		   AND USE_FLAG = 'Y'
		 ORDER BY SITE_CODE
	</select>

	<select id="selectWtbSettInfo" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbSettRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbSettResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.selectWtbSettInfo */
		       A.SITE_CODE
		     , A.BRANDSHOP_TAB_USE_FLAG
		     , A.PURCH_ONLINE_TYPE_CODE -- 구 BUYONLINE_TYPE
		     , B.CODE_VAL_NM AS PURCH_ONLINE_TYPE_NM -- 구 BUYONLINE_TYPE_NAME
		     , A.COUNTRY_COMBO_FLAG
		     , A.ONLINE_RETAIL_ORDER_FLAG
		     , IF(D.CONFIG_VAL = 'Y', 'Y', 'N') AS ONLINE_WTB_COUNTRY_USE_FLAG
		     , A.RADIUS_UNIT_TYPE_CODE
		     , A.STOCK_LIST_USE_FLAG 
		     , A.STORE_DTL_FLAG
		     , A.WTB_TYPE_CODE
		     , A.PURCH_OFFLINE_TYPE_CODE -- 구 WTB_USE_FLAG
		     , C.CODE_VAL_NM AS PURCH_OFFLINE_TYPE_NM -- 구 WTB_USE_FLAG_NAME
		     , A.LV1_ZOOM_RADIUS_VAL
		     , A.LV2_ZOOM_RADIUS_VAL
		     , A.LV3_ZOOM_RADIUS_VAL
		     , A.LV4_ZOOM_RADIUS_VAL
		     , A.LV5_ZOOM_RADIUS_VAL
		     , A.LV6_ZOOM_RADIUS_VAL
		     , A.LV7_ZOOM_RADIUS_VAL
		     , A.LV8_ZOOM_RADIUS_VAL
		     , A.LV9_ZOOM_RADIUS_VAL
		     , A.LV10_ZOOM_RADIUS_VAL
		     , A.LV11_ZOOM_RADIUS_VAL
		     , A.LV12_ZOOM_RADIUS_VAL
		     , A.LV13_ZOOM_RADIUS_VAL
		     , A.LV14_ZOOM_RADIUS_VAL
		     , A.LV15_ZOOM_RADIUS_VAL
		     , A.LV16_ZOOM_RADIUS_VAL
		     , A.LV17_ZOOM_RADIUS_VAL
		     , A.LV18_ZOOM_RADIUS_VAL
		     , A.LV19_ZOOM_RADIUS_VAL
		     , A.LV20_ZOOM_RADIUS_VAL
		     , A.LV21_ZOOM_RADIUS_VAL
		  FROM DSP_WTB_SETT_M A
		  LEFT OUTER JOIN COM_COMMON_CODE_D B
		    ON A.PURCH_ONLINE_TYPE_CODE = B.COMMON_CODE_VAL
		   AND B.COMMON_CODE = 'WTB_ONLINE'
		  LEFT OUTER JOIN COM_COMMON_CODE_D C
		    ON A.PURCH_OFFLINE_TYPE_CODE = C.COMMON_CODE_VAL
		   AND C.COMMON_CODE = 'WTB_OFFLINE'
		  LEFT OUTER JOIN COM_SYS_CONF_D D
		    ON A.SITE_CODE = D.SITE_CODE
		   AND D.CONFIG_CODE = 'wtb_online_retailer_country_use_flag'
		   AND D.USE_FLAG = 'Y'
		 WHERE A.SITE_CODE = #{siteCode}
	</select>

	<select id="selectWtbDistributorList" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbDistributorRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbDistributorResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.selectWtbDistributorList */
		       E.*
		     , (SELECT GROUP_CONCAT(DISTINCT A.CATEGORY_NM ORDER BY A.CATEGORY_NM SEPARATOR ',')
		          FROM DSP_DISPLAY_CATEGORY_M A
		          JOIN DSP_DCATEGORY_PRODUCT_R B
		            ON (A.CATEGORY_CODE = B.LV3_CATEGORY_CODE AND A.SITE_CODE = B.SITE_CODE)
		         INNER JOIN DSP_DISPLAY_CATEGORY_M C
		            ON A.HIGH_LV_CATEGORY_CODE = C.CATEGORY_CODE 
		           AND A.SITE_CODE = C.SITE_CODE
		           AND A.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		           AND A.USE_FLAG = C.USE_FLAG
		         WHERE A.SITE_CODE = #{siteCode}
		           AND A.BIZ_TYPE_CODE = 'B2C'
		           AND A.CATEGORY_LV_NO = IF(#{categoryStandardFlag} = 'Y', '3', '2')
		           AND A.USE_FLAG = 'Y' 
		<if test='distributorCategoryUse == "Y"'>
		           AND B.USE_FLAG = 'Y'
		</if>
		           AND B.DISTRIBUTOR_ID = E.DISTRIBUTOR_ID) CATEGORY
		     , (SELECT GROUP_CONCAT(DISTINCT C.CATEGORY_NM ORDER BY A.CATEGORY_NM SEPARATOR ',')
		          FROM DSP_DISPLAY_CATEGORY_M A
		         INNER JOIN DSP_DCATEGORY_PRODUCT_R B
		            ON A.CATEGORY_CODE = B.LV3_CATEGORY_CODE
		           AND A.SITE_CODE = B.SITE_CODE
		         INNER JOIN DSP_DISPLAY_CATEGORY_M C
		            ON A.HIGH_LV_CATEGORY_CODE = C.CATEGORY_CODE 
		           AND A.SITE_CODE = C.SITE_CODE
		           AND A.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		           AND A.USE_FLAG = C.USE_FLAG
		         WHERE A.SITE_CODE = #{siteCode}
		           AND A.BIZ_TYPE_CODE = 'B2C'
		           AND A.CATEGORY_LV_NO = IF(#{categoryStandardFlag} = 'Y', '3', '2')
		           AND A.USE_FLAG = 'Y'
		<if test='distributorCategoryUse == "Y"'>
		           AND B.USE_FLAG = 'Y'
		</if> 
		           AND B.DISTRIBUTOR_ID = E.DISTRIBUTOR_ID) SUPER_CATEGORY
		   FROM
		      ( SELECT D.*
		<if test="wtbCateId == null or wtbCateId == ''">
		              , ROW_NUMBER() OVER(PARTITION BY D.DISTRIBUTOR_NM, D.DISTANCE ORDER BY D.DISTRIBUTOR_NM) AS RN
		</if>
		          FROM
		              ( SELECT DISTINCT A.DISTRIBUTOR_LOGO_URL
		                     , A.DISTRIBUTOR_ID
		                     , A.DISTRIBUTOR_NM
		                     , A.DISTRIBUTOR_TYPE_CODE
		                     , A.SITE_CODE
		                     , IFNULL(C.POSIT_LTD_VAL, A.LTD_VAL) AS WTB_LATITUDE_VALUE
		                     , IFNULL(C.POSIT_LNG_VAL, A.LNG_VAL) AS WTB_LONGITUDE_VALUE
		<choose>
		    <when test="radiusType != 'KM'">
		                     , ROUND(3958.8 * ACOS(COS(RADIANS(#{lat})) * COS(RADIANS(IFNULL(C.POSIT_LTD_VAL, A.LTD_VAL))) * COS(RADIANS(IFNULL(C.POSIT_LNG_VAL, A.LNG_VAL)) - RADIANS(#{lng})) + SIN(RADIANS(#{lat})) * SIN(RADIANS(IFNULL(C.POSIT_LTD_VAL, A.LTD_VAL)))), 1) AS DISTANCE
		    </when>
		    <otherwise>
		                     , ROUND(6371 * ACOS(COS(RADIANS(#{lat})) * COS(RADIANS(IFNULL(C.POSIT_LTD_VAL, A.LTD_VAL))) * COS(RADIANS(IFNULL(C.POSIT_LNG_VAL, A.LNG_VAL)) - RADIANS(#{lng})) + SIN(RADIANS(#{lat})) * SIN(RADIANS(IFNULL(C.POSIT_LTD_VAL, A.LTD_VAL)))), 1) AS DISTANCE
		    </otherwise>
		</choose>
		                     , CONCAT_WS(' ', A.ADDRLINE_CNTS1, A.ADDRLINE_CNTS2, A.ADDRLINE_CNTS3, A.ADDRLINE_CNTS4) AS ADDRESS
		                     , A.PIN_NO
		                     , A.CONTACT_LOCAL_NO
		                     , CONCAT_WS(',', A.CITY_NM , A.CITY_STATENM , A.COUNTRY_NM) AS REGION
		                     , A.MAJOR_BRANCH_NM
		                     , CASE A.BRAND_SHOP_FLAG
		                            WHEN 'Y' THEN 'true'
		                            ELSE 'false'
		                        END BRAND
		<choose>
		    <when test="siteCode == 'IN'">
		                     , IFNULL(CASE WHEN A.CONTACT_NO1 = '' THEN A.CONTACT_NO2 ELSE A.CONTACT_NO1 END, A.CELLP_NO1) AS WTB_PHONE_NO
		                     , A.SITE_URL AS WTB_URL
		                     , CONCAT_WS(' ~ ', A.OPENING_TIME, A.CLOSING_TIME) AS WTB_TIME
		    </when>
		    <otherwise>
		                     , A.CELLP_NO1 AS WTB_PHONE_NO
		                     , CASE INSTR(IF(TRIM(A.SITE_URL) = '', NULL, A.SITE_URL), 'http')
		                            WHEN 1 THEN A.SITE_URL
		                            WHEN 0 THEN CONCAT_WS('', 'http://', A.SITE_URL)
		                            ELSE A.SITE_URL
		                        END WTB_URL
		                     , A.OPENING_TIME AS WTB_TIME
		    </otherwise>
		</choose>
		                     , A.SITE_URL
		                     , C.POSIT_ID
		                     , A.PRIORITY_SEQ
		                     , A.BRANDSHOP_MANAGER_PNM
		                     , A.PSN_PNM
		                     , '' AS CORPORATION_CODE
		                     , '' AS DIRECTION
		<choose>
		    <when test="brandshopTabFlag == 'true' and (wtbCateId == null or wtbCateId == '')">
		                     , ' ' AS LV3_CATEGORY_CODE
		    </when>
		    <otherwise>
		                     , B.LV3_CATEGORY_CODE
		    </otherwise>
		</choose>
		                     , A.CITY_NM
		                     , A.CITY_STATENM
		                     , A.COUNTRY_NM
		                     , A.ADDRLINE_CNTS1
		                     , A.ADDRLINE_CNTS2
		                     , A.ADDRLINE_CNTS3
		                     , A.ADDRLINE_CNTS4
		                     , A.POSTAL_NO
		                     , A.CELLP_NO1
		                     , A.CELLP_NO2
		                     , A.CONTACT_NO1
		                     , A.CONTACT_NO2
		                     , A.EMAIL_ADDR
		                     , A.CONTACT_LOCAL_NO || A.PIN_NO AS PHONE_NO
		                     , A.FAX_NO
		                     , A.OPENING_TIME
		                     , A.CLOSING_TIME
		                     , A.PERD_DAYOFF_NM
		                     , A.WEEKLY_OPEN_TIME_SETT_FLAG
		                     , A.SAT_OPEN_FLAG
		                     , A.SUN_OPEN_FLAG
		                     , A.HOLIDAY_OPEN_FLAG
		                     , CASE
		                            WHEN A.DISTRIBUTOR_NM LIKE 'LG BRAND STORE%'
		                            THEN 1
		                            ELSE 2
		                        END AS WTB_NAME_ORDER
		                     , CASE
		                            WHEN IFNULL(A.LTD_VAL, 0) = 0 AND IFNULL(A.LNG_VAL, 0) = 0 THEN 'AC003'
		                            WHEN IFNULL(A.ADDRLINE_CNTS1, '') = '' THEN 'AC002'
		                            WHEN IFNULL(C.POSIT_NM, '') = ''
		                            THEN CASE
		                                      WHEN IFNULL(C.POSIT_ID, '') = '' THEN 'AC004'
		                                      ELSE 'AC005'
		                                  END
		                            ELSE 'AC001'
		                        END AS SRCH_ADDRESS_CHECKED
		                     , F.PDP_ID
		                     , F.USER_FRNDY_PRODUCT_NM
		                     , CONCAT('/content/dam/channel/wcms', F.SML_IMG_URL) AS THUMBNAIL
		                  FROM DSP_DISTRIBUTOR_M A
		<choose>
		    <when test="brandshopTabFlag == 'true'">
		                  LEFT OUTER JOIN DSP_DCATEGORY_PRODUCT_R B
		    </when>
		    <otherwise>
		                 INNER JOIN DSP_DCATEGORY_PRODUCT_R B
		    </otherwise>
		</choose>
		                    ON A.SITE_CODE = B.SITE_CODE
		                   AND A.DISTRIBUTOR_ID = B.DISTRIBUTOR_ID
		                   AND B.USE_FLAG = 'Y'
		<if test='signatureWTBUseFlag == "Y" and (signatureWTBUseFlag != null and signatureWTBUseFlag != "")'>
		    <choose>
		        <when test="siteCode == 'JP' or siteCode == 'IN'">
		                   AND B.PDP_ID = #{pdpId}
		        </when>
		        <otherwise>
		                   AND B.SIGNATURE_FLAG = 'Y'
		        </otherwise>
		    </choose>
		</if>
		<if test="wtbCateId != null and wtbCateId != ''">
		    <choose>
		        <when test="siteCode == 'IN'">
		                   AND B.LV3_CATEGORY_CODE = #{wtbCateId}
		                   AND B.PDP_ID IS NULL OR B.PDP_ID = #{pdpId}
		        </when>
		        <otherwise>
		                   AND ((B.LV3_CATEGORY_CODE IN (#{wtbCateId}, (SELECT HIGH_LV_CATEGORY_CODE
		                                                                  FROM DSP_DISPLAY_CATEGORY_M
		                                                                 WHERE CATEGORY_CODE = #{wtbCateId}
		                                                                   AND SITE_CODE = #{siteCode}
		                                                                   AND CATEGORY_LV_NO = IF(#{categoryStandardFlag} = 'Y', '3', '2')))
		                         AND B.PDP_ID IS NULL) OR B.PDP_ID = #{pdpId})
		        </otherwise>
		    </choose>
		</if>
		<if test="categoryCode != null and categoryCode != ''">
		                   AND B.LV3_CATEGORY_CODE = #{categoryCode}
		</if>
		<if test="categoryIdList != null and categoryIdList.size > 0">
		    <foreach collection="categoryIdList" item="categoryIdList" open="AND B.LV3_CATEGORY_CODE IN (" separator="," close=")">
		                       #{categoryIdList}
		    </foreach>
		</if>
		                  LEFT OUTER JOIN (SELECT DPM.SITE_CODE
		                                        , DPM.PDP_ID
		                                        , DPM.USER_FRNDY_PRODUCT_NM
		                                        , DPD.SML_IMG_URL
		                                     FROM DSP_PDP_M DPM
		                                     JOIN DSP_PDP_D DPD
		                                       ON DPM.SITE_CODE = DPD.SITE_CODE
		                                      AND DPM.BIZ_TYPE_CODE = DPD.BIZ_TYPE_CODE
		                                      AND DPM.PDP_ID = DPD.PDP_ID) F
		                    ON A.SITE_CODE = F.SITE_CODE
		                   AND F.PDP_ID = #{pdpId}
		                  LEFT OUTER JOIN DSP_DISTRIBUTOR_GOOGLEMAP_D C
		                    ON A.SITE_CODE = C.SITE_CODE
		                   AND A.DISTRIBUTOR_ID = C.DISTRIBUTOR_ID
		                   AND C.USE_FLAG = 'Y'
		                   AND C.ACTIVE_FLAG = 'Y'
		<if test="siteCode != 'AE' and siteCode != 'AE_AR' and siteCode != 'RU'">
		                   AND (
		                         (IFNULL(C.POSIT_LTD_VAL, A.LTD_VAL) BETWEEN #{minLat} AND #{maxLat} AND IFNULL(C.POSIT_LNG_VAL, A.LNG_VAL) BETWEEN #{minLng} AND #{maxLng})
		                          OR
		                         (IFNULL(C.POSIT_LTD_VAL, A.LTD_VAL) BETWEEN #{maxLat} AND #{minLat} AND IFNULL(C.POSIT_LNG_VAL, A.LNG_VAL) BETWEEN #{maxLng} AND #{minLng})
		                       )
		</if>
		                 WHERE A.USE_FLAG = 'Y'
		                   AND A.SITE_CODE = #{siteCode}
		                   AND A.DISTRIBUTOR_TYPE_CODE <![CDATA[<>]]> 'ONLINE'
		<if test="brandshopTabFlag == 'true'">
		                   AND A.BRAND_SHOP_FLAG = 'Y'
		</if>
		              )D
		        WHERE D.SRCH_ADDRESS_CHECKED IN ('AC001', 'AC005')
		<choose>
		    <when test="siteCode == 'FR'">
		        ORDER BY D.PRIORITY_SEQ, D.DISTANCE, D.DISTRIBUTOR_ID
		    </when>
		    <when test="siteCode == 'IR'">
		        ORDER BY D.DISTANCE
		    </when>
		    <when test='brandShopImprovingFlag == "Y"'>
		        ORDER BY D.BRAND DESC, D.DISTANCE ASC
		    </when>
		    <when test="siteCode != 'FR' and brandshopTabFlag != 'true'">
		        ORDER BY D.DISTANCE, D.DISTRIBUTOR_ID
		    </when>
		    <when test="siteCode != 'FR' and brandshopTabFlag == 'true'">
		        ORDER BY D.DISTANCE, D.DISTRIBUTOR_ID, D.LV3_CATEGORY_CODE
		    </when>
		    <otherwise>
		        ORDER BY D.DISTRIBUTOR_ID, D.LV3_CATEGORY_CODE
		    </otherwise>
		</choose>
		      ) E
		 WHERE 1=1
		<choose>
		    <when test="siteCode == 'AE' or siteCode == 'AE_AR'">
		   AND E.DISTANCE <![CDATA[ <= ]]> 100000
		    </when>
		    <otherwise>
		        <if test="radius != null and radius != ''">
		   AND E.DISTANCE <![CDATA[ <= ]]> #{radius}
		        </if>
		    </otherwise>
		</choose>
		<if test="wtbCateId == null or wtbCateId == ''">
		   AND E.RN = 1
		</if>
		<if test="orderbyDistanceLocaleYn == 'Y'.toString()">
		 ORDER BY E.DISTANCE
		</if>
	</select>

	<select id="selectWtbStandardCategoryId" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCategoryIdRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCategoryIdResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.selectWtbCategoryId */
			   C.lv2_category_code AS superCategoryId
			  ,C.lv3_category_code AS categoryId
			  ,c.lv4_category_code AS subCategoryId
			  ,d.high_lv_category_code  AS b2cCategoryId
		FROM dsp_pdp_m A
		INNER JOIN DSP_PDP_D b
				ON A.site_code = B.site_code
				AND A.biz_type_code = B.biz_type_code
				AND A.use_flag = B.use_flag
		INNER JOIN dsp_pdp_category_r c
				ON A.pdp_id = C.pdp_id
				AND A.biz_type_code = C.biz_type_code
				AND A.site_code = C.site_code
				AND A.use_flag = C.use_flag
				AND C.default_map_flag = 'Y'
		INNER JOIN dsp_display_category_m d
				ON C.lv1_category_code = D.category_code
				AND C.biz_type_code = D.biz_type_code
				AND C.site_code = D.site_code
				AND C.use_flag = D.use_flag
		WHERE A.use_flag = 'Y'
		AND A.site_code = #{siteCode}
		AND A.biz_type_code = #{bizTypeCode}
		AND A.pdp_id = #{pdpId}
		limit 1
	</select>

	<select id="selectWtbNonStandardCategoryId" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCategoryIdRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCategoryIdResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.selectWtbCategoryId */
			   C.lv1_category_id AS superCategoryId
			  ,C.lv2_category_id AS categoryId
			  ,c.lv3_category_id AS subCategoryId
			  ,d.high_lv_category_code  AS b2cCategoryId
		FROM dsp_pdp_m A
		INNER JOIN DSP_PDP_D b
				ON A.site_code = B.site_code
				AND A.biz_type_code = B.biz_type_code
				AND A.use_flag = B.use_flag
		INNER JOIN dsp_old_pdp_category_r c
				ON A.pdp_id = C.pdp_id
				AND A.biz_type_code = C.biz_type_code
				AND A.site_code = C.site_code
				AND A.use_flag = C.use_flag
				AND C.default_map_flag = 'Y'
		INNER JOIN dsp_display_category_m d
				ON C.lv1_category_id = D.category_code
				AND C.biz_type_code = D.biz_type_code
				AND C.site_code = D.site_code
				AND C.use_flag = D.use_flag
		WHERE A.use_flag = 'Y'
		AND A.site_code = #{siteCode}
		AND A.biz_type_code = #{bizTypeCode}
		AND A.pdp_id = #{pdpId}
		limit 1
	</select>

	<select id="selectWtbCity" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCityRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCityResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.selectWtbCity */
		       DISTINCT
		       TRIM(MDM.CITY_NM) AS code
		      ,TRIM(MDM.CITY_NM) AS value
		  FROM DSP_DISTRIBUTOR_M MDM
		 INNER JOIN DSP_DCATEGORY_PRODUCT_R MDC
		    ON (MDM.SITE_CODE = MDC.SITE_CODE
		    AND MDM.DISTRIBUTOR_ID = MDC.DISTRIBUTOR_ID
		    AND MDC.LV3_CATEGORY_CODE IN (#{wtbSuperCateId}, #{wtbCateId}, #{wtbSubCateId})
		    AND MDC.USE_FLAG = 'Y')
		  WHERE MDM.USE_FLAG = 'Y'
		    AND MDM.SITE_CODE = #{siteCode}
		    AND MDM.CITYSTATE_CODE = #{state}
    		<if test="city != '' and city != null">
   		    AND MDM.CITY_NM = #{city}
    		</if>
	   ORDER BY code
	</select>

    <select id="selectWtbProductPageCityData" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbComboRequestVO"
        resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbComboResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbCombo */
               DISTINCT
               A.CITY_NM AS CODE
             , A.CITY_NM AS VALUE
          FROM DSP_DISTRIBUTOR_M A
         INNER JOIN DSP_DCATEGORY_PRODUCT_R B
            ON A.SITE_CODE = B.SITE_CODE
           AND A.DISTRIBUTOR_ID = B.DISTRIBUTOR_ID
        <if test="wtbCateId != null and wtbCateId != ''">
           AND B.LV3_CATEGORY_CODE = #{wtbCateId}
        </if>
         WHERE A.SITE_CODE = #{siteCode}
           AND A.USE_FLAG = 'Y'
         ORDER BY CODE
    </select>

    <select id="selectWtbCityList" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbComboRequestVO"
        resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbComboResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbCombo */
               D.CODE
             , D.VALUE
          FROM (
                 SELECT C.STATE_CODE AS CODE
                      , C.STATE_CODE AS VALUE
                      , CASE C.STATE_CODE
                             WHEN '北京市' THEN 1
                             WHEN '上海市' THEN 2
                             WHEN '重庆市' THEN 3
                             WHEN '天津市' THEN 4
                             ELSE ROW_NUMBER() OVER() + 4
                         END NUM
                   FROM (
                          SELECT DISTINCT
                                 A.CITYSTATE_CODE AS STATE_CODE
                            FROM DSP_DISTRIBUTOR_M A
                           WHERE A.SITE_CODE = #{siteCode}
                             AND A.CITYSTATE_CODE IS NOT NULL
                             AND A.USE_FLAG = 'Y'
        <if test="brand == true and siteCode == 'CN'">
                           UNION
                          SELECT DISTINCT
                                 CASE B.ASC_PROVINCE_NM
                                      WHEN '广西' THEN '广西省'
                                      ELSE ASC_PROVINCE_NM
                                  END STATE_CODE
                            FROM SVD_ASC_M B
                           WHERE B.USE_FLAG = 'Y'
                             AND B.COUNTRY_CODE = #{siteCode}
        </if>
                        ) C
                  WHERE C.STATE_CODE IS NOT NULL
                    AND C.STATE_CODE != ''
               ) D
         ORDER BY D.NUM ASC
    </select>

    <select id="selectWtbStateData" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbComboRequestVO"
        resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbComboResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbCombo */
               DISTINCT
               TRIM(A.CITYSTATE_CODE) AS CODE
             , TRIM(A.CITYSTATE_CODE) AS VALUE
          FROM DSP_DISTRIBUTOR_M A
         INNER JOIN DSP_DCATEGORY_PRODUCT_R B
            ON A.SITE_CODE = B.SITE_CODE
           AND A.DISTRIBUTOR_ID = B.DISTRIBUTOR_ID
        <if test="wtbCategories != null and wtbCategories.size != 0">
            <foreach collection="wtbCategories" item="c" index="idx" open="AND B.LV3_CATEGORY_CODE IN (" separator="," close=")">
               #{c}
            </foreach>
        </if>
           AND B.USE_FLAG = 'Y'
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
           AND A.CITYSTATE_CODE IS NOT NULL
           AND A.CITYSTATE_CODE != ''
         ORDER BY CODE
    </select>

    <select id="selectWtbTrStateData" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbComboRequestVO"
        resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbComboResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbCombo */
               DISTINCT
        <choose>
            <when test="selectedState != null and selectedState != ''">
               TRIM(CITY_NAME) AS CODE
             , TRIM(CITY_NAME) AS VALUE
            </when>
            <otherwise>
               TRIM(STATE_NAME) AS CODE
             , TRIM(STATE_NAME) AS VALUE
            </otherwise>
        </choose>
          FROM (
                 SELECT A.CITYSTATE_CODE AS STATE_CODE
                      , A.CITY_STATENM AS STATE_NAME
                      , A.CITY_NM AS CITY_NAME
                      , CASE WHEN IFNULL(A.LTD_VAL, 0)=0 AND IFNULL(A.LNG_VAL, 0)=0 THEN 'AC003'
                             WHEN IFNULL(A.ADDRLINE_CNTS1, '')='' THEN 'AC002'
                             WHEN IFNULL(C.POSIT_NM, '')=''
                                   THEN CASE WHEN IFNULL(C.posit_id, '')='' THEN 'AC004'
                                             ELSE 'AC005' END
                             ELSE 'AC001'
                         END AS SRCH_ADDRESS_CHECKED
                   FROM DSP_DISTRIBUTOR_M A
                  INNER JOIN DSP_DCATEGORY_PRODUCT_R B
                     ON A.SITE_CODE = B.SITE_CODE
                    AND A.DISTRIBUTOR_ID = B.DISTRIBUTOR_ID
        <if test="wtbCategories != null and wtbCategories.size != 0">
            <foreach collection="wtbCategories" item="c" index="idx" open="AND B.LV3_CATEGORY_CODE IN (" separator="," close=")">
                        #{c}
            </foreach>
        </if>
                    AND B.USE_FLAG = 'Y'
                   LEFT OUTER JOIN DSP_DISTRIBUTOR_GOOGLEMAP_D C
                     ON C.ACTIVE_FLAG = 'Y'
                    AND C.USE_FLAG = 'Y'
                    AND A.SITE_CODE = C.SITE_CODE
                    AND A.DISTRIBUTOR_ID = C.DISTRIBUTOR_ID
                  WHERE A.USE_FLAG = 'Y'
                    AND A.SITE_CODE = #{siteCode}
                    AND A.CITY_STATENM IS NOT NULL
                    AND A.CITY_STATENM != ''
        <if test="selectedState != null and selectedState != ''">
                    AND A.city_statenm = #{selectedState}
        </if>
               ) D
         WHERE D.SRCH_ADDRESS_CHECKED IN ('AC001', 'AC005')
         ORDER BY CODE
    </select>

	<select id="selectWtbCnCategoryList" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCategoryListRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCategoryListResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbCategoryList */
		       C.CATEGORY_CODE AS CODE
		     , C.CATEGORY_NM AS VALUE
		  FROM DSP_DISPLAY_CATEGORY_M C
		 WHERE C.SITE_CODE = #{siteCode}
		   AND C.USE_FLAG = 'Y'
		<if test="bizTypeCode != null and bizTypeCode != ''">
		   AND C.BIZ_TYPE_CODE = #{bizTypeCode}
		</if>
		<choose>
		    <when test="type == 'category'">
		   AND C.CATEGORY_LV_NO = #{categoryLvNo}
		   AND C.HIGH_LV_CATEGORY_CODE = #{superCategory}
		    </when>
		    <otherwise>
		   AND C.CATEGORY_LV_NO = #{categoryLvNo}
		    </otherwise>
		</choose>
		<if test="flagNcategory != null and flagNcategory.size != 0">
		   AND C.CATEGORY_CODE NOT IN
		    <foreach collection="flagNcategory" item="f" index="idx" open="(" separator="," close=")">
		       #{f}
		    </foreach>
		</if>
	</select>

	<select id="selectWtbTrCategoryList" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCategoryListRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCategoryListResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbCategoryList */
		       DISTINCT
		       A.CATEGORY_CODE AS CODE
		     , A.CATEGORY_NM AS VALUE
		  FROM DSP_DISPLAY_CATEGORY_M A
		 INNER JOIN DSP_DISPLAY_CATEGORY_M B
		    ON A.HIGH_LV_CATEGORY_CODE = B.CATEGORY_CODE
		   AND A.SITE_CODE = B.SITE_CODE
		   AND A.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
		   AND A.USE_FLAG = B.USE_FLAG
		 INNER JOIN DSP_DCATEGORY_PRODUCT_R C
		    ON C.LV3_CATEGORY_CODE = A.CATEGORY_CODE
		   AND C.SITE_CODE = A.SITE_CODE
		   AND C.USE_FLAG = 'Y'
		 INNER JOIN DSP_DISTRIBUTOR_M D
		    ON D.SITE_CODE = C.SITE_CODE
		   AND D.DISTRIBUTOR_ID = C.DISTRIBUTOR_ID
		   AND D.USE_FLAG = 'Y'
		 WHERE A.SITE_CODE = #{siteCode}
		   AND A.USE_FLAG = 'Y'
		   AND A.CATEGORY_LV_NO = #{categoryLvNo}
		<if test="bizTypeCode != null and bizTypeCode != ''">
		   AND A.BIZ_TYPE_CODE = #{bizTypeCode}
		</if>
		<if test="flagNcategory != null and flagNcategory.size != 0">
		   AND A.CATEGORY_CODE NOT IN
		    <foreach collection="flagNcategory" item="f" index="idx" open="(" separator="," close=")">
		       #{f}
		    </foreach>
		</if>
		ORDER BY B.DSP_SEQ , A.DSP_SEQ
	</select>

	<select id="selectWtbOfflineStoreList" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbOfflineStoreRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbOfflineStoreResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbOfflineStoreList */
		       DISTINCT
		       A.PDP_ID
		     , A.LGCOM_SKU_ID
		     , C.LV3_CATEGORY_CODE
		     , D.DISTRIBUTOR_ID
		     , D.DISTRIBUTOR_NM
		  FROM DSP_PDP_M A
		  JOIN DSP_PDP_D B
		    ON A.SITE_CODE = B.SITE_CODE
		   AND A.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
		   AND A.PDP_ID = B.PDP_ID
		   AND B.USE_FLAG = 'Y'
		   AND B.PRODUCT_STATE_CODE = 'ACTIVE'
		  JOIN DSP_DCATEGORY_PRODUCT_R C
		    ON A.SITE_CODE = C.SITE_CODE
		   AND A.CATEGORY_CODE = C.LV3_CATEGORY_CODE
		   AND C.USE_FLAG = 'Y'
		  JOIN DSP_DISTRIBUTOR_M D
		    ON C.SITE_CODE = D.SITE_CODE
		   AND C.DISTRIBUTOR_ID = D.DISTRIBUTOR_ID
		   AND D.USE_FLAG = 'Y'
		 WHERE A.SITE_CODE = #{siteCode}
		<if test="bizTypeCode != null and bizTypeCode != ''">
		   AND A.BIZ_TYPE_CODE = #{bizTypeCode}
		</if>
		   AND A.PDP_ID = #{pdpId}
		   AND A.USE_FLAG = 'Y'
	</select>

	<select id="selectTrWtbStateList" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbStateRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbStateResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbOfflineStoreList */
		       DISTINCT
		<choose>
		    <when test="selectedState != null and selectedState != ''">
		       TRIM(CITY_NAME)    AS  CODE
		     , TRIM(CITY_NAME)  AS  VALUE
		    </when>
		    <otherwise>
		       TRIM(STATE_NAME)    AS  CODE
		     , TRIM(STATE_NAME)  AS  VALUE
		    </otherwise>
		</choose>
		  FROM (
		         SELECT A.CITYSTATE_CODE AS STATE_CODE
		              , A.CITY_STATENM AS STATE_NAME
		              , A.CITY_NM AS CITY_NAME
		              , CASE WHEN IFNULL(A.LTD_VAL, 0) = 0 AND IFNULL(A.LNG_VAL, 0) = 0 THEN 'AC003'
		                     WHEN IFNULL(A.ADDRLINE_CNTS1, '') = '' THEN 'AC002'
		                     WHEN IFNULL(C.POSIT_NM, '') = ''
		                          THEN CASE WHEN IFNULL(C.posit_id, '') = '' THEN 'AC004'
		                                    ELSE 'AC005' END
		                     ELSE 'AC001'
		                 END AS SRCH_ADDRESS_CHECKED
		           FROM DSP_DISTRIBUTOR_M A
		          INNER JOIN DSP_DCATEGORY_PRODUCT_R B
		             ON A.SITE_CODE = B.SITE_CODE
		            AND A.DISTRIBUTOR_ID = B.DISTRIBUTOR_ID
		<if test="wtbCategories != null and wtbCategories.size != 0">
		    <foreach collection="wtbCategories" item="c" index="idx" open="AND B.LV3_CATEGORY_CODE IN (" separator="," close=")">
		                #{c}
		    </foreach>
		</if>
		            AND B.USE_FLAG = 'Y'
		           LEFT OUTER JOIN DSP_DISTRIBUTOR_GOOGLEMAP_D C
		             ON C.ACTIVE_FLAG = 'Y'
		            AND C.USE_FLAG = 'Y'
		            AND A.SITE_CODE = C.SITE_CODE
		            AND A.DISTRIBUTOR_ID = C.DISTRIBUTOR_ID
		          WHERE A.USE_FLAG = 'Y'
		            AND A.SITE_CODE = #{siteCode}
		            AND A.CITY_STATENM IS NOT NULL
		<if test="selectedState != null and selectedState != ''">
 		            AND A.CITY_STATENM = #{selectedState}
		</if>
		       ) D
		 WHERE D.SRCH_ADDRESS_CHECKED IN ('AC001', 'AC005')
		 ORDER BY 1
	</select>

	<select id="selectWtbCnDistributorList" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCnDistributorRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbCnDistributorResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbCnDistributorList */
		       E.*
		     , COUNT(*) OVER() AS TOTAL
		  FROM ( SELECT A.DISTRIBUTOR_ID
		              , A.DISTRIBUTOR_TYPE_CODE
		              , A.DISTRIBUTOR_NM AS WTB_NAME
		              , CONCAT_WS(' ', A.CITY_STATENM, A.CITY_NM, A.ADDRLINE_CNTS4, A.ADDRLINE_CNTS1) AS WTB_ADDRESS
		              , A.CITYSTATE_CODE AS STATE_CODE
		              , A.CITY_STATENM AS STATE_NAME
		<choose>
		    <when test="region == 'all' and city == 'all'">
		              , CASE A.CITYSTATE_CODE
		                     WHEN '北京市' THEN 1
		                     WHEN '上海市' THEN 2
		                     WHEN '重庆市' THEN 3
		                     WHEN '天津市' THEN 4
		                     ELSE ROW_NUMBER() OVER() + 4
		                 END SORT
		    </when>
		    <otherwise>
		              , '' AS SORT
		    </otherwise>
		</choose>
		              , A.CITY_NM AS CITY_NAME
		              , A.ADDRLINE_CNTS1
		              , A.ADDRLINE_CNTS2
		              , A.ADDRLINE_CNTS3
		              , A.ADDRLINE_CNTS4
		              , A.POSTAL_NO
		              , A.CELLP_NO1 AS WTB_PHONE_NO
		              , A.CELLP_NO2
		              , A.FAX_NO
		              , A.SITE_URL
		              , A.EMAIL_ADDR
		              , IFNULL(B.POSIT_LTD_VAL, A.LTD_VAL) AS WTB_LATITUDE_VALUE
		              , IFNULL(B.POSIT_LNG_VAL, A.LNG_VAL) AS WTB_LONGITUDE_VALUE
		              , IF(A.BRAND_SHOP_FLAG = 'Y', '1', '2') AS BRAND_SHOP_FLAG
		              , B.POSIT_ID
		              , CASE WHEN IFNULL(A.LTD_VAL, 0) = 0 AND IFNULL(A.LNG_VAL, 0) = 0 THEN 'AC003'
		                     WHEN IFNULL(A.ADDRLINE_CNTS1, '') = '' THEN 'AC002'
		                     WHEN IFNULL(B.POSIT_NM, '') = ''
		                          THEN CASE WHEN IFNULL(B.POSIT_ID, '') = '' THEN 'AC004'
		                               ELSE 'AC005'
		                           END
		                     ELSE 'AC001'
		                 END AS SRCH_ADDRESS_CHECKED
		           FROM DSP_DISTRIBUTOR_M A
		           LEFT JOIN DSP_DISTRIBUTOR_GOOGLEMAP_D B
		             ON B.ACTIVE_FLAG = 'Y'
		            AND B.USE_FLAG = 'Y'
		            AND A.SITE_CODE = B.SITE_CODE
		            AND A.DISTRIBUTOR_ID = B.DISTRIBUTOR_ID
		          WHERE A.USE_FLAG = 'Y'
		            AND A.SITE_CODE = #{siteCode}
		<if test="brand == true">
		            AND A.BRAND_SHOP_FLAG = 'Y'
		</if>
		<if test="superCategory == 'CT20108021'">
		            AND 1=2 /* Mobile */
		</if>
		<if test="region != null and region != '' and region != 'all'">
		            AND TRIM(A.CITYSTATE_CODE) = #{region}
		</if>
		<if test="city != null and city != '' and city != 'all'">
		            AND TRIM(A.CITY_NM) = #{city}
		</if>
		            AND A.DISTRIBUTOR_ID IN ( SELECT DISTINCT C.DISTRIBUTOR_ID
		                                        FROM DSP_DCATEGORY_PRODUCT_R C
		<if test="category == 'all' and superCategory != null and superCategory != '' and superCategory != 'all'">
		                                       INNER JOIN DSP_DISPLAY_CATEGORY_M D
		                                          ON D.USE_FLAG = 'Y'
		                                         AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
		                                         AND D.SITE_CODE = C.SITE_CODE
		                                         AND D.HIGH_LV_CATEGORY_CODE = #{superCategory}
		</if>
		                                       WHERE C.SITE_CODE = #{siteCode}
		                                         AND C.USE_FLAG = 'Y'
		<if test="signatureWTBUseFlag == 'Y'.toString()">
		                                         AND C.PDP_ID = #{pdpId}
		</if>
		<if test="category != null and category != '' and category != 'all'">
		    <choose>
		        <when test="categoryInfo != null and categoryInfo != ''">
		                                         AND C.PDP_ID = #{pdpId}
		                                         AND C.LV3_CATEGORY_CODE = #{categoryInfo}
		        </when>
		        <otherwise>
		                                         AND C.LV3_CATEGORY_CODE = #{category}
		        </otherwise>
		    </choose>
		</if>
		                                    )
		       )E
		 WHERE SRCH_ADDRESS_CHECKED IN ('AC001', 'AC005') /* srchAddressChecked : Checked, Wrong store name */
		 ORDER BY E.SORT, E.BRAND_SHOP_FLAG, E.DISTRIBUTOR_ID
		 LIMIT #{startNo}, #{pageCount}
	</select>

	<select id="selectWtbStoreCategoryList" parameterType="com.lge.d2x.domain.whereToBuy.v1.model.WtbStoreCategoryRequestVO"
		resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbStoreCategoryResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbStoreCategoryList */
		       'category1' AS gubunVal
		     , CATEGORY_CODE AS categoryId
		<![CDATA[
		     , REGEXP_REPLACE(REGEXP_REPLACE(CATEGORY_NM,'<[^>]*>',''),'( ){2,}', ' ') AS categoryName
		]]>
		  FROM DSP_DISPLAY_CATEGORY_M
		 WHERE SITE_CODE = #{siteCode}
		   AND BIZ_TYPE_CODE = 'B2C'
		   AND CATEGORY_LV_NO = '2'
		   AND USE_FLAG = 'Y'
		 ORDER BY CATEGORY_CODE
	</select>

	<select id="selectWtbStoreFilterList" resultType="com.lge.d2x.domain.whereToBuy.v1.model.WtbStoreFilterResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.whereToBuy.v1.service.WtbService.getWtbStoreFilterList */
		       * 
		  FROM (SELECT 'Category' AS filterText
		             , 'category1' AS gubunVal
		          FROM DUAL) A
		 ORDER BY A.filterText
	</select>
</mapper>
