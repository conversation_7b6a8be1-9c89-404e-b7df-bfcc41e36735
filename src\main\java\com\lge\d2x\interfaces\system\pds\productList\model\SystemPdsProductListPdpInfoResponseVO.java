package com.lge.d2x.interfaces.system.pds.productList.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lge.d2x.domain.productList.v1.model.ProductListModelVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemPdsProductListPdpInfoResponseVO {
    @Schema(description = "제품페이지 아이디(구 모델 아이디)", example = "MD00017389")
    private String pdpId;

    @Schema(description = "닷컴 sku id", example = "E2250V-PN.AEK.EEUK.UK.C")
    private String lgcomSkuId;

    @Schema(description = "pim sku id", example = "E2250V-PN.AEK.EEUK.C")
    private String skuId;

    @Schema(description = "제품명", example = "E2250V")
    private String productNm;

    @Schema(description = "사용자친숙제품명", example = "LG LED LCD Monitor. E50 Series")
    private String userFrndyProductNm;

    @Schema(description = "제품유형코드", example = "G")
    private String pdpTypeCode;

    @Schema(description = "비즈니스 유형 코드", example = "B2C")
    private String bizTypeCode;

    @Schema(description = "Sales Model 코드", example = "E2250V-PN")
    private String salesModelCode;

    @Schema(description = "Sales Model Suffix 코드", example = "AEK")
    private String salesModelSuffixCode;

    @Schema(description = "msrp", example = "1199.98000")
    private BigDecimal msrpSalesPrice;

    @Schema(description = "obs 판매 여부", example = "Y")
    private String obsSellFlag;

    @Schema(description = "wtb 사용 여부", example = "Y")
    private String wtbUseFlag;

    @Schema(description = "wtb 외부 링크 사용 여부", example = "Y")
    private String wtbExternalLinkUseFlag;

    @Schema(description = "wtb 외부 링크 명")
    private String wtbExtlLinkNm;

    @Schema(description = "wtb 외부 링크 URL")
    private String wtbExtlLinkUrl;

    @Schema(description = "wtb 외부 링크 target _self 여부")
    private String wtbExtlLinkSelfScreenFlag;

    @Schema(description = "에너지 라벨 등급", example = "4")
    private String elabelGrdCode;

    @Schema(description = "에너지 라벨 카테고리 코드", example = "EL_CAT_02")
    private String elabelClsCode;

    @Schema(description = "에너지 라벨 카테고리 명", example = "EL")
    private String elDocTypeCode;

    @Schema(description = "두번째 에너지 라벨 등급", example = "1")
    private String secondElabelGrdCode;

    @Schema(description = "두번째 에너지 라벨 카테고리", example = "EL_CAT_08")
    private String secondElabelClsCode;

    @Schema(description = "두번째 에너지 라벨 카테고리 명", example = "EL")
    private String secondElDocTypeCode;

    @Schema(description = "wash tower 제품 여부", example = "Y")
    private String wtowerProductFlag;

    @Schema(description = "시그니처 제품 여부", example = "Y")
    private String signtProductFlag;

    @Schema(description = "소매점 가격 책정 여부", example = "Y")
    private String retailerPricingFlag;

    @Schema(description = "itb 사용 여부", example = "Y")
    private String itbUseFlag;

    @Schema(description = "SKU coverage 계산을 위한 예외모델 설정 값", example = "EOL")
    private String exclProductSettCode;

    @Schema(description = "제품 release 날짜", example = "2023-12-15")
    private String productRelesDd;

    @Schema(description = "제품 페이지 url", example = "/uk/laundry/styler/s3wf/")
    private String pdpUrl;

    @Schema(description = "제품 상태 코드", example = "ACTIVE")
    private String productStateCode;

    @Schema(description = "멀티샵 제품 페이지 url")
    private String mtsPdpUrl;

    @Schema(
            description = "small image url",
            example =
                    "/uk/images/speakers/xboom/xg8t/gallery/av-xboom-xg8t-basic-01.jpg/jcr:content/renditions/thum-165x165.jpeg")
    private String smlImgUrl;

    @Schema(
            description = "medium image url",
            example =
                    "/uk/images/speakers/xboom/xg8t/gallery/av-xboom-xg8t-basic-01.jpg/jcr:content/renditions/thum-350x350.jpeg")
    private String mdmImgUrl;

    @Schema(
            description = "large image url",
            example = "/uk/images/speakers/xboom/xg8t/gallery/av-xboom-xg8t-basic-01.jpg")
    private String bigImgUrl;

    @Schema(description = "이미지대체문구", example = "Front view of LG WOW Bracket BT5-2P")
    private String imgAltTextCnts;

    @Schema(description = "모델 등록 일시", example = "2024-07-18 17:25:16.000")
    private String creationDate;

    @Schema(description = "제품 테마 유형 코드", example = "Light")
    private String productThemeTypeCode;

    @Schema(description = "default 제품 태그", example = "New")
    private String defaultProductTagCode;

    @Schema(description = "제품 태그 사용자 유형 코드", example = "ALL")
    private String productTagUserTypeCode1;

    @Schema(description = "제품 태그 사용자 유형 코드", example = "ALL")
    private String productTagUserTypeCode2;

    @Schema(description = "제품 태그", example = "COUPON 100€")
    private String productTagCode1;

    @Schema(description = "제품 태그 노출 시작일자", example = "2024-08-18 00:00:00.000")
    private String productTagExpBeginDate1;

    @Schema(description = "제품 태그 노출 종료일자", example = "2024-08-18 00:00:00.000")
    private String productTagExpEndDate1;

    @Schema(description = "두번째 제품 태그", example = "CYBERWEEK")
    private String productTagCode2;

    @Schema(description = "두번째 제품 태그 노출 시작일자", example = "2024-08-18 00:00:00.000")
    private String productTagExpBeginDate2;

    @Schema(description = "두번째 제품 태그 노출 종료일자", example = "2024-08-18 00:00:00.000")
    private String productTagExpEndDate2;

    @Schema(description = "1레벨 표준화 카테고리 코드")
    private String lv1CategoryCode;

    @Schema(description = "2레벨 표준화 카테고리 코드")
    private String lv2CategoryCode;

    @Schema(description = "3레벨 표준화 카테고리 코드")
    private String lv3CategoryCode;

    @Schema(description = "4레벨 표준화 카테고리 코드")
    private String lv4CategoryCode;

    @Schema(description = "1레벨 카테고리 id", example = "CT00008386")
    private String lv1CategoryId;

    @Schema(description = "2레벨 카테고리 id", example = "CT00008387")
    private String lv2CategoryId;

    @Schema(description = "3레벨 카테고리 id", example = "CT00008390")
    private String lv3CategoryId;

    @Schema(description = "사이트 카테고리 명", example = "Monitors")
    private String siteCategoryNm;

    @Schema(description = "카테고리 명", example = "Monitors")
    private String categoryNm;

    @Schema(description = "수퍼카테고리 명", example = "Computing")
    private String superCategoryNm;

    @Schema(description = "PLP Highlight Model 여부", example = "Y")
    private String plpHighlightModelFlag;

    @Schema(description = "default sibling Model 여부", example = "Y")
    private String defaultSiblingModelFlag;

    @Schema(description = "Sibling Group Code")
    private String siblingGrpCode;

    @Schema(description = "Sibling Code")
    private String siblingCode;

    @Schema(description = "Sibling Group Name")
    private String siblingGrpNm;

    @Schema(description = "Sibling Local Value")
    private String siblingLocalVal;

    @Schema(description = "Sibling 유형 코드")
    private String siblingTypeCode;

    @Schema(description = "Sibling target code")
    private String siblingSbjTypeCode;

    @Schema(description = "Participant Count")
    private int pCount;

    @Schema(description = "Rating Percent")
    private int ratingPercent;

    @Schema(description = "Star Rating")
    private int sRating;

    @Schema(description = "Star Rating2")
    private BigDecimal sRating2;

    @JsonIgnore
    @Schema(description = "Star Rating3")
    private BigDecimal sRating3;

    @Schema(description = "Promotion Text")
    private String promotionText;

    @Schema(description = "Promotion Link URL")
    private String promotionLinkUrl;

    @Schema(description = "External Link Target")
    private String externalLinkTarget;

    @Schema(description = "LV1 Product Code")
    private String lv1ProductCode;

    @Schema(description = "LV1 Category Code Nm")
    private String lv1CategoryCodeNm;

    @Schema(description = "LV2 Category Code Nm")
    private String lv2CategoryCodeNm;

    @Schema(description = "LV3 Category Code Nm")
    private String lv3CategoryCodeNm;

    @Schema(description = "LV4 Category Code Nm")
    private String lv4CategoryCodeNm;

    @Schema(description = "에너지라벨 유형 코드")
    private String elTypeCode;

    @Schema(description = "두번째 에너지라벨 유형 코드")
    private String secondElTypeCode;

    @Schema(description = "두번째 PIS 문서 유형 코드")
    private String secondPisDocTypeCode;

    @Schema(description = "두번째 PF 코드")
    private String secondPfCode;

    @Schema(description = "PIS 문서 유형 코드")
    private String pisDocTypeCode;

    @Schema(description = "PIS OLD 문서 여부")
    private String pisDocOldFlag;

    @Schema(description = "EPS 사용 여부")
    private String epsUseFlag;

    @Schema(description = "EPS USB고속충전 명")
    private String epsUsbPdNm;

    @Schema(description = "EPS 최대 전압")
    private String epsMaxVoltage;

    @Schema(description = "EPS 최소 전압")
    private String epsMinVoltage;

    @Schema(description = "주력 전시 순서")
    private String spotlightsDspSeq;

    @Schema(description = "문의 여부")
    private String inquiryFlag;

    @Schema(description = "찾기 딜러 사용 여부")
    private String findDealerUseFlag;

    @Schema(description = "총 건수")
    private int totalCount;

    public ProductListModelVO toVO() {
        return ProductListModelVO.builder()
                .modelId(this.pdpId)
                .sku(this.lgcomSkuId)
                .modelName(this.productNm)
                .modelType(this.pdpTypeCode)
                .bizType(this.bizTypeCode)
                .modelUrlPath(this.pdpUrl)
                .totalCount(this.totalCount)
                .superCategoryId(this.lv1CategoryId)
                .categoryId(this.lv2CategoryId)
                .subCategoryId(this.lv3CategoryId)
                .categoryName(this.siteCategoryNm)
                .retailerPricingFlag(this.retailerPricingFlag)
                .modelStatusCode(this.productStateCode)
                .salesModelCode(this.salesModelCode)
                .salesSuffixCode(this.salesModelSuffixCode)
                .productLevel1Code(this.lv1ProductCode)
                .defaultProductTag(this.defaultProductTagCode)
                .productTag1UserType(this.productTagUserTypeCode1)
                .productTag2UserType(this.productTagUserTypeCode2)
                .productTag1(this.productTagCode1)
                .productTag1BeginDay(this.productTagExpBeginDate1)
                .productTag1EndDay(this.productTagExpEndDate1)
                .productTag2(this.productTagCode2)
                .productTag2BeginDay(this.productTagExpBeginDate2)
                .productTag2EndDay(this.productTagExpEndDate2)
                .promotionText(this.promotionText)
                .imageAltText(this.imgAltTextCnts)
                .mediumImageAddr(this.mdmImgUrl)
                .smallImageAddr(this.smlImgUrl)
                .userFriendlyName(this.userFrndyProductNm)
                .bundlePlpDisplayFlag("Y")
                .pCount(this.pCount)
                .ratingPercent(this.ratingPercent)
                .sRating(this.sRating)
                .sRating2(this.sRating2)
                .sRating3(this.sRating3)
                .obsSellFlag(this.obsSellFlag)
                .wtbUseFlag(this.wtbUseFlag)
                .ecommerceTarget("self")
                .wtbExternalLinkUseFlag(this.wtbExternalLinkUseFlag)
                .wtbExternalLinkName(this.wtbExtlLinkNm)
                .wtbExternalLinkUrl(this.wtbExtlLinkUrl)
                .wtbExternalLinkSelfFlag(this.wtbExtlLinkSelfScreenFlag)
                .inquiryToBuyFlag(this.itbUseFlag)
                .findTheDealerFlag(this.findDealerUseFlag)
                .siblingGroupCode(this.siblingGrpCode)
                .seriesName(this.siblingGrpNm)
                .siblingCode(this.siblingCode)
                .defaultSiblingModelFlag(this.defaultSiblingModelFlag)
                .plpHighlightModelFlag(this.plpHighlightModelFlag)
                .siblingLocalValue(this.siblingLocalVal)
                .siblingType(this.siblingTypeCode)
                .superCategoryName(this.superCategoryNm)
                .categoryEngName(this.categoryNm)
                .signatureFlag(this.signtProductFlag)
                .modelYear(this.productRelesDd)
                .promotionLinkUrl(this.promotionLinkUrl)
                .externalLinkTarget(this.externalLinkTarget)
                .washTowerFlag(this.wtowerProductFlag)
                .themeType(this.productThemeTypeCode)
                .exclusionModel(this.exclProductSettCode)
                .buName1(this.lv1CategoryCodeNm)
                .buName2(this.lv2CategoryCodeNm)
                .buName3(this.lv3CategoryCodeNm)
                .classificationFlagLv1(this.lv1CategoryCodeNm)
                .classificationFlagLv2(this.lv2CategoryCodeNm)
                .classificationFlagLv3(this.lv3CategoryCodeNm)
                .classificationFlagLv4(this.lv4CategoryCodeNm)
                .target(this.siblingSbjTypeCode)
                .wtbExternalLinkName(this.wtbExtlLinkNm)
                .wtbExternalLinkUrl(this.wtbExtlLinkUrl)
                .wtbExternalLinkUseFlag(this.wtbExternalLinkUseFlag)
                .wtbExternalLinkSelfFlag(this.wtbExtlLinkSelfScreenFlag)
                .energyLabel(this.elabelGrdCode)
                .energyLabelCategory(this.elDocTypeCode)
                .secondEnergyLabel(this.secondElabelGrdCode)
                .secondEnergyLabelCategory(this.secondElDocTypeCode)
                .elType(this.elTypeCode)
                .secondElType(this.secondElTypeCode)
                .secondPisDocType(this.secondPisDocTypeCode)
                .secondPfCode(this.secondPfCode)
                .pisDocType(this.pisDocTypeCode)
                .pisDocOldFlag(this.pisDocOldFlag)
                .epsIncludeCharger(this.epsUseFlag)
                .epsUsbPd(this.epsUsbPdNm)
                .epsMaxPower(this.epsMaxVoltage)
                .epsMinPower(this.epsMinVoltage)
                .lgPickOrderNo(this.spotlightsDspSeq)
                .mtsModelUrlPath(this.mtsPdpUrl)
                .build();
    }
}
