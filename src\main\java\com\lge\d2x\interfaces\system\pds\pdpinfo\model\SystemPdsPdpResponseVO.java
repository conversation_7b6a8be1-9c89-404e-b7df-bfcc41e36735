package com.lge.d2x.interfaces.system.pds.pdpinfo.model;

import com.lge.d2x.core.constants.CommonConstants;
import com.lge.d2x.domain.product.v1.model.ProductSummaryResponseVO;
import java.math.BigDecimal;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemPdsPdpResponseVO {
    private String lgcomSkuId;
    private String skuId;
    private String pdpId;
    private String bizTypeCode;
    private String inchVal;
    private String siteCode;
    private String productNm;
    private String productStateCode;
    private BigDecimal msrpSalesPrice;
    private String pdpTypeCode;
    private String retailerPricingFlag;
    private String wtbUseFlag;
    private String wtbExtlLinkUseFlag;
    private String wtbExtlLinkNm;
    private String wtbExtlLinkUrl;
    private String wtbExtlLinkSelfScreenFlag;
    private String wtbSubId;
    private String elabelClsCode;
    private String elabelGrdCode;
    private String elDocTypeCode;
    private String pisDocTypeCode;
    private String elTypeCode;
    private String productYear;
    private String imgAltTextCnts;
    private String bigImgUrl;
    private String mdmImgUrl;
    private String smlImgUrl;
    private String product360ImgPath;
    private String product360Title;
    private String productVrImgPath;
    private String productVrTitle;
    private String productArImgPath;
    private String productArTitle;
    private String product360Dim3ImgPath;
    private String product360Dim3Title;
    private String productArMoblImgPath;
    private String productArMoblTitle;
    private String pdpType;
    private String inquiryFlag;
    private String userFrndyProductNm;
    private String seoPageTitle;
    private String seoDesc;
    private String signtProductFlag;
    private String thinqProductFlag;
    private String findDealerUseFlag;
    private String objetProductFlag;
    private String objetAcsryFlag;
    private String obsSellFlag;
    private String obsInventoryFlag;
    private String productThemeTypeCode;
    private String wtowerProductFlag;
    private String secondElDocTypeCode;
    private String secondPisDocTypeCode;
    private String pisDocOldFlag;
    private String secondPfCode;
    private String secondElTypeCode;
    private String secondElabelGrdCode;
    private String secondElabelClsCode;
    private String exclProductSettCode;
    private String classificationFlagLv1;
    private String classificationFlagLv2;
    private String classificationFlagLv3;
    private String classificationFlagLv4;
    private String wtbOfflineUseFlag;
    private String wtbOnlineUseFlag;
    private String wtbDirectUseFlag;
    private String bundleModelFlag;
    private String energyCertCode;
    private String categoryCode;
    private String productRelesDd;
    private String itbUseFlag;
    private String shopCode;
    private String bundleImgUrl;
    private String bundleMoblImgUrl;
    private String bundleImgAltTextCnts;
    private String bundleDesc;
    private String pdpUrl;
    private String defaultProductTagCode;
    private String productTagCode1;
    private String productTagExpBeginDate1;
    private String productTagExpEndDate1;
    private String productTagUseFlag1;
    private String productTagCode2;
    private String productTagExpBeginDate2;
    private String productTagExpEndDate2;
    private String productTagUseFlag2;
    private String realProductTag1;
    private String realProductTag1EndDate;
    private String realProductTag2;
    private String realProductTag2EndDate;
    private String productTagUserTypeCode1;
    private String productTagUserTypeCode2;
    private String aemPublFlag;
    private String aemPublDate;

    public ProductSummaryResponseVO toPdpSummaryVO() {
        return ProductSummaryResponseVO.builder()
                .bizType(this.bizTypeCode)
                .inchCode(this.inchVal)
                .modelName(this.productNm)
                .modelDisplayName(this.productNm)
                .modelId(this.pdpId)
                .sku(this.lgcomSkuId)
                .modelStatusCode(this.productStateCode)
                .msrp(this.msrpSalesPrice)
                .modelTypeM(this.pdpTypeCode)
                .obsSellFlag(this.obsSellFlag)
                .retailerPricingFlag(this.retailerPricingFlag)
                .wtbUseFlag(this.wtbUseFlag)
                .wtbOfflineUseFlag(this.wtbOfflineUseFlag)
                .wtbOnlineUseFlag(this.wtbOnlineUseFlag)
                .wtbDirectUseFlag(this.wtbDirectUseFlag)
                .imageAltText(this.imgAltTextCnts)
                .largeImageAddr(this.bigImgUrl)
                .mediumImageAddr(this.mdmImgUrl)
                .model360Image(this.product360ImgPath)
                .model360Title(this.product360Title)
                .model3603dImage(this.product360Dim3ImgPath)
                .model3603dTitle(this.product360Dim3Title)
                .modelType(this.pdpTypeCode)
                .smallImageAddr(this.smlImgUrl)
                .userFriendlyName(this.productNm)
                .modelUrlPath(this.pdpUrl)
                .categoryId(this.categoryCode)
                .defaultProductTag(this.defaultProductTagCode)
                .productTag1(this.realProductTag1)
                .productTag1EndDay(this.realProductTag1EndDate)
                .productTag2(this.realProductTag2)
                .productTag2EndDay(this.realProductTag2EndDate)
                .findTheDealerFlag(this.findDealerUseFlag)
                .wtbExternalLinkUseFlag(this.wtbExtlLinkUseFlag)
                .wtbExternalLinkName(this.wtbExtlLinkNm)
                .wtbExternalLinkUrl(this.wtbExtlLinkUrl)
                .wtbExternalLinkSelfFlag(this.wtbExtlLinkSelfScreenFlag)
                .inquiryToBuyFlag(this.itbUseFlag)
                .productEnquiryFlag(this.inquiryFlag)
                .energyLabel(this.elabelGrdCode)
                .energyLabelCategory(this.elDocTypeCode)
                .modelVrImage(this.productVrImgPath)
                .modelVrTitle(this.productVrTitle)
                .modelArImage(this.productArImgPath)
                .modelArTitle(this.productArTitle)
                .modelArMobileImage(this.productArMoblImgPath)
                .modelArMobileTitle(this.productArMoblTitle)
                .localeCode(this.siteCode)
                .productTag1UserType(this.productTagUserTypeCode1)
                .productTag2UserType(this.productTagUserTypeCode2)
                .modelYear(this.productYear)
                .buName1(this.classificationFlagLv1)
                .buName2(this.classificationFlagLv2)
                .buName3(this.classificationFlagLv3)
                .signatureFlag(this.signtProductFlag)
                .thinqFlag(this.thinqProductFlag)
                .objetAccessoryFlag(
                        StringUtils.defaultIfBlank(this.objetAcsryFlag, CommonConstants.NO_FLAG))
                .objetProductFlag(
                        StringUtils.defaultIfBlank(this.objetProductFlag, CommonConstants.NO_FLAG))
                .themeType(this.productThemeTypeCode)
                .secondEnergyLabel(this.secondElabelGrdCode)
                .secondEnergyLabelCategory(this.secondElDocTypeCode)
                .washTowerFlag(this.wtowerProductFlag)
                .exclusionModel(this.exclProductSettCode)
                .classificationFlagLv1(this.classificationFlagLv1)
                .classificationFlagLv2(this.classificationFlagLv2)
                .classificationFlagLv3(this.classificationFlagLv3)
                .classificationFlagLv4(this.classificationFlagLv4)
                .mktNameSeoTitle(this.seoPageTitle)
                .mktNameSeoDescription(this.seoDesc)
                .pisDocType(this.pisDocTypeCode)
                .pisDocOldFlag(this.pisDocOldFlag)
                .secondPisDocType(this.secondPisDocTypeCode)
                .secondPfCode(this.secondPfCode)
                .elType(this.elTypeCode)
                .secondElType(this.secondElTypeCode)
                .bundleModelFlag(this.bundleModelFlag)
                .eprelId(this.energyCertCode)
                .pimSku(this.skuId)
                .wtbSubId(this.wtbSubId)
                .shopCode(this.shopCode)
                .build();
    }
}
