package com.lge.d2x.domain.support.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class TypeOfInquiryProductResponseVO {
    @Schema(description = "code", example = "024")
    private String code;

    @Schema(description = "value", example = "ThinQ")
    private String value;

    @Schema(description = "sortNo", example = "19")
    private String sortNo;

    @Schema(description = "required", example = "Y")
    private String required;
}
