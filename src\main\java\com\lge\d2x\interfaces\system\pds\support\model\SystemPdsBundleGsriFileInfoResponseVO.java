package com.lge.d2x.interfaces.system.pds.support.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemPdsBundleGsriFileInfoResponseVO {
    private String modelId;
    private String docId;
    private String fileName;
    private String originalName;
    private String productLeve1Code;
    private String rank;
}
