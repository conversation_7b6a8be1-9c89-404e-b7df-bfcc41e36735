package com.lge.d2x.domain.support.v1.model;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductGsriFileInfoRequestVO {
    private String docTypeCode;
    private String pdpId;
    private String fEnergyLabelDocId;
    private String productFichelDocId;
    private String energyLabelDocId;
    private String siteCode;
    private List<String> docTypeList;
}
