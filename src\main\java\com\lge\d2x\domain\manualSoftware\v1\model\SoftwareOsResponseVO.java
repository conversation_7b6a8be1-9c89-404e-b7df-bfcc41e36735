package com.lge.d2x.domain.manualSoftware.v1.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SoftwareOsResponseVO {

    private String osName;
    private String osCode;

    private String modelType;
    private String linkTitle;
    private String linkName;
    private String linkAddr;
    private String fileSize;
    private String releaseDate;
    private String osupgradeDesc;
    private String lastUpdateDate;
    private String selectNum;
    private String selectMaxNum;
    private String csMdmsProductCode;
}
