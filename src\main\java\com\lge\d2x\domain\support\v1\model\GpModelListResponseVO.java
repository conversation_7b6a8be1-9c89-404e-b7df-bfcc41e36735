package com.lge.d2x.domain.support.v1.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class GpModelListResponseVO {
    private String code;
    private String value;
    private String salesModelFlag;
    private String gsfsUseFlag;
    private String obuCode;
    private String mdmsPrdgrpCode;
    private String moblModelFlag;
    private String lv1CategoryCode;
    private String lv2CategoryCode;
    private String lv3CategoryCode;
    private String lv1CategoryNm;
    private String lv2CategoryNm;
    private String lv3CategoryNm;
    private String lv1StickyImgUrl;
    private String lv2StickyImgUrl;
    private String lv3StickyImgUrl;
    private String lv1StickyHoverIconUrl;
    private String lv2StickyHoverIconUrl;
    private String lv3StickyHoverIconUrl;
    private String lbrWtyPrdText;
    private String partWtyPrdText;
    private String userFriendlyName;
    private String modelImgPath;
}
