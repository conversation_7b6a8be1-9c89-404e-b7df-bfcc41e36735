package com.lge.d2x.domain.pdpInfo.v1.model;

import java.math.BigDecimal;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PdpResponseVO {
    private String lgcomSkuId;
    private String skuId;
    private String pdpId;
    private String bizTypeCode;
    private String inchVal;
    private String siteCode;
    private String productNm;
    private String productStateCode;
    private BigDecimal msrpSalesPrice;
    private String pdpTypeCode;
    private String retailerPricingFlag;
    private String wtbUseFlag;
    private String wtbExtlLinkUseFlag;
    private String wtbExtlLinkNm;
    private String wtbExtlLinkUrl;
    private String wtbExtlLinkSelfScreenFlag;
    private String wtbSubId;
    private String elabelClsCode;
    private String elabelGrdCode;
    private String elDocTypeCode;
    private String pisDocTypeCode;
    private String elTypeCode;
    private String productYear;
    private String imgAltTextCnts;
    private String bigImgUrl;
    private String mdmImgUrl;
    private String smlImgUrl;
    private String product360ImgPath;
    private String product360Title;
    private String productVrImgPath;
    private String productVrTitle;
    private String productArImgPath;
    private String productArTitle;
    private String product360Dim3ImgPath;
    private String product360Dim3Title;
    private String productArMoblImgPath;
    private String productArMoblTitle;
    private String pdpType;
    private String inquiryFlag;
    private String userFrndyProductNm;
    private String seoPageTitle;
    private String seoDesc;
    private String signtProductFlag;
    private String thinqProductFlag;
    private String findDealerUseFlag;
    private String objetProductFlag;
    private String objetAcsryFlag;
    private String obsSellFlag;
    private String obsInventoryFlag;
    private String productThemeTypeCode;
    private String wtowerProductFlag;
    private String secondElDocTypeCode;
    private String secondPisDocTypeCode;
    private String pisDocOldFlag;
    private String secondPfCode;
    private String secondElTypeCode;
    private String secondElabelGrdCode;
    private String secondElabelClsCode;
    private String exclProductSettCode;
    private String classificationFlagLv1;
    private String classificationFlagLv2;
    private String classificationFlagLv3;
    private String classificationFlagLv4;
    private String wtbOfflineUseFlag;
    private String wtbOnlineUseFlag;
    private String wtbDirectUseFlag;
    private String bundleModelFlag;
    private String energyCertCode;
    private String categoryCode;
    private String productRelesDd;
    private String itbUseFlag;
    private String shopCode;
    private String bundleImgUrl;
    private String bundleMoblImgUrl;
    private String bundleImgAltTextCnts;
    private String bundleDesc;
    private String pdpUrl;
    private String defaultProductTagCode;
    private String productTagCode1;
    private String productTagExpBeginDate1;
    private String productTagExpEndDate1;
    private String productTagUseFlag1;
    private String productTagCode2;
    private String productTagExpBeginDate2;
    private String productTagExpEndDate2;
    private String productTagUseFlag2;
    private String realProductTag1;
    private String realProductTag1EndDate;
    private String realProductTag2;
    private String realProductTag2EndDate;
    private String productTagUserTypeCode1;
    private String productTagUserTypeCode2;
    private String aemPublFlag;
    private String aemPublDate;
}
