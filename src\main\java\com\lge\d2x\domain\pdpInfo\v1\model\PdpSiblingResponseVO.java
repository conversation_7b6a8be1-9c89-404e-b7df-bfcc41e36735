package com.lge.d2x.domain.pdpInfo.v1.model;

import java.math.BigDecimal;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PdpSiblingResponseVO {
    private String lgcomSkuId;
    private String skuId;
    private String pdpId;
    private String productNm;
    private String bizTypeCode;
    private BigDecimal msrp;
    private String userFrndyProductNm;
    private String productStateCode;
    private String pdpTypeCode;
    private String siteCode;
    private String defaultSiblingModelFlag;
    private String pdpTitle;
    private String siblingCode;
    private String siblingGrpCode;
    private String siblingValue;
    private String siblingTypeCode;
    private String pdpUrl;
    private String priceUseFlag;
    private String siblingGrpNm;
    private String siblingSbjTypeCode;
    private String displayOrderNo;
    private String priorityOrderNo;
    private String display1depth;
}
