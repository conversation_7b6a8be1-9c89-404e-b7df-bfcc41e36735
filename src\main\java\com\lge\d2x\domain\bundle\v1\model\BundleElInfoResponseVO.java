package com.lge.d2x.domain.bundle.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BundleElInfoResponseVO {
    @Schema(description = "번들 PDP ID")
    private String bundlePdpId;

    @Schema(description = "에너지라벨 등급 코드")
    private String elabelGrdCode;

    @Schema(description = "에너지라벨 문서 유형 코드")
    private String elDocTypeCode;

    @Schema(description = "F 에너지라벨 카테고리")
    private String fEnergyLabelCategory;

    @Schema(description = "PIS 문서 유형 코드")
    private String pisDocTypeCode;

    @Schema(description = "에너지라벨 유형 코드")
    private String elTypeCode;
}
