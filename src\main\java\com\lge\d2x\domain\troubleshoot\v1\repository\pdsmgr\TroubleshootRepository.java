package com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr;

import com.lge.d2x.domain.troubleshoot.v1.model.CategoryNameRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.CategoryRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.CategoryResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptomResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SuperCategoryRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SuperCategoryResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomResponseVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TroubleshootRepository {
    List<HelpfulArticlesResponseVO> selectHelpfulArticlesList(HelpfulArticlesRequestVO requestVO);

    List<HelpfulArticlesResponseVO> selectHelpfulArticlesList3Depth(
            HelpfulArticlesRequestVO requestVO);

    List<SuperCategoryResponseVO> selectGpSuperCategory(SuperCategoryRequestVO requestVO);

    List<CategoryResponseVO> selectGpCategory(CategoryRequestVO requestVO);

    List<SymptomResponseVO> selectSymptom(SymptomRequestVO requestVO);

    List<SubSymptomResponseVO> selectSubsymptom(SubSymptomRequestVO requestVO);

    List<SymptomResponseVO> selectSymptomT1Depth(SymptomRequestVO requestVO);

    List<SubSymptomResponseVO> selectSubsymptomT2DepthList(SubSymptomRequestVO requestVO);

    List<DetailSymptomResponseVO> selectSubsymptom3DepthTtype(SubSymptomRequestVO requestVO);

    String selectCategoryName(CategoryNameRequestVO requestVO);

    List<SubSymptCwTypeResponseVO> selectSubsymptomCWtypeList(SubSymptCwTypeRequestVO requestVO);

    List<SubSymptCwTypeResponseVO> selectSubSymptomCW2DepthTypeList(
            SubSymptCwTypeRequestVO requestVO);

    List<DetailSymptCwTypeResponseVO> selectSubsymptomCW3DepthTypeList(
            SubSymptCwTypeRequestVO requestVO);

    List<SymptCwTypeResponseVO> selectSymptomCWtypeList(SymptCwTypeRequestVO requestVO);

    List<SymptCwTypeResponseVO> selectSymptomCW1DepthTypeList(SymptCwTypeRequestVO requestVO);

    List<SymptomRTypeResponseVO> selectSymptomRtypeList(SymptomRTypeRequestVO requestVO);

    List<SymptomRTypeResponseVO> selectSymptomR1DepthTypeList(SymptomRTypeRequestVO requestVO);

    List<SubSymptRTypeResponseVO> selectSubSymptomRtypeList(SubSymptRTypeRequestVO requestVO);

    List<SubSymptRTypeResponseVO> selectSubSymptomR2DepthTypeList(SubSymptRTypeRequestVO requestVO);

    List<DetailSymptRTypeResponseVO> selectSubSymptomR3DepthTypeList(
            SubSymptRTypeRequestVO requestVO);
}
