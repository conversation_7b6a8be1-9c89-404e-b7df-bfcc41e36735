package com.lge.d2x.domain.whereToBuy.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class WtbDistributorRequestVO {
    @Schema(description = "SKU", example = "OLED55B3PSA.AWP.ESPS.PA.C")
    private String sku;

    @NotBlank
    @Schema(description = "위도", example = "8.9823792")
    private String lat;

    @NotBlank
    @Schema(description = "경도", example = "-79.51986959999999")
    private String lng;

    @Schema(description = "반경", example = "50")
    private String radius;

    @Schema(description = "WTB 카테고리 코드", example = "CT52006983")
    private String wtbCateId;

    @Schema(description = "WTB 슈퍼 카테고리 코드", example = "CT52006983")
    private String wtbSuperCateId;

    @Schema(description = "시그니처 사용 여부", example = "N")
    private String signatureWTBUseFlag;

    @Schema(description = "브랜드샵탭 여부", example = "true")
    private String brandshopTabFlag;

    @Schema(description = "gerp 재고 사용 여부", example = "Y")
    private String gerpDistributorStockUseFlag;

    @Schema(description = "카테고리 코드", example = "CT52006983")
    private String categoryCode;

    @Schema(description = "국가코드")
    private String countryCode;

    @Schema(description = "국가명")
    private String countryName;

    @Schema(description = "주코드")
    private String stateCode;

    @Schema(description = "주명")
    private String stateName;

    @Schema(description = "도시명")
    private String cityName;

    @Schema(description = "우편번호")
    private String postalCode;

    @Schema(description = "거리명")
    private String streetName;

    @Schema(description = "주설명")
    private String cityStateDesc;

    @Schema(description = "카테고리 아이디 리스트")
    private String categoryIdList;
}
