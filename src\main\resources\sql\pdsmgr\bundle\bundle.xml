<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.bundle.v1.repository.pdsmgr.BundleRepository">
    <select id="selectBundle" parameterType="com.lge.d2x.domain.bundle.v1.model.BundleListRequestVO" resultType="com.lge.d2x.domain.bundle.v1.model.BundleListResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.bundle.v1.service.BundleService.selectBundle */
		     AAA.*
		  <if test='bundleListFlag == "Y"'>
		   , COUNT(AAA.PDP_ID) OVER () AS TOTAL_COUNT
		  </if>
		FROM
		(
			SELECT A.PDP_ID
			     , A<PERSON><PERSON>_SKU_ID
			     , <PERSON><PERSON>_<PERSON>
				 , A.PRODUCT_NM
				 , <PERSON><PERSON>_<PERSON>_CODE
				 , A<PERSON>WTB_USE_FLAG
				 , IFNULL(A.MSRP_SALES_PRICE, 0.00) AS MSRP
				 , 'Y' AS ECOMMERCE_FLAG
				 , A.WTB_EXTL_LINK_USE_FLAG
			     , A.WTB_EXTL_LINK_NM
			     , A.WTB_EXTL_LINK_URL
			     , A.WTB_EXTL_LINK_SELF_SCREEN_FLAG
			     , A.EXCL_PRODUCT_SETT_CODE
			     , A.PDP_TYPE_CODE
			     , IFNULL(NULLIF(F.NEW_MKT_PRODUCT_NM, ''), A.USER_FRNDY_PRODUCT_NM) AS USER_FRIENDLY_NAME
				 , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productYear
				 , B.LV2_CATEGORY_CODE AS SUPER_CATEGORY_CODE
				 , B.LV3_CATEGORY_CODE AS CATEGORY_CODE
	 			 , B.LV4_CATEGORY_CODE AS SUB_CATEGORY_CODE
				 , C.SITE_CATEGORY_NM
				 , D.PRODUCT_STATE_CODE
				 , D.PDP_URL
				 , D.BUNDLE_IMG_URL
				 , D.BUNDLE_MOBL_IMG_URL
				 , D.BUNDLE_IMG_ALT_TEXT_CNTS
				 , D.BUNDLE_DESC
				 , D.CREATION_DATE
				 , D.DEFAULT_PRODUCT_TAG_CODE
				 , CASE
				   		WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN D.PRODUCT_TAG_EXP_BEGIN_DATE1 AND D.PRODUCT_TAG_EXP_END_DATE1) > 0
				   		THEN IF(D.PRODUCT_TAG_USE_FLAG1 = 'Y', D.PRODUCT_TAG_CODE1, NULL)
				    END PRODUCT_TAG1
				 , CASE
				  		WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN D.PRODUCT_TAG_EXP_BEGIN_DATE2 AND D.PRODUCT_TAG_EXP_END_DATE2) > 0
				  		THEN IF(D.PRODUCT_TAG_USE_FLAG2 = 'Y', D.PRODUCT_TAG_CODE2, NULL)
				   END PRODUCT_TAG2
				 , PRS.PROMOTION_TAG_VAL AS PROMOTION_TEXT
				 , E.SALES_MODEL_CODE
				 , E.SALES_MODEL_SUFFIX_CODE
				 , D_LV1.CATEGORY_NM AS classificationFlagLv1
			     , D_LV2.CATEGORY_NM AS classificationFlagLv2
			     , D_LV3.CATEGORY_NM AS classificationFlagLv3
			     , D_LV4.CATEGORY_NM AS classificationFlagLv4
			  FROM DSP_PDP_M A
			 INNER JOIN DSP_PDP_CATEGORY_R B
				ON  B.PDP_ID = A.PDP_ID
			   AND B.USE_FLAG = 'Y'
			   AND B.SITE_CODE = #{siteCode}
			   AND B.BIZ_TYPE_CODE = #{bizTypeCode}
			   AND B.DEFAULT_MAP_FLAG = 'Y'
		<if test='bundleListFlag != "Y"'>
			<if test="modelIds != null">
				<foreach collection="modelIds" item="m" open="AND A.PDP_ID IN (" separator="," close=")">
				#{m}
				</foreach>
			</if>
		</if>
			 INNER JOIN DSP_DISPLAY_CATEGORY_M C
			    ON C.CATEGORY_CODE = B.LV3_CATEGORY_CODE
			   AND C.SITE_CODE = #{siteCode}
			 INNER JOIN DSP_PDP_D D
				ON D.PDP_ID = A.PDP_ID
			   AND D.SITE_CODE = #{siteCode}
			   AND D.BIZ_TYPE_CODE = #{bizTypeCode}
			   AND D.AEM_PUBL_FLAG = 'Y'
			   AND D.SHOP_CODE = 'D2C'
			   AND D.USE_FLAG = 'Y'
			  <if test='bundleListFlag == "Y"'>
			   AND D.PRODUCT_STATE_CODE = #{productStateCode}
			 </if>
			 INNER JOIN PDM_PRODUCT_M E
			    ON E.SKU_ID = A.SKU_ID
			   AND E.USE_FLAG = 'Y'
			 INNER JOIN PDM_PRODUCT_D F
			    ON F.SKU_ID = E.SKU_ID
			   AND F.USE_FLAG = E.USE_FLAG
			   AND F.LOCALE_CODE = #{localeCode}
			 INNER JOIN PDM_PRODUCT_CATEGORY_R G
			    ON G.SKU_ID = E.SKU_ID
			   AND G.USE_FLAG = E.USE_FLAG
			   AND G.LOCALE_CODE = F.LOCALE_CODE
			 INNER JOIN PDM_CATEGORY_M D_LV1
		    	ON D_LV1.CATEGORY_CODE = G.LV1_CATEGORY_CODE
		       AND D_LV1.USE_FLAG = G.USE_FLAG
		       AND D_LV1.CATEGORY_LV_NO = 1
			 INNER JOIN PDM_CATEGORY_M D_LV2
		    	ON D_LV2.CATEGORY_CODE = G.LV2_CATEGORY_CODE
		       AND D_LV2.HIGH_CATEGORY_CODE = D_LV1.CATEGORY_CODE
		       AND D_LV2.CATEGORY_LV_NO = 2
		       AND D_LV2.USE_FLAG = A.USE_FLAG
			 INNER JOIN PDM_CATEGORY_M D_LV3
		    	ON D_LV3.CATEGORY_CODE = G.LV3_CATEGORY_CODE
		       AND D_LV3.HIGH_CATEGORY_CODE = D_LV2.CATEGORY_CODE
		       AND D_LV3.CATEGORY_LV_NO = 3
		       AND D_LV3.USE_FLAG = A.USE_FLAG
			 INNER JOIN PDM_CATEGORY_M D_LV4
		    	ON D_LV4.CATEGORY_CODE = G.LV4_CATEGORY_CODE
		       AND D_LV4.HIGH_CATEGORY_CODE = D_LV3.CATEGORY_CODE
		       AND D_LV4.CATEGORY_LV_NO = 4
		       AND D_LV4.USE_FLAG = A.USE_FLAG
			  LEFT JOIN (SELECT PRSSUB.*
			               FROM
								(SELECT PMM.PDP_ID
				                       ,MPM.PROMOTION_TAG_VAL
				                   FROM PRM_PROMOTION_PRODUCT_R PMM
				                  INNER JOIN PRM_PROMOTION_M MPM
				                     ON PMM.PROMOTION_ID = MPM.PROMOTION_ID
				                    AND PMM.SITE_CODE = MPM.SITE_CODE
				                  WHERE MPM.SITE_CODE = #{siteCode}
				                    AND MPM.USE_FLAG = 'Y'
				                    AND DATE_FORMAT(#{today},'%Y%m%d') BETWEEN MPM.PROMOTION_EXP_BEGIN_DATE AND MPM.PROMOTION_EXP_END_DATE
				                    AND PMM.USE_FLAG = 'Y'
				                    AND IFNULL(MPM.PROMOTION_NONEXP_FLAG,'N') <![CDATA[<>]]> 'Y'
				                  ORDER BY PMM.PROMOTION_PAGE_PRODUCT_SEQ IS NULL ASC, PMM.PROMOTION_PAGE_PRODUCT_SEQ ASC
			                      ) PRSSUB
	               				GROUP BY PRSSUB.PDP_ID
			                ) PRS
			       ON A.PDP_ID = PRS.PDP_ID
			WHERE A.SITE_CODE = #{siteCode}
			  AND A.BIZ_TYPE_CODE = #{bizTypeCode}
			 <if test='bundleListFlag == "Y"'>
			  AND A.PDP_TYPE_CODE IN ('B','O')
			 </if>
			  AND A.USE_FLAG = 'Y'
			GROUP BY A.PDP_ID
		)AAA
 <choose>
  <when test='bundleListFlag == "Y"'>
	ORDER BY AAA.CREATION_DATE DESC, AAA.PRODUCT_NM ASC
   <if test="viewAll == null or viewAll == ''">
	LIMIT  #{startNo}, #{pageCnt}
   </if>
  </when>
  <otherwise>
	LIMIT 10
  </otherwise>
 </choose>
    </select>

    <select id="selectBundleNotStandard" parameterType="com.lge.d2x.domain.bundle.v1.model.BundleListRequestVO" resultType="com.lge.d2x.domain.bundle.v1.model.BundleListResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.bundle.v1.service.BundleService.selectBundle */
		     AAA.*
		  <if test='bundleListFlag == "Y"'>
		   , COUNT(AAA.PDP_ID) OVER () AS TOTAL_COUNT
		  </if>
		FROM
		(
			SELECT A.PDP_ID
			     , A.LGCOM_SKU_ID
			     , A.SKU_ID
				 , A.PRODUCT_NM
				 , A.BIZ_TYPE_CODE
				 , A.WTB_USE_FLAG
				 , IFNULL(A.MSRP_SALES_PRICE, 0.00) AS MSRP
				 , 'Y' AS ECOMMERCE_FLAG
				 , A.WTB_EXTL_LINK_USE_FLAG
			     , A.WTB_EXTL_LINK_NM
			     , A.WTB_EXTL_LINK_URL
			     , A.WTB_EXTL_LINK_SELF_SCREEN_FLAG
			     , A.EXCL_PRODUCT_SETT_CODE
			     , A.PDP_TYPE_CODE
				 , IFNULL(NULLIF(F.NEW_MKT_PRODUCT_NM, ''), A.USER_FRNDY_PRODUCT_NM) AS USER_FRIENDLY_NAME
				 , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productYear
				 , B.LV1_CATEGORY_ID AS SUPER_CATEGORY_CODE
				 , B.LV2_CATEGORY_ID AS CATEGORY_CODE
	 			 , B.LV3_CATEGORY_ID AS SUB_CATEGORY_CODE
				 , C.SITE_CATEGORY_NM
				 , D.PRODUCT_STATE_CODE
				 , D.PDP_URL
				 , D.BUNDLE_IMG_URL
				 , D.BUNDLE_MOBL_IMG_URL
				 , D.BUNDLE_IMG_ALT_TEXT_CNTS
				 , D.BUNDLE_DESC
				 , D.CREATION_DATE
				 , D.DEFAULT_PRODUCT_TAG_CODE
				 , CASE
				   		WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN D.PRODUCT_TAG_EXP_BEGIN_DATE1 AND D.PRODUCT_TAG_EXP_END_DATE1) > 0
				   		THEN IF(D.PRODUCT_TAG_USE_FLAG1 = 'Y', D.PRODUCT_TAG_CODE1, NULL)
				    END PRODUCT_TAG1
				 , CASE
				  		WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN D.PRODUCT_TAG_EXP_BEGIN_DATE2 AND D.PRODUCT_TAG_EXP_END_DATE2) > 0
				  		THEN IF(D.PRODUCT_TAG_USE_FLAG2 = 'Y', D.PRODUCT_TAG_CODE2, NULL)
				   END PRODUCT_TAG2
				 , PRS.PROMOTION_TAG_VAL AS PROMOTION_TEXT
				 , E.SALES_MODEL_CODE
				 , E.SALES_MODEL_SUFFIX_CODE
				 , D_LV1.CATEGORY_NM AS classificationFlagLv1
			     , D_LV2.CATEGORY_NM AS classificationFlagLv2
			     , D_LV3.CATEGORY_NM AS classificationFlagLv3
			     , D_LV4.CATEGORY_NM AS classificationFlagLv4
			  FROM DSP_PDP_M A
			 INNER JOIN DSP_OLD_PDP_CATEGORY_R B
				ON  B.PDP_ID = A.PDP_ID
			   AND B.USE_FLAG = 'Y'
			   AND B.SITE_CODE = #{siteCode}
			   AND B.BIZ_TYPE_CODE = #{bizTypeCode}
			   AND B.DEFAULT_MAP_FLAG = 'Y'
		<if test='bundleListFlag != "Y"'>
			<if test="modelIds != null">
				<foreach collection="modelIds" item="m" open="AND A.PDP_ID IN (" separator="," close=")">
				#{m}
				</foreach>
			</if>
		</if>
			 INNER JOIN DSP_DISPLAY_CATEGORY_M C
			    ON C.CATEGORY_CODE = B.LV2_CATEGORY_ID
			   AND C.SITE_CODE = #{siteCode}
			 INNER JOIN DSP_PDP_D D
				ON D.PDP_ID = A.PDP_ID
			   AND D.SITE_CODE = #{siteCode}
			   AND D.BIZ_TYPE_CODE = #{bizTypeCode}
			   AND D.AEM_PUBL_FLAG = 'Y'
			   AND D.SHOP_CODE = 'D2C'
			 <if test='bundleListFlag == "Y"'>
			   AND D.PRODUCT_STATE_CODE = #{productStateCode}
			 </if>
			 INNER JOIN PDM_PRODUCT_M E
			    ON E.SKU_ID = A.SKU_ID
			   AND E.USE_FLAG = 'Y'
			 INNER JOIN PDM_PRODUCT_D F
			    ON F.SKU_ID = E.SKU_ID
			   AND F.USE_FLAG = E.USE_FLAG
			   AND F.LOCALE_CODE = #{localeCode}
			 INNER JOIN PDM_PRODUCT_CATEGORY_R G
			    ON G.SKU_ID = E.SKU_ID
			   AND G.USE_FLAG = E.USE_FLAG
			   AND G.LOCALE_CODE = F.LOCALE_CODE
			 INNER JOIN PDM_CATEGORY_M D_LV1
		    	ON D_LV1.CATEGORY_CODE = G.LV1_CATEGORY_CODE
		       AND D_LV1.USE_FLAG = G.USE_FLAG
		       AND D_LV1.CATEGORY_LV_NO = 1
			 INNER JOIN PDM_CATEGORY_M D_LV2
		    	ON D_LV2.CATEGORY_CODE = G.LV2_CATEGORY_CODE
		       AND D_LV2.CATEGORY_LV_NO = 2
		       AND D_LV2.USE_FLAG = A.USE_FLAG
			 INNER JOIN PDM_CATEGORY_M D_LV3
		    	ON D_LV3.CATEGORY_CODE = G.LV3_CATEGORY_CODE
		       AND D_LV3.CATEGORY_LV_NO = 3
		       AND D_LV3.USE_FLAG = A.USE_FLAG
			 INNER JOIN PDM_CATEGORY_M D_LV4
		    	ON D_LV4.CATEGORY_CODE = G.LV4_CATEGORY_CODE
		       AND D_LV4.CATEGORY_LV_NO = 4
		       AND D_LV4.USE_FLAG = A.USE_FLAG
			  LEFT JOIN (SELECT PRSSUB.*
			               FROM
								(SELECT PMM.PDP_ID
				                       ,MPM.PROMOTION_TAG_VAL
				                   FROM PRM_PROMOTION_PRODUCT_R PMM
				                  INNER JOIN PRM_PROMOTION_M MPM
				                     ON PMM.PROMOTION_ID = MPM.PROMOTION_ID
				                    AND PMM.SITE_CODE = MPM.SITE_CODE
				                  WHERE MPM.SITE_CODE = #{siteCode}
				                    AND MPM.USE_FLAG = 'Y'
				                    AND DATE_FORMAT(#{today},'%Y%m%d') BETWEEN MPM.PROMOTION_EXP_BEGIN_DATE AND MPM.PROMOTION_EXP_END_DATE
				                    AND PMM.USE_FLAG = 'Y'
				                    AND IFNULL(MPM.PROMOTION_NONEXP_FLAG,'N') <![CDATA[<>]]> 'Y'
				                    ORDER BY PMM.PROMOTION_PAGE_PRODUCT_SEQ IS NULL ASC, PMM.PROMOTION_PAGE_PRODUCT_SEQ ASC
			                      ) PRSSUB
	               				GROUP BY PRSSUB.PDP_ID
			                ) PRS
			       ON A.PDP_ID = PRS.PDP_ID
			WHERE A.SITE_CODE = #{siteCode}
			  AND A.BIZ_TYPE_CODE = #{bizTypeCode}
			 <if test='bundleListFlag == "Y"'>
			  AND A.PDP_TYPE_CODE IN ('B','O')
			 </if>
			  AND A.USE_FLAG = 'Y'
			GROUP BY A.PDP_ID
		)AAA
 <choose>
  <when test='bundleListFlag == "Y"'>
	ORDER BY AAA.CREATION_DATE DESC, AAA.PRODUCT_NM ASC
   <if test="viewAll == null or viewAll == ''">
	LIMIT  #{startNo}, #{pageCnt}
   </if>
  </when>
  <otherwise>
	LIMIT 10
  </otherwise>
 </choose>
    </select>

    <select id="selectBundleElInfo" parameterType="com.lge.d2x.domain.bundle.v1.model.BundleElInfoRequestVO" 
    resultType="com.lge.d2x.domain.bundle.v1.model.BundleElInfoResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.bundle.v1.service.BundleService.selectBundleElinfo */
               A.BUNDLE_PDP_ID
             , A.ELABEL_GRD_CODE
             , B.EL_DOC_TYPE_CODE
             , CASE WHEN B.EL_DOC_TYPE_CODE = 'EL'
                    THEN 'FL'
                    ELSE 'FE'
               END AS F_ENERGY_LABEL_CATEGORY
             , B.PIS_DOC_TYPE_CODE
             , B.EL_TYPE_CODE
          FROM (SELECT C.PDP_ID
                     , C.SITE_CODE
                     , C.BUNDLE_PDP_ID
                     , C.USE_FLAG
                     , F.LV1_PRODUCT_CODE
                     , F.LV2_PRODUCT_CODE
                     , F.ELABEL_GRD_CODE
                     , C.DSP_SEQ
                  FROM DSP_PRODUCT_BUNDLE_D C
                  LEFT JOIN (SELECT D.PDP_ID
                                  , D.SITE_CODE
                                  , D.ELABEL_GRD_CODE
                                  , E.LV1_PRODUCT_CODE
                                  , E.LV2_PRODUCT_CODE
                               FROM DSP_PDP_M D
                              INNER JOIN PDM_PRODUCT_M E
                                 ON D.SKU_ID = E.SKU_ID
                                AND D.BIZ_TYPE_CODE = E.BIZ_TYPE_CODE) F
                    ON C.BUNDLE_PDP_ID = F.PDP_ID
                   AND C.SITE_CODE = F.SITE_CODE) A
          LEFT JOIN PDM_PRODUCT_EL_PIS_R B
            ON A.SITE_CODE = B.SITE_CODE
           AND A.LV1_PRODUCT_CODE = B.LV1_EL_PRODUCT_CODE
           AND A.LV2_PRODUCT_CODE = B.LV2_EL_PRODUCT_CODE
         WHERE A.SITE_CODE = #{siteCode}
           AND A.USE_FLAG = 'Y'
           AND A.PDP_ID = #{pdpId}
         ORDER BY A.ELABEL_GRD_CODE DESC, A.DSP_SEQ
    </select>
</mapper>