package com.lge.d2x.domain.support.v1.rescontroller;

import com.lge.d2x.domain.support.v1.model.AscInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.AscInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.BasicPromotionWtyExpireRequestVO;
import com.lge.d2x.domain.support.v1.model.BasicPromotionWtyExpireResponseVO;
import com.lge.d2x.domain.support.v1.model.BundleGsriFileInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.CmsDocumentInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ExtendedwtyModelRequestVO;
import com.lge.d2x.domain.support.v1.model.ExtendedwtyModelResponseVO;
import com.lge.d2x.domain.support.v1.model.FindModelNumberRequestVO;
import com.lge.d2x.domain.support.v1.model.FindModelNumberResponseVO;
import com.lge.d2x.domain.support.v1.model.GameAppInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GameAppInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpAscAllRequestVO;
import com.lge.d2x.domain.support.v1.model.GpAscAllResponseVO;
import com.lge.d2x.domain.support.v1.model.GpAscListDistanceBrRequestVO;
import com.lge.d2x.domain.support.v1.model.GpAscListDistanceBrResponseVO;
import com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpModelDetailRequestVO;
import com.lge.d2x.domain.support.v1.model.GpModelDetailResponseVO;
import com.lge.d2x.domain.support.v1.model.GpModelListRequestVO;
import com.lge.d2x.domain.support.v1.model.GpModelListResponseVO;
import com.lge.d2x.domain.support.v1.model.GpRepairableProductInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpRepairableProductInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeRequestVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeResponseVO;
import com.lge.d2x.domain.support.v1.model.GpWtyInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpWtyInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.KmProductRequestVO;
import com.lge.d2x.domain.support.v1.model.KmProductResponseVO;
import com.lge.d2x.domain.support.v1.model.ModelWtyInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ModelWtyInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PdmProductInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PdmProductInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PreferenceCountryRequestVO;
import com.lge.d2x.domain.support.v1.model.PreferenceCountryResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductValidInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductValidInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PurchasePlaceInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PurchasePlaceInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.SvdSiteModelListRequestVO;
import com.lge.d2x.domain.support.v1.model.SvdSiteModelListResponseVO;
import com.lge.d2x.domain.support.v1.model.SympTomInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.SympTomInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.TypeOfInquiryProductResponseVO;
import com.lge.d2x.domain.support.v1.service.SupportService;
import com.lge.d2xfrm.model.common.CachedComLocaleCodeVO;
import com.lge.d2xfrm.model.common.D2xCommonResponseVO;
import com.lge.d2xfrm.util.common.D2xResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/support/v1")
@Tag(name = "SupportRestController", description = "Support 정보를 조회하는 컨트롤러이다")
public class SupportRestController {
    private final SupportService supportService;

    @GetMapping("/gp-model-detail")
    @Operation(summary = "GP 모델 상세정보 조회", description = "GP 모델 상세정보 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<GpModelDetailResponseVO>> gpModelDetail(
            @Valid GpModelDetailRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(supportService.gpModelDetail(req));
    }

    @GetMapping("/buyer-model-code-by-sales-code")
    @Operation(
            summary = "판매모델코드에 해당하는 Buyer Model 코드 조회",
            description = "판매모델코드에 해당하는 Buyer Model 코드 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<GpModelDetailResponseVO>> buyerModelCodeBySalesCode(
            @Valid GpModelDetailRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.buyerModelCodeBySalesCode(req));
    }

    @GetMapping("/inquiry-parts-by-model-affiliate")
    @Operation(summary = "모델에 해당하는 계열사코드 정보조회", description = "모델에 해당하는 계열사코드 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<GpModelDetailResponseVO>>
            inquiryPartsByModelAffiliate(@Valid GpModelDetailRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                supportService.inquiryPartsByModelAffiliate(req));
    }

    @GetMapping("/km-product-code")
    @Operation(summary = "KM 제품 코드 조회", description = "KM 제품 코드 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<KmProductResponseVO>> kmProductCode(
            @Valid KmProductRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(supportService.kmProductCode(req));
    }

    @GetMapping("/km-product-code-list")
    @Operation(summary = "KM제품 코드 목록 조회", description = "KM제품 코드 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<KmProductResponseVO>>> kMProductCodeList(
            @Valid KmProductRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(supportService.kMProductCodeList(req));
    }

    @GetMapping("/retrieve-product-gsri-file-info")
    @Operation(summary = "GSRI 제품파일정보조회", description = "GSRI 제품파일정보조회한다.")
    public ResponseEntity<D2xCommonResponseVO<ProductGsriFileInfoResponseVO>>
            retrieveProductGsriFileInfo(@Valid ProductGsriFileInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveProductGsriFileInfo(req));
    }

    @GetMapping("/retrieve-bundle-gsri-file-info")
    @Operation(summary = "GSRI 번들제품파일정보조회", description = "GSRI 번들제품파일정보조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<BundleGsriFileInfoResponseVO>>>
            retrieveBundleGsriFileInfo(@Valid ProductGsriFileInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveBundleGsriFileInfo(req));
    }

    @GetMapping("/retrieve-pdf-gsri-spec-info")
    @Operation(summary = "PDF 다운로드,GSRI목록 , 스펙 정보조회", description = "PDF 다운로드,GSRI목록 , 스펙 정보조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PdfGsriSpecInfoResponseVO>> retrievePdfDownloadInfo(
            @Valid PdfGsriSpecInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrievePdfGsriSpecInfo(req));
    }

    @GetMapping("/retrieve-product-manual-software-info")
    @Operation(summary = "Manual , Software 정보 조회", description = "Manual , Software 정보 조회.")
    public ResponseEntity<D2xCommonResponseVO<ProductManualSoftwareInfoResponseVO>>
            retrieveProductManualSoftwareInfo(@Valid ProductManualSoftwareInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveProductManualSoftwareInfo(req));
    }

    @GetMapping("/retrieve-model-wty-info-list")
    @Operation(
            summary = "판매코드에 해당하는 Model , Warranty 목록정보조회",
            description = "판매코드에 해당하는 Model , Warranty 목록정보조회.")
    public ResponseEntity<D2xCommonResponseVO<List<ModelWtyInfoResponseVO>>>
            retrieveModelWtyInfoList(@Valid ModelWtyInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveModelWtyInfoList(req));
    }

    @GetMapping("/retrieve-mc-support-manual-docs-list")
    @Operation(summary = "MC support 메뉴얼,문서목록", description = "MC support 메뉴얼,문서목록.")
    public ResponseEntity<D2xCommonResponseVO<List<CmsDocumentInfoResponseVO>>>
            retrieveMcSupportManualsDocsList(@Valid ProductManualSoftwareInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveMcSupportManualsDocsList(req));
    }

    @GetMapping("/retrieve-asc-info")
    @Operation(summary = "ASC 정보 조회", description = "ASC 정보 조회.")
    public ResponseEntity<D2xCommonResponseVO<AscInfoResponseVO>> retrieveASCInfo(
            @Valid AscInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveASCInfo(req));
    }

    @GetMapping("/retrieve-asc-list")
    @Operation(summary = "ASC 목록 조회", description = "ASC 목록 조회.")
    public ResponseEntity<D2xCommonResponseVO<List<AscInfoResponseVO>>> retrieveASCList(
            @Valid AscInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveASCList(req));
    }

    @GetMapping("/retrieve-gp-warranty-information")
    @Operation(summary = "GP WTY 정보 조회", description = "GP WTY 정보 조회")
    public ResponseEntity<D2xCommonResponseVO<GpWtyInfoResponseVO>> retrieveGpWarrantyInformation(
            @Valid GpWtyInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpWarrantyInformation(req));
    }

    @GetMapping("/select-extendedwty-model-list")
    @Operation(summary = "확장 WTY 모델 목록 조회", description = "확장 WTY 모델 목록 조회한다")
    public ResponseEntity<D2xCommonResponseVO<ExtendedwtyModelResponseVO>>
            selectExtendedwtyModelList(@Valid ExtendedwtyModelRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.selectExtendedwtyModelList(req));
    }

    @GetMapping("/retrieve-genuine-wty-country-list")
    @Operation(summary = "정품인증국가 목록조회", description = "정품인증국가 목록조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductValidInfoResponseVO>>>
            retrieveGenuineWtyConutryList(@Valid ProductValidInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGenuineWtyConutryList(req));
    }

    @GetMapping("/retrieve-genuine-wty-product-combo")
    @Operation(summary = "정품인증국가 목록조회", description = "정품인증국가 목록조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductValidInfoResponseVO>>>
            retrieveGenuineWtyProductCombo(@Valid ProductValidInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGenuineWtyProductCombo(req));
    }

    @GetMapping("/retrieve-genuine-wty-product-list")
    @Operation(summary = "정품인증제품 목록 조회", description = "정품인증제품 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductValidInfoResponseVO>>>
            retrieveGenuineWtyProductList(@Valid ProductValidInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGenuineWtyProductList(req));
    }

    @GetMapping("/retrieve-validation-model")
    @Operation(summary = "Validation 모델 정보 조회", description = "Validation 모델 정보 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<ProductValidInfoResponseVO>> retrieveValidationModel(
            @Valid ProductValidInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveValidationModel(req));
    }

    @GetMapping("/retrieve-gp-gsfs-category-list")
    @Operation(summary = "Gp Gsfs 카테고리(LV2) 목록 조회", description = "Gp Gsfs 카테고리(LV2) 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveGpGsfsCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpGsfsCategoryList(req));
    }

    @GetMapping("/retrieve-gp-gsfs-sub-category-list")
    @Operation(summary = "Gp Gsfs 서브카테고리(LV3) 목록 조회", description = "Gp Gsfs 서브카테고리(LV3) 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveGpGsfsSubCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpGsfsSubCategoryList(req));
    }

    @GetMapping("/retrieve-preference-country-list")
    @Operation(summary = "Preference 국가정보조회", description = "Preference 국가정보조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PreferenceCountryResponseVO>>>
            retrievePreferenceCountryList(@Valid PreferenceCountryRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrievePreferenceCountryList(req));
    }

    @GetMapping("/retrieve-preference-gp-country-list")
    @Operation(summary = "Preference GP 국가정보조회", description = "Preference GP  국가정보조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PreferenceCountryResponseVO>>>
            retrievePreferenceGpCountryList(@Valid PreferenceCountryRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrievePreferenceGpCountryList(req));
    }

    @GetMapping("/retrieve-gp-category-list")
    @Operation(summary = "Gp 카테고리(LV2) 목록 조회", description = "Gp 카테고리(LV2) 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>> retrieveGpCategoryList(
            @Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveGpCategoryList(req));
    }

    @GetMapping("/retrieve-customer-product-list")
    @Operation(summary = "고객모델제품목록", description = "고객모델제품목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GpModelDetailResponseVO>>>
            retrieveCustomerProductList(@Valid GpModelDetailRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveCustomerProductList(req));
    }

    @GetMapping("/retrieve-svd-purchase-placem-list")
    @Operation(summary = "Svd 구매정보 목록", description = "Svd 구매정보 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PurchasePlaceInfoResponseVO>>>
            retrieveSvdPurchasePlaceMList(@Valid PurchasePlaceInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveSvdPurchasePlaceMList(req));
    }

    @GetMapping("/retrieve-basic-promotion-wty-expire-list")
    @Operation(summary = "베이직프로모션보증만료내역 목록", description = "베이직프로모션보증만료내역 목록을 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<BasicPromotionWtyExpireResponseVO>>>
            retrieveBasicPromotionWtyExpireList(@Valid BasicPromotionWtyExpireRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveBasicPromotionWtyExpireList(req));
    }

    @GetMapping("/retrieve-pdm-product-list")
    @Operation(summary = "Pdm 제품 목록", description = "Pdm 제품 목록을 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PdmProductInfoResponseVO>>>
            retrievePdmProductList(@Valid PdmProductInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrievePdmProductList(req));
    }

    @GetMapping("/retrieve-gp-repair-service-type-info")
    @Operation(summary = "Gp 수리 서비스 유형 정보조회", description = "Gp 수리 서비스 유형 정보조회.")
    public ResponseEntity<D2xCommonResponseVO<GpServiceTypeInfoResponseVO>>
            retrieveGpRepairServiceTypeInfo(@Valid GpServiceTypeInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpRepairServiceTypeInfo(req));
    }

    @GetMapping("/retrieve-game-app-list")
    @Operation(summary = "Game App 목록", description = "Game App 목록을 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GameAppInfoResponseVO>>> retrieveGameAppList(
            @Valid GameAppInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveGameAppList(req));
    }

    @GetMapping("/retrieve-symptom-list")
    @Operation(summary = "증상목록", description = "증상목록을 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SympTomInfoResponseVO>>> retrieveSymptomList(
            @Valid SympTomInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveSymptomList(req));
    }

    @GetMapping("/retrieve-symptom-list-3depth")
    @Operation(summary = "증상목록 3Depth", description = "증상목록 3Depth를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SympTomInfoResponseVO>>>
            retrieveSymptomList3Depth(@Valid SympTomInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveSymptomList3Depth(req));
    }

    @GetMapping("/retrieve-sub-symptom-list")
    @Operation(summary = "부증상 목록", description = "부증상 목록을 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SympTomInfoResponseVO>>> retrieveSubSymptomList(
            @Valid SympTomInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveSubSymptomList(req));
    }

    @GetMapping("/retrieve-sub-symptom-list-3depth")
    @Operation(summary = "부증상 목록 3Depth", description = "부증상 목록 3Depth를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<SympTomInfoResponseVO>>>
            retrieveSubSymptomList3Depth(@Valid SympTomInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveSubSymptomList3Depth(req));
    }

    @GetMapping("/retrieve-repair-state-list")
    @Operation(summary = "Repair State 목록 조회", description = "Repair State 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<AscInfoResponseVO>>> retrieveRepairStateList(
            @Valid AscInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveRepairStateList(req));
    }

    @GetMapping("/retrieve-gp-model-cate-list")
    @Operation(summary = "GP 모델 상세정보 목록 조회", description = "GP 모델 상세정보 목록 조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpModelDetailResponseVO>>> gpModelCateList(
            @Valid GpModelDetailRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.gpModelCateList(req));
    }

    @GetMapping("/retrieve-sub-category-br")
    @Operation(summary = "Br Sub 카테고리 목록 조회", description = "Br Sub 카테고리 목록 조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>> retrieveSubCategoryBR(
            @Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveSubCategoryBR(req));
    }

    @GetMapping("/retrieve-gp-repairable-product")
    @Operation(summary = "Gp Repairable 제품 목록 조회", description = "Gp Repairable 제품 목록 조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpRepairableProductInfoResponseVO>>>
            retrieveGpRepairableProduct(@Valid GpRepairableProductInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpRepairableProduct(req));
    }

    @GetMapping("/retrieve-gp-asc-list-distance-br")
    @Operation(summary = "Gp 서비스 센터 Distance조회", description = "Gp 서비스 센터 Distance조회")
    public ResponseEntity<D2xCommonResponseVO<GpAscListDistanceBrResponseVO>>
            retrieveGpAscListDistanceBr(@Valid GpAscListDistanceBrRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpAscListDistanceBr(req));
    }

    @GetMapping("/retrieve-gp-asc-list-all")
    @Operation(summary = "Gp Asc 목록 전체 조회", description = "Gp Asc 목록 전체 조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpAscAllResponseVO>>> retrieveGpAscListAll(
            @Valid GpAscAllRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveGpAscListAll(req));
    }

    @GetMapping("/retrieve-gp-category-name-by-kM-product-code")
    @Operation(summary = "Gp 카테고리 명 조회(By KM 제품 코드) ", description = "Gp 카테고리 명 조회(By KM 제품 코드)")
    public ResponseEntity<D2xCommonResponseVO<List<KmProductResponseVO>>>
            retrieveGpCategoryNameByKMProductCode(@Valid KmProductRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpCategoryNameByKMProductCode(req));
    }

    @GetMapping("/retrieve-svd-site-model-list")
    @Operation(summary = "사이트에 해당하는 모델 목록조회", description = "사이트에 해당하는 모델 목록조회")
    public ResponseEntity<D2xCommonResponseVO<List<SvdSiteModelListResponseVO>>>
            retrieveSvdSiteModelList(@Valid SvdSiteModelListRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveSvdSiteModelList(req));
    }

    @GetMapping("/retrieve-super-category-one")
    @Operation(
            summary = "카테고리코드(lv2CategoryCode)에 해당하는 Super카테고리 코드 조회(lv1CategoryCode)",
            description = "카테고리코드(lv2CategoryCode)에 해당하는 Super카테고리 코드 조회(lv1CategoryCode)")
    public ResponseEntity<D2xCommonResponseVO<GpCateInfoResponseVO>> retrieveSuperCategoryOne(
            @Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveSuperCategoryOne(req));
    }

    @GetMapping("/retrieve-gp-model-list")
    @Operation(summary = "Gp Model 목록조회", description = "Gp Model목록조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpModelListResponseVO>>> retrieveGpModelList(
            @Valid GpModelListRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveGpModelList(req));
    }

    @GetMapping("/retrieve-gp-gsfs-super-category-list")
    @Operation(summary = "Gp Gsfs lv1 카테고리 목록조회", description = "Gp Gsfs lv1 카테고리 목록조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveGpGsfsSuperCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpGsfsSuperCategoryList(req));
    }

    @GetMapping("/retrieve-call-super-category-list")
    @Operation(summary = "Call lv1 카테고리 목록조회", description = "Call lv1 카테고리 목록조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveCallSuperCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveCallSuperCategoryList(req));
    }

    @GetMapping("/retrieve-call-category-list")
    @Operation(summary = "Call lv2 카테고리 목록조회", description = "Call lv2 카테고리 목록조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>> retrieveCallCategoryList(
            @Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveCallCategoryList(req));
    }

    @GetMapping("/retrieve-call-sub-category-list")
    @Operation(summary = "Call lv3 카테고리 목록조회", description = "Call lv3 카테고리 목록조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveCallSubCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveCallSubCategoryList(req));
    }

    @GetMapping("/retrieve-gp-sub-category-list")
    @Operation(summary = "Gp Sub(lv3) 카테고리 목록 조회", description = "Gp Sub(lv3) 카테고리 목록 조회")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveGpSubCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveGpSubCategoryList(req));
    }

    @Operation(summary = "GP 서비스 유형 조회", description = "GP 서비스 유형 조회을 조회한다.")
    @GetMapping(path = "/retrieve-gp-service-type")
    public ResponseEntity<D2xCommonResponseVO<GpServiceTypeResponseVO>> retrieveGpSubCategoryList(
            @Valid GpServiceTypeRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveGpServiceType(req));
    }

    @Operation(summary = "TOP카테고리 정보 조회", description = "FindMyModel의 TOP카테고리 정보를 조회한다")
    @GetMapping(path = "/retrieve-top-find-category-list")
    public ResponseEntity<D2xCommonResponseVO<List<FindModelNumberResponseVO>>>
            retrieveTopFindCategoryList(@Valid FindModelNumberRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveTopFindCategoryList(req));
    }

    @Operation(summary = "카테고리 정보 조회", description = "FindMyModel의 카테고리 정보를 조회한다")
    @GetMapping(path = "/retrieve-find-category-list")
    public ResponseEntity<D2xCommonResponseVO<List<FindModelNumberResponseVO>>>
            retrieveFindCategoryList(@Valid FindModelNumberRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveFindCategoryList(req));
    }

    @Operation(summary = "FindMyModel 상세정보 조회", description = "FindMyModel의 상세 정보를 조회한다")
    @GetMapping(path = "/retrieve-find-model-number")
    public ResponseEntity<D2xCommonResponseVO<FindModelNumberResponseVO>> retrieveFindmymodelNumber(
            @Valid FindModelNumberRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveFindmymodelNumber(req));
    }

    @GetMapping("/retrieve-findservicecenter-super-category-list")
    @Operation(
            summary = "FindServiceCenter 카테고리(LV1) 목록 조회",
            description = "FindServiceCenter 카테고리(LV1) 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveFindServiceCenterSuperCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveFindServiceCenterSuperCategoryList(req));
    }

    @GetMapping("/retrieve-findservicecenter-category-list")
    @Operation(
            summary = "FindServiceCenter 카테고리(LV2) 목록 조회",
            description = "FindServiceCenter 카테고리(LV2) 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveFindServiceCenterCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveFindServiceCenterCategoryList(req));
    }

    @GetMapping("/retrieve-locate-repair-center-category-list")
    @Operation(
            summary = "LOCATE_REPAIR_CENTER 카테고리(LV2) 목록 조회",
            description = "LOCATE_REPAIR_CENTER 카테고리(LV2) 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveLocateRepairCenterCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveLocateRepairCenterCategoryList(req));
    }

    @GetMapping("/retrieve-locate-repair-center-sub-category-list")
    @Operation(
            summary = "LOCATE_REPAIR_CENTER 카테고리(LV3) 목록 조회",
            description = "LOCATE_REPAIR_CENTER 카테고리(LV3) 목록 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<GpCateInfoResponseVO>>>
            retrieveLocateRepairCenterSubCategoryList(@Valid GpCateInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveLocateRepairCenterSubCategoryList(req));
    }

    @GetMapping("/retrieve-repair-city-list-br")
    @Operation(summary = "City 목록 조회", description = "City 목록 조회.")
    public ResponseEntity<D2xCommonResponseVO<List<AscInfoResponseVO>>> retrieveRepairCityListBR(
            @Valid AscInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(supportService.retrieveRepairCityListBR(req));
    }

    @GetMapping("/retrieve-gp-type-of-inquiry-list")
    @Operation(summary = "Type Of Inquiry 목록 조회", description = "Type Of Inquiry 목록 조회.")
    public ResponseEntity<D2xCommonResponseVO<List<TypeOfInquiryProductResponseVO>>>
            retrieveGpTypeOfInquiryList(@Valid CachedComLocaleCodeVO req) {
        return D2xResponseUtil.createSuccessResponse(
                supportService.retrieveGpTypeOfInquiryList(req));
    }
}
