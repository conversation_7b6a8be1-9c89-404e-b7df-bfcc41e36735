package com.lge.d2x.interfaces.system.pds.whereToBuy.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemPdsWtbCategoryIdRequestVO {
    @Schema(description = "PDP아이디", example = "MD08588000")
    private String pdpId;

    @Schema(description = "비즈니스유형코드", example = "B2C")
    private String bizTypeCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "사이트코드", example = "UK")
    private String siteCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "카테고리 스텐다드 flag", example = "N")
    private String categoryStandardFlag;
}
