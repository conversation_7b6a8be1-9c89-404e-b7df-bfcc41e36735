package com.lge.d2x.domain.gnbNotification.v1.restcontroller;

import com.lge.d2x.domain.gnbNotification.v1.model.GnbNotificationRequestVO;
import com.lge.d2x.domain.gnbNotification.v1.model.GnbNotificationResponseVO;
import com.lge.d2x.domain.gnbNotification.v1.service.GnbNotificationService;
import com.lge.d2xfrm.model.common.D2xCommonResponseVO;
import com.lge.d2xfrm.util.common.D2xResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/gnbNotification/v1")
@RequiredArgsConstructor
@Tag(name = "GnbNotificationRestController", description = "GnbNotification API")
public class GnbNotificationRestController {
    private final GnbNotificationService gnbNotificationService;

    @Operation(summary = "GnbNotificationBar 리스트", description = "GnbNotificationBar 리스트 정보를 조회한다.")
    @GetMapping(path = "/gnb-notification-bar-list")
    public ResponseEntity<D2xCommonResponseVO<List<GnbNotificationResponseVO>>>
            gnbNotificationBarList(@Valid GnbNotificationRequestVO requestVO) {

        return D2xResponseUtil.createSuccessResponse(
                gnbNotificationService.getGnbNotificationBar(requestVO));
    }
}
