package com.lge.d2x.domain.pdpInfo.v1.repository.pdsmgr;

import com.lge.d2x.domain.pdpInfo.v1.model.AccessoryProductRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.GsriFileInfoResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.KeyfeaturesRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.KeyfeaturesResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialDetailEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetPanelTypeEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetProductEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpEnergyLabelInfoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpEnergyLabelInfoResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductAccessoryRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductEpsRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductEpsResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductGsriFileInfoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductIconListRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductIconListResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductPanelEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductResourceRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductResourceResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductSpecBundleSimpleListRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductSpecBundleSimpleListResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductVideoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductVideoResponseVO;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PdpInfoRepository {

    PdpResponseVO selectPdpBasicInfo(PdpRequestVO pdpRequestVO);

    PdpResponseVO selectPdpBasicInfoNotStandard(PdpRequestVO pdpRequestVO);

    List<PdpCategoryResponseVO> selectPdpCategoryInfo(PdpCategoryRequestVO pdpCategoryRequestVO);

    List<PdpCategoryResponseVO> selectOldPdpCategoryInfo(
            PdpCategoryRequestVO oldPdpCategoryRequestVO);

    PdpEnergyLabelInfoResponseVO selectPdpEnergyLabelInfo(
            PdpEnergyLabelInfoRequestVO pdpEnergyLabelInfoRequestVO);

    List<KeyfeaturesResponseVO> selectKeyfeaturesList(
            KeyfeaturesRequestVO keyFeaturesInfoRequestVO);

    List<PdpSiblingResponseVO> selectPdpSiblingDataList(
            PdpSiblingRequestVO pdpSiblingInfoRequestVO);

    PdpUserReviewRatingResponseVO selectPdpUserReviewRatingData(
            PdpUserReviewRatingRequestVO pdpSiblingInfoRequestVO);

    PdpUserReviewRatingResponseVO selectPdpUserReviewSiblingRatingData(
            PdpUserReviewRatingRequestVO pdpSiblingInfoRequestVO);

    List<Map<String, Object>> selectAccessoryProductList(
            AccessoryProductRequestVO accessoryProductListRequestVO);

    List<Map<String, Object>> selectProductAccessoryList(
            ProductAccessoryRequestVO productAccessoryRequestVO);

    List<Map<String, Object>> selectProductAccessoryListNotStandard(
            ProductAccessoryRequestVO productAccessoryRequestVO);

    List<ProductIconListResponseVO> selectProductIconList(
            ProductIconListRequestVO productIconListRequestVO);

    ProductPanelEntityVO selectProductPanel(ProductPanelEntityVO productPanelVO);

    ObjetProductEntityVO selectObjetProduct(ObjetProductEntityVO objetProductVO);

    List<ObjetMaterialEntityVO> selectObjetMaterialList(ObjetMaterialEntityVO objetMaterialVO);

    List<ObjetMaterialDetailEntityVO> selectObjetMaterialDetailList(
            ObjetMaterialDetailEntityVO objetMaterialDetailVO);

    List<ObjetPanelTypeEntityVO> selectObjetPanelTypeList(ObjetPanelTypeEntityVO objetPanelTypeVO);

    List<ProductBundleResponseVO> selectProductBundleList(
            ProductBundleRequestVO productBundleListVO);

    List<ProductVideoResponseVO> selectProductVideoInfo(
            ProductVideoRequestVO productVideoRequestVO);

    PdpResponseVO selectMtsPdpBasicInfo(PdpRequestVO pdpRequestVO);

    PdpResponseVO selectMtsPdpBasicInfoNotStandard(PdpRequestVO pdpRequestVO);

    List<PdpSiblingResponseVO> selectMtsPdpSiblingDataList(
            PdpSiblingRequestVO pdpSiblingInfoRequestVO);

    List<ObjetMaterialDetailEntityVO> selectMtsObjetMaterialDetailList(
            ObjetMaterialDetailEntityVO objetMaterialDetailVO);

    List<ProductBundleResponseVO> selectMtsProductBundleList(
            ProductBundleRequestVO productBundleListVO);

    List<ProductSpecBundleSimpleListResponseVO> selectProductSpecBundleSimpleList(
            ProductSpecBundleSimpleListRequestVO requestVO);

    PdpSiblingResponseVO selectDefaultSiblingModelInfo(PdpSiblingRequestVO requestVO);

    List<ProductResourceResponseVO> selectProductResources(ProductResourceRequestVO requestVO);

    GsriFileInfoResponseVO selectProductGsriFileInfo(ProductGsriFileInfoRequestVO requestVO);

    List<Map<String, Object>> selectCompatibleProductsFor3Type(
            AccessoryProductRequestVO accessoryProductListRequestVO);

    ProductEpsResponseVO selectProductEpsInfo(ProductEpsRequestVO requestVO);
}
