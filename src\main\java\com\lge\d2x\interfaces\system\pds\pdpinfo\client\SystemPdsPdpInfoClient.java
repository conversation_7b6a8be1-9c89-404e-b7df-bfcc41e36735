package com.lge.d2x.interfaces.system.pds.pdpinfo.client;

import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsAccessoryProductRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsKeyfeaturesRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsKeyfeaturesResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsObjetMaterialDetailEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsObjetMaterialEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsObjetPanelTypeEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsObjetProductEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpCategoryRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpCategoryResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpSiblingRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpSiblingResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpUserReviewRatingRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpUserReviewRatingResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductAccessoryRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductBundleRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductBundleResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductEpsInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductEpsInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductPanelEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductResourceRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductResourceResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductSpecBundleSimpleListRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductSpecBundleSimpleListResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.url.SystemPdsPdpInfoUrlConstants;
import com.lge.d2xfrm.annotation.ClientCaching;
import com.lge.d2xfrm.client.configuration.D2xFeignClientConfiguration;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(
        name = "SystemPdsPdpInfoClient",
        url = "${client.url.system.pds}",
        configuration = D2xFeignClientConfiguration.class)
public interface SystemPdsPdpInfoClient {
    @ClientCaching(name = "getProductBasicInfo", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PDP_BASIC_INFO)
    SystemPdsPdpResponseVO getProductBasicInfo(@SpringQueryMap SystemPdsPdpRequestVO requestVO);

    @ClientCaching(name = "getMtsProductBasicInfo", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_MTS_PDP_BASIC_INFO)
    SystemPdsPdpResponseVO getMtsProductBasicInfo(@SpringQueryMap SystemPdsPdpRequestVO requestVO);

    @ClientCaching(name = "getEnergyLabelInfo", ttlMinutesExpiredAt = 7)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PDP_ENERGY_LABEL_INFO)
    SystemPdsPdpEnergyLabelInfoResponseVO getEnergyLabelInfo(
            @SpringQueryMap SystemPdsPdpEnergyLabelInfoRequestVO requestVO);

    @ClientCaching(name = "getPdpCategoryInfo", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PDP_CATEGORY_INFO)
    SystemPdsPdpCategoryResponseVO getPdpCategoryInfo(
            @SpringQueryMap SystemPdsPdpCategoryRequestVO requestVO);

    @ClientCaching(name = "getPdpAllCategoryInfo", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PDP_ALL_CATEGORY_INFO)
    List<SystemPdsPdpCategoryResponseVO> getPdpAllCategoryInfo(
            @SpringQueryMap SystemPdsPdpCategoryRequestVO requestVO);

    // @ClientCaching(name = "getPdpKeyFeatureList", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PDP_KEY_FEATURE_LIST)
    List<SystemPdsKeyfeaturesResponseVO> getPdpKeyFeatureList(
            @SpringQueryMap SystemPdsKeyfeaturesRequestVO requestVO);

    @ClientCaching(name = "getPdpSiblingDataList", ttlMinutesExpiredAt = 7)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PDP_SIBLING_DATA_LIST)
    List<SystemPdsPdpSiblingResponseVO> getPdpSiblingDataList(
            @SpringQueryMap SystemPdsPdpSiblingRequestVO requestVO);

    @ClientCaching(name = "getMtsPdpSiblingDataList", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_MTS_PDP_SIBLING_DATA_LIST)
    List<SystemPdsPdpSiblingResponseVO> getMtsPdpSiblingDataList(
            @SpringQueryMap SystemPdsPdpSiblingRequestVO requestVO);

    @ClientCaching(name = "getUserReviewSiblingRatings", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PDP_USER_REVIEW_SIBLING_RATING)
    SystemPdsPdpUserReviewRatingResponseVO getUserReviewSiblingRatings(
            @SpringQueryMap SystemPdsPdpUserReviewRatingRequestVO requestVO);

    // @ClientCaching(name = "getUserReviewRatings", ttlMinutesExpiredAt = 7)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PDP_USER_REVIEW_RATING)
    SystemPdsPdpUserReviewRatingResponseVO getUserReviewRatings(
            @SpringQueryMap SystemPdsPdpUserReviewRatingRequestVO requestVO);

    // @ClientCaching(name = "getSystemAccessoryProductList", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_ACCESSORY_PRODUCT_LIST)
    List<Map<String, Object>> getAccessoryProductList(
            @SpringQueryMap SystemPdsAccessoryProductRequestVO requestVO);

    @ClientCaching(name = "getProductAccessoryList", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PRODUCT_ACCESSORY_LIST)
    List<Map<String, Object>> getProductAccessoryList(
            @SpringQueryMap SystemPdsProductAccessoryRequestVO requestVO);

    @ClientCaching(name = "getProductIconList", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PRODUCT_ICON_LIST)
    List<SystemPdsProductIconListResponseVO> getProductIconList(
            @SpringQueryMap SystemPdsProductIconListRequestVO requestVO);

    // @ClientCaching(name = "getProductPanel", ttlMinutesExpiredAt = 7)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PRODUCT_PANEL)
    SystemPdsProductPanelEntityVO getProductPanel(
            @SpringQueryMap SystemPdsProductPanelEntityVO requestVO);

    @ClientCaching(name = "getObjetProduct", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_OBJET_PRODUCT)
    SystemPdsObjetProductEntityVO getObjetProduct(
            @SpringQueryMap SystemPdsObjetProductEntityVO requestVO);

    @ClientCaching(name = "getObjetMaterialList", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_OBJET_MATERIAL_LIST)
    List<SystemPdsObjetMaterialEntityVO> getObjetMaterialList(
            @SpringQueryMap SystemPdsObjetMaterialEntityVO requestVO);

    @ClientCaching(name = "getObjetMaterialDetailList", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_OBJET_MATERIAL_DETAIL_LIST)
    List<SystemPdsObjetMaterialDetailEntityVO> getObjetMaterialDetailList(
            @SpringQueryMap SystemPdsObjetMaterialDetailEntityVO requestVO);

    @ClientCaching(name = "getObjetPanelTypeList", ttlMinutesExpiredAt = 7)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_OBJET_PANEL_TYPE_LIST)
    List<SystemPdsObjetPanelTypeEntityVO> getObjetPanelTypeList(
            @SpringQueryMap SystemPdsObjetPanelTypeEntityVO requestVO);

    @ClientCaching(name = "getProductSpecBundleSimpleList", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PRODUCT_SPEC_BUNDLE_SIMPLE_LIST)
    List<SystemPdsProductSpecBundleSimpleListResponseVO> getProductSpecBundleSimpleList(
            @SpringQueryMap SystemPdsProductSpecBundleSimpleListRequestVO requestVO);

    @ClientCaching(name = "getDefaultSiblingProductInfo", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_DEFAULT_SIBLING_PRODUCT_INFO)
    SystemPdsPdpSiblingResponseVO getDefaultSiblingProductInfo(
            @SpringQueryMap SystemPdsPdpSiblingRequestVO requestVO);

    @ClientCaching(name = "getProductResources", ttlMinutesExpiredAt = 7)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PRODUCT_RESOURCES)
    List<SystemPdsProductResourceResponseVO> getProductResources(
            @SpringQueryMap SystemPdsProductResourceRequestVO requestVO);

    @ClientCaching(name = "getSystemProductBundleList", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PRODUCT_BUNDLE_LIST)
    List<SystemPdsProductBundleResponseVO> getProductBundleList(
            @SpringQueryMap SystemPdsProductBundleRequestVO requestVO);

    @ClientCaching(name = "getMtsProductBundleList", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_MTS_PRODUCT_BUNDLE_LIST)
    List<SystemPdsProductBundleResponseVO> getMtsProductBundleList(
            @SpringQueryMap SystemPdsProductBundleRequestVO requestVO);

    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_ACC_PRODUCT_LIST_FOR_3TYPE)
    List<Map<String, Object>> getAccProductsFor3Type(
            @SpringQueryMap SystemPdsAccessoryProductRequestVO requestVO);

    @ClientCaching(name = "getProductEpsInfo", ttlMinutesExpiredAt = 7)
    @GetMapping(value = SystemPdsPdpInfoUrlConstants.GET_PDPINFO_V1_PRODUCT_EPS_INFO)
    SystemPdsProductEpsInfoResponseVO getProductEpsInfo(
            @SpringQueryMap SystemPdsProductEpsInfoRequestVO requestVO);
}
