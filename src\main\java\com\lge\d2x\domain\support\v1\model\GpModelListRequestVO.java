package com.lge.d2x.domain.support.v1.model;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class GpModelListRequestVO {
    private List<String> obuCodeList;
    private List<String> categoryIdList;
    private String siteCode;
    private String localeCode;
    private String countryCode;
    private String euEcoCategoryDt;
    private String ecoCategoryFlag;
    private String customerNo;
    private String superCategoryId;
    private String categoryId;
    private String subCategoryId;
    private String pageFlag;
    private String page;
    private String searchCateType;
    private int countPerPage;
    private int offset;
    private String[] objetModelList;
}
