package com.lge.d2x.domain.pdpInfo.v1.service;

import com.lge.d2x.domain.pdpInfo.v1.model.AccessoryProductRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.GsriFileInfoResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.KeyfeaturesRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.KeyfeaturesResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialDetailEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetPanelTypeEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetProductEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpEnergyLabelInfoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpEnergyLabelInfoResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductAccessoryRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductEpsRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductEpsResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductGsriFileInfoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductIconListRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductIconListResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductPanelEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductResourceRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductResourceResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductSpecBundleSimpleListRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductSpecBundleSimpleListResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductVideoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductVideoResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.repository.pdsmgr.PdpInfoRepository;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Aspect
@Service
@RequiredArgsConstructor
public class PdpInfoService {

    private final PdpInfoRepository pdpRepository;

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public PdpResponseVO selectPdpBasicInfo(PdpRequestVO requestVO) {

        PdpResponseVO result;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = pdpRepository.selectPdpBasicInfo(requestVO);
        } else {
            result = pdpRepository.selectPdpBasicInfoNotStandard(requestVO);
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public PdpCategoryResponseVO selectPdpCategoryInfo(PdpCategoryRequestVO requestVO) {

        List<PdpCategoryResponseVO> result;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = pdpRepository.selectPdpCategoryInfo(requestVO);
        } else {
            result = pdpRepository.selectOldPdpCategoryInfo(requestVO);
        }

        return result.isEmpty() ? null : result.get(0);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<PdpCategoryResponseVO> selectPdpAllCategoryInfo(PdpCategoryRequestVO requestVO) {

        List<PdpCategoryResponseVO> result;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = pdpRepository.selectPdpCategoryInfo(requestVO);
        } else {
            result = pdpRepository.selectOldPdpCategoryInfo(requestVO);
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public PdpEnergyLabelInfoResponseVO selectPdpEnergyLabelInfo(
            PdpEnergyLabelInfoRequestVO requestVO) {

        return pdpRepository.selectPdpEnergyLabelInfo(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<KeyfeaturesResponseVO> selectKeyfeaturesList(KeyfeaturesRequestVO requestVO) {

        return pdpRepository.selectKeyfeaturesList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<PdpSiblingResponseVO> selectPdpSiblingDataList(PdpSiblingRequestVO requestVO) {

        return pdpRepository.selectPdpSiblingDataList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public PdpUserReviewRatingResponseVO selectPdpUserReviewRatingData(
            PdpUserReviewRatingRequestVO requestVO) {

        return pdpRepository.selectPdpUserReviewRatingData(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public PdpUserReviewRatingResponseVO selectPdpUserReviewSiblingRatingData(
            PdpUserReviewRatingRequestVO requestVO) {

        return pdpRepository.selectPdpUserReviewSiblingRatingData(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectAccessoryProductList(
            AccessoryProductRequestVO requestVO) {

        return pdpRepository.selectAccessoryProductList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectProductAccessoryList(
            ProductAccessoryRequestVO requestVO) {

        List<Map<String, Object>> result;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = pdpRepository.selectProductAccessoryList(requestVO);
        } else {
            result = pdpRepository.selectProductAccessoryListNotStandard(requestVO);
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductIconListResponseVO> selectProductIconList(
            ProductIconListRequestVO requestVO) {

        return pdpRepository.selectProductIconList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public ProductPanelEntityVO selectProductPanel(ProductPanelEntityVO productPanelVO) {

        return pdpRepository.selectProductPanel(productPanelVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public ObjetProductEntityVO selectObjetProduct(ObjetProductEntityVO objetProductVO) {

        return pdpRepository.selectObjetProduct(objetProductVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ObjetMaterialEntityVO> selectObjetMaterialList(
            ObjetMaterialEntityVO objetMaterialVO) {

        return pdpRepository.selectObjetMaterialList(objetMaterialVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ObjetMaterialDetailEntityVO> selectObjetMaterialDetailList(
            ObjetMaterialDetailEntityVO objetMaterialDetailVO) {

        return pdpRepository.selectObjetMaterialDetailList(objetMaterialDetailVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ObjetPanelTypeEntityVO> selectObjetPanelTypeList(
            ObjetPanelTypeEntityVO objetPanelTypeVO) {

        return pdpRepository.selectObjetPanelTypeList(objetPanelTypeVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductBundleResponseVO> selectProductBundleList(
            ProductBundleRequestVO productBundleListVO) {

        return pdpRepository.selectProductBundleList(productBundleListVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductSpecBundleSimpleListResponseVO> selectProductSpecBundleSimpleList(
            ProductSpecBundleSimpleListRequestVO requestVO) {

        return pdpRepository.selectProductSpecBundleSimpleList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductVideoResponseVO> selectProductVideoInfo(
            @Valid ProductVideoRequestVO productVideoRequestVO) {
        return pdpRepository.selectProductVideoInfo(productVideoRequestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public PdpSiblingResponseVO selectDefaultSiblingModelInfo(
            PdpSiblingRequestVO pdpSiblingRequestVO) {

        return pdpRepository.selectDefaultSiblingModelInfo(pdpSiblingRequestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<PdpSiblingResponseVO> selectMtsPdpSiblingDataList(PdpSiblingRequestVO requestVO) {

        return pdpRepository.selectMtsPdpSiblingDataList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public PdpResponseVO selectMtsPdpBasicInfo(PdpRequestVO requestVO) {

        PdpResponseVO result;

        if ("Y".equals(requestVO.getStandardFlag())) {
            result = pdpRepository.selectMtsPdpBasicInfo(requestVO);
        } else {
            result = pdpRepository.selectMtsPdpBasicInfoNotStandard(requestVO);
        }

        return result;
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ObjetMaterialDetailEntityVO> selectMtsObjetMaterialDetailList(
            ObjetMaterialDetailEntityVO objetMaterialDetailVO) {

        return pdpRepository.selectMtsObjetMaterialDetailList(objetMaterialDetailVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductBundleResponseVO> selectMtsProductBundleList(
            ProductBundleRequestVO productBundleListVO) {

        return pdpRepository.selectMtsProductBundleList(productBundleListVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<ProductResourceResponseVO> selectProductResources(
            ProductResourceRequestVO requestVO) {

        return pdpRepository.selectProductResources(requestVO);
    }

    @Transactional(readOnly = true)
    public GsriFileInfoResponseVO selectProductGsriFileInfo(
            ProductGsriFileInfoRequestVO requestVO) {

        return pdpRepository.selectProductGsriFileInfo(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectCompatibleProductsFor3Type(
            AccessoryProductRequestVO requestVO) {

        return pdpRepository.selectCompatibleProductsFor3Type(requestVO);
    }

    @Transactional(readOnly = true)
    public ProductEpsResponseVO selectProductEpsInfo(ProductEpsRequestVO req) {

        return pdpRepository.selectProductEpsInfo(req);
    }
}
