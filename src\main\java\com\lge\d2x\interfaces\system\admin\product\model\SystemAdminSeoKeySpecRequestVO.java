package com.lge.d2x.interfaces.system.admin.product.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemAdminSeoKeySpecRequestVO {
    @Schema(description = "지역 코드", example = "CA_EN")
    private String localeCode;

    @Schema(description = "모델 아이디", example = "MD06246916")
    private String modelId;
}
