package com.lge.d2x.domain.compare.v1.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lge.d2x.core.constants.CommonConstants;
import com.lge.d2x.domain.common.v1.service.CommonService;
import com.lge.d2x.domain.compare.v1.model.BundleFrameVO;
import com.lge.d2x.domain.compare.v1.model.CompareProductRequestVO;
import com.lge.d2x.domain.compare.v1.model.CompareProductResponseVO;
import com.lge.d2x.domain.compare.v1.model.CompareRequestVO;
import com.lge.d2x.domain.compare.v1.model.CompareResponseVO;
import com.lge.d2x.domain.compare.v1.model.CompareVO;
import com.lge.d2x.domain.compare.v1.model.ProductVO;
import com.lge.d2x.interfaces.system.admin.compare.client.SystemAdminCompareClient;
import com.lge.d2x.interfaces.system.admin.compare.model.SystemAdminCompareProductRequestVO;
import com.lge.d2x.interfaces.system.admin.compare.model.SystemAdminCompareProductResponseVO;
import com.lge.d2x.interfaces.system.admin.compare.model.SystemAdminCompareSpecRequestVO;
import com.lge.d2x.interfaces.system.admin.compare.model.SystemAdminCompareSpecResponseVO;
import com.lge.d2x.interfaces.system.admin.product.client.SystemAdminProductClient;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminFilterCountryListRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminFilterCountryListResponseVO;
import com.lge.d2x.interfaces.system.pds.compare.client.SystemPdsCompareClient;
import com.lge.d2x.interfaces.system.pds.compare.model.SystemPdsBundleCategoryRequestVO;
import com.lge.d2x.interfaces.system.pds.compare.model.SystemPdsBundleCategoryResponseVO;
import com.lge.d2x.interfaces.system.pds.compare.model.SystemPdsBundleModelIdRequestVO;
import com.lge.d2x.interfaces.system.pds.compare.model.SystemPdsBundleModelIdResponseVO;
import com.lge.d2x.interfaces.system.pds.compare.model.SystemPdsCompareProductInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.compare.model.SystemPdsCompareProductInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.client.SystemPdsPdpInfoClient;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpUserReviewRatingRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpUserReviewRatingResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListResponseVO;
import com.lge.d2x.interfaces.system.pds.product.client.SystemPdsProductClient;
import com.lge.d2x.interfaces.system.pds.product.model.SystemPdsProductRequestVO;
import com.lge.d2x.interfaces.system.pds.product.model.SystemPdsProductResponseVO;
import com.lge.d2x.interfaces.system.pds.productList.client.SystemPdsProductListClient;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsPdpIdListBySkuRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.client.SystemPdsSpecClient;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsDefaultKeySpecRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsDefaultKeySpecResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsKeySpecRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsKeySpecResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsMtsProductLgcomSpecRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsPdrSpecSchemaPart2RequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsPdrSpecSchemaPart2ResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductLgcomSpecRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductLgcomSpecResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductStandardSpecRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductStandardSpecResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsSpecSchemaPart2RequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsSpecSchemaPart2ResponseVO;
import com.lge.d2x.interfaces.system.pds.support.client.SystemPdsSupportClient;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoResponseVO;
import com.lge.d2xfrm.constants.CommonCodes;
import com.lge.d2xfrm.constants.enums.JobSeperateCodeEnum;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.CachedCodeRequestVO;
import com.lge.d2xfrm.model.common.CachedCommonCodeRequestVO;
import com.lge.d2xfrm.util.common.CachedDataUtil;
import com.lge.d2xfrm.util.common.DateUtil;
import com.lge.d2xfrm.util.common.RequestHeaderUtil;
import com.lge.d2xfrm.util.data.LDataUtil;
import com.lge.d2xfrm.util.data.data.LData;
import com.lge.d2xfrm.util.data.data.LMultiData;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CompareService {
    private final SystemPdsCompareClient systemPdsCompareClient;
    private final SystemPdsPdpInfoClient systemPdsPdpInfoClient;
    private final SystemPdsProductClient systemPdsProductClient;
    private final SystemPdsProductListClient systemPdsProductListClient;
    private final SystemPdsSpecClient systemPdsSpecClient;
    private final SystemPdsSupportClient systemPdsSupportClient;
    private final SystemAdminCompareClient systemAdminCompareClient;
    private final SystemAdminProductClient systemAdminProductClient;
    private final CachedDataUtil cachedDataUtil;
    private final CommonService commonService;

    public CompareResponseVO specCompareData(CompareRequestVO requestVO) {
        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String SHOP_CODE = CommonConstants.SHOP_CODE_D2C;
        String BIZ_TYPE = requestVO.getBizType();
        String SKU_LIST = requestVO.getSkuList();
        String countryCd = "";
        List<String> models = new ArrayList<>();
        List<Map<String, Object>> bundleProduct = null;
        List<Map<String, Object>> bundleCompare = null;
        List<Map<String, Object>> keySpecs = new ArrayList<>();
        LMultiData lmdata = new LMultiData();
        CompareResponseVO compareResult = new CompareResponseVO();
        List<Map<String, Object>> defaultKeySpecList = new ArrayList<>();

        try {

            /** 멀티샵 여부 */
            boolean mtsFlag = false;
            if (StringUtils.isNotBlank(requestVO.getMultishopCode())
                    && !CommonConstants.SHOP_CODE_D2C.equals(requestVO.getMultishopCode())) {
                mtsFlag = true;
                SHOP_CODE = requestVO.getMultishopCode();
            }

            /** 카테고리 표준화 국가 여부 조회 */
            String standardFlag = commonService.getCategoryStandardFlag();

            /** 타임존 조회 */
            String TIMEZONE =
                    cachedDataUtil
                            .getComLocaleCode(
                                    CachedCodeRequestVO.builder().siteCode(SITE_CODE).build())
                            .getTimezoneNm();

            String localeCode =
                    cachedDataUtil
                            .getComLocaleCode(CachedCodeRequestVO.builder().build())
                            .getLocaleCode();

            Map<String, Object> input = new HashMap<>();
            input.put("mtsFlag", mtsFlag);
            input.put("standardFlag", standardFlag);
            input.put("siteCode", SITE_CODE);
            input.put("bizType", BIZ_TYPE);
            input.put("timezone", TIMEZONE);
            input.put("shopCode", SHOP_CODE);
            input.put("localeCode", localeCode);
            input.put("countryCd", requestVO.getCountryCd());
            input.put("currentFlag", requestVO.getCurrentFlag());
            input.put("skuList", requestVO.getSkuList());

            LocalDateTime todayTime = LocalDateTime.now();
            String pattern = "yyyyMMdd HH:mm:ss";
            String today = "";
            try {
                todayTime = DateUtil.getCurrentTime(TIMEZONE, pattern);
                today = todayTime.toLocalDate().toString().replaceAll("-", "");
            } catch (Exception e) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                today = todayTime.atOffset(ZoneOffset.of("+09:00")).format(formatter);
            }

            input.put("today", today);

            String productLevel3NotUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference(
                                            "PRODUCT_LEVE3_CODE_NOT_USE_FLAG")),
                            CommonConstants.NO_FLAG);

            input.put("productLevel3NotUseFlag", productLevel3NotUseFlag);

            if (StringUtils.isNotBlank(requestVO.getCountryCd())) {
                countryCd = requestVO.getCountryCd();
            }

            if (StringUtils.isNotEmpty(SKU_LIST)) {
                /** ToDo.. 향후 멀티샵 RO 관련 customerGroup/customerGroupPassYn 관련 로직 추가 필요 */
                if (mtsFlag) {
                    models =
                            systemPdsProductListClient.getMtsPdpIdListBySkuId(
                                    SystemPdsPdpIdListBySkuRequestVO.builder()
                                            .standardFlag(standardFlag)
                                            .siteCode(SITE_CODE)
                                            .skuIds(SKU_LIST)
                                            .shopCode(SHOP_CODE)
                                            .build());
                } else {
                    models =
                            systemPdsProductListClient.getPdpIdListBySkuId(
                                    SystemPdsPdpIdListBySkuRequestVO.builder()
                                            .standardFlag(standardFlag)
                                            .siteCode(SITE_CODE)
                                            .skuIds(SKU_LIST)
                                            .build());
                }

                lmdata.put("modelId", models);
            }

            String keySpecUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference("KEY_SPEC_USE_FLAG")),
                            CommonConstants.YES_FLAG);

            String compareSpecType =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference(
                                            "COMPARE_CATEGORY_INTEGRATED")),
                            CommonConstants.NO_FLAG);

            String siblingModelTotal =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference(
                                            "REVIEW_SIBLING_MODEL_TOTAL_FLAG")),
                            CommonConstants.NO_FLAG);

            String obsCalculatorUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference(
                                            "OBS_CALCULATOR_USE_FLAG")),
                            CommonConstants.NO_FLAG);

            String obsInstallmentPromotionUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference(
                                            "OBS_INSTALLMENT_PROMOTION_USE_FLAG")),
                            CommonConstants.NO_FLAG);

            String emiUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference("EMI_USE_FLAG")),
                            CommonConstants.NO_FLAG);

            String obsEmiMsgFixedYn =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference("OBS_EMI_MSG_FIXED_YN")),
                            CommonConstants.NO_FLAG);

            String dummyImageUrl =
                    cachedDataUtil.getComSystemConfigurationCode(
                            CachedCodeRequestVO.ofComSysConfig("dummy_image_url"));

            input.put("emiUseFlag", emiUseFlag);
            input.put("obsEmiMsgFixedYn", obsEmiMsgFixedYn);
            input.put("siblingModelTotal", siblingModelTotal);
            input.put("compareSpecType", compareSpecType);
            input.put("dummyImageUrl", dummyImageUrl);

            if (0 < models.size()) {
                lmdata = retrieveValidModelIds(lmdata, input);

                if (ObjectUtils.isNotEmpty(lmdata) && ObjectUtils.isNotEmpty(lmdata.get("pdpId"))) {
                    /** Product data와 Compare 로직이 적용된 스펙데이터 */
                    bundleProduct = retrieveProductBundleList(lmdata, input);
                    bundleCompare = retrieveCompareBundleList(lmdata, input);

                    if (CommonConstants.YES_FLAG.equals(keySpecUseFlag)) {
                        keySpecs = retrieveKeySpecList(lmdata, input);
                        defaultKeySpecList = retrieveDefaultKeySpecList(lmdata, input);
                    }
                }
            } else {
                input.put("categoryId", Objects.toString(input.get("categoryId"), ""));
                input.put("bizType", Objects.toString(input.get("bizType"), ""));
                input.put("productCnt", "0");
            }

            if (StringUtils.isNotEmpty(countryCd)) {
                input.put("countryCode", countryCd);
                List<Map<String, Object>> filterCountryList = retrieveFilterCountryList(input);
                compareResult.setFilterCountryList(
                        filterCountryList); // CountryCd값이 입력시만 수행 아니면 null
            }

            LData data = new LData(input);

            // data 필요없는 항목 정리
            data.remove("modelIds");
            data.remove("countryCode");
            data.remove("categoryId");
            data.remove("categoryName");
            data.remove("superCategoryName");
            data.remove("superCategoryId");
            data.remove("buName");
            data.remove("modelId");
            data.remove("urlGubun");
            data.remove("docTypeCode");
            data.remove("specId");
            data.remove("countryList");
            data.remove("pdpId");
            data.remove("pimSkuId");
            data.remove("siteCode");
            data.remove("standardFlag");
            data.remove("timezone");
            data.remove("mtsFlag");
            data.remove("shopCode");
            data.set("localeCode", Objects.toString(input.get("siteCode"), ""));
            data.set(
                    "obsLoginFlag",
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference("OBS_LOGIN_FLAG")),
                            CommonConstants.NO_FLAG));
            if (mtsFlag) {
                data.set("multishopCode", SHOP_CODE);
            }

            compareResult.setObsCalculatorUseFlag(obsCalculatorUseFlag);
            compareResult.setObsInstallmentPromotionUseFlag(obsInstallmentPromotionUseFlag);
            compareResult.setSiblingModelTotalFlag(siblingModelTotal);
            compareResult.setCompareSpecType(compareSpecType);

            compareResult.setData(data);
            compareResult.setBundleProduct(bundleProduct);
            compareResult.setBundleCompare(bundleCompare);
            compareResult.setKeySpecs(keySpecs);
            compareResult.setDefaultKeySpecList(defaultKeySpecList);

        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return compareResult;
    }

    @SuppressWarnings("unchecked")
    public CompareProductResponseVO compareProductList(CompareProductRequestVO requestVO) {
        String SITE_CODE = RequestHeaderUtil.getSiteCode().toUpperCase();

        /** 카테고리 표준화 국가 여부 조회 */
        String standardFlag = commonService.getCategoryStandardFlag();

        String eLabelFlag =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.ofComSysConfig("Energy_Label_Flag"));
        String dummyImageUrl =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.ofComSysConfig("dummy_image_url"));
        String originPdpId = "";

        CompareProductResponseVO compareProductVO = new CompareProductResponseVO();
        compareProductVO.setCompareProductId(requestVO.getCompareProductId());
        compareProductVO.setIsPdpPage(
                (StringUtils.isNotBlank(requestVO.getIsPdpPage())) ? requestVO.getIsPdpPage() : "");
        compareProductVO.setEnergyLabelFlag(!"Y".equals(eLabelFlag) ? "N" : "Y");
        compareProductVO.setObsLoginFlag(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("OBS_LOGIN_FLAG")),
                        CommonConstants.NO_FLAG));
        compareProductVO.setReviewType(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("REVIEW_TYPE")),
                        CommonConstants.NO_FLAG));
        compareProductVO.setBuyNowUseFlag(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("BUY_NOW_FLAG")),
                        CommonConstants.NO_FLAG));
        compareProductVO.setLocaleCode(SITE_CODE);

        List<String> pdpIdList =
                systemPdsProductListClient.getPdpIdListBySkuId(
                        SystemPdsPdpIdListBySkuRequestVO.builder()
                                .siteCode(SITE_CODE)
                                .standardFlag(standardFlag)
                                .skuIds(requestVO.getSku())
                                .build());

        if (ObjectUtils.isNotEmpty(pdpIdList)) {
            originPdpId = pdpIdList.get(0);
        }

        List<SystemAdminCompareProductResponseVO> sysAdmimCompareProductList =
                systemAdminCompareClient.getCompareProductList(
                        SystemAdminCompareProductRequestVO.builder()
                                .localeCode(SITE_CODE)
                                .dummyImageUrl(dummyImageUrl)
                                .compareProductId(requestVO.getCompareProductId())
                                .modelId(originPdpId)
                                .build());

        if (ObjectUtils.isEmpty(sysAdmimCompareProductList)) {
            return compareProductVO;
        }

        List<Map<String, Object>> modelList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        for (SystemAdminCompareProductResponseVO sysAdmCompareProduct :
                sysAdmimCompareProductList) {
            /** 제품 GSRI(환경규제 관련 모델) 정보 조회 */
            String docTypeCode = "";
            String energyLabelDocId = "";
            String secondELabelDocId = "";
            String fEnergyLabelDocId = "";
            String fEnergyLabelFileName = "";
            String fEnergyLabelOriginalName = "";
            String productFichelDocId = "";
            String secondProductFicheDocId = "";
            String productFicheFileName = "";
            String productFicheOriginalName = "";
            String energyLabelFileName = "";
            String energyLabelOriginalName = "";
            String energyLabelproductLeve1Code = "";
            String productFicheproductLeve1Code = "";
            String fEnergyLabelproductLeve1Code = "";

            String energyLabelCategory = sysAdmCompareProduct.getEnergyLabelCategory();
            String secondELabelCategory = sysAdmCompareProduct.getSecondEnergyLabelCategory();

            String reviewType =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference("REVIEW_TYPE")),
                            CommonConstants.NO_FLAG);
            String bizReviewUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference(
                                            "BUSINESS_REVIEW_USE_FLAG")),
                            CommonConstants.NO_FLAG);

            if (CommonConstants.BIZ_TYPE_B2B.equals(sysAdmCompareProduct.getBizType())
                    && "N".equals(bizReviewUseFlag)) {

                reviewType = "N";
            }

            Map<String, Object> modelMap = mapper.convertValue(sysAdmCompareProduct, Map.class);
            modelMap.put("reviewType", reviewType);
            String rsUseFlag = "N";
            String pdpId = modelMap.get("modelId").toString();

            if ("RS".equals(SITE_CODE)
                    && ("EL_CAT_01".equals(energyLabelCategory)
                            || "EL_CAT_02".equals(energyLabelCategory)
                            || "EL_CAT_03".equals(energyLabelCategory)
                            || "EL_CAT_05".equals(energyLabelCategory))) {
                rsUseFlag = "Y";
            }

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                docTypeCode = "EL";
            } else if ("TR".equals(SITE_CODE)) {
                docTypeCode = "TE";
            } else {
                docTypeCode = "EN";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsProductGsriFileResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductGsriFileResp.getResultMap();

                energyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                energyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                energyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                energyLabelproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            modelMap.put("energyLabelDocId", energyLabelDocId);
            modelMap.put("energyLabelFileName", energyLabelFileName);
            modelMap.put("energyLabelOriginalName", energyLabelOriginalName);
            modelMap.put("energyLabelproductLeve1Code", energyLabelproductLeve1Code);

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                docTypeCode = "PF";
            } else if ("TR".equals(SITE_CODE) && !"EL_CAT_04".equals(energyLabelCategory)) {
                docTypeCode = "TI";
            } else if (rsUseFlag.equals("Y")) {
                docTypeCode = "SP";
            } else {
                docTypeCode = "PI";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsProductFicheResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductFicheResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductFicheResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductFicheResp.getResultMap();

                productFichelDocId = Objects.toString(resultMap.get("docId"), "");
                productFicheFileName = Objects.toString(resultMap.get("fileName"), "");
                productFicheOriginalName = Objects.toString(resultMap.get("originalName"), "");
                productFicheproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            modelMap.put("productFicheDocId", productFichelDocId);
            modelMap.put("productFicheFileName", productFicheFileName);
            modelMap.put("productFicheOriginalName", productFicheOriginalName);
            modelMap.put("productFicheproductLeve1Code", productFicheproductLeve1Code);

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                docTypeCode = "FL";
            } else {
                docTypeCode = "FE";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsFEnergyLabelResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp)
                    && ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsFEnergyLabelResp.getResultMap();

                fEnergyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                fEnergyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                fEnergyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                fEnergyLabelproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            modelMap.put("fEnergyLabelDocId", fEnergyLabelDocId);
            modelMap.put("fEnergyLabelFileName", fEnergyLabelFileName);
            modelMap.put("fEnergyLabelOriginalName", fEnergyLabelOriginalName);
            modelMap.put("fenergyLabelproductLeve1Code", fEnergyLabelproductLeve1Code);

            /** 제품 에너지 라벨 조회 */
            SystemPdsPdpEnergyLabelInfoResponseVO eLabelInfoResponseVO =
                    systemPdsPdpInfoClient.getEnergyLabelInfo(
                            SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                    .elabelClsCode(energyLabelCategory)
                                    .elabelGrdCode(sysAdmCompareProduct.getEnergyLabel())
                                    .build());

            String eLabelImageAddr = "";
            String eLabelName = "";

            if (ObjectUtils.isNotEmpty(eLabelInfoResponseVO)) {
                eLabelImageAddr = Objects.toString(eLabelInfoResponseVO.getElabelImgPath(), "");
                eLabelName =
                        Objects.toString(
                                cachedDataUtil.getComCommonCodeValue(
                                        CachedCommonCodeRequestVO.ofCommonCodeValue(
                                                "Energy_Label_Code",
                                                eLabelInfoResponseVO.getElabelGrdCode(),
                                                JobSeperateCodeEnum.D2C)),
                                "");
            }

            modelMap.put("energyLabelImageAddr", eLabelImageAddr);
            modelMap.put("energyLabelName", eLabelName);

            String secondELabelImageAddr = "";
            String secondELabelName = "";

            if (CommonConstants.YES_FLAG.equals(sysAdmCompareProduct.getWashTowerFlag())) {

                if ("RS".equals(SITE_CODE)
                        && ("EL_CAT_01".equals(secondELabelCategory)
                                || "EL_CAT_02".equals(secondELabelCategory)
                                || "EL_CAT_03".equals(secondELabelCategory)
                                || "EL_CAT_05".equals(secondELabelCategory))) {
                    rsUseFlag = "Y";
                }

                if ("EL_CAT_06".equals(secondELabelCategory)
                        || "EL_CAT_07".equals(secondELabelCategory)
                        || "EL_CAT_08".equals(secondELabelCategory)
                        || "EL_CAT_09".equals(secondELabelCategory)
                        || "EL_CAT_10".equals(secondELabelCategory)
                        || "EL_CAT_11".equals(secondELabelCategory)) {
                    docTypeCode = "EL";
                } else if ("TR".equals(SITE_CODE)) {
                    docTypeCode = "TE";
                } else {
                    docTypeCode = "EN";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductGsriFileResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondProductGsriFileResp.getResultMap();

                    secondELabelDocId = Objects.toString(resultMap.get("docId"), "");

                    modelMap.put("secondEnergyLabelDocId", secondELabelDocId);
                    modelMap.put(
                            "secondEnergyLabelFileName",
                            Objects.toString(resultMap.get("fileName"), ""));
                    modelMap.put(
                            "secondEnergyLabelOriginalName",
                            Objects.toString(resultMap.get("originalName"), ""));
                    modelMap.put(
                            "secondEnergyLabelproductLeve1Code",
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                if ("EL_CAT_06".equals(secondELabelCategory)
                        || "EL_CAT_07".equals(secondELabelCategory)
                        || "EL_CAT_08".equals(secondELabelCategory)
                        || "EL_CAT_09".equals(secondELabelCategory)
                        || "EL_CAT_10".equals(secondELabelCategory)
                        || "EL_CAT_11".equals(secondELabelCategory)) {
                    docTypeCode = "PF";
                } else if ("TR".equals(SITE_CODE) && !"EL_CAT_04".equals(secondELabelCategory)) {
                    docTypeCode = "TI";
                } else if (rsUseFlag.equals("Y")) {
                    docTypeCode = "SP";
                } else {
                    docTypeCode = "PI";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductFicheResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(secondELabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondProductFicheResp.getResultMap();

                    secondProductFicheDocId = Objects.toString(resultMap.get("docId"), "");

                    modelMap.put("secondProductFicheDocId", secondProductFicheDocId);
                    modelMap.put(
                            "secondProductFicheFileName",
                            Objects.toString(resultMap.get("fileName"), ""));
                    modelMap.put(
                            "secondProductFicheOriginalName",
                            Objects.toString(resultMap.get("originalName"), ""));
                    modelMap.put(
                            "SecondProductFicheproductLeve1Code",
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                if ("EL_CAT_06".equals(secondELabelCategory)
                        || "EL_CAT_07".equals(secondELabelCategory)
                        || "EL_CAT_08".equals(secondELabelCategory)
                        || "EL_CAT_09".equals(secondELabelCategory)
                        || "EL_CAT_10".equals(secondELabelCategory)
                        || "EL_CAT_11".equals(secondELabelCategory)) {
                    docTypeCode = "FL";
                } else {
                    docTypeCode = "FE";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondFEnergyLabelResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(secondELabelDocId)
                                        .productFichelDocId(secondProductFicheDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondFEnergyLabelResp.getResultMap();
                    modelMap.put(
                            "secondFEnergyLabelDocId",
                            Objects.toString(resultMap.get("docId"), ""));
                    modelMap.put(
                            "secondFEnergyLabelFileName",
                            Objects.toString(resultMap.get("fileName"), ""));
                    modelMap.put(
                            "secondFEnergyLabelOriginalName",
                            Objects.toString(resultMap.get("originalName"), ""));
                    modelMap.put(
                            "secondFEnergyLabelproductLeve1Code",
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                SystemPdsPdpEnergyLabelInfoResponseVO secondELabelInfoResponseVO =
                        systemPdsPdpInfoClient.getEnergyLabelInfo(
                                SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                        .elabelClsCode(secondELabelCategory)
                                        .elabelGrdCode(sysAdmCompareProduct.getSecondEnergyLabel())
                                        .build());

                if (ObjectUtils.isNotEmpty(secondELabelInfoResponseVO)) {
                    secondELabelImageAddr =
                            Objects.toString(secondELabelInfoResponseVO.getElabelImgPath(), "");
                    secondELabelName =
                            Objects.toString(
                                    cachedDataUtil.getComCommonCodeValue(
                                            CachedCommonCodeRequestVO.ofCommonCodeValue(
                                                    "Energy_Label_Code",
                                                    secondELabelInfoResponseVO.getElabelGrdCode(),
                                                    JobSeperateCodeEnum.D2C)),
                                    "");
                }

                modelMap.put("secondEnergyLabelImageAddr", secondELabelImageAddr);
                modelMap.put("secondEnergyLabelName", secondELabelName);
            }

            if (null == sysAdmCompareProduct.getSRating2()) {
                modelMap.put("sRating2", 0);
            }

            modelMap.put("pCount", String.valueOf(sysAdmCompareProduct.getPCount()));
            modelMap.put("rsUseFlag", rsUseFlag);
            modelMap.remove("modelId");
            modelList.add(modelMap);
        }

        List<SystemAdminCompareSpecResponseVO> sysAdmCompareSpecList =
                systemAdminCompareClient.getCompareSpecList(
                        SystemAdminCompareSpecRequestVO.builder()
                                .localeCode(SITE_CODE)
                                .modelId(originPdpId)
                                .compareProductId(requestVO.getCompareProductId())
                                .build());

        List<Map<String, Object>> specList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(sysAdmCompareSpecList)) {
            for (SystemAdminCompareSpecResponseVO sysAdmCompareSpec : sysAdmCompareSpecList) {
                Map<String, Object> specMap = mapper.convertValue(sysAdmCompareSpec, Map.class);
                specList.add(specMap);
            }
        }

        compareProductVO.setModelList(modelList);
        compareProductVO.setSpecList(specList);

        return compareProductVO;
    }

    @SuppressWarnings("unchecked")
    private LMultiData retrieveValidModelIds(LMultiData models, Map<String, Object> input) {
        List<Map<String, Object>> resultMapList = new ArrayList<>();

        List<SystemPdsBundleModelIdResponseVO> sysPdsBundleModelIdList =
                systemPdsCompareClient.getBundleModelIdList(
                        SystemPdsBundleModelIdRequestVO.builder()
                                .standardFlag(Objects.toString(input.get("standardFlag"), ""))
                                .siteCode(Objects.toString(input.get("siteCode"), ""))
                                .modelIds((List<String>) models.get("modelId"))
                                .shopCode(Objects.toString(input.get("shopCode"), ""))
                                .build());

        if (ObjectUtils.isNotEmpty(sysPdsBundleModelIdList)) {
            String bizType =
                    "".equals(Objects.toString(input.get("bizType")))
                            ? sysPdsBundleModelIdList.get(0).getBizTypeCode()
                            : Objects.toString(input.get("bizType"));
            String superCategoryId = sysPdsBundleModelIdList.get(0).getSuperCategoryCode();

            input.put("bizType", bizType);
            input.put("categoryId", sysPdsBundleModelIdList.get(0).getCategoryCode());
            input.put(
                    "categoryName",
                    changeStringForAdobe(sysPdsBundleModelIdList.get(0).getCategoryNm()));
            input.put(
                    "superCategoryName",
                    changeStringForAdobe(sysPdsBundleModelIdList.get(0).getSuperCategoryNm()));
            input.put("superCategoryId", superCategoryId);
            input.put("productCnt", String.valueOf(sysPdsBundleModelIdList.size()));
            String buName = "";
            buName =
                    "**********".equals(superCategoryId)
                            ? "COM"
                            : ("**********".equals(superCategoryId)
                                    ? "MU"
                                    : ("**********".equals(superCategoryId) ? "HE" : "HA"));
            input.put("buName", changeStringForAdobe(buName));

            StringBuffer emailHref = new StringBuffer();

            String serverInfoUrl =
                    cachedDataUtil.getComSystemConfigurationCode(
                            CachedCodeRequestVO.ofComSysConfig("frontUrl"));
            String compareUrl =
                    cachedDataUtil.getComSystemConfigurationCode(
                            CachedCodeRequestVO.ofComSysConfig("compareUrl"));

            List<String> skuList =
                    sysPdsBundleModelIdList.stream()
                            .map(SystemPdsBundleModelIdResponseVO::getLgcomSkuId)
                            .collect(Collectors.toList());
            String skus = "";
            for (int i = 0; i < skuList.size(); i++) {
                if (i == skuList.size() - 1) {
                    skus += skuList.get(i);
                } else {
                    skus += skuList.get(i) + ":";
                }
            }

            emailHref
                    .append(serverInfoUrl)
                    .append("/")
                    .append(Objects.toString(input.get("siteCode")).toLowerCase())
                    .append(compareUrl)
                    .append("?")
                    .append("bizType=")
                    .append(bizType)
                    .append("&")
                    .append("sku")
                    .append("=")
                    .append(skus);
            input.put("emailHref", emailHref.toString());

            ObjectMapper mapper = new ObjectMapper();
            for (SystemPdsBundleModelIdResponseVO resp : sysPdsBundleModelIdList) {
                Map<String, Object> map = mapper.convertValue(resp, Map.class);
                resultMapList.add(map);
            }
            models = new LMultiData(resultMapList);
        }

        return models;
    }

    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> retrieveProductBundleList(
            LMultiData validModelInfoList, Map<String, Object> input) {
        List<Map<String, Object>> productBundleList = new ArrayList<>();
        List<String> modelIds = new ArrayList<>();

        String siteCode = Objects.toString(input.get("siteCode"), "");
        String shopCode = Objects.toString(input.get("shopCode"), "");
        String bizType = Objects.toString(input.get("bizType"), "");
        String today = Objects.toString(input.get("today"), "");
        String pdpId = "";
        String energyLabel = "";
        String energyLabelCategory = "";

        boolean mtsFlag = Boolean.parseBoolean(Objects.toString(input.get("mtsFlag")));
        String buyNowUrl =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.ofComSysConfig("default_buy_now_url"));
        String emiPopupUrl =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.ofComSysConfig("emi_popup_url"));
        String addToCartUrl =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("addToCartUrl")),
                        "");
        String inquiryToBuyUrl =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("b2b_inquiry_to_buy_url")),
                        "");
        inquiryToBuyUrl = inquiryToBuyUrl + "?selectItem=Y&superCateId=";
        String reStockAlertUrl =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.ofComSysConfig("re_stock_alert_url"));
        String obsMembershipLinkUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("obs_membership_link_use_flag")),
                        CommonConstants.NO_FLAG);
        String obsMembershipLinkUrl =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("obs_membership_link_url")),
                        "");
        String obsMembershipLinkTarget =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("obs_membership_link_target")),
                        "_self");

        String siblingModelTotal =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig(
                                        "REVIEW_SIBLING_MODEL_TOTAL_FLAG")),
                        CommonConstants.NO_FLAG);

        String preOrderTagEnableModelList =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig(
                                        "pre_order_tag_enable_model_list")),
                        "");
        String[] preOrderTagEnableModelArr = preOrderTagEnableModelList.split(",");

        String labelUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("LABEL_USE_FLAG")),
                        CommonConstants.NO_FLAG);
        String repairabilityIndexUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("repairability_index_use_flag")),
                        CommonConstants.NO_FLAG);

        /** 제품 GSRI(환경규제 관련 모델) 정보 조회 */
        String docTypeCode = "";
        String energyLabelDocId = "";
        String fEnergyLabelDocId = "";
        String fEnergyLabelFileName = "";
        String fEnergyLabelOriginalName = "";
        String productFichelDocId = "";
        String productFicheFileName = "";
        String productFicheOriginalName = "";
        String energyLabelFileName = "";
        String energyLabelOriginalName = "";

        for (int i = 0; i < validModelInfoList.getMaxDataCount(); i++) {
            pdpId = validModelInfoList.getLData(i).getString("pdpId");
            SystemPdsCompareProductInfoResponseVO resp = null;

            if (mtsFlag) {
                resp =
                        systemPdsCompareClient.getMtsCompareProductInfo(
                                SystemPdsCompareProductInfoRequestVO.builder()
                                        .standardFlag(
                                                Objects.toString(input.get("standardFlag"), ""))
                                        .siteCode(siteCode)
                                        .pdpId(pdpId)
                                        .today(Objects.toString(input.get("today"), ""))
                                        .bizTypeCode(Objects.toString(input.get("bizType"), ""))
                                        .shopCode(shopCode)
                                        .build());
            } else {
                resp =
                        systemPdsCompareClient.getCompareProductInfo(
                                SystemPdsCompareProductInfoRequestVO.builder()
                                        .standardFlag(
                                                Objects.toString(input.get("standardFlag"), ""))
                                        .siteCode(siteCode)
                                        .pdpId(pdpId)
                                        .today(Objects.toString(input.get("today"), ""))
                                        .bizTypeCode(Objects.toString(input.get("bizType"), ""))
                                        .build());
            }

            input.put("modelId", resp.getPdpId());
            pdpId = resp.getPdpId();
            modelIds.add(resp.getPdpId());

            energyLabel = resp.getElabelGrdCode();
            energyLabelCategory = resp.getElabelClsCode();

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                docTypeCode = "EL";
            } else if ("TR".equals(siteCode)) {
                docTypeCode = "TE";
            } else {
                docTypeCode = "EN";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsProductGsriFileResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductGsriFileResp.getResultMap();

                energyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                energyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                energyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
            }

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                docTypeCode = "PF";
            } else if ("TR".equals(siteCode) && !"EL_CAT_04".equals(energyLabelCategory)) {
                docTypeCode = "TI";
            } else {
                docTypeCode = "PI";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsProductFicheResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductFicheResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductFicheResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductFicheResp.getResultMap();

                productFichelDocId = Objects.toString(resultMap.get("docId"), "");
                productFicheFileName = Objects.toString(resultMap.get("fileName"), "");
                productFicheOriginalName = Objects.toString(resultMap.get("originalName"), "");
            }

            /** 제품 에너지 라벨 조회 */
            SystemPdsPdpEnergyLabelInfoResponseVO eLabelInfoResponseVO =
                    systemPdsPdpInfoClient.getEnergyLabelInfo(
                            SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                    .elabelClsCode(energyLabelCategory)
                                    .elabelGrdCode(energyLabel)
                                    .build());

            String eLabelImageAddr = "";
            String eLabelName = "";

            if (ObjectUtils.isNotEmpty(eLabelInfoResponseVO)) {
                eLabelImageAddr =
                        StringUtils.defaultIfBlank(eLabelInfoResponseVO.getElabelImgPath(), "");
                eLabelName =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComCommonCodeValue(
                                        CachedCommonCodeRequestVO.ofCommonCodeValue(
                                                "Energy_Label_Code",
                                                eLabelInfoResponseVO.getElabelGrdCode(),
                                                JobSeperateCodeEnum.D2C)),
                                "");
            }

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                docTypeCode = "FL";
            } else {
                docTypeCode = "FE";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsFEnergyLabelResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp)
                    && ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsFEnergyLabelResp.getResultMap();

                fEnergyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                fEnergyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                fEnergyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
            }

            /** PRODUCT 정보 조회 (PIM) */
            SystemPdsProductResponseVO systemPdsProductRespVO = getProductInfo(resp.getSkuId());

            String salesSuffixCode = systemPdsProductRespVO.getSalesModelSuffixCode();
            String pimUserFrndyProductNm = systemPdsProductRespVO.getNewMktProductNm();

            String userFrndyProductNm =
                    StringUtils.isNotBlank(pimUserFrndyProductNm)
                            ? pimUserFrndyProductNm
                            : resp.getUserFrndyProductNm();

            if (StringUtils.isNotBlank(userFrndyProductNm)) {
                userFrndyProductNm = userFrndyProductNm.replaceAll("\\\"", "&#39;");
            }

            Map<String, Object> product = new HashMap<>();
            product.put("bizType", resp.getBizTypeCode());
            product.put("buyNowUrl", buyNowUrl);
            product.put("msrp", resp.getMsrpSalesPrice());
            product.put("modelType", resp.getPdpTypeCode());
            product.put("classificationFlagLv1", systemPdsProductRespVO.getLv1CategoryCodeNm());
            product.put("classificationFlagLv2", systemPdsProductRespVO.getLv2CategoryCodeNm());
            product.put("classificationFlagLv3", systemPdsProductRespVO.getLv3CategoryCodeNm());
            product.put("classificationFlagLv4", systemPdsProductRespVO.getLv4CategoryCodeNm());
            product.put("emiPopupUrl", emiPopupUrl);
            product.put("addToCartUrl", appendSku(addToCartUrl, resp.getLgcomSkuId()));
            product.put("inquiryToBuyUrl", inquiryToBuyUrl + resp.getSuperCategoryId());
            product.put("reStockAlertUrl", reStockAlertUrl);
            product.put("sku", resp.getLgcomSkuId());
            product.put("modelName", resp.getProductNm());
            product.put("modelStatusCode", resp.getProductStateCode());
            product.put("mediumImageAddr", resp.getMdmImgUrl());
            product.put("smallImageAddr", resp.getSmlImgUrl());
            product.put("userFriendlyName", userFrndyProductNm);
            product.put("modelUrlPath", resp.getPdpUrl());
            product.put("wtbUseFlag", resp.getWtbUseFlag());
            product.put("energyLabel", energyLabel);
            product.put("energyLabelDocId", energyLabelDocId);
            product.put("energyLabelFileName", energyLabelFileName);
            product.put("energyLabelOriginalName", energyLabelOriginalName);
            product.put("energyLabelCategory", energyLabelCategory);
            product.put("productFicheDocId", productFichelDocId);
            product.put("productFicheFileName", productFicheFileName);
            product.put("productFicheOriginalName", productFicheOriginalName);
            product.put("energyLabelImageAddr", eLabelImageAddr);
            product.put("energyLabelName", eLabelName);
            product.put("fenergyLabelDocId", fEnergyLabelDocId);
            product.put("fenergyLabelFileName", fEnergyLabelFileName);
            product.put("fenergyLabelOriginalName", fEnergyLabelOriginalName);
            product.put("salesSuffixCode", salesSuffixCode);
            product.put("superCategoryName", resp.getSuperCategoryNm());
            product.put("modelYear", resp.getModelYear());
            product.put("obsMembershipLinkUseFlag", obsMembershipLinkUseFlag);
            product.put("obsMembershipLinkUrl", obsMembershipLinkUrl);
            product.put("obsMembershipLinkTarget", obsMembershipLinkTarget);

            if (StringUtils.isNotBlank(resp.getPdpUrl())) {
                product.put("whereToBuyUrl", resp.getPdpUrl() + "#pdp_where");
                product.put("findTheDealerUrl", resp.getPdpUrl() + "#pdp_findadealer");
            }

            if (StringUtils.isNotBlank(resp.getSiblingGroupCode())
                    && CommonConstants.YES_FLAG.equals(siblingModelTotal)) {

                SystemPdsPdpUserReviewRatingResponseVO sysPdsUserReviewResp =
                        systemPdsPdpInfoClient.getUserReviewSiblingRatings(
                                SystemPdsPdpUserReviewRatingRequestVO.builder()
                                        .siteCode(siteCode)
                                        .pdpId(pdpId)
                                        .build());

                product.put(
                        "srating",
                        Integer.parseInt(
                                ObjectUtils.isEmpty(sysPdsUserReviewResp.getSRating())
                                        ? "0"
                                        : sysPdsUserReviewResp.getSRating().toString()));
                product.put(
                        "srating2",
                        new BigDecimal(
                                ObjectUtils.isEmpty(sysPdsUserReviewResp.getSRating2())
                                        ? "0.00"
                                        : sysPdsUserReviewResp.getSRating2().toString()));
                product.put("pcount", sysPdsUserReviewResp.getPCount());
                product.put(
                        "ratingPercent",
                        Integer.parseInt(sysPdsUserReviewResp.getRatingPercent().toString()));
            } else {
                product.put("pcount", resp.getPCount());
                product.put("ratingPercent", resp.getRatingPercent());
                product.put("srating", resp.getSRating());
                product.put("srating2", resp.getSRating2());
            }

            /** keyfeature */
            String featureName[] = resp.getFeatureName().split("%%%");
            List<String> bulletFeatures = new ArrayList<>();
            for (int j = 0; j < featureName.length; j++) {
                bulletFeatures.add(featureName[j]);
            }
            product.put("bulletFeatures", bulletFeatures);
            product.put("productLevel1Code", systemPdsProductRespVO.getLv1ProductCode());

            String preOrderTagEnableFlag = null;
            for (int j = 0; j < preOrderTagEnableModelArr.length; j++) {
                if (pdpId.equals(preOrderTagEnableModelArr[j])) {
                    preOrderTagEnableFlag = CommonConstants.YES_FLAG;
                    break;
                }
            }
            product.put("preOrderTagEnableFlag", preOrderTagEnableFlag);
            product.put("labelUseFlag", labelUseFlag);
            product.put("repairabilityIndexUseFlag", repairabilityIndexUseFlag);
            product.put("labelRepairMap", new ArrayList<Map<String, Object>>());

            /** 현재 사용하지 않는 값이나, AS-IS API return json과 동일하게 맞추기 위해 빈값으로 세팅 */
            product.put("buName1", "");
            product.put("buName2", "");
            product.put("buName3", "");
            product.put("ecommerceTarget", "");
            product.put("mcFamilyFlag", "");
            product.put("rsUseFlag", "");
            product.put("retailerPricingFlag", "");

            productBundleList.add(product);
        }

        if (CommonConstants.YES_FLAG.equals(labelUseFlag)
                && CommonConstants.YES_FLAG.equals(repairabilityIndexUseFlag)) {
            input.put("modelIds", modelIds);

            List<SystemPdsProductIconListResponseVO> labelRepairList =
                    systemPdsPdpInfoClient.getProductIconList(
                            SystemPdsProductIconListRequestVO.builder()
                                    .siteCode(siteCode)
                                    .bizTypeCode(bizType)
                                    .today(today)
                                    .pdpIdList(modelIds)
                                    .repairabilityIndexFlag(CommonConstants.YES_FLAG)
                                    .build());
            ObjectMapper mapper = new ObjectMapper();
            for (int i = 0; i < productBundleList.size(); i++) {
                List<Map<String, Object>> labelRepairMapList = new ArrayList<>();
                Map<String, Object> productInfoMap = productBundleList.get(i);

                for (SystemPdsProductIconListResponseVO resp : labelRepairList) {
                    if (productInfoMap.get("pdplId").equals(resp.getPdpId())) {
                        Map<String, Object> map = mapper.convertValue(resp, Map.class);
                        labelRepairMapList.add(map);
                        productInfoMap.put("labelRepairMap", labelRepairMapList);
                        productBundleList.set(i, productInfoMap);
                        break;
                    }
                }
            }
        }

        return productBundleList;
    }

    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> retrieveCompareBundleList(
            LMultiData validModelInfoList, Map<String, Object> input) {
        List<Map<String, Object>> bundleCompare = null;
        List<BundleFrameVO> bundleCompareVOList = null;

        bundleCompareVOList = partOneMakeFrame(validModelInfoList, input);

        for (BundleFrameVO bundleFrame : bundleCompareVOList) {

            List<List<String>> modelIdList = bundleFrame.getModelIdList();
            List<List<String>> pimSkuIdList = bundleFrame.getPimSkuList();
            List<List<String>> modelPdrList = bundleFrame.getModelPdrList();
            LMultiData lMultiData = null;
            List<CompareVO> compareList = new ArrayList<>();

            for (int i = 0; i < modelIdList.size(); i++) {

                lMultiData = new LMultiData();
                List<String> modelIds = modelIdList.get(i);
                List<String> modelPdrs = modelPdrList.get(i);
                List<String> pimSkuIds = pimSkuIdList.get(i);

                for (int j = 0; j < modelIds.size(); j++) {
                    lMultiData.add("modelId", modelIds.get(j));
                    lMultiData.add("pdrUseFlag", modelPdrs.get(j));
                    lMultiData.add("pimSkuId", pimSkuIds.get(j));
                }

                /**
                 * COMPARE START
                 *
                 * <p>1. 각 모델 ID에 대한 SPEC 정보를 묶어 가져와 셋팅한다 2. EMPTY에 해당 하는건 DUMMY 데이터를 셋팅한다. 3. 셋팅된
                 * 데이터의 로우 단위로 COMPARE 로직이 수행된다.
                 *
                 * <p>EX) RESULT
                 *
                 * <p>CATEGORY 1
                 *
                 * <p>SPEC 1 SPEC 2 SPEC 3 (COMPARE)
                 *
                 * <p>CATEGORY 2
                 *
                 * <p>DUMMY SPEC 4 SPEC 5 (COMPARE)
                 *
                 * <p>CATEGORY 3
                 *
                 * <p>DUMMY DUMMY SPEC 6 (COMPARE)
                 */
                CompareVO compare = partTwoCompare(lMultiData, input);
                /** COMPARE END */
                compareList.add(compare);
            }
            bundleFrame.setCompareList(compareList);

            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> bundleFrameMap = mapper.convertValue(bundleFrame, Map.class);
            bundleCompare = new ArrayList<>();
            bundleCompare.add(bundleFrameMap);
        }

        return bundleCompare;
    }

    @SuppressWarnings("unchecked")
    private List<BundleFrameVO> partOneMakeFrame(
            LMultiData validModelInfoList, Map<String, Object> input) {
        List<BundleFrameVO> bundleFrameListVO = new ArrayList<>();
        List<Map<String, Object>> bundleCategoryList = new ArrayList<>();
        LMultiData categoryResultAll = new LMultiData();

        String standardFlag = Objects.toString(input.get("standardFlag"), "");
        String siteCode = Objects.toString(input.get("siteCode"), "");
        String localeCode = Objects.toString(input.get("localeCode"), "");
        String shopCode = Objects.toString(input.get("shopCode"), "");
        String compareSpecType = Objects.toString(input.get("compareSpecType"), "");
        boolean mtsFlag = Boolean.parseBoolean(Objects.toString(input.get("mtsFlag")));
        String pdpId = "";

        ObjectMapper mapper = new ObjectMapper();
        for (int i = 0; i < validModelInfoList.getMaxDataCount(); i++) {
            pdpId = validModelInfoList.getLData(i).getString("pdpId");
            List<SystemPdsBundleCategoryResponseVO> sysPdsBundleCategoryList = null;

            if (mtsFlag) {
                sysPdsBundleCategoryList =
                        systemPdsCompareClient.getMtsCompareBundleCategoryList(
                                SystemPdsBundleCategoryRequestVO.builder()
                                        .standardFlag(standardFlag)
                                        .siteCode(siteCode)
                                        .localeCode(localeCode)
                                        .pdpId(pdpId)
                                        .compareSpecType(compareSpecType)
                                        .shopCode(shopCode)
                                        .build());
            } else {
                sysPdsBundleCategoryList =
                        systemPdsCompareClient.getCompareBundleCategoryList(
                                SystemPdsBundleCategoryRequestVO.builder()
                                        .standardFlag(standardFlag)
                                        .siteCode(siteCode)
                                        .localeCode(localeCode)
                                        .pdpId(pdpId)
                                        .compareSpecType(compareSpecType)
                                        .build());
            }

            if (ObjectUtils.isNotEmpty(sysPdsBundleCategoryList)) {
                for (SystemPdsBundleCategoryResponseVO resp : sysPdsBundleCategoryList) {
                    Map<String, Object> map = mapper.convertValue(resp, Map.class);
                    bundleCategoryList.add(map);
                    categoryResultAll.addLMultiData(
                            LDataUtil.getLMultiDataFromListMap(bundleCategoryList));
                }
            }
        }

        List<String> categoryIdList =
                LDataUtil.getDistinctData((List<String>) categoryResultAll.get("categoryCode"));

        ArrayList<String[]> grid = new ArrayList<>();
        ArrayList<LMultiData> modelIdGrid = new ArrayList<>();
        ArrayList<LMultiData> skuGrid = new ArrayList<>();
        ArrayList<LMultiData> pimSkuGrid = new ArrayList<>();
        ArrayList<LMultiData> modelNameGrid = new ArrayList<>();
        ArrayList<LMultiData> modelPdrGrid = new ArrayList<>();

        if (categoryIdList.size() > 0) {
            String[] gridRow = new String[categoryIdList.size()];

            for (int i = 0; i < categoryIdList.size(); i++) {
                gridRow[i] = categoryIdList.get(i);
            }
            grid.add(gridRow);

            for (int i = 0; i < validModelInfoList.getMaxDataCount(); i++) {
                pdpId = validModelInfoList.getLData(i).getString("pdpId");

                List<SystemPdsBundleCategoryResponseVO> sysPdsBundleCategoryList = null;

                if (mtsFlag) {
                    sysPdsBundleCategoryList =
                            systemPdsCompareClient.getMtsCompareBundleCategoryList(
                                    SystemPdsBundleCategoryRequestVO.builder()
                                            .standardFlag(standardFlag)
                                            .siteCode(siteCode)
                                            .localeCode(localeCode)
                                            .pdpId(pdpId)
                                            .compareSpecType(compareSpecType)
                                            .shopCode(shopCode)
                                            .build());
                } else {
                    sysPdsBundleCategoryList =
                            systemPdsCompareClient.getCompareBundleCategoryList(
                                    SystemPdsBundleCategoryRequestVO.builder()
                                            .standardFlag(standardFlag)
                                            .siteCode(siteCode)
                                            .localeCode(localeCode)
                                            .pdpId(pdpId)
                                            .compareSpecType(compareSpecType)
                                            .build());
                }

                LMultiData modelIdTemp = new LMultiData();
                LMultiData skuTemp = new LMultiData();
                LMultiData pimSkuTemp = new LMultiData();
                LMultiData modelNameTemp = new LMultiData();
                LMultiData modelPdrTemp = new LMultiData();

                for (int k = 0; k < sysPdsBundleCategoryList.size(); k++) {

                    int cnt = 0;
                    gridRow = new String[categoryIdList.size()];

                    if (k == 0) {
                        for (int j = 0; j < grid.get(0).length; j++) {

                            if (grid.get(0)[j].equals(
                                    sysPdsBundleCategoryList.get(k).getCategoryCode())) {
                                gridRow[j] = String.valueOf(++cnt);
                            } else {
                                gridRow[j] = String.valueOf(0);
                            }
                        }
                        grid.add(gridRow);
                    } else {
                        String[] tempRow = grid.get(grid.size() - 1);

                        for (int j = 0; j < grid.get(0).length; j++) {

                            if (grid.get(0)[j].equals(
                                    sysPdsBundleCategoryList.get(k).getCategoryCode())) {
                                tempRow[j] = String.valueOf(Integer.parseInt(tempRow[j]) + 1);
                            } else {
                                tempRow[j] = String.valueOf(Integer.parseInt(tempRow[j]) + 0);
                            }
                        }
                        grid.set(grid.size() - 1, tempRow);
                    }
                    modelIdTemp.add(
                            sysPdsBundleCategoryList.get(k).getCategoryCode(),
                            sysPdsBundleCategoryList.get(k).getPdpId());
                    skuTemp.add(
                            sysPdsBundleCategoryList.get(k).getCategoryCode(),
                            sysPdsBundleCategoryList.get(k).getSku());
                    pimSkuTemp.add(
                            sysPdsBundleCategoryList.get(k).getCategoryCode(),
                            sysPdsBundleCategoryList.get(k).getPimSku());
                    modelNameTemp.add(
                            sysPdsBundleCategoryList.get(k).getCategoryCode(),
                            sysPdsBundleCategoryList.get(k).getProductNm());
                    modelPdrTemp.add(
                            sysPdsBundleCategoryList.get(k).getCategoryCode(),
                            sysPdsBundleCategoryList.get(k).getPimSpecUseFlag());
                }
                modelIdGrid.add(modelIdTemp);
                skuGrid.add(skuTemp);
                pimSkuGrid.add(pimSkuTemp);
                modelNameGrid.add(modelNameTemp);
                modelPdrGrid.add(modelPdrTemp);
            }

            for (int i = 0; i < grid.get(0).length; i++) {

                BundleFrameVO bundleFrame = new BundleFrameVO();

                bundleFrame.setCategoryId(grid.get(0)[i]);
                bundleFrame.setCategoryName(
                        ((List<String>) categoryResultAll.get("categoryNm"))
                                .get(
                                        ((List<String>) categoryResultAll.get("categoryCode"))
                                                .indexOf(grid.get(0)[i])));

                List<Integer> tmpCnt = new ArrayList<>();

                for (int j = 0; j < validModelInfoList.getMaxDataCount(); j++) {
                    try {
                        tmpCnt.add(Integer.parseInt(grid.get(j + 1)[i]));
                    } catch (IndexOutOfBoundsException e) {
                        continue;
                    }
                }

                int maxCnt = Collections.max(tmpCnt);

                List<List<String>> idTwoDimension = new ArrayList<>();
                List<List<String>> skuTwoDimension = new ArrayList<>();
                List<List<String>> pimSkuTwoDimension = new ArrayList<>();
                List<List<String>> nameTwoDimension = new ArrayList<>();
                List<List<String>> pdrTwoDimension = new ArrayList<>();

                for (int j = 0; j < maxCnt; j++) {

                    List<String> idOneDimension = new ArrayList<>();
                    List<String> skuOneDimension = new ArrayList<>();
                    List<String> pimSkuOneDimension = new ArrayList<>();
                    List<String> nameOneDimension = new ArrayList<>();
                    List<String> pdrOneDimension = new ArrayList<>();

                    for (int k = 0; k < validModelInfoList.getMaxDataCount(); k++) {

                        try {

                            if (modelIdGrid.get(k).get(grid.get(0)[i]) == null) {
                                idOneDimension.add("EMPTY");
                                skuOneDimension.add("EMPTY");
                                nameOneDimension.add("EMPTY");
                                pdrOneDimension.add("EMPTY");
                            } else {
                                idOneDimension.add(
                                        ((List<String>) modelIdGrid.get(k).get(grid.get(0)[i]))
                                                .get(j));
                                skuOneDimension.add(
                                        ((List<String>) skuGrid.get(k).get(grid.get(0)[i])).get(j));
                                pimSkuOneDimension.add(
                                        ((List<String>) pimSkuGrid.get(k).get(grid.get(0)[i]))
                                                .get(j));
                                nameOneDimension.add(
                                        ((List<String>) modelNameGrid.get(k).get(grid.get(0)[i]))
                                                .get(j));
                                pdrOneDimension.add(
                                        ((List<String>) modelPdrGrid.get(k).get(grid.get(0)[i]))
                                                .get(j));
                            }

                        } catch (IndexOutOfBoundsException e) {
                            idOneDimension.add("EMPTY");
                            skuOneDimension.add("EMPTY");
                            pimSkuOneDimension.add("EMPTY");
                            nameOneDimension.add("EMPTY");
                            pdrOneDimension.add("EMPTY");
                            continue;
                        }
                    }
                    idTwoDimension.add(idOneDimension);
                    skuTwoDimension.add(skuOneDimension);
                    pimSkuTwoDimension.add(pimSkuOneDimension);
                    nameTwoDimension.add(nameOneDimension);
                    pdrTwoDimension.add(pdrOneDimension);
                }
                bundleFrame.setModelIdList(idTwoDimension);
                bundleFrame.setSkuList(skuTwoDimension);
                bundleFrame.setPimSkuList(pimSkuTwoDimension);
                bundleFrame.setModelNameList(nameTwoDimension);
                bundleFrame.setModelPdrList(pdrTwoDimension);

                bundleFrameListVO.add(bundleFrame);
            }
        }

        return bundleFrameListVO;
    }

    @SuppressWarnings("unchecked")
    public CompareVO partTwoCompare(LMultiData lMultiData, Map<String, Object> input) {
        CompareVO compare = new CompareVO();
        List<LMultiData> valueList = new ArrayList<>();

        List<String> pdrUseFlag = (List<String>) lMultiData.get("pdrUseFlag");

        for (int i = 0; i < lMultiData.getMaxDataCount(); i++) {

            if ("EMPTY".equals(lMultiData.getLData(i).getString("modelId"))) {
                ProductVO dummyProduct = new ProductVO();
                compare.addProduct(dummyProduct);

                LMultiData dummySpecResult = new LMultiData();

                List<SystemPdsSpecSchemaPart2ResponseVO> dummySpecList = new ArrayList<>();
                SystemPdsSpecSchemaPart2ResponseVO dummySpecVO =
                        new SystemPdsSpecSchemaPart2ResponseVO();
                dummySpecVO.setSpecId("DUMMY");
                dummySpecVO.setSpecLevelNo(1);
                dummySpecVO.setSpecName("DUMMY");
                dummySpecVO.setHighLevelSpecId("");
                dummySpecVO.setDisplayOrderNo(1);
                dummySpecVO.setSpecValueName("");
                dummySpecList.add(dummySpecVO);

                dummySpecResult.addLMultiData(LDataUtil.getLMultiDataFromListObj(dummySpecList));
                dummySpecResult.setNullToInitialize(true);
                compare.setSpecSchema(dummySpecResult);

                if (i == lMultiData.getMaxDataCount() - 1) {
                    compare.setSchema();
                }
                valueList.add(dummySpecResult);
            } else {
                ProductVO dummyProduct = new ProductVO();
                compare.addProduct(dummyProduct);

                input.put("pimSkuId", lMultiData.getLData(i).getString("pimSkuId"));
                input.put("pdpId", lMultiData.getLData(i).getString("modelId"));

                LMultiData specResult = new LMultiData();

                compare.setSpecSchema(retrieveSpecResult(specResult, input, pdrUseFlag.get(i)));

                if (i == lMultiData.getMaxDataCount() - 1) {
                    compare.setSchema();
                }
                valueList.add(specResult);
            }
        }

        int i = 0;
        for (LMultiData lmdate : valueList) {
            compare.setSpecData(i++, lmdate);
        }

        compare.compress();

        return compare;
    }

    @SuppressWarnings("unchecked")
    public LMultiData retrieveSpecResult(
            LMultiData specResult, Map<String, Object> input, String pdrUseFlag) {
        boolean mtsFlag = Boolean.parseBoolean(Objects.toString(input.get("mtsFlag")));

        if (CommonConstants.YES_FLAG.equals(pdrUseFlag)) {
            List<SystemPdsProductStandardSpecResponseVO> sysPdsPdrSpecList =
                    systemPdsSpecClient.getProductStandardSpecs(
                            SystemPdsProductStandardSpecRequestVO.builder()
                                    .skuId(Objects.toString(input.get("pimSkuId"), ""))
                                    .localeCode(Objects.toString(input.get("localeCode"), ""))
                                    .pdpId(Objects.toString(input.get("pdpId"), ""))
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsPdrSpecList)) {
                List<Map<String, Object>> specMapList = new ArrayList<>();
                ObjectMapper mapper = new ObjectMapper();
                for (SystemPdsProductStandardSpecResponseVO resp : sysPdsPdrSpecList) {
                    Map<String, Object> specMap = mapper.convertValue(resp, Map.class);
                    specMapList.add(specMap);
                }

                // SYSTEM LAYER에서 받아온 spec정보 중 lv1SpecCode로 distinct한 값을 List<Map<String, Object>>로
                // convert
                List<Map<String, Object>> specIdList =
                        specMapList.stream()
                                .map(map -> (String) map.get("lv1SpecCode"))
                                .filter(lv1SpecCode -> lv1SpecCode != null)
                                .distinct()
                                .map(
                                        lv1SpecCode -> {
                                            Map<String, Object> newMap = new HashMap<>();
                                            newMap.put("specId", lv1SpecCode);
                                            return newMap;
                                        })
                                .collect(Collectors.toList());

                for (Map<String, Object> map : specIdList) {
                    List<SystemPdsPdrSpecSchemaPart2ResponseVO> list =
                            systemPdsSpecClient.getComparePdrSpecSchemaPart2(
                                    SystemPdsPdrSpecSchemaPart2RequestVO.builder()
                                            .localeCode(
                                                    Objects.toString(input.get("localeCode"), ""))
                                            .skuId(Objects.toString(input.get("pimSkuId"), ""))
                                            .specCode(Objects.toString(map.get("specId"), ""))
                                            .build());
                    specResult.addLMultiData(LDataUtil.getLMultiDataFromListObj(list));
                }
            }

        } else {

            List<SystemPdsProductLgcomSpecResponseVO> sysPdsLgcomSpecList = null;

            if (mtsFlag) {
                sysPdsLgcomSpecList =
                        systemPdsSpecClient.getMtsProductLgcomSpecs(
                                SystemPdsMtsProductLgcomSpecRequestVO.builder()
                                        .pdpId(Objects.toString(input.get("pdpId"), ""))
                                        .siteCode(Objects.toString(input.get("siteCode"), ""))
                                        .shopCode(Objects.toString(input.get("shopCode"), ""))
                                        .build());
            } else {
                sysPdsLgcomSpecList =
                        systemPdsSpecClient.getProductLgcomSpecs(
                                SystemPdsProductLgcomSpecRequestVO.builder()
                                        .pdpId(Objects.toString(input.get("pdpId"), ""))
                                        .siteCode(Objects.toString(input.get("siteCode"), ""))
                                        .build());
            }

            if (ObjectUtils.isNotEmpty(sysPdsLgcomSpecList)) {
                List<Map<String, Object>> specMapList = new ArrayList<>();
                ObjectMapper mapper = new ObjectMapper();
                for (SystemPdsProductLgcomSpecResponseVO resp : sysPdsLgcomSpecList) {
                    Map<String, Object> specMap = mapper.convertValue(resp, Map.class);
                    specMapList.add(specMap);
                }

                // SYSTEM LAYER에서 받아온 spec정보 중 lv1SpecCode로 distinct한 값을 List<Map<String, Object>>로
                // convert
                List<Map<String, Object>> specIdList =
                        specMapList.stream()
                                .map(map -> (String) map.get("lv1SpecCode"))
                                .filter(lv1SpecCode -> lv1SpecCode != null)
                                .distinct()
                                .map(
                                        lv1SpecCode -> {
                                            Map<String, Object> newMap = new HashMap<>();
                                            newMap.put("specId", lv1SpecCode);
                                            return newMap;
                                        })
                                .collect(Collectors.toList());

                for (Map<String, Object> map : specIdList) {
                    List<SystemPdsSpecSchemaPart2ResponseVO> list =
                            systemPdsSpecClient.getCompareSpecSchemaPart2(
                                    SystemPdsSpecSchemaPart2RequestVO.builder()
                                            .siteCode(Objects.toString(input.get("siteCode"), ""))
                                            .pdpId(Objects.toString(input.get("pdpId"), ""))
                                            .specId(Objects.toString(map.get("specId"), ""))
                                            .build());

                    specResult.addLMultiData(LDataUtil.getLMultiDataFromListObj(list));
                }
            }
        }
        specResult.setNullToInitialize(true);

        return specResult;
    }

    public List<Map<String, Object>> retrieveKeySpecList(
            LMultiData validModelInfoList, Map<String, Object> input) {
        List<Map<String, Object>> keySpecsList = new ArrayList<>();
        List<Map<String, Object>> keySpecList = new ArrayList<>();
        Map<String, Object> keySpecsMap = new HashMap<>();
        input.put("modelIds", validModelInfoList.get("pdpId"));

        String skuId = "";
        for (int i = 0; i < validModelInfoList.getMaxDataCount(); i++) {
            skuId = validModelInfoList.getLData(i).getString("skuId");
            List<SystemPdsKeySpecResponseVO> sysPdsKeySpecList =
                    systemPdsSpecClient.getKeySpecs(
                            SystemPdsKeySpecRequestVO.builder()
                                    .localeCode(Objects.toString(input.get("localeCode"), ""))
                                    .siteCode(Objects.toString(input.get("siteCode"), ""))
                                    .skuId(skuId)
                                    .lv3ProductCodeNotUseFlag(
                                            Objects.toString(
                                                    input.get("productLevel3NotUseFlag"), ""))
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsKeySpecList)) {
                for (SystemPdsKeySpecResponseVO sysPdsKeySpec : sysPdsKeySpecList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("specName", sysPdsKeySpec.getSpecNm());
                    map.put("specValue", sysPdsKeySpec.getSpecNm());
                    map.put("keySpecOrder", Integer.parseInt(sysPdsKeySpec.getDspSeq()));
                    keySpecList.add(map);
                }

                keySpecsMap.put("keySpec", keySpecList);
                keySpecsMap.put("sku", skuId);

                keySpecsList.add(keySpecsMap);
            }
        }

        return keySpecsList;
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> retrieveDefaultKeySpecList(
            LMultiData validModelInfoList, Map<String, Object> input) {

        List<Map<String, Object>> defaultKeySpecList = null;

        List<SystemPdsDefaultKeySpecResponseVO> sysPdsDefaultKeySpecList =
                systemPdsSpecClient.getCompareDefaultKeySpecList(
                        SystemPdsDefaultKeySpecRequestVO.builder()
                                .skuIdList((List<String>) validModelInfoList.get("skuId"))
                                .localeCode(Objects.toString(input.get("localeCode"), ""))
                                .siteCode(Objects.toString(input.get("siteCode"), ""))
                                .lv3ProductCodeNotUseFlag(
                                        Objects.toString(input.get("productLevel3NotUseFlag"), ""))
                                .build());

        if (ObjectUtils.isNotEmpty(sysPdsDefaultKeySpecList)) {
            defaultKeySpecList = new ArrayList<>();
            for (SystemPdsDefaultKeySpecResponseVO sysPdsDefaultKeySpec :
                    sysPdsDefaultKeySpecList) {
                Map<String, Object> map = new HashMap<>();
                map.put("productLevel2Code", sysPdsDefaultKeySpec.getLv2ProductCode());
                map.put("specName", sysPdsDefaultKeySpec.getSpecNm());
                map.put("keySpecOrder", Integer.parseInt(sysPdsDefaultKeySpec.getDspSeq()));
                defaultKeySpecList.add(map);
            }
        }

        return defaultKeySpecList;
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> retrieveFilterCountryList(Map<String, Object> input) {

        List<Map<String, Object>> filterCountryList = new ArrayList<>();
        List<String> countryList = null;

        /** Filter Country Code 조회 */
        if (null != input.get("countryCode") && !"".equals(input.get("countryCode"))) {
            String countryCode = input.get("countryCode").toString();
            if (countryCode != null && !"".equals(countryCode)) {
                countryList = new ArrayList<>();
                String[] countryCodes = countryCode.split(",");
                for (String cc : countryCodes) {
                    countryList.add(cc);
                }
                input.put("countryList", countryList);
            }
        }

        List<SystemAdminFilterCountryListResponseVO> sysAdminFilterCountryList =
                systemAdminProductClient.getFilterCountryList(
                        SystemAdminFilterCountryListRequestVO.builder()
                                .localeCode(Objects.toString(input.get("siteCode"), ""))
                                .bizType(Objects.toString(input.get("bizType"), ""))
                                .superCategoryId(Objects.toString(input.get("superCategoryId"), ""))
                                .countryCodeList(countryList)
                                .build());

        if (ObjectUtils.isNotEmpty(sysAdminFilterCountryList)) {
            ObjectMapper mapper = new ObjectMapper();
            filterCountryList = new ArrayList<>();
            for (SystemAdminFilterCountryListResponseVO resp : sysAdminFilterCountryList) {
                Map<String, Object> map = mapper.convertValue(resp, Map.class);
                filterCountryList.add(map);
            }
        }

        return filterCountryList;
    }

    private String changeStringForAdobe(String str) {

        String value = "";
        if (str != null) {
            value = str.replaceAll("[^\uAC00-\uD7A3xfe0-9a-zA-Z\\s\\-\\/]", "").trim();

            value = value.replaceAll("/", "-");
            value = value.replaceAll(" ", "-");
            value = value.replaceAll("--", "-").replaceAll("--", "-").replaceAll("--", "-");
        }
        return value.toLowerCase();
    }

    private SystemPdsProductResponseVO getProductInfo(String pimSkuId) {

        SystemPdsProductResponseVO systemPdsProductRespVO =
                systemPdsProductClient.getProductInfo(
                        SystemPdsProductRequestVO.builder()
                                .skuId(pimSkuId)
                                .localeCode(
                                        cachedDataUtil
                                                .getComLocaleCode(
                                                        CachedCodeRequestVO.builder().build())
                                                .getLocaleCode())
                                .build());

        if (ObjectUtils.isEmpty(systemPdsProductRespVO)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        return systemPdsProductRespVO;
    }

    private String appendSku(String url, String sku) {
        String appendUrl = null;
        if (null != url) {
            StringBuffer stBu = new StringBuffer();
            stBu.append(url).append("?sku=").append(sku);
            appendUrl = stBu.toString();
            stBu.setLength(0);
        }
        return appendUrl;
    }
}
