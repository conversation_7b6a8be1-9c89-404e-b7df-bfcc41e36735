package com.lge.d2x.interfaces.system.admin.product.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemAdminProductPromotionMsgResponseVO {
    @Schema(description = "모델 아이디")
    private String modelId;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 1 사용여부")
    private String obsAdditionalDisclaimerText1Flag;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 1 메세지")
    private String obsAdditionalDisclaimerText1Msg;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 2 사용여부")
    private String obsAdditionalDisclaimerText2Flag;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 2 메세지")
    private String obsAdditionalDisclaimerText2Msg;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 3 사용여부")
    private String obsAdditionalDisclaimerText3Flag;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 3 메세지")
    private String obsAdditionalDisclaimerText3Msg;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 4 사용여부")
    private String obsAdditionalDisclaimerText4Flag;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 4 메세지")
    private String obsAdditionalDisclaimerText4Msg;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 5 사용여부")
    private String obsAdditionalDisclaimerText5Flag;

    @Schema(description = "OBS 제품 Shipping관련 추가 고지사항 5 메세지")
    private String obsAdditionalDisclaimerText5Msg;
}
