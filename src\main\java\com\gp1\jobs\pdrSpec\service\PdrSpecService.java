/*******************************************************************************
 * FILE : PdrSpecService.java
 * DESC : PDR SPEC Service
 * PROJ : PIM
 *******************************************************************************
 *                  Modification History
 *******************************************************************************
 * CSR No.          DATE            AUTHOR          DESCRIPTION
 *******************************************************************************
 * PIMSI-171        2025-02-10      LEEJAEYOUNG     [PIM] PDR SPEC LOCALE NAME 변경 로직 수정 건
 * LGCOMMON-13811   2025-04-02      TAEYOUNGEUM     Spec Pool 통합 요청 건. AV/AO Locale Name 통합
 *******************************************************************************/
package com.gp1.jobs.pdrSpec.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gp1.cmm.util.NullUtil;
import com.gp1.jobs.pdrSpec.mapper.inf.PdrSpecInfMapper;
import com.gp1.jobs.pdrSpec.mapper.mgr.PdrSpecMgrMapper;
import com.gp1.jobs.pdrSpec.model.PdrSpecMasterInfModel;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PdrSpecService {

	public String batchHistoryId;

	@Autowired
	private PdrSpecInfMapper pdrSpecInfMapper;

	@Autowired
	private PdrSpecMgrMapper pdrSpecMgrMapper;
	
	//compliance value code contstant
	private static final String COMPLIANCE_VALUE_CODE = "Definable Spec Only in LG.COM";

	@Transactional(value = "mgrTransactionManager")
	public void executeTask(StepContribution contribution, ChunkContext chunkContext) throws Exception {
    	
		List<PdrSpecMasterInfModel> Info;
		List<PdrSpecMasterInfModel> InfoStatus;
		
		Map<String, Object> pdrPimSpec = new HashMap<String, Object>();
		
		List<PdrSpecMasterInfModel> copyValueMapList = new ArrayList<PdrSpecMasterInfModel>();
		List<PdrSpecMasterInfModel> copyBtobValueMapList = new ArrayList<PdrSpecMasterInfModel>();
		List<PdrSpecMasterInfModel> copyItemCodeList = new ArrayList<PdrSpecMasterInfModel>();
		List<PdrSpecMasterInfModel> copyCategoryCodeList = new ArrayList<PdrSpecMasterInfModel>();

		List<PdrSpecMasterInfModel> copySpecLevel1UpdateList = new ArrayList<PdrSpecMasterInfModel>();
		List<PdrSpecMasterInfModel> copySpecLevel2UpdateList = new ArrayList<PdrSpecMasterInfModel>();
		List<PdrSpecMasterInfModel> copySpecValueMapUpdateList = new ArrayList<PdrSpecMasterInfModel>();
		List<PdrSpecMasterInfModel> copySpecBtobValueMapUpdateList = new ArrayList<PdrSpecMasterInfModel>();

		List<PdrSpecMasterInfModel> copyInfSpecUpdateMap = new ArrayList<PdrSpecMasterInfModel>();

		try {

			batchHistoryId = this.getClass().getSimpleName().replace("Service", "Job");

			// Transfer 값이 N 인 GLOBAL 데이터를 I/F 테이블에서 조회
			Info = pdrSpecInfMapper.retrievePdrTansferMasterInfo();
			log.debug("1. PdrSpecTasklet^^PdrSpecService^^executeTask >> Info >> " + Info);

			// Transfer 값이 N 인 GLOBAL 데이터를 I/F 테이블에서 조회 - Sales Model 기준으로 UPDATE/DELETE/INSERT 건수를 조회 
			InfoStatus = pdrSpecInfMapper.retrievePdrTransferMasterStatus();
			log.debug("2. PdrSpecTasklet^^PdrSpecService^^executeTask >> InfoStatus >> " + InfoStatus);

			Set<String> exceptionList = pdrSpecMgrMapper.retrievePdrExceptionList();
			Set<String> exceptionSuffixCodeList = pdrSpecMgrMapper.retrievePdrExceptionSuffixCodeList();

			PdrSpecMasterInfModel modelData = null;
			List<PdrSpecMasterInfModel> modelDataList = new ArrayList<PdrSpecMasterInfModel>();

			if (!Info.isEmpty()) {
				for (PdrSpecMasterInfModel item : Info) {
					
					Map<String, Object> pimLocaleMap = pdrSpecMgrMapper.retrievePimLocaleMap(item);
						
					item.setUseFlag("Y");
					item.setBatchHistoryId(batchHistoryId);
					item.setLocaleCode(NullUtil.nvl(pimLocaleMap.get("localeCode"), ""));
					item.setLanguageCode(NullUtil.nvl(pimLocaleMap.get("languageCode"), ""));
					item.setAffiliateCode(NullUtil.nvl(pimLocaleMap.get("affiliateCode"), ""));
					item.setLgComLocaleCode(NullUtil.nvl(pimLocaleMap.get("lgComLocaleCode"), ""));

					item.setExceptionListFlag(isDivisionExceptionList(item));
					item.setExceptionSuffixCodeFlag(isDivisionExceptionSuffixCode(item));

					Map<String, Object> level1Map = new HashMap<String, Object>();
					Map<String, Object> level2Map = new HashMap<String, Object>();

					boolean isExceptionModel = "Y".equals(isDivisionExceptionList(item)) ? false // exceptionList 무시 
							: "Y".equals(isDivisionExceptionSuffixCode(item)) ? exceptionSuffixCodeList.contains(item.getDivisionCode() + "." + item.getSalesModelName()) // exceptionList 중 suffixCode 무시
									: exceptionList.contains(item.getDivisionCode() + "." + item.getSalesModelName() + (item.getSalesModelSuffixCode() == null ? "" : "." + item.getSalesModelSuffixCode())); // exceptionList 전체 체크

					if (!isExceptionModel) {

						log.debug("3. PdrSpecTasklet^^PdrSpecService^^executeTask >> item >> " + item);

						//get sku Product Map (sku, bizType, productLv1Code, productLv3Code)
						Map<String, Object> mdmsProductMap = new HashMap<String, Object>();
						List<Map<String, Object>> skuProductList = (List<Map<String, Object>>) pdrSpecMgrMapper.retrievePdrSkuProduct(item);

						if (skuProductList.isEmpty() && skuProductList.size() == 0) {
							Map<String, Object> salesModelMap = new HashMap<String, Object>();
							salesModelMap.put("existFlag", "N");

							skuProductList.add(salesModelMap);
						}
						
						log.debug("3-1. PdrSpecTasklet^^PdrSpecService^^executeTask >> skuProductList  : " + skuProductList);

						for (int i = 0; skuProductList.size() > i; i++) {
							Map<String, Object> skuProductMap = skuProductList.get(i);

							String sku = "";
							String bizType = "B2C";
							String skuLocaleCode = "";

							String pdrUseFlag = "";
							String pimSpecFlag = "Y";
							String productLv1Code = "";
							String productLv3Code = "";
							if ("Y".equals(skuProductMap.get("existFlag"))) {
								sku = NullUtil.nvl(skuProductMap.get("sku"), "");
								bizType = NullUtil.nvl(skuProductMap.get("bizType"), "");
								skuLocaleCode = NullUtil.nvl(skuProductMap.get("localeCode"), "");
								pdrUseFlag = NullUtil.nvl(skuProductMap.get("pdrUseFlag"), "");
								productLv1Code = NullUtil.nvl(skuProductMap.get("productLv1Code"), "");
								productLv3Code = NullUtil.nvl(skuProductMap.get("productLv3Code"), "");
							} else {
								mdmsProductMap = (Map<String, Object>) pdrSpecMgrMapper.retrievePdrMdmsProduct(item);
								if (null != mdmsProductMap) {
									productLv1Code = NullUtil.nvl(mdmsProductMap.get("productLv1Code"), "");
									productLv3Code = NullUtil.nvl(mdmsProductMap.get("productLv3Code"), "");
								}
							}

							item.setSku(sku);
							item.setBizType(bizType);
							item.setSkuLocaleCode(skuLocaleCode);

							item.setPdrUseFlag(pdrUseFlag);
							item.setPimSpecFlag(pimSpecFlag);
							item.setProductLv1Code(productLv1Code);
							item.setProductLv3Code(productLv3Code);
							log.info("3-2. PdrSpecTasklet^^PdrSpecService^^executeTask >> skuMap >> sku >> " + sku + " , localeCode : " +item.getLgComLocaleCode() + " , bizType >> " + bizType + 
									" , productLv1Code >> " + productLv1Code + " , productLv3Code >> " + productLv3Code + " , pdrUseFlag : " + pdrUseFlag + " , existFlag : " + skuProductMap.get("existFlag"));

							//1LEVEL
							item.setHighLevelSpecNo("1");
							//1LEVEL 데이터가 존재하면 display order 는 999 로 세팅
							int level1Count = pdrSpecMgrMapper.retrieveLevale1OrderCheck(item);
							if (level1Count > 0) {
								item.setCategoryOrderValue("999");
							}

							//1LEVEL SPEC_ID 에 대한 duplicateCheck 
							//chage_flag 값이 U 일 경우 문자열 비교를 위해 level1SpecId, level1SpecName 같이 조회 
							level1Map = pdrSpecMgrMapper.retrieveLevel1Check(item);
							log.debug("4. PdrSpecTasklet^^PdrSpecService^^executeTask >> level1 duplicateCheck >> " + NullUtil.nvl(level1Map.get("duplicateCheck"), "N"));

							//insert into pim_spec_pdr_m_inf
							if ("I".equals(item.getChangeFlag())) {
								if (!"Y".equals(NullUtil.nvl(level1Map.get("duplicateCheck"), "N"))) {
									pdrSpecMgrMapper.insertPdrSpec1LevelMaster(item);
										copyCategoryCodeList.add(item);
								} else {
									if (!"".equals(productLv1Code) && !"".equals(productLv3Code)) {
										copyCategoryCodeList.add(item);
									}
								}
							}

							//2LEVEL
							item.setHighLevelSpecNo("2");
							//1LEVEL에 대한 2LEVEL 이 존재하면 display order 는 999 로 세팅
							int level2Count = pdrSpecMgrMapper.retrieveLevale2OrderCheck(item);
							if (level2Count > 0) {
								item.setItemOrderValue("999");
							}

							//2LEVEL SPEC_ID 에 대한 duplicateCheck
							//chage_flag 값이 U 일 경우 문자열 비교를 위해 level2SpecId, level2SpecName 같이 조회 
							level2Map = pdrSpecMgrMapper.retrieveLevel2Check(item);
							log.debug("5. PdrSpecTasklet^^PdrSpecService^^executeTask >> level2 duplicateCheck >> " + NullUtil.nvl(level2Map.get("duplicateCheck"), "N"));

							//insert into pim_spec_pdr_m_inf
							if ("I".equals(item.getChangeFlag())) {
								if (!"Y".equals(NullUtil.nvl(level2Map.get("duplicateCheck"), "N"))) {
									pdrSpecMgrMapper.insertPdrSpec2LevelMaster(item);
									copyItemCodeList.add(item);
								} else {
									if (!"".equals(productLv1Code) && !"".equals(productLv3Code)) {
										copyItemCodeList.add(item);
									}
								}
							}

							if (!"D".equals(item.getChangeFlag())) {
								//1LEVEL , 2LEVEL UPDATE
								if (!item.getLgcomCategory().equals(NullUtil.nvl(level1Map.get("level1SpecName"), "")) && item.getCategoryCode().equals(NullUtil.nvl(level1Map.get("level1SpecId"), ""))) {
									pdrSpecMgrMapper.updatePdrSpec1LevelMaster(item);
									copySpecLevel1UpdateList.add(item);
								}
								if (!item.getLgcomItem().equals(NullUtil.nvl(level2Map.get("level2SpecName"), "")) && item.getItemCode().equals(NullUtil.nvl(level2Map.get("level2SpecId"), ""))) {
									pdrSpecMgrMapper.updatePdrSpec2LevelMaster(item);
									copySpecLevel2UpdateList.add(item);
								}
							}

							//important : 수정시에는 use_yn 값을 변경하지 않음. 삭제 시에만 use_yn ='N' 으로 변경하고 pdr_delete_flag='Y' 로 처리
							if ("D".equals(item.getChangeFlag())) {
								item.setUseFlag("N");
							} else if ("U".equals(item.getChangeFlag())) {
								item.setUseFlag("Y");
							} else {
								item.setUseFlag("Y");
							}

							PdrSpecMasterInfModel btobItem = new PdrSpecMasterInfModel();
							if ("B2B".equals(skuProductMap.get("bizType"))) {
								BeanUtils.copyProperties(item, btobItem);
							}

							//exist check the model spec map 
							
							Map<String, Object> modelSpecDupMap = pdrSpecMgrMapper.retrievePdrModelSpecMapInfCheck(item);
							log.debug("7-1. PdrSpecTasklet^^PdrSpecService^^executeTask >> modelSpecDupMap >> " + modelSpecDupMap);

							String modelSpecDuplCheck = modelSpecDupMap.get("modelSpecDuplCheck").toString();
							if ("I".equals(item.getChangeFlag()) && "Y".equals(modelSpecDuplCheck) && "N".equals(modelSpecDupMap.get("useFlag"))) {
								item.setChangeFlag("U");
							}

							//check the compliance
							if (!"".equals(NullUtil.nvl(item.getLgcomValue(), ""))) {
								if (COMPLIANCE_VALUE_CODE.equals(item.getLgcomValue())) {
									item.setComplianceUseFlag("Y");
								} else {
									item.setComplianceUseFlag("N");
								}
							}

							if (!"Y".equals(modelSpecDuplCheck) && "I".equals(item.getChangeFlag())) {
								//insert model_spec_pdr_map_inf
								pdrSpecMgrMapper.insertPdrModelSpecMapInf(item);
								log.debug("7-2. PdrSpecTasklet^^PdrSpecService^^executeTask >> insertPdrModelSpecMapInf");

								if ("B2C".equals(item.getBizType())) {
									copyValueMapList.add(item);
								} else if ("B2B".equals(item.getBizType())) {
									copyBtobValueMapList.add(btobItem);
								}

							} else {

								//이미 insert 된 대상은 무조건 U/D 상태값에서만 update가 이루어져야 함. (I 로 오는 경우에 대한 예외 처리)
								if (!"I".equals(item.getChangeFlag())) {

									//1LEVEL , 2LEVEL UPDATE. LGCOMMON-13811. Spec Pool 통합 요청 건
									if (!item.getLgcomCategory().equals(NullUtil.nvl(level1Map.get("level1SpecName"), "")) 
											&& item.getCategoryCode().equals(NullUtil.nvl(level1Map.get("level1SpecId"), ""))
											&& ("AV".equals(item.getProductLv1Code()) || "AO".equals(item.getProductLv1Code()))
											) {
										pdrSpecMgrMapper.updatePdrModelSpecMapLv1Inf(item);
									}
									//LGCOMMON-13811. Spec Pool 통합 요청 건
									if (!item.getLgcomItem().equals(NullUtil.nvl(level2Map.get("level2SpecName"), "")) 
											&& item.getItemCode().equals(NullUtil.nvl(level2Map.get("level2SpecId"), ""))
											&& ("AV".equals(item.getProductLv1Code()) || "AO".equals(item.getProductLv1Code()))
											) {
										pdrSpecMgrMapper.updatePdrModelSpecMapLv2Inf(item);
									}
									
									//update model_spec_pdr_map_inf
									pdrSpecMgrMapper.updatePdrModelSpecMapInf(item);
									log.debug("7-3. PdrSpecTasklet^^PdrSpecService^^executeTask >> updatePdrModelSpecMapInf");

									if ("B2C".equals(item.getBizType())) {
										copySpecValueMapUpdateList.add(item);
									} else if ("B2B".equals(item.getBizType())) {
										copySpecBtobValueMapUpdateList.add(btobItem);
									}

								} else {

									if (null != btobItem.getSku()) {
										copyBtobValueMapList.add(btobItem);
									} else if (null != item.getSku()) {
										copyValueMapList.add(item);
									}

								}

							}
							
							//pim_model_spec_value_map_inf & pim_pdr_model_info_inf  handle start
							if (null != sku && !"".equals(sku)) {

								modelData = new PdrSpecMasterInfModel();

								//sku exist check 를 위한 object
								modelData.setSkuStr(sku);
								modelData.setBizType(item.getBizType());
								modelData.setLocaleCode(item.getLocaleCode());
								modelData.setSkuLocaleCode(item.getSkuLocaleCode());
								modelData.setSalesModelName(item.getSalesModelName());
								modelData.setSalesModelSuffixCode(item.getSalesModelSuffixCode());
								modelDataList.add(modelData);

								if ("I".equals(item.getChangeFlag())) {

									if ("B2C".equals(item.getBizType())) {
										copyInfSpecUpdateMap.add(item);
									} else if ("B2B".equals(item.getBizType())) {
										copyInfSpecUpdateMap.add(btobItem);
									}

								}

							}

						}

						if ("N".equals(skuProductList.get(0).get("existFlag"))) {
							skuProductList.clear();
						}

					}
				}
			}

			if (!InfoStatus.isEmpty()) {

				int UpdateCnt = 0;
				int DeleteCnt = 0;
				int processRow = -1; // 제외 되지 않은 로우 확인

				String skuStr = "";
				String localeCode = "";

				for (int i = 0; i < InfoStatus.size(); i++) {

					Map<String, Object> pimLocaleMap = pdrSpecMgrMapper.retrievePimLocaleMap(InfoStatus.get(i));

					InfoStatus.get(i).setBatchHistoryId(batchHistoryId);
					InfoStatus.get(i).setLocaleCode(NullUtil.nvl(pimLocaleMap.get("localeCode"), ""));
					InfoStatus.get(i).setLgComLocaleCode(NullUtil.nvl(pimLocaleMap.get("lgComLocaleCode"), ""));
					InfoStatus.get(i).setAffiliateCode(NullUtil.nvl(pimLocaleMap.get("affiliateCode"), ""));

					PdrSpecMasterInfModel itemC = InfoStatus.get(i);

					itemC.setExceptionListFlag(isDivisionExceptionList(itemC));
					itemC.setExceptionSuffixCodeFlag(isDivisionExceptionSuffixCode(itemC));

					PdrSpecMasterInfModel itemNext = null;
					if (i + 1 < InfoStatus.size()) {
						itemNext = InfoStatus.get(i + 1);
					}

					log.info("7. PdrSpecTasklet^^PdrSpecService^^executeTask >> InfoStatus LocaleCode : " + itemC.getLgComLocaleCode() + " , DivisionCode : " + itemC.getDivisionCode() + " , SalesModelName : " + itemC.getSalesModelName() + " , SalesModelSuffixCode : " + itemC.getSalesModelSuffixCode() + " , bizType : " + itemC.getBizType());
					if (itemNext != null) {
						log.info("7.1. PdrSpecTasklet^^PdrSpecService^^executeTask >> InfoStatus next LocaleCode : " + itemNext.getLocaleCode() + " , DivisionCode:" + itemNext.getDivisionCode() + ", SalesModelName : " + itemNext.getSalesModelName() + " , SalesModelSuffixCode : " + itemNext.getSalesModelSuffixCode() + " , bizType : " + itemNext.getBizType());
					}
					
					boolean isExceptionModel = "Y".equals(isDivisionExceptionList(itemC)) ? false // exceptionList 무시
							:  "Y".equals(isDivisionExceptionSuffixCode(itemC)) ? exceptionSuffixCodeList.contains(itemC.getDivisionCode()+"."+itemC.getSalesModelName()) // exceptionList 중 suffixCode 무시
							:  exceptionList.contains(itemC.getDivisionCode()+"."+itemC.getSalesModelName()+(itemC.getSalesModelSuffixCode()==null?"":"."+itemC.getSalesModelSuffixCode())); // exceptionList 전체 체크

					// 다음 것이 같을 때는 상태만 체크
					if (itemNext != null && itemC.getLgComLocaleCode().equals(itemNext.getLocaleCode()) && itemC.getSalesModelName().equals(itemNext.getSalesModelName()) && itemC.getSalesModelSuffixCode().equals(itemNext.getSalesModelSuffixCode()) && itemC.getBizType().equals(itemNext.getBizType())) {
						if (!isExceptionModel) {
							processRow = i;

							if ("Y".equals(itemC.getUpdateFlag()) || "Y".equals(itemC.getDeleteFlag())) {
								UpdateCnt++;
								DeleteCnt++;
							}

							InfoStatus.get(i).setEndFlag("Y");
						} else {
							// 무시된 모델은 E로 넣기로 함
							InfoStatus.get(i).setEndFlag("E");
							InfoStatus.get(i).setPdrUseFlag("N");
						}

					} else {

						if (!isExceptionModel) {
							processRow = i;

							if ("Y".equals(itemC.getUpdateFlag()) || "Y".equals(itemC.getDeleteFlag())) {
								UpdateCnt++;
								DeleteCnt++;
							}

							InfoStatus.get(i).setEndFlag("Y");
						} else {
							// 무시된 모델은 E로 넣기로 함
							InfoStatus.get(i).setEndFlag("E");
							InfoStatus.get(i).setPdrUseFlag("N");
						}

						if (processRow >= 0) {
							PdrSpecMasterInfModel item = InfoStatus.get(processRow);

							item.setUseFlag("Y");
							item.setBatchHistoryId(batchHistoryId);
							modelDataList = modelDataList.stream().distinct().collect(Collectors.toList());
							
							for (PdrSpecMasterInfModel modelItem : modelDataList) {

								skuStr = "";
								localeCode = "";

								if (modelItem.getLocaleCode().equals(item.getLocaleCode())) {
									if (modelItem.getSalesModelName().equals(item.getSalesModelName())) {
										if (modelItem.getSalesModelSuffixCode().equals(item.getSalesModelSuffixCode())) {
											if (modelItem.getBizType().equals(item.getBizType())) {
												skuStr = modelItem.getSkuStr();
												localeCode = modelItem.getSkuLocaleCode();
											}
										}
									}
								}

								if (modelItem.getSkuLocaleCode().equals(item.getLocaleCode())) {
									if (!"".equals(skuStr)) {
										item.setSkuStr(skuStr);
										item.setLocaleCode(localeCode);

										//PDR SPEC이 들어오기 이전 SPEC은 PIM_MODEL_SPEC_VALUE_MAP_NON_PDR 테이블로 적재
										if ("N".equals(item.getPdrUseFlag())) {
											pdrSpecMgrMapper.InsertChangeNonPdrSpec(item);
											pdrSpecMgrMapper.deleteChangeNonPdrSpec(item);
										}

										item.setPdrUseFlag("Y");
										item.setPimSpecFlag("Y");
										log.info("8. PdrSpecTasklet^^PdrSpecService^^executeTask >> skuStr >> " + skuStr + " localeCode : " + item.getLocaleCode());

										pdrSpecMgrMapper.updateProductPimSpecUse(item);
									}
									//pim_model_spec_value_map_inf & pim_pdr_model_info_inf  handle end
								}
								
							}

							if (UpdateCnt > 0 || DeleteCnt > 0) {
								item.setNewDataFlag("N");
								item.setUpdateDataFlag("Y");
							} else {
								item.setNewDataFlag("Y");
								item.setUpdateDataFlag("N");
							}

							//get sales_model count
							String modelInfoInsertFlag = "Y";
							if ("B2B".equals(item.getBizType()) && "N".equals(item.getExistModelFlag())) {
								modelInfoInsertFlag = "N";
							}

							int pdrModelInfoInfCount = pdrSpecMgrMapper.retrievePdrModelInfoCount(item);
							log.debug("9. PdrSpecTasklet^^PdrSpecService^^executeTask >> pdrModelInfoInfCount >> " + pdrModelInfoInfCount);

							//insert or update pdr_model_info_inf
							if (pdrModelInfoInfCount > 0) {
								pdrSpecMgrMapper.updatePdrModelInfoInf(item);
							} else {
								if ("Y".equals(modelInfoInsertFlag)) {
									pdrSpecMgrMapper.insertPdrModelInfoInf(item);
								}
							}

							processRow = -1;
						}
						UpdateCnt = 0;
						DeleteCnt = 0;
					}
				}

				for (int i = 0; i < InfoStatus.size(); i++) {
					PdrSpecMasterInfModel itemC = InfoStatus.get(i);

					if ("E".equals(itemC.getEndFlag())) {
						//EXCEPTION_LIST 모델은 pim_product_m 테이블에 pdr_use_flag n / pim_produc_d 테이블 pim_spec_flag n 처리
						pdrSpecMgrMapper.updateProductPimSpecUse(itemC);
					}

					pdrSpecInfMapper.updatePdrTransferMasterResult(itemC);
					pdrSpecInfMapper.updatePdrTransferDetailResult(itemC);
				}

			}
			
			//insert and update spec, spect value
			pdrPimSpec.put("copyValueMapList", copyValueMapList);
			pdrPimSpec.put("copyBtobValueMapList", copyBtobValueMapList);
			pdrPimSpec.put("copyItemCodeList", copyItemCodeList);
			pdrPimSpec.put("copyCategoryCodeList", copyCategoryCodeList);
			
			pdrPimSpec.put("copySpecLevel1UpdateList", copySpecLevel1UpdateList);
			pdrPimSpec.put("copySpecLevel2UpdateList", copySpecLevel2UpdateList);
			pdrPimSpec.put("copySpecValueMapUpdateList", copySpecValueMapUpdateList);
			pdrPimSpec.put("copySpecBtobValueMapUpdateList", copySpecBtobValueMapUpdateList);

			pdrPimSpec.put("copyInfSpecUpdateMap", copyInfSpecUpdateMap);

			this.copyPdrPimSpec(pdrPimSpec);
			
		} catch (Exception e) {
			log.error(this.getClass().getName() + "." + "execBatch()^^Exception!!! : " + ExceptionUtils.getStackTrace(e));
			throw e;
		}

    }

	@SuppressWarnings({ "unchecked" })
	public void copyPdrPimSpec(Map<String, Object> pdrPimSpec) throws Exception {

		List<PdrSpecMasterInfModel> itemCodeList = null;
		List<PdrSpecMasterInfModel> valueMapList = null;
		List<PdrSpecMasterInfModel> btobValueMapList = null;
		List<PdrSpecMasterInfModel> categoryCodeList = null;

		List<PdrSpecMasterInfModel> updateLevel1SpecList = null;
		List<PdrSpecMasterInfModel> updateLevel2SpecList = null;
		List<PdrSpecMasterInfModel> updateValueMapSpecList = null;
		List<PdrSpecMasterInfModel> updateBtobValueMapSpecList = null;
		
		List<PdrSpecMasterInfModel> infSpecUpdateList = null;

		try {
			log.info("100. PdrSpecTasklet^^PdrSpecService^^executeTask >> copyPdrPimSpec >> Start!!!");
			
			if (!pdrPimSpec.isEmpty()) {
				
				//INSERT PIM_SPEC_LV1_M / PIM_SPEC_LV3_M / INF_SPEC_UPDATE_MAP(LV1/LV3)
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyCategoryCodeList")).size() > 0) {
					categoryCodeList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyCategoryCodeList");

					int lv1PimSpecLv1Cnt = 0;
					int lv1PimSpecLv3Cnt = 0;
					int lv1PimLocaleSpecCnt = 0;

					Map<String, Object> lv1DupMap = new HashMap<String, Object>();
					for (PdrSpecMasterInfModel category : categoryCodeList) {

						if (!"".equals(category.getProductLv1Code()) && !"".equals(category.getProductLv3Code())) {

							lv1DupMap = pdrSpecMgrMapper.pimPdrLv1SpecCodeDupCheck(category);
							log.debug("101_0. PdrSpecTasklet^^PdrSpecService^^executeTask >> copyPdrPimSpec >> lv1DupMap >> " + lv1DupMap);
							if (null != lv1DupMap) {
								if (!"Y".equals(lv1DupMap.get("lv1SpecDupCheck"))) {
									lv1PimSpecLv1Cnt++;
									pdrSpecMgrMapper.insertPdrPimSpecLv1(category);
								}
								if (!"Y".equals(lv1DupMap.get("lv1SpecPv3DupCheck"))) {
									lv1PimSpecLv3Cnt++;
									pdrSpecMgrMapper.insertLv1PdrPimSpecLv3(category);
								}
								if (!"Y".equals(lv1DupMap.get("lv1SpecNameDupCheck"))) {
									lv1PimLocaleSpecCnt++;
									pdrSpecMgrMapper.insertLv1PdrPimSpecNameMap(category);
								}

								pdrSpecMgrMapper.updatePdrPimSpecLv1Inf(category);
							}
						}

					}

					log.info("101. copyPdrSpec >> copyCategoryCodeList(LV1) >> INSERT >> SIZE >> : " + "categoryCodeList : " + categoryCodeList.size() + " lv1PimSpecLv1Cnt : " + lv1PimSpecLv1Cnt + " lv1PimSpecLv3Cnt : " + lv1PimSpecLv3Cnt + " lv1PimLocaleSpecCnt : " + lv1PimLocaleSpecCnt);
				}

				//INSERT PIM_SPEC_LV1_M / PIM_SPEC_LV3_M / INF_SPEC_UPDATE_MAP(LV2/LV3)
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyItemCodeList")).size() > 0) {
					itemCodeList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyItemCodeList");
					
					int lv2PimSpecLv1Cnt = 0;
					int lv2PimSpecLv3Cnt = 0;
					int lv2PimLocaleSpecCnt = 0;

					Map<String, Object> lv2DupMap = new HashMap<String, Object>();
					for (PdrSpecMasterInfModel item : itemCodeList) {
						
						if (!"".equals(item.getProductLv1Code()) && !"".equals(item.getProductLv3Code())) {

							lv2DupMap = pdrSpecMgrMapper.pimPdrLv2SpecCodeDupCheck(item);
							log.debug("102_0. PdrSpecTasklet^^PdrSpecService^^executeTask >> copyPdrPimSpec >> lv2DupMap >> " + lv2DupMap);

							if (null != lv2DupMap) {
								if (!"Y".equals(lv2DupMap.get("lv2SpecDupCheck"))) {
									lv2PimSpecLv1Cnt++;
									pdrSpecMgrMapper.insertPdrPimSpecLv2(item);
								}
								if (!"Y".equals(lv2DupMap.get("lv2SpecPv3DupCheck"))) {
									lv2PimSpecLv3Cnt++;
									pdrSpecMgrMapper.insertLv2PdrPimSpecLv3(item);
								}
								if (!"Y".equals(lv2DupMap.get("lv2SpecNameDupCheck"))) {
									lv2PimLocaleSpecCnt++;
									pdrSpecMgrMapper.insertLv2PdrPimSpecNameMap(item);
								}

								pdrSpecMgrMapper.updatePdrPimSpecLv2Inf(item);
							}

						}

					}

					log.info("102. copyPdrSpec >> copyItemCodeList(LV2) >> INSERT >> SIZE >> : " + "itemCodeList : " + itemCodeList.size() + " lv2PimSpecLv1Cnt : " + lv2PimSpecLv1Cnt + " lv2PimSpecLv3Cnt : " + lv2PimSpecLv3Cnt + " lv2PimLocaleSpecCnt : " + lv2PimLocaleSpecCnt);
				}
				
				//INSERT PIM_MODEL_SPEC_VALUE_MAP / INF_SPEC_UPDATE_MAP(B2C)
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyValueMapList")).size() > 0) {
					valueMapList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyValueMapList");

					int specValueMapCnt = 0;

					for (PdrSpecMasterInfModel value : valueMapList) {

						String specValueMapCheck = "";
						if (!"".equals(value.getSku()) && !"".equals(value.getProductLv1Code()) && !"".equals(value.getProductLv3Code())) {

							//check the compliance
							if (!"".equals(NullUtil.nvl(value.getLgcomValue(), ""))) {
								if (COMPLIANCE_VALUE_CODE.equals(value.getLgcomValue())) {
									value.setComplianceUseFlag("Y");
								} else {
									value.setComplianceUseFlag("N");
								}
							}

							specValueMapCheck = pdrSpecMgrMapper.pimPdrSpecValueMapDupCheck(value);
							log.debug("103_0. PdrSpecTasklet^^PdrSpecService^^executeTask >> copyValueMapList >> specValueMapCheck : " + specValueMapCheck);

							if (!"Y".equals(specValueMapCheck)) {
								specValueMapCnt++;
								pdrSpecMgrMapper.insertPdrPimModelSpecValueMap(value);

								pdrSpecMgrMapper.updatePimPdrModelSpecMapInf(value);
							}
						}

					}
					log.info("103. copyPdrSpec >> PIM_MODEL_SPEC_VALUE_MAP >> INSERT >> SIZE : " + "copyValueMapList : " + valueMapList.size() + " specValueMapCnt : " + specValueMapCnt);
				}

				//UPDATE PIM_MODEL_SPEC_VALUE_MAP / INSERT INF_SPEC_UPDATE_MAP(VALUE/B2C)
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copySpecValueMapUpdateList")).size() > 0) {
					updateValueMapSpecList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copySpecValueMapUpdateList");

					int updateSpecValueCnt = 0;
					int deleteSpecValueCnt = 0;

					for (PdrSpecMasterInfModel updateValueMap : updateValueMapSpecList) {
						if (!"".equals(updateValueMap.getSku()) && !"".equals(updateValueMap.getProductLv1Code()) && !"".equals(updateValueMap.getProductLv3Code())) {
							log.debug("PdrSpecTasklet^^PdrSpecService^^executeTask >> copySpecValueMapUpdateList >> UPDATE >> changeFlag : " + updateValueMap.getChangeFlag());

							//check the compliance
							if (!"".equals(NullUtil.nvl(updateValueMap.getLgcomValue(), ""))) {
								if (COMPLIANCE_VALUE_CODE.equals(updateValueMap.getLgcomValue())) {
									updateValueMap.setComplianceUseFlag("Y");
								} else {
									updateValueMap.setComplianceUseFlag("N");
								}
							}

							if ("U".equals(updateValueMap.getChangeFlag())) {
								updateSpecValueCnt++;
							} else {
								deleteSpecValueCnt++;
							}

							pdrSpecMgrMapper.updatePdrPimSpecValueMap(updateValueMap);

							if (!"N".equals(updateValueMap.getPdrUseFlag())) {
								pdrSpecInfMapper.insertPimInfSpecUpdateMap(updateValueMap);
								//LGCOMMON-13811. Spec Pool 통합 요청 건. AV/AO Locale Name 통합
								if ("U".equals(updateValueMap.getChangeFlag()) && ("AV".equals(updateValueMap.getProductLv1Code()) || "AO".equals(updateValueMap.getProductLv1Code()))) {
									pdrSpecInfMapper.insertPimInfSpecUpdateMapAVO(updateValueMap);
								}
							}

							pdrSpecMgrMapper.updatePimPdrModelSpecMapInf(updateValueMap);
						}
					}

					log.info("104. copySpecValueMapUpdateList >> UPDATE >> SIZE : " + "updateValueMapSpecList : " + updateValueMapSpecList.size() + " updateSpecValueCnt :" + updateSpecValueCnt + " deleteSpecValueCnt" + deleteSpecValueCnt);
				}

				//INSERT PIM_MODEL_SPEC_VALUE_MAP / INF_SPEC_UPDATE_MAP(B2B)
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyBtobValueMapList")).size() > 0) {
					btobValueMapList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyBtobValueMapList");

					int btobSpecValueMapCnt = 0;

					for (PdrSpecMasterInfModel value : btobValueMapList) {

						String specValueMapCheck = "";
						if (!"".equals(value.getSku()) && !"".equals(value.getProductLv1Code()) && !"".equals(value.getProductLv3Code())) {

							//check the compliance
							if (!"".equals(NullUtil.nvl(value.getLgcomValue(), ""))) {
								if (COMPLIANCE_VALUE_CODE.equals(value.getLgcomValue())) {
									value.setComplianceUseFlag("Y");
								} else {
									value.setComplianceUseFlag("N");
								}
							}

							specValueMapCheck = pdrSpecMgrMapper.pimPdrSpecValueMapDupCheck(value);
							log.debug("105_0. PdrSpecTasklet^^PdrSpecService^^executeTask >> copyBtobValueMapList >> specValueMapCheck : " + specValueMapCheck);

							if (!"Y".equals(specValueMapCheck)) {
								btobSpecValueMapCnt++;
								pdrSpecMgrMapper.insertPdrPimModelSpecValueMap(value);

								pdrSpecMgrMapper.updatePimPdrModelSpecMapInf(value);
							}
						}

					}
					log.info("105. copyPdrSpec >> PIM_MODEL_SPEC_VALUE_MAP(B2B) >> INSERT >> SIZE : " + "btobValueMapList : " + btobValueMapList.size() + " btobSpecValueMapCnt : " + btobSpecValueMapCnt);
				}

				//UPDATE PIM_MODEL_SPEC_VALUE_MAP / INSERT INF_SPEC_UPDATE_MAP(VALUE/B2B)
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copySpecBtobValueMapUpdateList")).size() > 0) {
					updateBtobValueMapSpecList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copySpecBtobValueMapUpdateList");

					int updateBtobSpecValueCnt = 0;
					int deleteBtobSpecValueCnt = 0;

					for (PdrSpecMasterInfModel updateValueMap : updateBtobValueMapSpecList) {
						if (!"".equals(updateValueMap.getSku()) && !"".equals(updateValueMap.getProductLv1Code()) && !"".equals(updateValueMap.getProductLv3Code())) {
							log.debug("PdrSpecTasklet^^PdrSpecService^^executeTask >> copySpecBtobValueMapUpdateList >> UPDATE >> changeFlag : " + updateValueMap.getChangeFlag());

							//check the compliance
							if (!"".equals(NullUtil.nvl(updateValueMap.getLgcomValue(), ""))) {
								if (COMPLIANCE_VALUE_CODE.equals(updateValueMap.getLgcomValue())) {
									updateValueMap.setComplianceUseFlag("Y");
								} else {
									updateValueMap.setComplianceUseFlag("N");
								}
							}

							if ("U".equals(updateValueMap.getChangeFlag())) {
								updateBtobSpecValueCnt++;
							} else {
								deleteBtobSpecValueCnt++;
							}

							pdrSpecMgrMapper.updatePdrPimSpecValueMap(updateValueMap);

							if (!"N".equals(updateValueMap.getPdrUseFlag())) {
								pdrSpecInfMapper.insertPimInfSpecUpdateMap(updateValueMap);
								
								//LGCOMMON-13811. Spec Pool 통합 요청 건. AV/AO Locale Name 통합
								if ("U".equals(updateValueMap.getChangeFlag()) && ("AV".equals(updateValueMap.getProductLv1Code()) || "AO".equals(updateValueMap.getProductLv1Code()))) {
									pdrSpecInfMapper.insertPimInfSpecUpdateMapAVO(updateValueMap);
								}								
							}

							pdrSpecMgrMapper.updatePimPdrModelSpecMapInf(updateValueMap);
						}
					}
					log.info("106. copySpecBtobValueMapUpdateList(B2B) >> UPDATE >> SIZE : " + "updateValueMapSpecList : " + updateValueMapSpecList.size() + " updateBtobSpecValueCnt :" + updateBtobSpecValueCnt + " deleteBtobSpecValueCnt" + deleteBtobSpecValueCnt);
				}

				//UPDATE PIM_LOCALE_SPECNAME_MAP / INSERT INF_SPEC_UPDATE_MAP (LV1)
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copySpecLevel1UpdateList")).size() > 0) {
					updateLevel1SpecList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copySpecLevel1UpdateList");

					int updateLv1LocaleSpecCnt = 0;

					for (PdrSpecMasterInfModel updateLevel1 : updateLevel1SpecList) {

						if (!"".equals(updateLevel1.getSku()) && !"".equals(updateLevel1.getProductLv1Code()) && !"".equals(updateLevel1.getProductLv3Code())) {

							// PDR에서 'I'로 들어 올 시에도 LOCALE NAME을 변경하는걸로 수정되었기때문에 FLAG를 'U'로 업데이트
							if ("I".equals(updateLevel1.getChangeFlag())) {
								updateLevel1.setChangeFlag("U");
								updateLevel1.setChangeFlagUpdateStatus("Y");
							}

							updateLv1LocaleSpecCnt++;
							updateLevel1.setSpecLevelNo("1");
							int updateCnt = pdrSpecMgrMapper.updateLv1PdrPimSpecNameMap(updateLevel1);

							if (updateCnt > 0) {
								pdrSpecMgrMapper.updatePdrPimSpecLv1Inf(updateLevel1);
								
								//PIMSI-171 START 업데이트 되어진 SKU 외에 같은 SPEC_CODE 사용하는 SKU 보내는 기능 주석처리
//								this.loadSpecRow(updateLevel1);
								//PIMSI-171 END
							}

						}

					}

					log.info("107. copySpecLevel1UpdateList >> UPDATE >> SIZE : " + "updateLevel1SpecList : " + updateLevel1SpecList.size() + " updateLv1LocaleSpecCnt : " + updateLv1LocaleSpecCnt);
				}

				//UPDATE PIM_LOCALE_SPECNAME_MAP / INSERT INF_SPEC_UPDATE_MAP (LV2)
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copySpecLevel2UpdateList")).size() > 0) {
					updateLevel2SpecList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copySpecLevel2UpdateList");

					int updateLv2LocaleSpecCnt = 0;

					for (PdrSpecMasterInfModel updateLevel2 : updateLevel2SpecList) {

						if (!"".equals(updateLevel2.getSku()) && !"".equals(updateLevel2.getProductLv1Code()) && !"".equals(updateLevel2.getProductLv3Code())) {

							// PDR에서 'I'로 들어 올 시에도 LOCALE NAME을 변경하는걸로 수정되었기때문에 FLAG를 'U'로 업데이트
							if ("I".equals(updateLevel2.getChangeFlag())) {
								updateLevel2.setChangeFlag("U");
								updateLevel2.setChangeFlagUpdateStatus("Y");
							}

							updateLv2LocaleSpecCnt++;
							updateLevel2.setSpecLevelNo("2");
							int updateCnt = pdrSpecMgrMapper.updateLv2PdrPimSpecNameMap(updateLevel2);

							if (updateCnt > 0) {
								pdrSpecMgrMapper.updatePdrPimSpecLv2Inf(updateLevel2);
								
								//PIMSI-171 START 업데이트 되어진 SKU 외에 같은 SPEC_CODE 사용하는 SKU 보내는 기능 주석처리
//								this.loadSpecRow(updateLevel2);
								//PIMSI-171 END
							}

						}

					}

					log.info("108. copySpecLevel2UpdateList >> UPDATE >> SIZE : " + "updateLevel2SpecList : " + updateLevel2SpecList.size() + " updateLv2LocaleSpecCnt : " + updateLv2LocaleSpecCnt);
				}

				//MKT INF_MKT_PRODUCT_SPEC 테이블의 보내기전 대기 테이블 INF_SPEC_UPDATE_MAP 테이블에 PDR SPEC INSERT
				if (((List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyInfSpecUpdateMap")).size() > 0) {
					infSpecUpdateList = (List<PdrSpecMasterInfModel>) pdrPimSpec.get("copyInfSpecUpdateMap");

					int gp1TransferCnt = 0;
					int gp1SyncTransferCnt = 0;
					int changeFlagUpdateCount = 0;

					PdrSpecMasterInfModel pdrSyncMap = null;
					List<PdrSpecMasterInfModel> pdrSyncSkuList = new ArrayList<PdrSpecMasterInfModel>();

					for (int i = 0; i < infSpecUpdateList.size(); i++) {
						PdrSpecMasterInfModel infSpecUpdate = infSpecUpdateList.get(i);
						
						if (!"".equals(infSpecUpdate.getSku()) && !"".equals(infSpecUpdate.getProductLv1Code()) && !"".equals(infSpecUpdate.getProductLv3Code())) {

							if ("N".equals(infSpecUpdate.getPdrUseFlag())) {
								
								PdrSpecMasterInfModel infSpecUpdateNext = null;
								if (i + 1 < infSpecUpdateList.size()) {
									infSpecUpdateNext = infSpecUpdateList.get(i + 1);
								}
								
								if (null != infSpecUpdateNext && infSpecUpdate.getLgComLocaleCode().equals(infSpecUpdateNext.getLgComLocaleCode()) && infSpecUpdate.getSku().equals(infSpecUpdateNext.getSku())) {
									// 같으면 중복으로 안들어가도록 처리
								} else {
									pdrSyncMap = new PdrSpecMasterInfModel();

									pdrSyncMap.setSku(infSpecUpdate.getSku());
									pdrSyncMap.setLocaleCode(infSpecUpdate.getLocaleCode());
									pdrSyncMap.setProductLv3Code(infSpecUpdate.getProductLv3Code());
									log.info("pdrSyncMap.getSku() : " + pdrSyncMap.getSku() + " // pdrSyncMap.getLocaleCode() : " + pdrSyncMap.getLocaleCode());


									pdrSyncSkuList.add(pdrSyncMap);
								}
								
							} else {
								gp1TransferCnt++;

								if ("Y".equals(infSpecUpdate.getChangeFlagUpdateStatus())) {
									changeFlagUpdateCount++;
									infSpecUpdate.setChangeFlag("I");
								}
								pdrSpecInfMapper.insertPimInfSpecUpdateMap(infSpecUpdate);
							}

						}
					}

					for (int i = 0; pdrSyncSkuList.size() > i; i++) {
						PdrSpecMasterInfModel syncSkuMap = pdrSyncSkuList.get(i);
						
						List<PdrSpecMasterInfModel> skuPimSpecList = new ArrayList<PdrSpecMasterInfModel>();

						skuPimSpecList = pdrSpecMgrMapper.retrieveSpecValueInfo(syncSkuMap);

						for (PdrSpecMasterInfModel specMap : skuPimSpecList) {
							gp1SyncTransferCnt++;

							specMap.setSelectTransferFlag("Y");
							specMap.setBatchHistoryId(batchHistoryId);
							pdrSpecInfMapper.insertPimInfSpecUpdateMap(specMap);
						}
					}

					log.info("109. copyInfSpecUpdateMap >> INSERT >> SIZE : " + "infSpecUpdateList : " + infSpecUpdateList.size() + " gp1TransferCnt : " + gp1TransferCnt + " gp1SyncTransferCnt : " + gp1SyncTransferCnt + " changeFlagUpdateCount : " + changeFlagUpdateCount);
				}

			} // SKU는 없더라도 PRODUCT_LV1_CODE / PRODUCT_LV3_CODE 존재하면 LV1/LV3 테이블에 데이터 적재 SKU 있을시에는 VALUE까지 적재

			log.info("110. PdrSpecTasklet^^PdrSpecService^^executeTask >> copyPdrPimSpec >> End!!!");

		} catch (Exception e) {
			log.error("111. PdrSpecTasklet^^PdrSpecService^^executeTask >> copyPdrPimSpec >> Exception : " + ExceptionUtils.getStackTrace(e));
		}

	}
	
	//PIMSI-171 업데이트 되어진 SKU 외에 같은 SPEC_CODE 사용하는 SKU 보내는 기능 주석처리
	/* public void loadSpecRow(PdrSpecMasterInfModel spec) {

		try {

			List<PdrSpecMasterInfModel> specSkuList = new ArrayList<PdrSpecMasterInfModel>();

			String lv1SpecCode = spec.getProductLv1Code() + "_".concat(spec.getLv1SpecCode());
			String lv2SpecCode = spec.getProductLv1Code() + "_".concat(spec.getLv2SpecCode());

			if ("1".equals(spec.getSpecLevelNo())) {
				specSkuList = pdrSpecMgrMapper.retrieveSpecLv2CodeListWithSku(spec);
			} else {
				specSkuList = pdrSpecMgrMapper.retrieveSkuListWithSpecValue(spec);
			}

			if (specSkuList.size() > 0) {
				for (PdrSpecMasterInfModel skuMap : specSkuList) {
					skuMap.setBatchHistoryId(batchHistoryId);

					skuMap.setSelectTransferFlag("Y");
					skuMap.setPimAdmSpecSyncFlag("N");
					skuMap.setChangeFlag(spec.getChangeFlag());

					skuMap.setLgComLocaleCode(spec.getLgComLocaleCode());
					skuMap.setLocaleCode(Objects.toString(spec.getLocaleCode(), ""));

					skuMap.setLv1SpecCode(lv1SpecCode);
					skuMap.setLv2SpecCode(lv2SpecCode);

					skuMap.setCategoryName(spec.getCategoryName());
					skuMap.setLgcomCategory(Objects.toString(spec.getLgcomCategory(), ""));
					skuMap.setCategoryOrderValue(Objects.toString(spec.getCategoryOrderValue(), ""));

					if ("2".equals(spec.getSpecLevelNo())) {
						skuMap.setProductLv1Code(spec.getProductLv1Code());

						skuMap.setItemName(spec.getItemName());
						skuMap.setLgcomItem(Objects.toString(spec.getLgcomItem(), ""));
						skuMap.setItemOrderValue(Objects.toString(spec.getItemOrderValue(), ""));
					}

					log.info("PdrSpecTasklet^^PdrSpecService^^loadSpecRow >> skuMap DATA >>> " + skuMap);

					pdrSpecInfMapper.insertPimInfSpecUpdateMap(skuMap);

				}
			}

		} catch (Exception e) {
			log.error("PdrSpecTasklet^^PdrSpecService^^executeTask >> loadSpecRow >> Exception : " + ExceptionUtils.getStackTrace(e));
		}
	} */

	private String isDivisionExceptionList(PdrSpecMasterInfModel item) throws Exception {
		String exceptionListFlag = "N";
		try {
			String division = item.getDivisionCode();

			// 5.0에서는 CONF_CODE 테이블 조회하여 DIVISION LIST 가져오지만 PIM 환경은 CONF 환경이없어 일단 String 대체
			String confVal = "ANZ,CDZ,CNZ,CVZ,CWZ,DFZ,DGZ,DLZ,DVZ,GLZ,PNZ";
			if (confVal != null && !"".equals(confVal)) {
				exceptionListFlag = confVal.toUpperCase().indexOf(division.toUpperCase()) > -1 ? "Y" : "N";
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + "." + "execBatch()^^Exception!!! : " + ExceptionUtils.getStackTrace(e));
			throw e;
		}
		return exceptionListFlag;
	}

	private String isDivisionExceptionSuffixCode(PdrSpecMasterInfModel item) throws Exception {
		String exceptionSuffixCodeFlag = "N";
		try {
			String division = item.getDivisionCode();

			// 5.0에서는 CONF_CODE 테이블 조회하여 DIVISION LIST 가져오지만 PIM 환경은 CONF 환경이없어 일단 String 대체
			String confVal = "GLZ,PNZ";
			if (confVal != null && !"".equals(confVal)) {
				exceptionSuffixCodeFlag = confVal.toUpperCase().indexOf(division.toUpperCase()) > -1 ? "Y" : "N";
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + "." + "execBatch()^^Exception!!! : " + ExceptionUtils.getStackTrace(e));
			throw e;
		}
		return exceptionSuffixCodeFlag;
	}

}
