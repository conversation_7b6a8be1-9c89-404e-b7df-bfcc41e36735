<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.packageDeal.v1.repository.pdsmgr.PackageDealRepository">
    <select id="selectPackageDealInfo" parameterType="com.lge.d2x.domain.packageDeal.v1.model.PackageDealInfoRequestVO" 
    resultType="com.lge.d2x.domain.packageDeal.v1.model.PackageDealInfoResponseVO">
	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.packageDeal.v1.service.packageDealService.selectPackageDealInfo */
	       PKGDEAL_TITLE
         , PKGDEAL_CNTS
         , BANNER_TYPE_CODE
         , BANNER_CNTS
         , BANNER_DESKTOP_IMG_PATH
         , BANNER_DESKTOP_IMG_DESC
         , BANNER_MOBL_IMG_PATH
         , BANNER_MOBL_IMG_DESC
		 , DATE_FORMAT(PKGDEAL_EXP_BEGIN_DD, #{dateFormat}) AS PKGDEAL_EXP_BEGIN_DD
		 , DATE_FORMAT(PKGDEAL_EXP_END_DD, #{dateFormat}) AS PKGDEAL_EXP_END_DD
         , PKGDEAL_TERM_EXP_FLAG
     FROM  PRM_PACKAGE_DEAL_M
    WHERE  SITE_CODE 	= #{siteCode}
      AND  PKGDEAL_ID	= #{packageDealId}
      AND  USE_FLAG		= 'Y' 
    </select>
    
    <select id="selectPackageDealSettingList" parameterType="com.lge.d2x.domain.packageDeal.v1.model.PackageDealSettingRequestVO" 
    resultType="com.lge.d2x.domain.packageDeal.v1.model.PackageDealSettingResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.packageDeal.v1.service.packageDealService.selectPackageDealSettingList */
               A.DCNTRATE_DCNT_QUANTITY_VAL
             , A.DCNT_INFO_TITLE
             , A.DCNT_INFO_CNTS
             , (
                SELECT MAX(CAST(DCNTRATE_DCNT_QUANTITY_VAL AS UNSIGNED))
                  FROM PRM_PACKAGE_DEAL_SETTING_D A
                 WHERE A.SITE_CODE = B.SITE_CODE
                   AND A.PKGDEAL_ID = B.PKGDEAL_ID
                   AND A.USE_FLAG = 'Y'
               ) AS MAX_DISCOUNT_RATE
             , A.DCNT_TYPE_CODE
          FROM PRM_PACKAGE_DEAL_M B
         INNER JOIN PRM_PACKAGE_DEAL_SETTING_D A
            ON B.SITE_CODE = A.SITE_CODE
           AND B.PKGDEAL_ID = A.PKGDEAL_ID
           AND A.USE_FLAG = 'Y'
         WHERE B.SITE_CODE = #{siteCode}
           AND B.PKGDEAL_ID = #{packageDealId}
           AND B.USE_FLAG = 'Y'
         ORDER BY A.DSP_SEQ ASC
    </select>
    
    <select id="selectPackageDealCategoryList" parameterType="com.lge.d2x.domain.packageDeal.v1.model.PackageDealCategoryRequestVO" 
    resultType="com.lge.d2x.domain.packageDeal.v1.model.PackageDealCategoryResponseVO">
	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.packageDeal.v1.service.packageDealService.selectPackageDealCategoryList */
            DISTINCT B.LV3_CATEGORY_CODE 	AS categoryCode
          , D.SITE_CATEGORY_NM 				AS categoryNm
          , E.STICKY_IMG_URL
          , E.STICKY_HOVER_ICON_URL
 <choose>
    <when test='categoryStandardFlag == "Y"'>
          , F.LV2_CATEGORY_CODE				AS superCategoryCode
    </when>
    <otherwise>
          , F.LV1_CATEGORY_ID 				AS superCategoryCode
    </otherwise>
 </choose> 
       FROM PRM_PACKAGE_DEAL_M A
      INNER JOIN PRM_PACKAGE_DEAL_CATEGORY_R B
         ON A.SITE_CODE			= B.SITE_CODE
        AND A.PKGDEAL_ID		= B.PKGDEAL_ID
        AND B.USE_FLAG			= 'Y'
      INNER JOIN PRM_PACKAGE_DEAL_PRODUCT_R C
         ON A.PKGDEAL_ID		= C.PKGDEAL_ID
        AND C.USE_FLAG			= 'Y'
      INNER JOIN DSP_DISPLAY_CATEGORY_M D
         ON B.SITE_CODE			= D.SITE_CODE
        AND B.LV3_CATEGORY_CODE	= D.CATEGORY_CODE
        AND D.USE_FLAG			= 'Y'
      INNER JOIN DSP_DISPLAY_CATEGORY_D E
         ON B.SITE_CODE			= E.SITE_CODE
        AND B.LV3_CATEGORY_CODE	= E.CATEGORY_CODE
        AND E.USE_FLAG			= 'Y'
 <choose>
    <when test='categoryStandardFlag == "Y"'>
      INNER JOIN DSP_PDP_CATEGORY_R F
         ON A.SITE_CODE			= F.SITE_CODE
        AND B.LV3_CATEGORY_CODE	= F.LV3_CATEGORY_CODE
    </when>
    <otherwise>
      INNER JOIN DSP_OLD_PDP_CATEGORY_R F
         ON A.SITE_CODE			= F.SITE_CODE
        AND B.LV3_CATEGORY_CODE	= F.LV2_CATEGORY_ID
    </otherwise>
 </choose> 
        AND F.USE_FLAG			= 'Y'
        AND C.PDP_ID			= F.PDP_ID
        AND F.DEFAULT_MAP_FLAG	= 'Y'
      INNER JOIN DSP_PDP_M G
         ON A.SITE_CODE			= G.SITE_CODE
        AND C.PDP_ID			= G.PDP_ID
        AND G.USE_FLAG			= 'Y'
      INNER JOIN DSP_PDP_D H
         ON A.SITE_CODE			= H.SITE_CODE
        AND C.PDP_ID			= H.PDP_ID
        AND H.USE_FLAG			= 'Y'        
      WHERE A.SITE_CODE			= #{siteCode}
        AND A.PKGDEAL_ID		= #{packageDealId}
        AND A.USE_FLAG			= 'Y'
      ORDER BY B.CATEGORY_DSP_SEQ IS NULL ASC, B.CATEGORY_DSP_SEQ ASC, D.DSP_SEQ
    </select>
    
    <select id="selectPackageDealSkuList" parameterType="com.lge.d2x.domain.packageDeal.v1.model.PackageDealSkuRequestVO" 
    resultType="com.lge.d2x.domain.packageDeal.v1.model.PackageDealSkuResponseVO">
	
	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.packageDeal.v1.service.packageDealService.selectPackageDealSkuList */
		   SQL_CACHE
		       GROUP_CONCAT(AAA.LGCOM_SKU_ID SEPARATOR ',') AS skuList
		 , TOTAL_COUNT
	 FROM  (
			  SELECT BBB.LGCOM_SKU_ID 
				   , COUNT(BBB.LGCOM_SKU_ID) OVER () AS TOTAL_COUNT
			   FROM  (
				        SELECT  DISTINCT F.LGCOM_SKU_ID 
					         ,  C.PRODUCT_DSP_SEQ 
					      FROM  PRM_PACKAGE_DEAL_M A
				         INNER  JOIN PRM_PACKAGE_DEAL_CATEGORY_R B
					        ON  A.SITE_CODE				= B.SITE_CODE
					       AND  A.PKGDEAL_ID			= B.PKGDEAL_ID
					       AND  B.USE_FLAG				= 'Y'
				         INNER  JOIN PRM_PACKAGE_DEAL_PRODUCT_R C
					        ON  A.PKGDEAL_ID			= C.PKGDEAL_ID
					       AND  C.USE_FLAG				= 'Y'
				         INNER  JOIN DSP_DISPLAY_CATEGORY_M D
					        ON  B.SITE_CODE				= D.SITE_CODE
					       AND  B.LV3_CATEGORY_CODE		= D.CATEGORY_CODE
					       AND  D.USE_FLAG				= 'Y'
					<choose>
					   <when test='categoryStandardFlag == "Y"'>
				         INNER  JOIN DSP_PDP_CATEGORY_R E
					        ON  A.SITE_CODE				= E.SITE_CODE
					       AND  B.LV3_CATEGORY_CODE		= E.LV3_CATEGORY_CODE
					   </when>
					   <otherwise>
				         INNER  JOIN DSP_OLD_PDP_CATEGORY_R E
					        ON  A.SITE_CODE				= E.SITE_CODE
					       AND  B.LV3_CATEGORY_CODE		= E.LV2_CATEGORY_ID
					   </otherwise>
					</choose> 
					       AND  E.USE_FLAG				= 'Y'
					       AND  C.PDP_ID				= E.PDP_ID
					       AND  E.DEFAULT_MAP_FLAG		= 'Y'
				         INNER  JOIN DSP_PDP_M F
					        ON  A.SITE_CODE				= F.SITE_CODE
					       AND  C.PDP_ID				= F.PDP_ID
					       AND  F.USE_FLAG				= 'Y'
				         INNER  JOIN DSP_PDP_D G
					        ON  A.SITE_CODE				= G.SITE_CODE
					       AND  C.PDP_ID				= G.PDP_ID
					       AND  G.USE_FLAG				= 'Y'
					       AND  G.AEM_PUBL_FLAG			= 'Y'        
					     WHERE  A.SITE_CODE				= #{siteCode}
					       AND  A.PKGDEAL_ID			= #{packageDealId}
					       AND  A.USE_FLAG				= 'Y'
					   <if test="categoryId != null and categoryId != ''">
						<choose>
						   <when test='categoryStandardFlag == "Y"'>
					       AND  E.LV3_CATEGORY_CODE		= #{categoryId}
						   </when>
						   <otherwise>
					       AND  E.LV2_CATEGORY_ID		= #{categoryId}
						   </otherwise>
						</choose> 
					   </if>
	   			     ) BBB		    
			  ORDER BY BBB.PRODUCT_DSP_SEQ
 		   <if test='viewAll != "Y"'>
 		        LIMIT  #{startNo}, #{pageCnt}
           </if>
           ) AAA
    </select>
</mapper>