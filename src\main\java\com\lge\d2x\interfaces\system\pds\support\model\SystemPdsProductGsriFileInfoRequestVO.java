package com.lge.d2x.interfaces.system.pds.support.model;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemPdsProductGsriFileInfoRequestVO {
    private String docTypeCode;
    private String pdpId;
    private String fEnergyLabelDocId;
    private String productFichelDocId;
    private String energyLabelDocId;
    private String siteCode;
    private List<String> docTypeList;
}
