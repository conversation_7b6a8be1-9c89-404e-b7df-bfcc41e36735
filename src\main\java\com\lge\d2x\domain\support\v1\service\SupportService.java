package com.lge.d2x.domain.support.v1.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lge.d2x.domain.support.v1.model.AscInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.AscInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.BasicPromotionWtyExpireRequestVO;
import com.lge.d2x.domain.support.v1.model.BasicPromotionWtyExpireResponseVO;
import com.lge.d2x.domain.support.v1.model.BundleGsriFileInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.CmsDocumentInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ExtendedwtyModelRequestVO;
import com.lge.d2x.domain.support.v1.model.ExtendedwtyModelResponseVO;
import com.lge.d2x.domain.support.v1.model.FindModelNumberRequestVO;
import com.lge.d2x.domain.support.v1.model.FindModelNumberResponseVO;
import com.lge.d2x.domain.support.v1.model.GameAppInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GameAppInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpAscAllRequestVO;
import com.lge.d2x.domain.support.v1.model.GpAscAllResponseVO;
import com.lge.d2x.domain.support.v1.model.GpAscListDistanceBrRequestVO;
import com.lge.d2x.domain.support.v1.model.GpAscListDistanceBrResponseVO;
import com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpModelDetailRequestVO;
import com.lge.d2x.domain.support.v1.model.GpModelDetailResponseVO;
import com.lge.d2x.domain.support.v1.model.GpModelListRequestVO;
import com.lge.d2x.domain.support.v1.model.GpModelListResponseVO;
import com.lge.d2x.domain.support.v1.model.GpRepairableProductInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpRepairableProductInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeRequestVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeResponseVO;
import com.lge.d2x.domain.support.v1.model.GpWtyInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpWtyInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GsriListDataVO;
import com.lge.d2x.domain.support.v1.model.KmProductRequestVO;
import com.lge.d2x.domain.support.v1.model.KmProductResponseVO;
import com.lge.d2x.domain.support.v1.model.ModelWtyInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ModelWtyInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PdfDownloadInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PdmProductInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PdmProductInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PreferenceCountryRequestVO;
import com.lge.d2x.domain.support.v1.model.PreferenceCountryResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoDataVO;
import com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductSupportInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductValidInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductValidInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PurchasePlaceInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PurchasePlaceInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.SoftwareAllInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.SvdSiteModelListRequestVO;
import com.lge.d2x.domain.support.v1.model.SvdSiteModelListResponseVO;
import com.lge.d2x.domain.support.v1.model.SympTomInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.SympTomInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.TypeOfInquiryProductResponseVO;
import com.lge.d2x.domain.support.v1.repository.pdsmgr.SupportRepository;
import com.lge.d2xfrm.model.common.CachedComLocaleCodeVO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Aspect
@Service
@RequiredArgsConstructor
public class SupportService {

    private final SupportRepository supportRepository;

    @Transactional(readOnly = true)
    public GpModelDetailResponseVO gpModelDetail(GpModelDetailRequestVO requestVO) {

        return supportRepository.gpModelDetail(requestVO);
    }

    @Transactional(readOnly = true)
    public GpModelDetailResponseVO buyerModelCodeBySalesCode(GpModelDetailRequestVO requestVO) {

        return supportRepository.gpModelDetail(requestVO);
    }

    @Transactional(readOnly = true)
    public GpModelDetailResponseVO inquiryPartsByModelAffiliate(GpModelDetailRequestVO requestVO) {

        return supportRepository.inquiryPartsByModelAffiliate(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public KmProductResponseVO kmProductCode(KmProductRequestVO requestVO) {

        return supportRepository.kmProductCode(requestVO);
    }
    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<KmProductResponseVO> kMProductCodeList(KmProductRequestVO requestVO) {
        List<KmProductResponseVO> resultList = new ArrayList<KmProductResponseVO>();
        String searchType = requestVO.getSearchType();
        if ("KM".equals(searchType)) {
            resultList = supportRepository.kMProductCodeByModel(requestVO);
        } else if ("KC".equals(searchType)) {
            resultList = supportRepository.kMProductCodeList(requestVO);
        } else if ("KCC".equals(searchType)) {
            resultList = supportRepository.kMProductCodeListByModel(requestVO);
        } else if ("GKM".equals(searchType)) {
            resultList = supportRepository.gpKMProductCodeByModel(requestVO);
        } else if ("GKC".equals(searchType)) {
            resultList = supportRepository.gpKMProductCodeList(requestVO);
        } else if ("GKCC".equals(searchType)) {
            resultList = supportRepository.gpKMProductCodeListByModel(requestVO);
        } else if ("WKC".equals(searchType)) {
            resultList = supportRepository.wtbKMProductCodeList(requestVO);
        } else if ("WKCC".equals(searchType)) {
            resultList = supportRepository.wtbKMProductCodeListByModel(requestVO);
        }
        return resultList;
    }

    @Transactional(readOnly = true)
    public ProductGsriFileInfoResponseVO retrieveProductGsriFileInfo(
            ProductGsriFileInfoRequestVO requestVO) {
        ProductGsriFileInfoResponseVO resultVO = new ProductGsriFileInfoResponseVO();
        ProductGsriFileInfoDataVO filInfo = supportRepository.productGsriFileInfo(requestVO);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        if (ObjectUtils.isNotEmpty(filInfo)) {
            ObjectMapper objectMapper = new ObjectMapper();
            resultMap = objectMapper.convertValue(filInfo, Map.class);
        }
        resultVO.setResultMap(resultMap);
        return resultVO;
    }

    @Transactional(readOnly = true)
    public List<BundleGsriFileInfoResponseVO> retrieveBundleGsriFileInfo(
            ProductGsriFileInfoRequestVO requestVO) {

        return supportRepository.bundleGsriFileInfo(requestVO);
    }

    @Transactional(readOnly = true)
    public PdfGsriSpecInfoResponseVO retrievePdfGsriSpecInfo(PdfGsriSpecInfoRequestVO requestVO) {

        PdfGsriSpecInfoResponseVO resultVO = new PdfGsriSpecInfoResponseVO();
        List<Map<String, Object>> gsriMapList = new ArrayList<Map<String, Object>>();
        // pdf
        resultVO.setPdfDownloadInfo(supportRepository.pdfDownloadInfo(requestVO));

        /** Download Spec Sheet 문서 조회 */
        PdfDownloadInfoResponseVO specSheet = new PdfDownloadInfoResponseVO();
        String docDownloadType = requestVO.getDocDownloadType();
        String siteCode = requestVO.getSiteCode();
        String filePathAddr = "";
        if ("Y".equals(docDownloadType) || "D".equals(docDownloadType)) {
            requestVO.setModelFileTypeCode("DU06");
            specSheet = supportRepository.specDownloadInfo(requestVO);
        } else if ("I".equals(docDownloadType)) {
            requestVO.setModelFileTypeCode("DU02");
            specSheet = supportRepository.iframeSpecDownloadInfo(requestVO);
        }
        if (specSheet != null) {
            filePathAddr = specSheet.getFilePathAddr();
        }
        resultVO.setFilePathAddr(filePathAddr);

        PdfDownloadInfoResponseVO gsriCnt = supportRepository.gsriListCnt(siteCode);
        List<GsriListDataVO> gsriList = new ArrayList<GsriListDataVO>();
        if (gsriCnt != null && gsriCnt.getCnt() > 0) {
            gsriList = supportRepository.gsriList(requestVO);
        }
        if (gsriList != null && gsriList.size() > 0) {
            for (GsriListDataVO gsriVO : gsriList) {
                Map<String, Object> gsriMap = new HashMap<String, Object>();
                if (ObjectUtils.isNotEmpty(gsriVO)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    gsriMap = objectMapper.convertValue(gsriVO, Map.class);
                    gsriMapList.add(gsriMap);
                }
            }
        }
        resultVO.setGsriList(gsriMapList);

        return resultVO;
    }

    @Transactional(readOnly = true)
    public ProductManualSoftwareInfoResponseVO retrieveProductManualSoftwareInfo(
            ProductManualSoftwareInfoRequestVO requestVO) {
        ProductManualSoftwareInfoResponseVO resultVO = new ProductManualSoftwareInfoResponseVO();
        ProductSupportInfoResponseVO supportRes = supportRepository.productSupportInfo(requestVO);
        List<Map<String, Object>> manualMapList = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> softwareMapList = new ArrayList<Map<String, Object>>();

        String moblieFlag = "";
        String csSalesCode = "";
        String csCustomerModel = "";
        if (supportRes != null) {
            moblieFlag =
                    supportRes.getCsMobileModelFlag() != null
                            ? supportRes.getCsMobileModelFlag()
                            : "";
            csSalesCode = supportRes.getCsSalesCode() != null ? supportRes.getCsSalesCode() : "";
            csCustomerModel =
                    supportRes.getCsCustomerModel() != null ? supportRes.getCsCustomerModel() : "";

            requestVO.setCsSalesCode(csSalesCode);
        }
        // retrieveGpGSCSManualsDocsListAll <- PDS 대상 아닌 테이블 데이터와 합쳐지는 내용이라 필요가 없어짐
        List<CmsDocumentInfoResponseVO> manualList =
                supportRepository.retrieveMcSupportManualsDocsList(requestVO);

        if (manualList == null || manualList.size() == 0) {
            manualList = supportRepository.retrieveCmsDocument(requestVO);
        }
        if (manualList != null || manualList.size() > 0) {
            for (CmsDocumentInfoResponseVO manualVo : manualList) {
                Map<String, Object> manualMap = new HashMap<String, Object>();
                if (ObjectUtils.isNotEmpty(manualVo)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    manualMap = objectMapper.convertValue(manualVo, Map.class);
                    manualMapList.add(manualMap);
                }
            }
        }

        List<SoftwareAllInfoResponseVO> softwareList = new ArrayList<SoftwareAllInfoResponseVO>();
        if (!"Y".equals(moblieFlag)) {
            requestVO.setCustomerModelCode(csCustomerModel);
            softwareList = supportRepository.retrieveSoftwareListAll(requestVO);
            if (softwareList != null) {
                for (int i = 0; i < softwareList.size(); i++) {
                    String softwareOriginFileName = softwareList.get(i).getFileNamePrint();
                    String[] softwareFileType = softwareOriginFileName.split("\\.");
                    softwareList.get(i).setFileType(softwareFileType[softwareFileType.length - 1]);
                    Map<String, Object> softwareMap = new HashMap<String, Object>();
                    if (ObjectUtils.isNotEmpty(softwareList.get(i))) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        softwareMap = objectMapper.convertValue(softwareList.get(i), Map.class);
                        softwareMapList.add(softwareMap);
                    }
                }
            }
        }

        resultVO.setManualList(manualMapList);
        resultVO.setSoftwareList(softwareMapList);
        return resultVO;
    }

    @Transactional(readOnly = true)
    public List<ModelWtyInfoResponseVO> retrieveModelWtyInfoList(ModelWtyInfoRequestVO requestVO) {
        return supportRepository.retrieveModelWtyInfoList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<CmsDocumentInfoResponseVO> retrieveMcSupportManualsDocsList(
            ProductManualSoftwareInfoRequestVO requestVO) {
        return supportRepository.retrieveMcSupportManualsDocsList(requestVO);
    }

    @Transactional(readOnly = true)
    public AscInfoResponseVO retrieveASCInfo(AscInfoRequestVO requestVO) {
        return supportRepository.retrieveASCInfo(requestVO);
    }

    @Transactional(readOnly = true)
    public List<AscInfoResponseVO> retrieveASCList(AscInfoRequestVO requestVO) {
        return supportRepository.retrieveASCList(requestVO);
    }

    @Transactional(readOnly = true)
    public GpWtyInfoResponseVO retrieveGpWarrantyInformation(GpWtyInfoRequestVO requestVO) {
        return supportRepository.retrieveGpWarrantyInformation(requestVO);
    }

    @Transactional(readOnly = true)
    public ExtendedwtyModelResponseVO selectExtendedwtyModelList(
            ExtendedwtyModelRequestVO requestVO) {
        ExtendedwtyModelResponseVO resultVO = new ExtendedwtyModelResponseVO();
        resultVO.setResultList(supportRepository.selectExtendedwtyModelList(requestVO));
        resultVO.setTotalCnt(supportRepository.selectExtendedwtyModelListCnt(requestVO));
        return resultVO;
    }

    @Transactional(readOnly = true)
    public List<ProductValidInfoResponseVO> retrieveGenuineWtyConutryList(
            ProductValidInfoRequestVO requestVO) {
        return supportRepository.retrieveGenuineWtyConutryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<ProductValidInfoResponseVO> retrieveGenuineWtyProductList(
            ProductValidInfoRequestVO requestVO) {
        return supportRepository.retrieveGenuineWtyProductList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<ProductValidInfoResponseVO> retrieveGenuineWtyProductCombo(
            ProductValidInfoRequestVO requestVO) {
        return supportRepository.retrieveGenuineWtyProductCombo(requestVO);
    }

    @Transactional(readOnly = true)
    public ProductValidInfoResponseVO retrieveValidationModel(ProductValidInfoRequestVO requestVO) {
        return supportRepository.retrieveValidationModel(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveGpGsfsCategoryList(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveGpGsfsCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveGpGsfsSubCategoryList(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveGpGsfsSubCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<PreferenceCountryResponseVO> retrievePreferenceCountryList(
            PreferenceCountryRequestVO requestVO) {
        return supportRepository.retrievePreferenceCountryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<PreferenceCountryResponseVO> retrievePreferenceGpCountryList(
            PreferenceCountryRequestVO requestVO) {
        return supportRepository.retrievePreferenceGpCountryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveGpCategoryList(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveGpCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpModelDetailResponseVO> retrieveCustomerProductList(
            GpModelDetailRequestVO requestVO) {
        return supportRepository.retrieveCustomerProductList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<PurchasePlaceInfoResponseVO> retrieveSvdPurchasePlaceMList(
            PurchasePlaceInfoRequestVO requestVO) {
        return supportRepository.retrieveSvdPurchasePlaceMList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<BasicPromotionWtyExpireResponseVO> retrieveBasicPromotionWtyExpireList(
            BasicPromotionWtyExpireRequestVO requestVO) {
        return supportRepository.retrieveBasicPromotionWtyExpireList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<PdmProductInfoResponseVO> retrievePdmProductList(
            PdmProductInfoRequestVO requestVO) {
        return supportRepository.retrievePdmProductList(requestVO);
    }

    @Transactional(readOnly = true)
    public GpServiceTypeInfoResponseVO retrieveGpRepairServiceTypeInfo(
            GpServiceTypeInfoRequestVO requestVO) {
        return supportRepository.retrieveGpRepairServiceTypeInfo(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GameAppInfoResponseVO> retrieveGameAppList(GameAppInfoRequestVO requestVO) {
        return supportRepository.retrieveGameAppList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SympTomInfoResponseVO> retrieveSymptomList(SympTomInfoRequestVO requestVO) {
        return supportRepository.retrieveSymptomList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SympTomInfoResponseVO> retrieveSymptomList3Depth(SympTomInfoRequestVO requestVO) {
        return supportRepository.retrieveSymptomList3Depth(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SympTomInfoResponseVO> retrieveSubSymptomList(SympTomInfoRequestVO requestVO) {
        return supportRepository.retrieveSubSymptomList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SympTomInfoResponseVO> retrieveSubSymptomList3Depth(
            SympTomInfoRequestVO requestVO) {
        return supportRepository.retrieveSubSymptomList3Depth(requestVO);
    }

    @Transactional(readOnly = true)
    public List<AscInfoResponseVO> retrieveRepairStateList(AscInfoRequestVO requestVO) {
        return supportRepository.retrieveRepairStateList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpModelDetailResponseVO> gpModelCateList(GpModelDetailRequestVO requestVO) {
        return supportRepository.gpModelCateList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveSubCategoryBR(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveSubCategoryBR(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpRepairableProductInfoResponseVO> retrieveGpRepairableProduct(
            GpRepairableProductInfoRequestVO requestVO) {
        return supportRepository.retrieveGpRepairableProduct(requestVO);
    }

    @Transactional(readOnly = true)
    public GpAscListDistanceBrResponseVO retrieveGpAscListDistanceBr(
            GpAscListDistanceBrRequestVO requestVO) {
        return supportRepository.retrieveGpAscListDistanceBr(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpAscAllResponseVO> retrieveGpAscListAll(GpAscAllRequestVO requestVO) {
        return supportRepository.retrieveGpAscListAll(requestVO);
    }

    @Transactional(readOnly = true)
    public List<KmProductResponseVO> retrieveGpCategoryNameByKMProductCode(
            KmProductRequestVO requestVO) {
        return supportRepository.retrieveGpCategoryNameByKMProductCode(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SvdSiteModelListResponseVO> retrieveSvdSiteModelList(
            SvdSiteModelListRequestVO requestVO) {
        return supportRepository.retrieveSvdSiteModelList(requestVO);
    }

    @Transactional(readOnly = true)
    public GpCateInfoResponseVO retrieveSuperCategoryOne(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveSuperCategoryOne(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpModelListResponseVO> retrieveGpModelList(GpModelListRequestVO requestVO) {
        return supportRepository.retrieveGpModelList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveGpGsfsSuperCategoryList(
            GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveGpGsfsSuperCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveCallSuperCategoryList(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveCallSuperCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveCallCategoryList(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveCallCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveCallSubCategoryList(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveCallSubCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveGpSubCategoryList(GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveGpSubCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public GpServiceTypeResponseVO retrieveGpServiceType(GpServiceTypeRequestVO requestVO) {
        return supportRepository.retrieveGpServiceType(requestVO);
    }

    @Transactional(readOnly = true)
    public List<FindModelNumberResponseVO> retrieveTopFindCategoryList(
            FindModelNumberRequestVO requestVO) {
        return supportRepository.retrieveTopFindCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<FindModelNumberResponseVO> retrieveFindCategoryList(
            FindModelNumberRequestVO requestVO) {
        return supportRepository.retrieveFindCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public FindModelNumberResponseVO retrieveFindmymodelNumber(FindModelNumberRequestVO requestVO) {
        return supportRepository.retrieveFindmymodelNumber(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveFindServiceCenterSuperCategoryList(
            GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveFindServiceCenterSuperCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveFindServiceCenterCategoryList(
            GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveFindServiceCenterCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveLocateRepairCenterCategoryList(
            GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveLocateRepairCenterCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<GpCateInfoResponseVO> retrieveLocateRepairCenterSubCategoryList(
            GpCateInfoRequestVO requestVO) {
        return supportRepository.retrieveLocateRepairCenterSubCategoryList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<AscInfoResponseVO> retrieveRepairCityListBR(AscInfoRequestVO requestVO) {
        return supportRepository.retrieveRepairCityListBR(requestVO);
    }

    @Transactional(readOnly = true)
    public List<TypeOfInquiryProductResponseVO> retrieveGpTypeOfInquiryList(
            CachedComLocaleCodeVO requestVO) {
        return supportRepository.retrieveGpTypeOfInquiryList(requestVO);
    }
}
