package com.lge.d2x.domain.product.v1.model;

import com.lge.d2x.domain.common.v1.model.SnsShareInfoResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbInitResponseVO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductSummaryResponseVO {
    private String affiliateCode;
    private String bizType;
    private String ecommerceTarget;
    private String inchCode;
    private String modelEanCode;
    private String modelName;
    private String modelDisplayName;
    private String modelId;
    private String sku;
    private String modelStatusCode;
    private BigDecimal msrp;
    private String modelTypeM;
    private String obsSellFlag;
    private String productLevelCode;
    private String salesModelCode;
    private String salesSuffixCode;
    private String retailerPricingFlag;
    private String wtbUseFlag;
    private String wtbOfflineUseFlag;
    private String wtbOnlineUseFlag;
    private String wtbDirectUseFlag;
    private String imageAltText;
    private String largeImageAddr;
    private String mediumImageAddr;
    private String model360Image;
    private String model360Title;
    private String model3603dImage;
    private String model3603dTitle;
    private String modelType;
    private String smallImageAddr;
    private String userFriendlyName;
    private String modelUrlPath;
    private String categoryId;
    private String categoryName;
    private String subCategoryId;
    private String subCategoryName;
    private String superCategoryId;
    private String superCategoryName;
    private int pCount;
    private int ratingPercent;
    private int sRating;
    private BigDecimal sRating2;
    private String defaultProductTag;
    private String productTag1;
    private String productTag1EndDay;
    private String productTag2;
    private String productTag2EndDay;
    private List<Map<String, Object>> compatibleModels = new ArrayList<Map<String, Object>>();
    private List<KeyfeaturesVO> bulletFeatures = new ArrayList<KeyfeaturesVO>();
    private List<PdpSiblingItemVO> siblings = new ArrayList<PdpSiblingItemVO>();
    private String findTheDealerFlag;
    private String wtbExternalLinkUseFlag;
    private String wtbExternalLinkName;
    private String wtbExternalLinkUrl;
    private String wtbExternalLinkSelfFlag;
    private String inquiryToBuyFlag;
    private String pdpInquiryToBuyTabUseFlag;
    private String pdpInquiryToBuyTabLinkUrl;
    private String buyNowFlag;
    private String buyNowURL;
    private String productSupportFlag;
    private String productSupportUrl;
    private String productEnquiryFlag;
    private String productEnquiryUrl;
    private int compatibleCount;
    private SnsShareInfoResponseVO shareInfo;
    private List<Map<String, Object>> superCateList = new ArrayList<Map<String, Object>>();
    private List<Map<String, Object>> countryList = new ArrayList<Map<String, Object>>();
    private String reviewType;
    private String buyNowUseFlag;
    private String obsLoginFlag;
    private String b2bResourceFlag;
    private String energyLabel;
    private String energyLabelFileName;
    private String energyLabelOriginalName;
    private String productFicheFileName;
    private String productFicheOriginalName;
    private String energyLabelDocId;
    private String productFicheDocId;
    private String energyLabelImageAddr;
    private String energyLabelName;
    private String energyLabelFlag;
    private String energyLabelCategory;
    private String rsUseFlag;
    private String vipPriceFlag;
    private List<String> warrantyMsgExceptionList;
    private String modelVrImage;
    private String modelVrTitle;
    private String modelArImage;
    private String modelArTitle;
    private String modelArMobileImage;
    private String modelArMobileTitle;
    private List<ProductIconListResponseVO> labelIconList =
            new ArrayList<ProductIconListResponseVO>();
    private String pdpLabelUse;
    private String localeCode;
    private String atcTrackEventFlag;
    private String productTag1UserType;
    private String productTag2UserType;
    private String obsComTagShowFlag;
    private String promotionHighlightUseFlag;
    private String pdrUseFlag;
    private String pdpSummaryImproveFlag;
    private String loginGroup;
    private String modelYear;
    private String fEnergyLabelDocId;
    private String fEnergyLabelFileName;
    private String fEnergyLabelOriginalName;
    private String buName1;
    private String buName2;
    private String buName3;
    private String superCategoryEngName;
    private String signatureFlag;
    private String thinqFlag;
    private String wishUseFlag;
    private String wishCnt;
    private String loginUseFlag;
    private String useId;
    private String wishTotalCnt;
    private String specMsgFlag;
    private String shippingFlag;
    private String shippingMsg;
    private String deliveryFlag;
    private String deliveryMsg;
    private String installationFlag;
    private String installationMsg;
    private String catalogueUseFlag;
    private String catalogueFilePathText;
    private String catalogueFileName;
    private String mwoPanelUseFlag;
    private String mwoPanelFilePathText;
    private String mwoPanelFileName;
    private String objetAccessoryFlag;
    private String objetProductFlag;
    private ObjetProductEntityVO objetModel;
    private List<ObjetMaterialEntityVO> objetMaterialList;
    private List<ObjetMaterialDetailEntityVO> objetMaterialDetailList;
    private List<ObjetPanelTypeEntityVO> objetPanelList;
    private String repairComment;
    private String themeType;
    private String obsAdditionalDisclaimerText1Flag;
    private String obsAdditionalDisclaimerText1Msg;
    private String obsAdditionalDisclaimerText2Flag;
    private String obsAdditionalDisclaimerText2Msg;
    private String obsAdditionalDisclaimerText3Flag;
    private String obsAdditionalDisclaimerText3Msg;
    private String obsAdditionalDisclaimerText4Flag;
    private String obsAdditionalDisclaimerText4Msg;
    private String obsAdditionalDisclaimerText5Flag;
    private String obsAdditionalDisclaimerText5Msg;
    private String secondEnergyLabel;
    private String secondEnergyLabelFileName;
    private String secondEnergyLabelOriginalName;
    private String secondProductFicheFileName;
    private String secondProductFicheOriginalName;
    private String secondEnergyLabelDocId;
    private String secondProductFicheDocId;
    private String secondEnergyLabelImageAddr;
    private String secondEnergyLabelName;
    private String secondEnergyLabelCategory;
    private String washTowerFlag;
    private String secondFEnergyLabelDocId;
    private String secondFEnergyLabelFileName;
    private String secondFEnergyLabelOriginalName;
    private String energyLabelproductLeve1Code;
    private String fenergyLabelproductLeve1Code;
    private String productFicheproductLeve1Code;
    private String secondEnergyLabelproductLeve1Code;
    private String secondFEnergyLabelproductLeve1Code;
    private String secondProductFicheproductLeve1Code;
    private String firstLabelCheckFlag;
    private String siblingGroupCode;
    private String seriesName;
    private String capacityLinkUseFlag;
    private String exclusionModel;
    private String classificationFlagLv1;
    private String classificationFlagLv2;
    private String classificationFlagLv3;
    private String classificationFlagLv4;
    private String repairModelAreaYn;
    private List<ProductIconListResponseVO> labelRepairMap =
            new ArrayList<ProductIconListResponseVO>();
    private String labelUseFlag;
    private String repairabilityIndexFlag;
    private String taxInformationUseFlag;
    private String threeDUseFlag;
    private String arUseFlag;
    private String vrUseFlag;
    private String findTheDealerHideFlag;
    private String compatibleProducts3TypeUseFlag;
    private String mktNameSeoUseFlag;
    private String mktNameSeoTitle;
    private String mktNameSeoDescription;
    private String pisDocType;
    private String pisDocOldFlag;
    private String secondPisDocType;
    private String secondPfCode;
    private String elType;
    private String secondElType;
    private String epsIncludeCharger;
    private String epsMinPower;
    private String epsMaxPower;
    private String epsUsbPd;
    private String bundleModelFlag;
    private String warrantyInfoFreeYear;
    private String eprelId;
    private String expertChatYn;
    private String pimSku;
    private String wtbSubId;
    private String shopCode;

    public WtbInitResponseVO toWtbInfoVO() {
        return WtbInitResponseVO.builder()
                .modelId(this.modelId)
                .localeCode(this.localeCode)
                .sku(this.sku)
                .bizType(this.bizType)
                .modelName(this.modelName)
                .modelEanCode(this.modelEanCode)
                .modelStatusCode(this.modelStatusCode)
                .wtbUseFlag(this.wtbUseFlag)
                .wtbExternalLinkUseFlag(this.wtbExternalLinkUseFlag)
                .wtbExternalLinkName(this.wtbExternalLinkName)
                .wtbExternalLinkUrl(this.wtbExternalLinkUrl)
                .wtbExternalLinkSelfFlag(this.wtbExternalLinkSelfFlag)
                .wtbSuperCateId(this.superCategoryId)
                .wtbCateId(this.categoryId)
                .wtbCateName(this.categoryName)
                .wtbSubCateId(this.subCategoryId)
                .wtbSubId(this.wtbSubId)
                .salesModelCode(this.salesModelCode)
                .salesSuffixCode(this.salesSuffixCode)
                .modelUrlPath(this.modelUrlPath)
                .imageAltText(this.imageAltText)
                .largeImageAddr(this.largeImageAddr)
                .mediumImageAddr(this.mediumImageAddr)
                .smallImageAddr(this.smallImageAddr)
                .userFriendlyName(this.userFriendlyName)
                .energyLabel(this.energyLabel)
                .energyLabelCategory(this.energyLabelCategory)
                .secondEnergyLabel(this.secondEnergyLabel)
                .secondEnergyLabelCategory(this.secondEnergyLabelCategory)
                .washTowerFlag(this.washTowerFlag)
                .classificationFlagLv1(this.classificationFlagLv1)
                .classificationFlagLv2(this.classificationFlagLv2)
                .classificationFlagLv3(this.classificationFlagLv3)
                .classificationFlagLv4(this.classificationFlagLv4)
                .msrp(this.msrp)
                .modelType(this.modelType)
                .modelYear(this.modelYear)
                .obsSellFlag(this.obsSellFlag)
                .build();
    }
}
