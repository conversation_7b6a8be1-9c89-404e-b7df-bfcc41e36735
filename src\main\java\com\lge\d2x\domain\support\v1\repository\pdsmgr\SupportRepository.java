package com.lge.d2x.domain.support.v1.repository.pdsmgr;

import com.lge.d2x.domain.support.v1.model.AscInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.AscInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.BasicPromotionWtyExpireRequestVO;
import com.lge.d2x.domain.support.v1.model.BasicPromotionWtyExpireResponseVO;
import com.lge.d2x.domain.support.v1.model.BundleGsriFileInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.CmsDocumentInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ExtendedwtyModelDataVO;
import com.lge.d2x.domain.support.v1.model.ExtendedwtyModelRequestVO;
import com.lge.d2x.domain.support.v1.model.FindModelNumberRequestVO;
import com.lge.d2x.domain.support.v1.model.FindModelNumberResponseVO;
import com.lge.d2x.domain.support.v1.model.GameAppInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GameAppInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpAscAllRequestVO;
import com.lge.d2x.domain.support.v1.model.GpAscAllResponseVO;
import com.lge.d2x.domain.support.v1.model.GpAscListDistanceBrRequestVO;
import com.lge.d2x.domain.support.v1.model.GpAscListDistanceBrResponseVO;
import com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpModelDetailRequestVO;
import com.lge.d2x.domain.support.v1.model.GpModelDetailResponseVO;
import com.lge.d2x.domain.support.v1.model.GpModelListRequestVO;
import com.lge.d2x.domain.support.v1.model.GpModelListResponseVO;
import com.lge.d2x.domain.support.v1.model.GpRepairableProductInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpRepairableProductInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeRequestVO;
import com.lge.d2x.domain.support.v1.model.GpServiceTypeResponseVO;
import com.lge.d2x.domain.support.v1.model.GpWtyInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.GpWtyInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.GsriListDataVO;
import com.lge.d2x.domain.support.v1.model.KmProductRequestVO;
import com.lge.d2x.domain.support.v1.model.KmProductResponseVO;
import com.lge.d2x.domain.support.v1.model.ModelWtyInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ModelWtyInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PdfDownloadInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PdmProductInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PdmProductInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PreferenceCountryRequestVO;
import com.lge.d2x.domain.support.v1.model.PreferenceCountryResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoDataVO;
import com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductSupportInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.ProductValidInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.ProductValidInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.PurchasePlaceInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.PurchasePlaceInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.SoftwareAllInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.SvdSiteModelListRequestVO;
import com.lge.d2x.domain.support.v1.model.SvdSiteModelListResponseVO;
import com.lge.d2x.domain.support.v1.model.SympTomInfoRequestVO;
import com.lge.d2x.domain.support.v1.model.SympTomInfoResponseVO;
import com.lge.d2x.domain.support.v1.model.TypeOfInquiryProductResponseVO;
import com.lge.d2xfrm.model.common.CachedComLocaleCodeVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SupportRepository {

    public GpModelDetailResponseVO gpModelDetail(GpModelDetailRequestVO data);

    public GpModelDetailResponseVO inquiryPartsByModelAffiliate(GpModelDetailRequestVO data);

    public KmProductResponseVO kmProductCode(KmProductRequestVO data);

    public GpModelDetailResponseVO buyerModelCodeBySalesCode(GpModelDetailRequestVO data);

    public List<KmProductResponseVO> kMProductCodeByModel(KmProductRequestVO input);

    public List<KmProductResponseVO> kMProductCodeList(KmProductRequestVO input);

    public List<KmProductResponseVO> kMProductCodeListByModel(KmProductRequestVO input);

    public List<KmProductResponseVO> gpKMProductCodeByModel(KmProductRequestVO input);

    public List<KmProductResponseVO> gpKMProductCodeList(KmProductRequestVO input);

    public List<KmProductResponseVO> gpKMProductCodeListByModel(KmProductRequestVO input);

    public List<KmProductResponseVO> wtbKMProductCodeList(KmProductRequestVO input);

    public List<KmProductResponseVO> wtbKMProductCodeListByModel(KmProductRequestVO input);

    public ProductGsriFileInfoDataVO productGsriFileInfo(ProductGsriFileInfoRequestVO input);

    public List<BundleGsriFileInfoResponseVO> bundleGsriFileInfo(
            ProductGsriFileInfoRequestVO input);

    public PdfDownloadInfoResponseVO pdfDownloadInfo(PdfGsriSpecInfoRequestVO input);

    public PdfDownloadInfoResponseVO specDownloadInfo(PdfGsriSpecInfoRequestVO input);

    public PdfDownloadInfoResponseVO iframeSpecDownloadInfo(PdfGsriSpecInfoRequestVO input);

    public PdfDownloadInfoResponseVO gsriListCnt(String siteCode);

    public List<GsriListDataVO> gsriList(PdfGsriSpecInfoRequestVO input);

    public ProductSupportInfoResponseVO productSupportInfo(
            ProductManualSoftwareInfoRequestVO input);

    public List<CmsDocumentInfoResponseVO> retrieveCmsDocument(
            ProductManualSoftwareInfoRequestVO input);

    public List<CmsDocumentInfoResponseVO> retrieveMcSupportManualsDocsList(
            ProductManualSoftwareInfoRequestVO input);

    public List<SoftwareAllInfoResponseVO> retrieveSoftwareListAll(
            ProductManualSoftwareInfoRequestVO input);

    public List<ModelWtyInfoResponseVO> retrieveModelWtyInfoList(ModelWtyInfoRequestVO input);

    public AscInfoResponseVO retrieveASCInfo(AscInfoRequestVO input);

    public List<AscInfoResponseVO> retrieveASCList(AscInfoRequestVO input);

    public GpWtyInfoResponseVO retrieveGpWarrantyInformation(GpWtyInfoRequestVO input);

    public List<ExtendedwtyModelDataVO> selectExtendedwtyModelList(
            ExtendedwtyModelRequestVO categoryRequestVO);

    public int selectExtendedwtyModelListCnt(ExtendedwtyModelRequestVO categoryRequestVO);

    public List<ProductValidInfoResponseVO> retrieveGenuineWtyConutryList(
            ProductValidInfoRequestVO input);

    public List<ProductValidInfoResponseVO> retrieveGenuineWtyProductCombo(
            ProductValidInfoRequestVO input);

    public List<ProductValidInfoResponseVO> retrieveGenuineWtyProductList(
            ProductValidInfoRequestVO input);

    public ProductValidInfoResponseVO retrieveValidationModel(ProductValidInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveGpGsfsCategoryList(GpCateInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveGpGsfsSubCategoryList(GpCateInfoRequestVO input);

    public List<PreferenceCountryResponseVO> retrievePreferenceCountryList(
            PreferenceCountryRequestVO input);

    public List<GpCateInfoResponseVO> retrieveGpCategoryList(GpCateInfoRequestVO input);

    public List<GpModelDetailResponseVO> retrieveCustomerProductList(GpModelDetailRequestVO input);

    public List<PurchasePlaceInfoResponseVO> retrieveSvdPurchasePlaceMList(
            PurchasePlaceInfoRequestVO input);

    public List<BasicPromotionWtyExpireResponseVO> retrieveBasicPromotionWtyExpireList(
            BasicPromotionWtyExpireRequestVO input);

    public List<PdmProductInfoResponseVO> retrievePdmProductList(PdmProductInfoRequestVO input);

    public GpServiceTypeInfoResponseVO retrieveGpRepairServiceTypeInfo(
            GpServiceTypeInfoRequestVO input);

    public List<GameAppInfoResponseVO> retrieveGameAppList(GameAppInfoRequestVO input);

    public List<SympTomInfoResponseVO> retrieveSymptomList(SympTomInfoRequestVO input);

    public List<SympTomInfoResponseVO> retrieveSymptomList3Depth(SympTomInfoRequestVO input);

    public List<SympTomInfoResponseVO> retrieveSubSymptomList(SympTomInfoRequestVO input);

    public List<SympTomInfoResponseVO> retrieveSubSymptomList3Depth(SympTomInfoRequestVO input);

    public List<AscInfoResponseVO> retrieveRepairStateList(AscInfoRequestVO input);

    public List<GpModelDetailResponseVO> gpModelCateList(GpModelDetailRequestVO data);

    public List<GpCateInfoResponseVO> retrieveSubCategoryBR(GpCateInfoRequestVO data);

    public List<GpRepairableProductInfoResponseVO> retrieveGpRepairableProduct(
            GpRepairableProductInfoRequestVO data);

    public List<PreferenceCountryResponseVO> retrievePreferenceGpCountryList(
            PreferenceCountryRequestVO data);

    public GpAscListDistanceBrResponseVO retrieveGpAscListDistanceBr(
            GpAscListDistanceBrRequestVO data);

    public List<GpAscAllResponseVO> retrieveGpAscListAll(GpAscAllRequestVO input);

    public List<KmProductResponseVO> retrieveGpCategoryNameByKMProductCode(
            KmProductRequestVO input);

    public List<SvdSiteModelListResponseVO> retrieveSvdSiteModelList(
            SvdSiteModelListRequestVO input);

    public GpCateInfoResponseVO retrieveSuperCategoryOne(GpCateInfoRequestVO input);

    public List<GpModelListResponseVO> retrieveGpModelList(GpModelListRequestVO input);

    public List<GpCateInfoResponseVO> retrieveGpGsfsSuperCategoryList(GpCateInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveCallSuperCategoryList(GpCateInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveCallCategoryList(GpCateInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveCallSubCategoryList(GpCateInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveGpSubCategoryList(GpCateInfoRequestVO input);

    public GpServiceTypeResponseVO retrieveGpServiceType(GpServiceTypeRequestVO input);

    public List<FindModelNumberResponseVO> retrieveTopFindCategoryList(
            FindModelNumberRequestVO input);

    public List<FindModelNumberResponseVO> retrieveFindCategoryList(FindModelNumberRequestVO input);

    public FindModelNumberResponseVO retrieveFindmymodelNumber(FindModelNumberRequestVO input);

    public List<GpCateInfoResponseVO> retrieveFindServiceCenterSuperCategoryList(
            GpCateInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveFindServiceCenterCategoryList(
            GpCateInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveLocateRepairCenterCategoryList(
            GpCateInfoRequestVO input);

    public List<GpCateInfoResponseVO> retrieveLocateRepairCenterSubCategoryList(
            GpCateInfoRequestVO input);

    public List<AscInfoResponseVO> retrieveRepairCityListBR(AscInfoRequestVO input);

    public List<TypeOfInquiryProductResponseVO> retrieveGpTypeOfInquiryList(
            CachedComLocaleCodeVO input);
}
