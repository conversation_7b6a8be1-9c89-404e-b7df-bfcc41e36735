<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.support.v1.repository.pdsmgr.SupportRepository">


	<select id="kmProductCode" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.kmProductCode */
			DISTINCT KM_PRDGRP_CODE
		FROM   SVD_MDMS_GSCS_KM_PROD_R
			WHERE  1 = 1
			AND MDMS_PRDGRP_CODE= #{mdmsPrdGrpCode}
		LIMIT 1
	</select>


	<select id="kMProductCodeByModel" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.kMProductCodeByModel */
			 DISTINCT A.KM_PRODUCT_CODE AS KM_PRODUCT_CODE
			,C.LV2_CATEGORY_CODE AS PRODUCT_CODE
		FROM SVD_MDMS_GSCS_KM_PROD_R A
		JOIN PDM_PRODUCT_SVC_D B ON A.MDMS_PRDGRP_CODE = B.MDMS_PRDGRP_CODE
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON B.SVD_SKU_ID =C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode}
		JOIN SVD_PRODUCT_SVC_MODEL_D D ON B.SVD_SKU_ID = D.SVD_SKU_ID AND D.SITE_CODE = #{siteCode}
		WHERE 1=1
		AND D.SALES_CODE = #{modelCode}
	</select>

	<select id="kMProductCodeList" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
   		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.kMProductCodeList */
   	         DISTINCT A.KM_PRODUCT_CODE AS KM_PRODUCT_CODE
			,C.LV2_CATEGORY_CODE AS PRODUCT_CODE
		FROM SVD_MDMS_GSCS_KM_PROD_R A
		JOIN PDM_PRODUCT_SVC_D B ON A.MDMS_PRDGRP_CODE = B.MDMS_PRDGRP_CODE
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON B.SVD_SKU_ID =C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode}
		WHERE 1=1
		AND C.LV2_CATEGORY_CODE = #{categoryCode}
		LIMIT 1
	</select>


	<select id="kMProductCodeListByModel" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
    	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.kMProductCodeListByModel */
			 DISTINCT A.KM_PRODUCT_CODE AS KM_PRODUCT_CODE
			,B.LV2_CATEGORY_CODE AS PRODUCT_CODE
		FROM SVD_MDMS_GSCS_KM_PROD_R A
		JOIN SVD_CUSTOMER_MODEL_M B ON A.MDMS_PRDGRP_CODE = B.MDMS_PRDGRP_CODE
		WHERE 1=1
		AND B.LV2_CATEGORY_CODE = #{categoryCode}
		AND B.SITE_CODE = #{siteCode}
 		LIMIT 1
	</select>

	<select id="gpKMProductCodeByModel" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.gpKMProductCodeByModel */
			DISTINCT A.KM_PRODUCT_CODE AS KM_PRODUCT_CODE
		FROM SVD_MDMS_GSCS_KM_PROD_R A
		JOIN SVD_CUSTOMER_MODEL_M B ON A.MDMS_PRDGRP_CODE = B.MDMS_PRDGRP_CODE
		WHERE 1=1
		AND B.SITE_CODE = #{siteCode}
		AND B.CUST_MODEL_CODE = (
									SELECT DISTINCT C.BUYER_MODEL_CODE FROM PDM_PRODUCT_SVC_D C
                                      JOIN SVD_PRODUCT_SVC_MODEL_D D
                                        ON (C.SVD_SKU_ID =D.SVD_SKU_ID)
                                     WHERE D.SITE_CODE = #{siteCode}
                                       AND (C.BUYER_MODEL_CODE = #{modelNum} OR D.SALES_CODE = #{modelNum})
                                       AND C.USE_FLAG = 'Y'
									 LIMIT 1
								)
	</select>


	<select id="gpKMProductCodeList" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.gpKMProductCodeList */
			DISTINCT A.KM_PRODUCT_CODE AS KM_PRODUCT_CODE
		FROM SVD_MDMS_GSCS_KM_PROD_R A
		JOIN PDM_PRODUCT_SVC_D B ON A.MDMS_PRDGRP_CODE = B.MDMS_PRDGRP_CODE
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON B.SVD_SKU_ID =C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode}
		AND B.USE_FLAG= 'Y'
		AND C.USE_FLAG= 'Y'
		AND C.SITE_CODE = #{siteCode}
		AND C.LV2_CATEGORY_CODE = #{categoryCode}
		<if test="siteCode == 'BR' and (subCategoryCode != null and subCategoryCode != '')">
		AND C.LV3_CATEGORY_CODE = #{subCategoryCode}
		</if>

	</select>

	<select id="gpKMProductCodeListByModel" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.gpKMProductCodeListByModel */
			DISTINCT A.KM_PRODUCT_CODE AS KM_PRODUCT_CODE
		FROM SVD_MDMS_GSCS_KM_PROD_R A
		JOIN SVD_CUSTOMER_MODEL_M B ON A.MDMS_PRDGRP_CODE = B.MDMS_PRDGRP_CODE
		WHERE 1=1
		AND B.LV2_CATEGORY_CODE = #{categoryCode}
		AND B.SITE_CODE = #{siteCode}
		<if test="siteCode == 'BR' and (subCategoryCode != null and subCategoryCode != '')">
		AND B.LV3_CATEGORY_CODE = #{subCategoryCode}
		</if>
	</select>

	<select id="wtbKMProductCodeList" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.wtbKMProductCodeList */
			DISTINCT A.KM_PRODUCT_CODE AS KM_PRODUCT_CODE
		FROM SVD_MDMS_GSCS_KM_PROD_R A
		JOIN SVD_CATEGORY_MDMS_R B ON A.MDMS_PRDGRP_CODE = B.MDMS_PRDGRP_CODE
		WHERE B.SITE_CODE = #{siteCode}
		<if test="superCategoryCode != 'all' and categoryCode == 'all'">
		AND B.LV1_CATEGORY_CODE = #{superCategoryCode}
		</if>
		<if test="superCategoryCode == 'all' and categoryCode != 'all'">
		AND B.LV2_CATEGORY_CODE = #{categoryCode}
		</if>
	</select>

	<select id="wtbKMProductCodeListByModel" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.wtbKMProductCodeListByModel */
			DISTINCT A.KM_PRODUCT_CODE AS KM_PRODUCT_CODE
		FROM SVD_MDMS_GSCS_KM_PROD_R A
		JOIN SVD_CUSTOMER_MODEL_M B ON A.MDMS_PRDGRP_CODE = B.MDMS_PRDGRP_CODE
		WHERE B.SITE_CODE = #{siteCode}
		<if test="superCategoryCode != 'all' and categoryCode == 'all'">
		AND B.LV1_CATEGORY_CODE = #{superCategoryCode}
		</if>
		<if test="superCategoryCode == 'all' and categoryCode != 'all'">
		AND B.LV2_CATEGORY_CODE = #{categoryCode}
		</if>
	</select>

	<select id="gpModelDetail" resultType="com.lge.d2x.domain.support.v1.model.GpModelDetailResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpModelDetailRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.gpModelDetail */
				B.SALES_CODE                                                                        -- CS_SALES_CODE
			, C.SALES_MODEL_CODE                                                                  -- CS_SALES_MODEL_CODE
			, C.SALES_MODEL_SUFFIX_CODE                                                           -- CS_SALES_SUFFIX_CODE
			, C.BUYER_MODEL_CODE                                                                  -- CS_CUSTOMER_MODEL
			, IFNULL(F.PDP_ID, 'NOT_MATCHED')                                                     AS CS_MATCHED_MODEL_CODE
			, C.MDMS_PRDGRP_CODE                                                                  -- CS_MDMS_PRODUCT_CODE
			, A.LV1_CATEGORY_CODE                                                                 -- CS_SUPER_CATEGORY_ID
			, SUPER.SITE_CATEGORY_NM AS LV1_CATEGORY_NM                                           -- CS_SUPER_CATEGORY_NAME
			, A.LV2_CATEGORY_CODE                                                                 -- CS_CATEGORY_ID
			, D.SITE_CATEGORY_NM AS LV2_CATEGORY_NM                                               -- CS_CATEGORY_NAME
			, IF(trim(UPPER(A.LV3_CATEGORY_CODE))='OTHERS',
				'Others', A.LV3_CATEGORY_CODE) AS LV3_CATEGORY_CODE                              -- CS_SUB_CATEGORY_ID
			, IF(trim(UPPER(A.LV3_CATEGORY_CODE))='OTHERS',
				'Others', SUB.SITE_CATEGORY_NM) AS LV3_CATEGORY_NM                               -- CS_SUB_CATEGORY_NAME
			, IFNULL(IFNULL(NULLIF(PPD.NEW_MKT_PRODUCT_NM, ''), G.USER_FRNDY_PRODUCT_NM ), '')    AS USER_FRNDY_PRODUCT_NM
			, MS.SIBLING_CODE                                                                     AS SIBLING_CODE
			, IFNULL(B.MODEL_IMG_PATH, '') AS MODEL_IMG_PATH                                      -- CS_MODEL_IMAGE_PATH
			, IFNULL(F.PRODUCT_STATE_CODE, 'NOT_MATCHED')                                         AS CS_PRODUCT_PAGE_LINK_STATUS
			, F.PDP_URL                                                                           AS CS_PRODUCT_PAGE_LINK
			, B.MOBL_MODEL_FLAG                                                                   -- CS_MOBILE_MODEL_FLAG
			, CASE IFNULL(F.PDP_ID, 'NOT_MATCHED') 
					WHEN 'NOT_MATCHED'
					THEN E.CUST_MODEL_CODE
					ELSE IFNULL(G.PRODUCT_NM, '')
				END                                                                                AS CS_CUSTOMER_MAPPING_MODEL
			, IFNULL((SELECT CONCAT(K.SALES_MODEL_CODE,'.',K.SALES_MODEL_SUFFIX_CODE)
						FROM PDM_PRODUCT_M K
						WHERE K.SKU_ID = G.SKU_ID
						AND K.SALES_MODEL_CODE IS NOT NULL
						LIMIT 1
					), G.PRODUCT_NM)                                                              AS CS_MATCHED_MODEL_DISPLAY
			, IFNULL(B.GSFS_USE_FLAG, 'Y')                                                        AS GSFS_FLAG
			, B.SALES_MODEL_FLAG                                                                  AS SALES_MODEL_FLAG
			, CASE (F.PRODUCT_STATE_CODE)
					WHEN 'ACTIVE' THEN REPLACE(F.PDP_URL,'.csp','')
					WHEN 'DISCONTINUED' THEN REPLACE(F.PDP_URL,'.csp','')
					ELSE NULL
					END AS MKT_PAGE_LINK
			, C.OBU_CODE                                          
			, A.SITE_CODE
			FROM SVD_PRODUCT_SVC_MODEL_CATEGORY_R A
			INNER JOIN SVD_PRODUCT_SVC_MODEL_D B
			ON A.SITE_CODE = B.SITE_CODE
			AND A.SVD_SKU_ID = B.SVD_SKU_ID
			INNER JOIN PDM_PRODUCT_SVC_D C
			ON A.SVD_SKU_ID = C.SVD_SKU_ID
			INNER JOIN DSP_DISPLAY_CATEGORY_M D
			ON A.SITE_CODE = D.SITE_CODE
			AND A.LV2_CATEGORY_CODE = D.CATEGORY_CODE
			LEFT JOIN DSP_DISPLAY_CATEGORY_M SUPER
			ON A.SITE_CODE = SUPER.SITE_CODE
			AND A.LV1_CATEGORY_CODE = SUPER.CATEGORY_CODE
			LEFT JOIN DSP_DISPLAY_CATEGORY_M SUB
			ON A.SITE_CODE = SUB.SITE_CODE
			AND A.LV3_CATEGORY_CODE = SUB.CATEGORY_CODE
			LEFT JOIN SVD_CUSTOMER_MODEL_M E
			ON A.SITE_CODE = E.SITE_CODE
			AND C.BUYER_MODEL_CODE = E.CUST_MODEL_CODE
			AND E.USE_FLAG='Y'
			LEFT JOIN DSP_PDP_D F
			ON E.PDP_ID=F.PDP_ID
			AND F.USE_FLAG = 'Y'
			AND F.AEM_PUBL_FLAG = 'Y'
			AND F.SITE_CODE = A.SITE_CODE
			AND F.SHOP_CODE  = 'D2C'
			LEFT JOIN DSP_PDP_M G 
			ON F.PDP_ID = G.PDP_ID
			AND F.SITE_CODE = G.SITE_CODE 
			LEFT JOIN PDM_PRODUCT_D PPD
			ON G.SKU_ID = PPD.SKU_ID
			AND F.BIZ_TYPE_CODE = PPD.BIZ_TYPE_CODE
			LEFT JOIN PDSMGR_PUBL.DSP_SIBLING_D MS
			ON G .PDP_ID = MS.PDP_ID
			AND G .BIZ_TYPE_CODE = MS.BIZ_TYPE_CODE
			WHERE A.USE_FLAG = 'Y'
				AND B.USE_FLAG = 'Y'
				AND C.USE_FLAG = 'Y'
				AND A.SITE_CODE = #{siteCode}
				AND D.SUPP_USE_FLAG = 'Y'
		   <if test="modelNum != null and modelNum != ''">
				AND (B.SALES_CODE = UPPER(#{modelNum}) OR C.BUYER_MODEL_CODE = UPPER(#{modelNum}))
		   </if>
		   <if test="csSalesCode != null and csSalesCode != ''">
		   		AND B.SALES_CODE = #{csSalesCode}
		   </if>
		   LIMIT 1
	</select>

	<select id="gpModelCateList" resultType="com.lge.d2x.domain.support.v1.model.GpModelDetailResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpModelDetailRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.gpModelCateList */
			   C.SALES_CODE        
			 , A.SVD_SKU_ID       
			 , A.BUYER_MODEL_CODE 
			 , A.SALES_MODEL_CODE  
			 , A.MDMS_PRDGRP_CODE  
			 , A.OBU_CODE
			 , B.LV1_CATEGORY_CODE 
			 , B.LV2_CATEGORY_CODE
			 , B.LV3_CATEGORY_CODE 
			 , IFNULL(IFNULL(NULLIF(PPD.NEW_MKT_PRODUCT_NM, ''), G.USER_FRNDY_PRODUCT_NM ), '') AS USER_FRNDY_PRODUCT_NM 
			 , MS.SIBLING_CODE                                                                     AS SIBLING_CODE
			 , C.MODEL_IMG_PATH   
			 , C.SALES_MODEL_FLAG
			 , C.MOBL_MODEL_FLAG  
			 , D.SITE_CATEGORY_NM AS CATEGORY_NM
		   FROM PDM_PRODUCT_SVC_D A
		   JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
		   JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode}
		   JOIN DSP_DISPLAY_CATEGORY_M D
	          ON  D.SITE_CODE = #{siteCode}
	          AND D.CATEGORY_CODE =
	          
	           <if test='"1".equals(cateLv)'>
	           		B.LV1_CATEGORY_CODE
	           </if>
	           <if test='"2".equals(cateLv)'>
	           		B.LV2_CATEGORY_CODE
	           </if>
	           <if test='"3".equals(cateLv)'>
	           		B.LV3_CATEGORY_CODE
	           </if>
	           <if test='cateLv==null or "".equals(cateLv)'>
	           		B.LV1_CATEGORY_CODE
	           </if>
	       JOIN DSP_DISPLAY_CATEGORY_D E
	          ON  E.SITE_CODE = #{siteCode}
	          AND E.CATEGORY_CODE = D.CATEGORY_CODE
	          AND E.SHOP_CODE = 'D2C'
	       LEFT JOIN SVD_CUSTOMER_MODEL_M F
	          ON F.SITE_CODE = #{siteCode}
	          AND A.BUYER_MODEL_CODE = F.CUST_MODEL_CODE
	          AND E.USE_FLAG='Y'
	       LEFT JOIN DSP_PDP_M G 
	          ON F.PDP_ID = G.PDP_ID
	          AND F.SITE_CODE = G.SITE_CODE 
	       LEFT JOIN PDM_PRODUCT_D PPD
	          ON G.SKU_ID = PPD.SKU_ID
	          AND G.BIZ_TYPE_CODE = PPD.BIZ_TYPE_CODE
	       LEFT JOIN PDSMGR_PUBL.DSP_SIBLING_D MS
	          ON G.PDP_ID = MS.PDP_ID
	          AND G.BIZ_TYPE_CODE = MS.BIZ_TYPE_CODE
		   WHERE 1=1
		   AND A.USE_FLAG = 'Y'
		   AND B.USE_FLAG = 'Y'
		   AND C.USE_FLAG = 'Y'
		   <if test="modelNum != null and modelNum != ''">
				AND (C.SALES_CODE = UPPER(#{modelNum}) OR A.BUYER_MODEL_CODE = UPPER(#{modelNum}))
		   </if>
		   <if test="csSalesCode != null and csSalesCode != ''">
		   		AND C.SALES_CODE = #{csSalesCode}
		   </if>
			<if test="suppUseFlag != null and suppUseFlag != ''">
				AND D.SUPP_USE_FLAG = #{suppUseFlag}
		   </if>
		   
	</select>

	<select id="inquiryPartsByModelAffiliate" resultType="com.lge.d2x.domain.support.v1.model.GpModelDetailResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpModelDetailRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.inquiryPartsByModelAffiliate */
			 C.AFFILIATE_CODE
			,B.SALES_CODE
			FROM PDM_PRODUCT_SVC_D A
			JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
			JOIN SVD_CUSTOMER_MODEL_FACTORY_R C ON C.SITE_CODE = #{siteCode} AND A.BUYER_MODEL_CODE = C.CUST_MODEL_CODE
			WHERE 1=1
			AND (B.SALES_CODE = UPPER(#{modelNum}) OR A.BUYER_MODEL_CODE = UPPER(#{modelNum}))
			LIMIT 1
    </select>
    
	<select id="buyerModelCodeBySalesCode" resultType="com.lge.d2x.domain.support.v1.model.GpModelDetailResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpModelDetailRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.buyerModelCodeBySalesCode */
		   A.BUYER_MODEL_CODE AS CUSTOMER_MODEL_CODE
	   FROM PDM_PRODUCT_SVC_D A
	   JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
	   WHERE 1=1
	   AND C.SALES_CODE = #{csSalesCode}
	   LIMIT 1
	</select>
	
	<select id="productGsriFileInfo" resultType="com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoDataVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.productGsriFileInfo */
		    C.DOC_ID AS DOC_ID
		   ,C.FILE_NM AS FILE_NAME
		   ,C.ORIGINAL_FILE_NM AS ORIGINAL_NAME
		   ,IFNULL(C.ATTR6 , C.LV1_PRODUCT_CODE) AS PRODUCT_LEVE1_CODE
	   FROM PDM_PRODUCT_M A 
	   JOIN DSP_PDP_M B ON A.SKU_ID = B.SKU_ID 
	   LEFT JOIN SVD_GSRI_DOC_M C 
	   ON (A.SALES_MODEL_CODE = C.SALES_MODEL_CODE
	   	   AND B.SITE_CODE = C.SITE_CODE
	   	   AND C.DOC_TYPE_CODE = #{docTypeCode}
	   	   AND C.DEL_FLAG = 'N')
	   WHERE 1=1
	   AND B.PDP_ID = #{pdpId}
	   AND C.GSRI_DOC_ID NOT IN (#{fEnergyLabelDocId}, #{productFichelDocId}, #{energyLabelDocId})
	   ORDER BY C.ATTR5 DESC , C.GSRI_DOC_ID DESC
	   LIMIT 1
	</select>
	
    <select id="bundleGsriFileInfo" resultType="com.lge.d2x.domain.support.v1.model.ProductGsriFileInfoDataVO"
    parameterType="com.lge.d2x.domain.support.v1.model.BundleGsriFileInfoResponseVO">
        SELECT * 
          FROM (SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.bundleGsriFileInfo */
                       B.PDP_ID AS MODEL_ID
                     , C.DOC_ID AS DOC_ID
                     , C.FILE_NM AS FILE_NAME
                     , C.ORIGINAL_FILE_NM AS ORIGINAL_NAME
                     , IFNULL(C.ATTR6 , C.LV1_PRODUCT_CODE) AS PRODUCT_LEVE1_CODE
                     , ROW_NUMBER() OVER(PARTITION BY C.SALES_MODEL_CODE, C.DOC_TYPE_CODE ORDER BY C.ATTR5 DESC, C.GSRI_DOC_ID DESC) AS RANK
                  FROM PDM_PRODUCT_M A 
                  JOIN DSP_PDP_M B ON A.SKU_ID = B.SKU_ID 
                  LEFT JOIN SVD_GSRI_DOC_M C 
                    ON A.SALES_MODEL_CODE = C.SALES_MODEL_CODE
                   AND B.SITE_CODE = C.SITE_CODE
        <if test="docTypeList != null">
            <foreach collection="docTypeList" item="c" open="AND C.DOC_TYPE_CODE IN (" separator="," close=")">
                       {c}
            </foreach>
        </if>
                   AND C.DEL_FLAG = 'N'
                 WHERE 1=1
                   AND B.SITE_CODE = #{siteCode}
                   AND B.PDP_ID = #{pdpId}
               ) D
         WHERE D.RANK = 1
    </select>
	
	<select id="pdfDownloadInfo" resultType="com.lge.d2x.domain.support.v1.model.PdfDownloadInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoRequestVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.pdfDownloadInfo */
		 DISTINCT 
		 B.PDP_ID AS MODEL_ID
		,B.SITE_CODE AS LOCALE_CODE
		,F.GSRI_DOC_ID AS DOC_ID
		,F.DOC_TYPE_CODE
		,F.DOC_TYPE_NM AS DOC_TYPE_NAME
		,F.FILE_NM AS DOC_FILE_NAME
		,F.FILE_SIZE AS DOC_FILE_SIZE
		,F.MODEL_NM AS MODEL_NAME
		,F.ORIGINAL_FILE_NM AS DOC_ORIGINAL_NAME
		,F.SALES_MODEL_CODE AS SALES_MODEL
		,G.LINK_LABEL_NM AS LINK_LABEL_NAME
		,G.TARGET_TYPE_NM AS LINK_TARGET_NAME
		,H.FILE_PATH AS SPEC_DOWNLOAD_ADDR
		,CASE
		           WHEN SUBSTR(LOWER(G.LINK_URL), 1, 4) = 'http' THEN
		            G.LINK_URL
		           ELSE
		            REPLACE(G.LINK_URL, '.jsp', '')
		       END AS LINK_ADDR
		FROM PDM_PRODUCT_M A 
	    JOIN DSP_PDP_M B ON A.SKU_ID = B.SKU_ID 
		LEFT OUTER JOIN (
			SELECT 
			 C.BUYER_MODEL_CODE 
			,C.SALES_MODEL_CODE 
			,C.SALES_MODEL_SUFFIX_CODE
			FROM PDM_PRODUCT_SVC_D C  		
			JOIN SVD_PRODUCT_SVC_MODEL_D D ON C.SVD_SKU_ID = D.SVD_SKU_ID AND D.SITE_CODE = #{siteCode}
			WHERE 1=1 
			AND C.USE_FLAG = 'Y'
			AND D.USE_FLAG = 'Y'	
		) C ON C.SALES_MODEL_CODE = #{salesModelCode} AND C.SALES_MODEL_SUFFIX_CODE = #{salesModelSuffixCode}
		LEFT OUTER JOIN SVD_GSRI_DOC_M F
		ON ( 
				F.SITE_CODE = B.SITE_CODE
			AND F.DOC_TYPE_NM = #{docTypeNm}
			AND F.DEL_FLAG = 'N'
			AND F.SALES_MODEL_CODE = A.SALES_MODEL_CODE
			)
		LEFT OUTER JOIN SVD_MODEL_LINK_R G
		ON (
				G.USE_FLAG  = 'Y'
			AND G.SITE_CODE = B.SITE_CODE
			AND TRIM(UPPER(G.LINK_LABEL_NM)) = UPPER(#{linkLabelNm})
			AND G.CUST_MODEL_CODE = C.BUYER_MODEL_CODE
			)
		LEFT OUTER JOIN SVD_MODEL_DOC_D H
		ON (
				H.USE_FLAG = 'Y'
			AND H.FILE_PATH IS NOT NULL
			AND H.SITE_CODE = B.SITE_CODE 
			AND H.MODEL_FILE_TYPE_CODE=#{modelFileTypeCode}
			AND H.CUST_MODEL_CODE = C.BUYER_MODEL_CODE
			)
		WHERE 1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND B.SITE_CODE =#{siteCode}
		AND B.PDP_ID = #{pdpId}
		LIMIT 1	;
	</select>
	<select id="specDownloadInfo" resultType="com.lge.d2x.domain.support.v1.model.PdfDownloadInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.specDownloadInfo */
		FILE_PATH AS FILE_PATH_ADDR
		FROM SVD_MODEL_DOC_D A 
		INNER JOIN 
			(SELECT B.BUYER_MODEL_CODE  , C.SITE_CODE
			 FROM PDM_PRODUCT_SVC_D B 
			 JOIN SVD_PRODUCT_SVC_MODEL_D C ON B.SVD_SKU_ID = C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode}
			 WHERE 1=1
			 AND B.USE_FLAG='Y'
			 AND C.USE_FLAG='Y'
		    ) B ON A.CUST_MODEL_CODE = B.BUYER_MODEL_CODE
		 INNER JOIN DSP_PDP_M E
			 ON (E.PDP_ID    = #{pdpId} 
			 AND E.SITE_CODE = #{siteCode}
			 AND E.USE_FLAG  = 'Y'
			 <if test="bizTypeCode != null and bizTypeCode != ''">
			    AND E.BIZ_TYPE_CODE = #{bizTypeCode}
			 </if>
			 )
		 WHERE 1=1
		 AND A.USE_FLAG  = 'Y'
		 AND A.SITE_CODE = #{siteCode}
		 AND A.MODEL_FILE_TYPE_CODE = #{modelFileTypeCode}
		 LIMIT 1 
	</select>
	
	<select id="iframeSpecDownloadInfo" resultType="com.lge.d2x.domain.support.v1.model.PdfDownloadInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoRequestVO">
			SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.iframeSpecDownloadInfo */
		       DISTINCT C.PDP_ID AS MODEL_ID 
		               ,C.CUST_MODEL_CODE AS CUSTOMER_MODEL_CODE
		               ,C.SITE_CODE AS LOCALE_CODE
		               ,D.FILE_PATH AS FILE_PATH_ADDR
		  FROM (SELECT A.PDP_ID
		              ,A.CUST_MODEL_CODE
		              ,A.SITE_CODE
		          FROM SVD_CUSTOMER_MODEL_M A
		         WHERE A.USE_FLAG = 'Y'
		           AND A.SITE_CODE = #{siteCode}
		           AND A.PDP_ID = #{pdpId}
		        UNION ALL
		        SELECT B.PDP_ID
		              ,B.CUST_MODEL_CODE
		              ,B.SITE_CODE
		          FROM SVD_CUSTOMER_MODEL_PDP_R B
		         WHERE B.USE_FLAG = 'Y'
		           AND B.SITE_CODE = #{siteCode}
		           AND B.PDP_ID = #{pdpId}
		         LIMIT 1
		       ) C
		 INNER JOIN SVD_MODEL_DOC_D D
		    ON D.CUST_MODEL_CODE = C.CUST_MODEL_CODE
		   AND D.SITE_CODE = C.SITE_CODE
		   AND D.MODEL_FILE_TYPE_CODE = #{modelFileTypeCode}
		 LIMIT 1
	</select>
	<select id="gsriListCnt" resultType="com.lge.d2x.domain.support.v1.model.PdfDownloadInfoResponseVO" parameterType="String">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.gsriListCnt */ 
		COUNT(A.DEL_FLAG) AS CNT 
		FROM SVD_GSRI_DOC_M A
		WHERE 1=1
		AND A.SITE_CODE = #{siteCode}
		AND A.DEL_FLAG = 'N'
	</select>
	
	<select id="gsriList" resultType="com.lge.d2x.domain.support.v1.model.GsriListDataVO" parameterType="com.lge.d2x.domain.support.v1.model.PdfGsriSpecInfoRequestVO">
	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.gsriList */
       MAX(A.DOC_ID) AS CEDOC_ID
      ,A.FILE_NM AS FILE_NAME
      ,A.ORIGINAL_FILE_NM AS ORIGINAL_FILE_NAME
      ,A.FILE_SIZE
      ,A.SALES_MODEL_CODE AS SALES_MODEL
      ,A.MODEL_NM AS MODEL_NAME
      ,A.PRODUCT_NM AS PRODUCT_NAME
      ,A.DOC_TYPE_CODE AS DOC_TYPE_CODE
		<choose>
			<when test="siteCode == 'RS'">
				  ,CASE
					   WHEN A.ATTR2 = 'Y' THEN CONCAT( A.DOC_TYPE_NM, '(', A.ATTR3, ')' )
					   ELSE A.DOC_TYPE_NM
				   END AS DOC_TYPE_NAME
				  ,COUNT(A.MODEL_NM) OVER(PARTITION BY A.DOC_TYPE_CODE, A.ATTR1) AS DOC_TYPE_CNT
				  ,A.ATTR1
			</when>
			<otherwise>
				  ,A.DOC_TYPE_NM AS DOC_TYPE_NAME
				  ,COUNT(A.MODEL_NM) OVER(PARTITION BY A.DOC_TYPE_CODE) AS DOC_TYPE_CNT
			</otherwise>
		</choose>
		FROM SVD_GSRI_DOC_M A
		INNER JOIN(SELECT B.SALES_MODEL_CODE
					 FROM PDM_PRODUCT_M B
					 JOIN DSP_PDP_M C ON B.SKU_ID = C.SKU_ID 
					 WHERE 1=1 
					 AND B.USE_FLAG = 'Y'
					 AND C.USE_FLAG = 'Y'
					 AND C.PDP_ID = #{pdpId}
					) B ON A.SALES_MODEL_CODE=B.SALES_MODEL_CODE
		WHERE 1=1
		AND A.SITE_CODE = #{siteCode}
		AND A.DEL_FLAG = 'N'
		<if test="cedocUseFlag == 'N'.toString">
			AND A.DOC_TYPE_CODE <![CDATA[<>]]> 'CD'
		</if>
		<if test="energyLabelFlag == 'Y'.toString and washTowerFlag == 'Y'.toString">
			AND A.DOC_TYPE_NM <![CDATA[<>]]> ('WEB INFO')
			AND A.DOC_TYPE_NM <![CDATA[<>]]> ('UK DoC')
		</if>
		<if test="washTowerFlag != 'Y'.toString and (energyLabelCategory == 'EL_CAT_01'.toString 
												  or energyLabelCategory == 'EL_CAT_02'.toString 
												  or energyLabelCategory == 'EL_CAT_03'.toString 
												  or energyLabelCategory == 'EL_CAT_05'.toString 
												  or pdfFlag != 'Y'.toString 
												  and energyLabelCategory == 'EL_CAT_04'.toString)" >
												  
		    AND A.DOC_TYPE_CODE NOT IN('FL','EL','TL')
		    AND A.DOC_TYPE_NM <![CDATA[<>]]> ('PRODUCT FICHE')
 		</if>
		<if test="siteCode == 'TR' and energyLabelCategory == 'EL_CAT_04'.toString">
 		   AND A.DOC_TYPE_CODE NOT IN ('TL' , 'TF' , 'TI', 'EL' , 'PF' )
 		</if>
 		GROUP BY A.FILE_NM
		         ,A.ORIGINAL_FILE_NM
		         ,A.FILE_SIZE
		         ,A.SALES_MODEL_CODE
		         ,A.MODEL_NM
		         ,A.PRODUCT_NM
		         ,A.DOC_TYPE_CODE
		         ,A.DOC_TYPE_NM
		<if test="siteCode == 'RS'">
		         ,A.ATTR1
		         ,A.ATTR2
		         ,A.ATTR3
		</if>
		 ORDER BY A.DOC_TYPE_CODE
		         ,A.MODEL_NM
	</select>
	
	<select id="productSupportInfo" resultType="com.lge.d2x.domain.support.v1.model.ProductSupportInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.productSupportInfo */
			   H.LV2_CATEGORY_CODE AS CS_CATEGORY_ID
			  ,H.BUYER_MODEL_CODE AS CS_CUSTOMER_MODEL
			  ,H.MOBL_MODEL_FLAG AS CS_MOBILE_MODEL_FLAG
			  ,H.LV3_CATEGORY_CODE AS CS_SUB_CATEGORY_ID
			  ,H.LV1_CATEGORY_CODE AS CS_SUPER_CATEGORY_ID
			  ,H.SALES_CODE AS CS_SALES_CODE
		FROM (	 
		SELECT C.MOBL_MODEL_FLAG 
			  ,B.LV2_CATEGORY_CODE
			  ,B.LV1_CATEGORY_CODE
			  ,B.LV3_CATEGORY_CODE
			  ,A.BUYER_MODEL_CODE
			  ,C.SALES_CODE
		FROM PDM_PRODUCT_SVC_D A 
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
		JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode}
		WHERE 1=1 
		AND A.SALES_MODEL_CODE = #{salesModelCode}
		AND A.SALES_MODEL_SUFFIX_CODE = #{salesSuffixCode}
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		UNION 
		SELECT F.MOBL_MODEL_FLAG 
			  ,E.LV2_CATEGORY_CODE
			  ,E.LV1_CATEGORY_CODE
			  ,E.LV3_CATEGORY_CODE
			  ,D.BUYER_MODEL_CODE
			  ,F.SALES_CODE
		FROM PDM_PRODUCT_SVC_D D
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R E ON D.SVD_SKU_ID = E.SVD_SKU_ID AND E.SITE_CODE = #{siteCode}
		JOIN SVD_PRODUCT_SVC_MODEL_D F ON D.SVD_SKU_ID = F.SVD_SKU_ID AND F.SITE_CODE = #{siteCode}
		JOIN SVD_CUSTOMER_MODEL_PDP_R G ON (    G.USE_FLAG = 'Y'
											AND G.SITE_CODE = #{siteCode}
											AND G.CUST_MODEL_CODE = D.BUYER_MODEL_CODE
											AND G.PDP_ID = #{pdpId})
		)H
		LIMIT 1
	</select>
	
	
	
		
	<select id="retrieveCmsDocument" resultType="com.lge.d2x.domain.support.v1.model.CmsDocumentInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoRequestVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveCmsDocument */
		        MANUAL_TYPE
			  , FILE_NAME_PRINT
			  , '' AS FILE_NAME
			  , FILE_TYPE
			  , TARGET_TYPE
			  , 'Y' AS CMS_FLAG
			  , FILE_SIZE
			  , IFNULL(DATE_FORMAT(RELEASE_DATE,#{dateFormatInfo}),'N/A') AS RELEASE_DATE
			  , IFNULL(SORT_DATE,'N/A') AS SORT_DATE
			  , FILE_URL
			  , LANGUAGE_CODE
	       FROM
		   (SELECT 
                   (SELECT C.MSG_CNTS 
					 FROM COM_COMMON_CODE_M	A
					 JOIN COM_COMMON_CODE_D B ON A.COMMON_CODE = B.COMMON_CODE 
					 JOIN COM_MESSAGE_M C ON B.CODE_VAL_NM = C.MSG_CODE
					 JOIN COM_SITE_M D ON C.LOCALE_CODE = D.LOCALE_CODE
					 WHERE 1=1
					 AND A.COMMON_CODE_NM = 'DOCU000000'
					 AND B.COMMON_CODE_VAL = MODEL_FILE_TYPE_CODE
					 AND C.USE_FLAG = 'Y'
					 AND C.DSP_SVC_MSG_SP_CODE = 'SVC'
					 AND D.SITE_CODE = #{siteCode}
					 )AS MANUAL_TYPE
                  ,(SELECT C.MSG_CNTS 
					 FROM COM_COMMON_CODE_M	A
					 JOIN COM_COMMON_CODE_D B ON A.COMMON_CODE = B.COMMON_CODE 
					 JOIN COM_MESSAGE_M C ON B.CODE_VAL_NM = C.MSG_CODE
					 JOIN COM_SITE_M D ON C.LOCALE_CODE = D.LOCALE_CODE
					 WHERE 1=1
					 AND A.COMMON_CODE_NM = 'CS05'
					 AND B.COMMON_CODE_VAL = LOWER(LANG_CODE)
					 AND C.USE_FLAG = 'Y'
					 AND C.DSP_SVC_MSG_SP_CODE = 'SVC'
					 AND D.SITE_CODE = #{siteCode}
					 )AS FILE_NAME_PRINT
	             , 'pdf' AS FILE_TYPE
	             , '_self' AS TARGET_TYPE
		         , FILE_SIZE AS FILE_SIZE
		         , LAST_UPDATE_DATE AS RELEASE_DATE
		         , LAST_UPDATE_DATE AS SORT_DATE
		         , FILE_PATH AS FILE_URL
		         , LANG_CODE AS LANGUAGE_CODE
		      FROM SVD_MODEL_DOC_D
		     WHERE CUST_MODEL_CODE = #{custModelCode}
		       AND SITE_CODE =  #{siteCode}
		       AND FILE_PATH IS NOT NULL
			<if test='mobileFlag == "N"'>
			<![CDATA[
	  		   AND MODEL_FILE_TYPE_CODE <> 'DU03'
			  ]]>
			</if>
			<![CDATA[
		    UNION ALL
			SELECT 'ADDITIONAL' AS MANUAL_TYPE
				 , LINK_LABEL_NM AS FILE_NAME_PRINT
				 , LINK_TYPE_NM AS FILE_TYPE
				 , TARGET_TYPE_NM AS TARGET_TYPE
			     , '' AS FILE_SIZE
			     , LAST_UPDATE_DATE AS RELEASE_DATE
			     , LAST_UPDATE_DATE AS SORT_DATE
			     , CASE WHEN SUBSTR(LOWER(LINK_URL),1,4) = 'http' THEN LINK_URL
	               ELSE REPLACE(LINK_URL,'.jsp','')
	               END AS FILE_URL
	             , '' AS LANGUAGE_CODE
			 FROM SVD_MODEL_LINK_R
			WHERE USE_FLAG = 'Y'
			  AND SITE_CODE = #{siteCode}
			  AND CUST_MODEL_CODE= #{custModelCode}
		) AA
		ORDER BY AA.RELEASE_DATE DESC
		]]>
		<choose>
			<when test='manualCnt != null and manualCnt != ""'>
			LIMIT ${manualCnt}
			</when>
			<otherwise>
			LIMIT 1
			</otherwise>
		</choose>
	</select>

	<select id="retrieveMcSupportManualsDocsList" resultType="com.lge.d2x.domain.support.v1.model.CmsDocumentInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoRequestVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveMcSupportManualsDocsList */
			            ZZ.MANUAL_TYPE
                      , ZZ.DOC_ID
                      , ZZ.AVAILABLE_FLAG
                      , ZZ.ORIGINAL_FILE_NAME
                      , ZZ.FILE_URL
                      , ZZ.FILE_NAME_PRINT
                      , ZZ.FILE_NAME
                      , ZZ.FILE_TYPE
                      , ZZ.FILE_SIZE
                      , ZZ.RELEASE_DATE
                      , ZZ.SORT_DATE
                      , ZZ.LANGUAGE_CODE
				FROM (
					SELECT
					       UU.MANUAL_TYPE
					      ,UU.FILE_TYPE
					      ,UU.DOC_ID
					      ,UU.AVAILABLE_FLAG
					      ,UU.FILE_NAME
					      ,UU.ORIGINAL_FILE_NAME
					      ,UU.FILE_SIZE
					      ,DATE_FORMAT(UU.RELEASE_DATE,#{dateFormatInfo}) AS RELEASE_DATE
					      ,UU.SORT_DATE
					      ,UU.FILE_NAME_PRINT
					      ,UU.SELECT_MAX_NUM
					      ,UU.ORDER_NUM
					      ,ROW_NUMBER() OVER(PARTITION BY UU.MANUAL_TYPE ORDER BY @rownum ASC) AS SELECTED_NUM
					      ,'N/A' AS FILE_URL
					      ,UU.LANGUAGE_CODE
					FROM (SELECT 'OSU' AS MANUAL_TYPE
					           , Z.FILE_TYPE
					           , Z.DOC_ID
					           , Z.AVAILABLE_FLAG
					           , Z.FILE_NAME
					           , Z.ORIGINAL_FILE_NAME
					           , Z.FILE_SIZE
					           , MAX(Z.RELEASE_DATE) AS RELEASE_DATE
					           , MAX(Z.SORT_DATE) AS SORT_DATE
					           , Z.OS_LIST AS  FILE_NAME_PRINT
					           , COUNT(1) OVER() AS SELECT_MAX_NUM
					           , 0 AS ORDER_NUM
					           , GROUP_CONCAT(Z.LANGUAGE_CODE ORDER BY Z.LANGUAGE_CODE, FILE_TYPE, DOC_ID,', ') AS LANGUAGE_CODE
					      FROM (SELECT FILE_TYPE
					                 , DOC_ID
					                 , AVAILABLE_FLAG
					                 , FILE_NAME
					                 , ORIGINAL_FILE_NAME
					                 , FILE_SIZE
					                 , DISPLAY_ORDER_NO
					                 , RELEASE_DATE
					                 , SORT_DATE
					                 , LANGUAGE_NAME
					                 , RANK() OVER(PARTITION BY FILE_TYPE,DOC_ID ORDER BY @rownum:=@rownum+1 DESC) AS SELECTED_MANUAL2
					                 , LANGUAGE_CODE
					                 , OS_LIST
					            FROM (SELECT DISTINCT B.FILE_EXETENSION_NM AS FILE_TYPE
					                       , B.MANUAL_ID AS DOC_ID
					                       , (CASE IFNULL(D.DEL_FLAG,'N') WHEN 'Y' THEN 'N' WHEN 'N' THEN 'Y' END) AS AVAILABLE_FLAG
					                       , B.MANUAL_FILE_ID  AS FILE_NAME
					                       , REPLACE(B.ORIGINAL_FILE_NM,' ','_') AS ORIGINAL_FILE_NAME
					                       , B.FILE_SIZE AS FILE_SIZE
					                       , RANK() OVER(PARTITION BY E.LANG_CODE ORDER BY B.ISSU_DD DESC) AS SELECTED_MANUAL
					                       , DSP_SEQ AS DISPLAY_ORDER_NO
					                       , IFNULL(B.ISSU_DD,'N/A') AS RELEASE_DATE
					                       , IFNULL(B.ISSU_DD,'N/A') AS SORT_DATE
										   ,(SELECT C.MSG_CNTS 
											 FROM COM_COMMON_CODE_M	A
											 JOIN COM_COMMON_CODE_D B ON A.COMMON_CODE = B.COMMON_CODE 
											 JOIN COM_MESSAGE_M C ON B.CODE_VAL_NM = C.MSG_CODE
											 JOIN COM_SITE_M D ON C.LOCALE_CODE = D.LOCALE_CODE
											 WHERE 1=1
											 AND A.COMMON_CODE_NM = 'CS05'
											 AND B.COMMON_CODE_VAL = E.LANG_CODE
											 AND C.USE_FLAG = 'Y'
											 AND C.DSP_SVC_MSG_SP_CODE = 'SVC'
											 AND C.SHOP_CODE = 'D2C'
											 AND D.SITE_CODE = #{siteCode}
											 )AS LANGUAGE_NAME
					                       , @rownum:=0
					                       , B.LANG_LIST_CNTS AS LANGUAGE_CODE
					                       , B.OS_LIST_CNTS AS OS_LIST
					                  FROM SVD_MANUAL_FILE_D B
					                  LEFT OUTER JOIN SVD_GSCS_DELETE_PDF_D D ON (B.MANUAL_ID = D.DOC_ID)
					                  JOIN SVD_MANUAL_MODEL_D C ON (B.MANUAL_ID = C.MANUAL_ID)
					                  JOIN SVD_COUNTRY_LANG_D  E ON (INSTR(B.LANG_LIST_CNTS,E.LANG_CODE)>0)
					                  WHERE B.MANUAL_CODE = 'OSU'
					                    AND (C.AFFILIATE_CODE, C.FACTORY_MODEL_CODE, C.FACTORY_SUFFIX_CODE, C.OBU_CODE) IN
					                        (
												SELECT 
													D.AFFILIATE_CODE , D.FACTORY_MODEL_CODE , D.FACTORY_SUFFIX_CODE,D.OBU_CODE
												FROM PDM_PRODUCT_SVC_D A 
												JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
												JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode} AND C.SALES_CODE = #{csSalesCode}
												JOIN SVD_GSCS_MODEL_M D ON A.BUYER_MODEL_CODE = D.BUYER_MODEL_CODE AND D.BUYER_CODE = #{buyerCode}
											)
					                    AND E.COUNTRY_CODE =  #{countryCode}
					          ) M
					          WHERE SELECTED_MANUAL  = 1
					      ) Z
					    WHERE (AVAILABLE_FLAG) <![CDATA[<>]]> 'N'
					    GROUP BY Z.FILE_TYPE
					         , Z.DOC_ID
					         , Z.AVAILABLE_FLAG
					         , Z.FILE_NAME
					         , Z.ORIGINAL_FILE_NAME
					         , Z.FILE_SIZE
					    ORDER BY FILE_NAME_PRINT ASC , RELEASE_DATE DESC
					    ) UU
				) ZZ
			ORDER BY ZZ.RELEASE_DATE DESC, ZZ.ORDER_NUM  ASC, ZZ.SELECTED_NUM ASC
			<choose>
				<when test='manualCnt != null and manualCnt != ""'>
				LIMIT ${manualCnt}
				</when>
				<otherwise>
				LIMIT 1
				</otherwise>
			</choose>
	</select>
	
	<select id="retrieveSoftwareListAll" resultType="com.lge.d2x.domain.support.v1.model.SoftwareAllInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductManualSoftwareInfoRequestVO">
		WITH  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSoftwareListAll */
		   SWMDL AS (
              SELECT  C.BUYER_MODEL_CODE, C.DOC_ID , Z.SITE_CODE
	     FROM SVD_GSCS_MODEL_M G
	     JOIN SVD_SOFTWARE_MODEL_R C
	       ON ( G.AFFILIATE_CODE = C.AFFILIATE_CODE  AND G.FACTORY_MODEL_CODE = C.FACTORY_MODEL_CODE  AND G.FACTORY_SUFFIX_CODE = C.FACTORY_SUFFIX_CODE  AND G.OBU_CODE = C.OBU_CODE)
	     JOIN  SVD_SOFTWARE_M A
	       ON (C.DOC_ID = A.DOC_ID)
	     JOIN SVD_SOFTWARE_BUYER_R B
	       ON (A.DOC_ID = B.DOC_ID)
	     JOIN COM_SVD_PREFERENCE_M X
	       ON (B.BUYER_CODE = X.BUYER_CODE)
        JOIN COM_LOCALE_M Y 
		  ON (X.COUNTRY_CODE = Y.COUNTRY_CODE)
 			    JOIN COM_SITE_M Z 
		 ON (Y.LOCALE_CODE = Z.LOCALE_CODE) 
	     WHERE 1=1
	     AND G.BUYER_MODEL_CODE = UPPER(#{customerModelCode})
	     AND G.BUYER_CODE = #{buyerCode}
	     AND A.OPEN_STATE_CODE IN ('ONSERVICE','TEMP')
	     AND A.BUYER_CODE = '00000000'
	     AND A.DEL_FLAG='N'
	     AND Z.SITE_CODE=#{siteCode}
	     ),
	     SWLIST AS (SELECT AA1.CUSTOMER_MODEL_CODE
			  , AA1.DOC_ID
			  , AA1.GLOBAL_DOC_ID
			  , AA1.TITLE_NAME
			  , <![CDATA[CASE WHEN INSTR(TRIM(AA1.DETAIL_CONTENT2), '<') = 1 THEN REPLACE(NVL(AA1.DETAIL_CONTENT,AA1.DETAIL_CONTENT2),'\N','<BR>') ELSE REPLACE(REPLACE(NVL(AA1.DETAIL_CONTENT,AA1.DETAIL_CONTENT2),'\N','<BR>'),' ','&NBSP;') END AS DETAIL_CONTENT]]>
			  , AA1.PARENT_DOC_ID
			  , AA1.SW_SEQ
			  , AA1.SW_DETAIL_PATH
			  , AA1.SW_VISIBLE_FLAG
			  , AA1.IS_SW_DRV_LOCAL
			  , AA1.SW_FILE_NAME 
		  FROM (SELECT A1.CUSTOMER_MODEL_CODE
					 , A1.DOC_ID
					 , A1.GLOBAL_DOC_ID
					 , A1.TITLE_NAME
					 , (
						<![CDATA[SELECT GROUP_CONCAT('<H4>',A.CONTENT_TITLE,'</H4>','<P>',A.CONTENT_CNTS,'</P>')]]>
						  FROM SVD_SOFTWARE_CONTENT_D A
						 WHERE A.USE_FLAG = 'Y'
						   AND A.SW_CONTENT_DOC_ID = A1.GLOBAL_DOC_ID
						   GROUP BY A.SW_CONTENT_DOC_ID
						)
					   AS DETAIL_CONTENT
					 , A1.DETAIL_CONTENT AS DETAIL_CONTENT2
					 , A1.PARENT_DOC_ID
					 , A2.SW_SEQ
					 , A2.SW_DETAIL_PATH
					 , A2.SW_VISIBLE_FLAG
					 , A1.IS_SW_DRV_LOCAL
					 , A2.SW_FILE_NAME
				FROM (SELECT DISTINCT C.BUYER_MODEL_CODE AS CUSTOMER_MODEL_CODE
						   , A.SW_CONTENT_DOC_ID AS DOC_ID
						   , A.SW_CONTENT_DOC_ID AS GLOBAL_DOC_ID
						   , E.SUBJECT_CNTS AS TITLE_NAME
						   ,<![CDATA[ CONCAT('<H4>',A.CONTENT_TITLE,'</H4>','<P>',A.CONTENT_CNTS,'</P>') AS DETAIL_CONTENT]]>
						   , E.HIGH_DOC_ID AS PARENT_DOC_ID
						   , '' AS SW_SEQ
						   , '' AS SW_DETAIL_PATH
						   , '' AS SW_VISIBLE_FLAG
						   , '' AS SW_FILE_NAME
						   , 'N' AS  IS_SW_DRV_LOCAL
						FROM SVD_SOFTWARE_CONTENT_D A
							 JOIN SWMDL C ON (A.SW_CONTENT_DOC_ID = C.DOC_ID)
							 JOIN SVD_SOFTWARE_M E ON (A.SW_CONTENT_DOC_ID = E.DOC_ID)
					   WHERE C.SITE_CODE =  #{siteCode}
						 AND A.USE_FLAG = 'Y'
						 AND E.DEL_FLAG ='N'
						 AND E.OPEN_STATE_CODE  IN ( 'ONSERVICE','TEMP')
						 AND E.LANG_CODE IN (SELECT LANG_CODE
												   FROM   SVD_COUNTRY_LANG_D F
												  WHERE  COUNTRY_CODE =  #{countryCode})
						) A1 LEFT OUTER JOIN
					 (SELECT DISTINCT C.BUYER_MODEL_CODE AS CUSTOMER_MODEL_CODE
						   , A.SW_CONTENT_DOC_ID
						   , A.SW_CONTENT_DOC_ID AS GLOBAL_DOC_ID
						   , D.SUBJECT_CNTS  AS TITLE_NAME
						   , '' AS DETAIL_CONTENT
						   , '' AS PARENT_DOC_ID
						   , '' AS SW_SEQ
						   , '' AS SW_DETAIL_PATH
						   , '' AS SW_VISIBLE_FLAG
						   , '' AS SW_FILE_NAME
						FROM SVD_SOFTWARE_CONTENT_D A
							 JOIN SWMDL C ON (A.SW_CONTENT_DOC_ID = C.DOC_ID)
							 JOIN SVD_SOFTWARE_M D ON (A.SW_CONTENT_DOC_ID = D.DOC_ID)
					   WHERE C.SITE_CODE = #{siteCode}
						 AND A.USE_FLAG = 'Y'
						 AND D.LANG_CODE IN (SELECT LANG_CODE
												   FROM   SVD_COUNTRY_LANG_D F
												  WHERE  COUNTRY_CODE =  #{countryCode})
					) A2
					ON A1.GLOBAL_DOC_ID = A2.GLOBAL_DOC_ID
			  ) AA1 
		)
		SELECT FF.TITLE
			 , FF.F_FILE_NAME AS FILE_NAME
			 , FF.F_ORIGINAL_FILE_NAME AS FILE_NAME_PRINT
			 , FF.RELEASE_DATE
			 , FF.F_FILE_SIZE AS FILE_SIZE
			 <![CDATA[, CASE WHEN INSTR(TRIM(FF.DETAIL_CONTENT),'<') = 1 THEN REPLACE(FF.DETAIL_CONTENT,'\N','<BR>') ELSE REPLACE(REPLACE(FF.DETAIL_CONTENT,'\N','<BR>'),' ','&NBSP;') END AS DETAIL_CONTENT]]>
		 FROM (SELECT SWLIST.CUSTOMER_MODEL_CODE
					, SWLIST.DOC_ID
					, SWLIST.GLOBAL_DOC_ID
					, SWLIST.TITLE_NAME AS TITLE
					, SWLIST.DETAIL_CONTENT
					, B.OCCR_DATE AS ISSUE_DATE
					, DATE_FORMAT(B.OCCR_DATE ,#{dateFormatInfo}) AS RELEASE_DATE
					, '' AS FILE_SIZE
					, IS_SW_DRV_LOCAL
					, SWLIST.SW_DETAIL_PATH
					, SWLIST.SW_SEQ AS SEQ_NO
					, SWLIST.SW_FILE_NAME
					, OS.OS_ID OS_CODE
					, OS.OS_NM  AS OS_NAME
					, F.F_FILE_NAME
					, F.F_ORIGINAL_FILE_NAME
					, F.F_FILE_SIZE
					, F.F_FILE_PATH
					, F.F_FILE_SYSTEM_CODE
					, DATE_FORMAT(B.OCCR_DATE ,#{dateFormatInfo} ) AS F_RELEASE_DATE 
					, F.FILE_SEQ AS F_FILE_SEQ
					, SWLIST.SW_VISIBLE_FLAG
					, ROW_NUMBER() OVER(PARTITION BY SWLIST.CUSTOMER_MODEL_CODE,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ORDER BY SWLIST.CUSTOMER_MODEL_CODE,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ) AS DOC_DISPLAY
					, ROW_NUMBER() OVER(PARTITION BY SWLIST.CUSTOMER_MODEL_CODE,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, OS.OS_ID ORDER BY SWLIST.CUSTOMER_MODEL_CODE,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, OS.OS_ID) AS OS_DISPLAY
					, ROW_NUMBER() OVER(PARTITION BY SWLIST.CUSTOMER_MODEL_CODE,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, F.FILE_SEQ ORDER BY SWLIST.CUSTOMER_MODEL_CODE,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, F.FILE_SEQ) AS FILE_DISPLAY
					, ROW_NUMBER() OVER(PARTITION BY SWLIST.CUSTOMER_MODEL_CODE,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, SWLIST.SW_SEQ ORDER BY SWLIST.CUSTOMER_MODEL_CODE,SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID, SWLIST.SW_SEQ) AS SW_DISPLAY
					, ROW_NUMBER() OVER(PARTITION BY OS.OS_ID ORDER BY OS.OS_ID) AS OS_ROW
					, ROW_NUMBER() OVER(PARTITION BY F.FILE_ROW ORDER BY F.FILE_ROW) AS FILE_ROW
					, CONCAT(MAX(DATE_FORMAT(B.OCCR_DATE ,'%Y%M%D')) OVER(PARTITION BY SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ORDER BY SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID),
					  MAX(CONCAT(LPAD(SWLIST.DOC_ID, 25, '0'),SWLIST.GLOBAL_DOC_ID)) OVER(PARTITION BY SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID ORDER BY SWLIST.DOC_ID, SWLIST.GLOBAL_DOC_ID))
					  AS DOC_ORD
				FROM
				 SWLIST LEFT OUTER JOIN
				(SELECT @ROWID:=@ROWID+1 AS FILE_ROW,
				   SW_FILE_DOC_ID AS DOC_ID,
					   SW_FILE_SEQ AS FILE_SEQ,
					   SW_FILE_DOC_ID AS F_DOC_ID,
					   SW_FILE_DOC_ID AS F_GLOBAL_DOC_ID,
					   FILE_NM AS F_FILE_NAME,
					   REPLACE(ORIGINAL_FILE_NM, ' ', '_') AS F_ORIGINAL_FILE_NAME,
					   FILE_SIZE AS F_FILE_SIZE,
					   FILE_PATH AS F_FILE_PATH,
					   'GCSC' AS F_FILE_SYSTEM_CODE,
					   LIST_EXP_FLAG
					FROM SVD_SOFTWARE_FILE_D, (SELECT @ROWID:=0) AS INIT
					WHERE (SW_FILE_DOC_ID,'N') IN ((SELECT DISTINCT DOC_ID,IS_SW_DRV_LOCAL FROM SWLIST))
				  ) F
					ON SWLIST.DOC_ID = F.DOC_ID
					AND F.LIST_EXP_FLAG = '1'
					LEFT OUTER JOIN
				  (SELECT A1.DOC_ID
						, A1.OCCR_DATE
					 FROM (SELECT A.SW_AUTHOR_DOC_ID AS DOC_ID
								, A.OCCR_DATE
								, A.SW_AUTHOR_SEQ
								, MAX(A.SW_AUTHOR_SEQ) OVER(PARTITION BY A.SW_AUTHOR_DOC_ID ORDER BY A.SW_AUTHOR_DOC_ID) AS MX_WRITER_SEQ
							 FROM SVD_SOFTWARE_WRITER_D A
							WHERE A.SW_AUTHOR_DOC_ID  IN ((SELECT DOC_ID FROM SWLIST))
						  ) A1
					 WHERE A1.SW_AUTHOR_SEQ = A1.MX_WRITER_SEQ
				   ) B
					 ON F.DOC_ID = B.DOC_ID
					 LEFT OUTER JOIN SVD_SOFTWARE_OS_D AS OS
					 ON SWLIST.PARENT_DOC_ID = OS.DOC_ID
				) FF
		 WHERE FF.FILE_DISPLAY = '1'
		 ORDER BY NVL(FF.ISSUE_DATE,FF.RELEASE_DATE)DESC, FF.DOC_ORD DESC
		<choose>
			<when test='softwareCnt != null and softwareCnt != ""'>
			LIMIT ${softwareCnt}
			</when>
			<otherwise>
			LIMIT 2
			</otherwise>
		</choose>
    </select>
    
    
    <select id="retrieveModelWtyInfoList" resultType="com.lge.d2x.domain.support.v1.model.ModelWtyInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ModelWtyInfoRequestVO">
	    SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveModelWtyInfoList */ 					 
		(
			SELECT O.LBR_WTY_PRODUCT_TEXT_CNTS
			FROM SVD_WTY_INFO_M O
			JOIN SVD_WTY_INFO_MODEL_R P
					 ON O.DOC_ID = P.DOC_ID
			WHERE  O.SITE_CODE =  #{siteCode}
			AND    O.COUNTRY_CODE = #{countryCode}
			AND    Nvl(O.USE_FLAG, 'Y') = 'Y'
			AND    O.OPEN_STATE_CODE = 'ONSERVICE'
			AND   (P.CUST_MODEL_CODE = C.SALES_CODE or P.CUST_MODEL_CODE = A.BUYER_MODEL_CODE)
			UNION ALL
			SELECT Q.LBR_WTY_PRODUCT_TEXT_CNTS
			FROM   SVD_WTY_INFO_M Q
			WHERE  Q.SITE_CODE =  #{siteCode}
			AND    Q.COUNTRY_CODE = #{countryCode}
			AND    Nvl(Q.USE_FLAG, 'Y') = 'Y'
			AND    Q.OPEN_STATE_CODE = 'ONSERVICE'
			AND    Q.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
			AND    Q.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
			AND    Q.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
			AND    NOT EXISTS (SELECT 'Y'
								 FROM SVD_WTY_INFO_MODEL_R R
								WHERE R.SITE_CODE =  #{siteCode}
								  AND R.DOC_ID = Q.DOC_ID)
			UNION ALL
			SELECT S.LBR_WTY_PRODUCT_TEXT_CNTS
			FROM   SVD_WTY_INFO_M S
			WHERE  S.SITE_CODE =  #{siteCode}
				   AND S.COUNTRY_CODE = #{countryCode}
				   AND Nvl(S.USE_FLAG, 'Y') = 'Y'
				   AND S.OPEN_STATE_CODE = 'ONSERVICE'
				   AND S.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
				   AND S.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
				   AND NOT EXISTS (SELECT 'Y'
								   FROM   SVD_WTY_INFO_MODEL_R T
								   WHERE  T.SITE_CODE =  #{siteCode}
								   AND T.DOC_ID = S.DOC_ID)
			UNION ALL
			SELECT U.LBR_WTY_PRODUCT_TEXT_CNTS
			FROM   SVD_WTY_INFO_M U
			WHERE  U.SITE_CODE =#{siteCode}
			AND    U.COUNTRY_CODE =#{countryCode}
			AND    Nvl(U.USE_FLAG, 'Y') = 'Y'
			AND    U.OPEN_STATE_CODE = 'ONSERVICE'
			AND    U.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
			AND    NOT EXISTS (SELECT 'Y'
								 FROM  SVD_WTY_INFO_MODEL_R V
								WHERE  V.SITE_CODE =  #{siteCode}
								  AND  V.DOC_ID = U.DOC_ID)
			LIMIT  1
			) AS LBR_WTY_PRD_TEXT
			, (
			SELECT W.PART_WTY_PRODUCT_TEXT_CNTS
			FROM   SVD_WTY_INFO_M W
			JOIN   SVD_WTY_INFO_MODEL_R X
			ON     W.DOC_ID = X.DOC_ID
			WHERE  W.SITE_CODE = #{siteCode}
			AND W.COUNTRY_CODE = #{countryCode}
			AND Nvl(W.USE_FLAG, 'Y') = 'Y'
			AND W.OPEN_STATE_CODE = 'ONSERVICE'
			AND (X.CUST_MODEL_CODE = C.SALES_CODE or X.CUST_MODEL_CODE = A.BUYER_MODEL_CODE)
			UNION ALL
			SELECT Y.PART_WTY_PRODUCT_TEXT_CNTS
			FROM   SVD_WTY_INFO_M Y
			WHERE  Y.SITE_CODE =  #{siteCode}
			AND    Y.COUNTRY_CODE = #{countryCode}
			AND    Nvl(Y.USE_FLAG, 'Y') = 'Y'
			AND    Y.OPEN_STATE_CODE = 'ONSERVICE'
			AND    Y.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
			AND    Y.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
			AND    Y.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
			AND    NOT EXISTS (SELECT 'Y'
								 FROM  SVD_WTY_INFO_MODEL_R Z
								WHERE  Z.SITE_CODE =  #{siteCode}
								  AND  Z.DOC_ID = Y.DOC_ID)
			UNION ALL
			SELECT AA.PART_WTY_PRODUCT_TEXT_CNTS
			FROM   SVD_WTY_INFO_M AA
			WHERE  AA.SITE_CODE =  #{siteCode}
			AND    AA.COUNTRY_CODE = #{countryCode}
			AND    Nvl(AA.USE_FLAG, 'Y') = 'Y'
			AND    AA.OPEN_STATE_CODE = 'ONSERVICE'
			AND    AA.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
			AND    AA.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
			AND    NOT EXISTS (SELECT 'Y'
							 FROM SVD_WTY_INFO_MODEL_R BB
							WHERE BB.SITE_CODE = #{siteCode}
								  AND BB.DOC_ID = AA.DOC_ID)
			UNION ALL
			SELECT CC.PART_WTY_PRODUCT_TEXT_CNTS
			FROM   SVD_WTY_INFO_M CC
			WHERE  CC.SITE_CODE =  #{siteCode}
			AND    CC.COUNTRY_CODE = #{countryCode}
			AND    Nvl(CC.USE_FLAG, 'Y') = 'Y'
			AND    CC.OPEN_STATE_CODE = 'ONSERVICE'
			AND    CC.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
			AND    NOT EXISTS (SELECT 'Y'
								 FROM  SVD_WTY_INFO_MODEL_R DD
								WHERE  DD.SITE_CODE =  #{siteCode}
								  AND  DD.DOC_ID = CC.DOC_ID)
			LIMIT  1
			) AS PART_WTY_PRD_TEXT		   
		,C.MODEL_IMG_PATH AS MODEL_IMAGE_PATH
		,C.SALES_CODE  AS MODEL_URL
		,B.LV1_CATEGORY_CODE AS CS_SUPER_CATEGORY_ID
		,B.LV2_CATEGORY_CODE AS CS_CATEGORY_ID
		,B.LV3_CATEGORY_CODE AS CS_SUB_CATEGORY_ID
		,(SELECT N.USER_FRNDY_PRODUCT_NM 
						FROM DSP_PDP_M N
						WHERE 1=1
						AND N.SITE_CODE = #{siteCode}
						AND N.PDP_ID = (SELECT PDP_ID 
										FROM SVD_CUSTOMER_MODEL_M E
										WHERE 1=1
										AND E.SITE_CODE = #{siteCode}
										AND E.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
									  )
					  	) AS USER_FRIENDLY_NAME
		FROM PDM_PRODUCT_SVC_D A
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID =B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
		JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND C.SITE_CODE =#{siteCode}
		WHERE 1=1
		AND A.USE_FLAG='Y'
		AND B.USE_FLAG='Y'
		AND C.USE_FLAG='Y'
		AND C.SALES_CODE = #{csSalesCode}
    </select>
    
    <select id="retrieveASCInfo" resultType="com.lge.d2x.domain.support.v1.model.AscInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.AscInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveASCInfo */
		       A.ASC_ID
		     , A.ASC_IMG_ADDR
		     , A.CITY_NM
			 , A.ASC_DESC
			 , A.ASC_PET_NM
		  FROM SVD_ASC_M A 
		 WHERE A.ASC_ID       = #{ascId}
		   AND A.COUNTRY_CODE = #{countryCode}
		   AND A.USE_FLAG     = 'Y'
		 LIMIT 1
	</select>

	<select id="retrieveASCList" resultType="com.lge.d2x.domain.support.v1.model.AscInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.AscInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveASCList */
                   A.ASC_ID
                 , A.COUNTRY_CODE
                 ,CASE WHEN (SELECT C.PREFERENCE_VAL
                              FROM COM_SYS_PREFERENCE_D C
                             WHERE C.SITE_CODE = 'CA_EN'
                               AND C.PREFERENCE_CODE = 'CST_ASC_PET_NAME_USE_FLAG'
                               AND C.USE_FLAG = 'Y'
                             UNION ALL
                            SELECT 'N'
                             LIMIT 1) = 'Y' THEN NVL(A.ASC_PET_NM, A.ASC_NM)
                    ELSE A.ASC_NM END AS ASC_NM
                 , A.ASC_LTD_VAL
                 , A.ASC_LNG_VAL
                 , A.ASC_IMG_ADDR
                 , A.COUNTRY_CODE
                 , A.ASC_PET_NM
                 , A.ASC_TEL_NO
                 , A.ASC_FAX_NO
                 , A.ASC_POSTAL_NO
                 , A.ASC_ADDR1
                 , A.ASC_ADDR2
                 , A.ASC_ADDR3
                 , A.ASC_CITY_NM
                 , A.EMAIL_ADDR
                 , A.WEBSITE_URL
                 , A.SHIP_CODE
                 , A.REPAIR_POSS_PRODUCT_CODE
                 , IFNULL(A.EVAL_POINT_NO, 0) AS EVAL_POINT_NO
                 , A.USE_FLAG
                 , A.INQUIRY_FLAG
                 , A.REPAIR_FLAG
                 , A.ASC_LG_SVC_FLAG
                 , A.ASC_PROVINCE_NM
                 , A.CITY_NM
                 , A.ASC_MAP_ADDR
                 , A.WORK_TIME_DESC
                 , A.SVC_WH_DTL_FLAG
                 , A.TEMP_ATTR1
                 , A.TEMP_ATTR2
                 , A.TEMP_ATTR3
                 , A.TEMP_ATTR4
                 , A.APP_NO AS WHATSAPP
        FROM SVD_ASC_M A
        WHERE 1=1
        <if test='ascId != null and ascId != ""'>
            AND A.ASC_ID = #{ascId}
        </if>
        <choose>
            <when test='countryCode != null and countryCode != ""'>
            AND A.COUNTRY_CODE  = #{countryCode}
			AND A.USE_FLAG = 'Y'
            </when>
            <when test="siteCode == 'CAC' or siteCode == 'PA' or siteCode == 'PE'">
            AND A.COUNTRY_CODE IN (SELECT COUNTRY_CODE FROM COM_SVD_PREFERENCE_M
                                         WHERE LOCALE_CODE = #{siteCode}
                                         AND USE_FLAG='Y')
			AND A.USE_FLAG = 'Y'
            </when>
            <otherwise>
            AND A.COUNTRY_CODE IN
            (
                SELECT B.COUNTRY_CODE 
                  FROM COM_SVD_PREFERENCE_M B
                 WHERE B.SITE_CODE  IN ( SELECT  C.SITE_CODE 
                                             FROM COM_SYS_PREFERENCE_D C
                                            WHERE 1=1
                                            AND C.PREFERENCE_VAL = 'Y' 
                                            AND C.USE_FLAG = 'Y' 
                                            AND C.PREFERENCE_CODE = 'GP1_USE_FLAG'
                                         )
            )
            AND A.COUNTRY_CODE NOT IN ( 'US','KR')
            </otherwise>
        </choose>
		<!-- GP1SI-254 START -->
		<if test='viewAllFlag neq "Y"'>
		<![CDATA[
			AND LAST_UPDATE_DATE > DATE_SUB(NOW(), INTERVAL #{intervalHour} HOUR)
		]]>
		</if>
		<!-- GP1SI-254 END -->
	</select>
	
	 
	<select id="retrieveRepairStateList" resultType="com.lge.d2x.domain.support.v1.model.AscInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.AscInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveRepairStateList */
			<choose>
			<when test="siteCode == 'AR' ">
				DISTINCT A.TEMP_ATTR6 AS CODE
					   , A.TEMP_ATTR6 AS VALUE
			</when>
			<when test="siteCode == 'CAC' or siteCode == 'PA' or siteCode == 'PE' ">  
				DISTINCT A.COUNTRY_CODE AS CODE
					   , IFNULL((SELECT P.COUNTRY_NM
								   FROM COM_SVD_PREFERENCE_M P
								  WHERE P.SITE_CODE = #{siteCode}
								    AND P.USE_FLAG = 'Y'
								    AND P.GCM_COUNTRY_CODE = A.COUNTRY_CODE LIMIT 1), A.COUNTRY_CODE) AS VALUE
			</when>
			<otherwise>
				DISTINCT A.ASC_PROVINCE_NM AS CODE
					   , A.ASC_PROVINCE_NM AS VALUE
			</otherwise>
			</choose>
		  FROM SVD_ASC_M A
		 WHERE A.USE_FLAG = 'Y'
		<choose>
		<when test="siteCode == 'CAC' or siteCode == 'PA' or siteCode == 'PE'">
		   AND A.COUNTRY_CODE IN (SELECT B.COUNTRY_CODE FROM COM_SVD_PREFERENCE_M B 
								   WHERE B.SITE_CODE = #{siteCode}
								     AND USE_FLAG='Y')
		</when>
		<when test="siteCode == 'AR' ">
		   AND A.TEMP_ATTR6 IS NOT NULL
		</when>
		<otherwise>
		   AND A.COUNTRY_CODE = #{siteCode}
		   AND A.ASC_PROVINCE_NM IS NOT NULL
		</otherwise>
		</choose>
		 ORDER BY VALUE
	</select>
	
	<select id="retrieveGpWarrantyInformation" resultType="com.lge.d2x.domain.support.v1.model.GpWtyInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpWtyInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpWarrantyInformation */
			 	 T.LBR_WTY_PRODUCT_TEXT_CNTS
		       , T.LBR_WTY_EXCLD_TEXT_CNTS
		       , T.PART_WTY_PRODUCT_TEXT_CNTS
		       , T.PART_WTY_EXCLD_TEXT_CNTS
		       , T.WTY_EXCLD_TEXT_CNTS
		       , T.CARY_LABOR_FEE_TEXT_CNTS
	           , T.ONSITE_LABOR_FEE_TEXT_CNTS
	           , T.GAS_FEE_TEXT_CNTS
			   , T.WTY_PRL_TEXT_CNTS
			   , T.WTY_POLICY_DESC
	           , T.INSTALL_PRICE_TEXT_CNTS
	           , T.INSTALL_POLICY_DESC  			   
		FROM   (SELECT   A.LBR_WTY_PRODUCT_TEXT_CNTS
		               , A.LBR_WTY_EXCLD_TEXT_CNTS
		               , A.PART_WTY_PRODUCT_TEXT_CNTS
		               , A.PART_WTY_EXCLD_TEXT_CNTS
		               , A.WTY_EXCLD_TEXT_CNTS
	                   , A.CARY_LABOR_FEE_TEXT_CNTS
	                   , A.ONSITE_LABOR_FEE_TEXT_CNTS
	                   , A.GAS_FEE_TEXT_CNTS
	                   , A.WTY_PRL_TEXT_CNTS
	                   , A.WTY_POLICY_DESC
	                   , A.INSTALL_PRICE_TEXT_CNTS
	                   , A.INSTALL_POLICY_DESC  			   
		        FROM SVD_WTY_INFO_M A
		        JOIN SVD_WTY_INFO_MODEL_R B
		                 ON A.DOC_ID = B.DOC_ID
		        WHERE  A.SITE_CODE = #{siteCode}
		               AND A.COUNTRY_CODE = #{countryCode}
		               AND Nvl(A.USE_FLAG, 'Y') = 'Y'
		               AND A.OPEN_STATE_CODE = 'ONSERVICE'
		               AND B.CUST_MODEL_CODE = (	
													SELECT DISTINCT C.BUYER_MODEL_CODE 
													FROM PDM_PRODUCT_SVC_D C
													JOIN SVD_PRODUCT_SVC_MODEL_D D ON C.SVD_SKU_ID = D.SVD_SKU_ID AND D.SITE_CODE = #{siteCode}
													WHERE 1=1
													AND (D.SALES_CODE = UPPER(#{modelNum}) OR C.BUYER_MODEL_CODE = UPPER(#{modelNum}))
													AND C.USE_FLAG = 'Y'
													AND D.USE_FLAG = 'Y'
													LIMIT 1
												)
		        UNION ALL
				SELECT   E.LBR_WTY_PRODUCT_TEXT_CNTS
		               , E.LBR_WTY_EXCLD_TEXT_CNTS
		               , E.PART_WTY_PRODUCT_TEXT_CNTS
		               , E.PART_WTY_EXCLD_TEXT_CNTS
		               , E.WTY_EXCLD_TEXT_CNTS
	                   , E.CARY_LABOR_FEE_TEXT_CNTS
	                   , E.ONSITE_LABOR_FEE_TEXT_CNTS
	                   , E.GAS_FEE_TEXT_CNTS
	                   , E.WTY_PRL_TEXT_CNTS
	                   , E.WTY_POLICY_DESC
	                   , E.INSTALL_PRICE_TEXT_CNTS
	                   , E.INSTALL_POLICY_DESC  			   
		        FROM   SVD_WTY_INFO_M E
		        WHERE  E.SITE_CODE = #{siteCode}
		               AND E.COUNTRY_CODE = #{countryCode}
		               AND Nvl(E.USE_FLAG, 'Y') = 'Y'
		               AND E.OPEN_STATE_CODE = 'ONSERVICE'
			           AND E.LV1_CATEGORY_CODE = #{superCategoryId}
			           AND E.LV2_CATEGORY_CODE = #{categoryId}
		               AND E.LV3_CATEGORY_CODE = #{subCategoryId}
		               AND NOT EXISTS (SELECT 'Y'
		                               FROM   SVD_WTY_INFO_MODEL_R F
		                               WHERE  F.SITE_CODE = #{siteCode}
		                               AND    F.DOC_ID = E.DOC_ID)
		        UNION ALL
		        SELECT   G.LBR_WTY_PRODUCT_TEXT_CNTS
		               , G.LBR_WTY_EXCLD_TEXT_CNTS
		               , G.PART_WTY_PRODUCT_TEXT_CNTS
		               , G.PART_WTY_EXCLD_TEXT_CNTS
		               , G.WTY_EXCLD_TEXT_CNTS
	                   , G.CARY_LABOR_FEE_TEXT_CNTS
	                   , G.ONSITE_LABOR_FEE_TEXT_CNTS
	                   , G.GAS_FEE_TEXT_CNTS
	                   , G.WTY_PRL_TEXT_CNTS
	                   , G.WTY_POLICY_DESC
	                   , G.INSTALL_PRICE_TEXT_CNTS
	                   , G.INSTALL_POLICY_DESC  			   
		        FROM   SVD_WTY_INFO_M G
		        WHERE  G.SITE_CODE = #{siteCode}
		               AND G.COUNTRY_CODE = #{countryCode}
		               AND Nvl(G.USE_FLAG, 'Y') = 'Y'
		               AND G.OPEN_STATE_CODE = 'ONSERVICE'
		               AND G.LV1_CATEGORY_CODE = #{superCategoryId}
			           AND G.LV2_CATEGORY_CODE = #{categoryId}
		               AND NOT EXISTS (SELECT 'Y'
		                               FROM   SVD_WTY_INFO_MODEL_R H
		                               WHERE  H.SITE_CODE = #{siteCode}
		                               AND    H.DOC_ID = G.DOC_ID)
		        UNION ALL
		        SELECT 	 I.LBR_WTY_PRODUCT_TEXT_CNTS
		               , I.LBR_WTY_EXCLD_TEXT_CNTS
		               , I.PART_WTY_PRODUCT_TEXT_CNTS
		               , I.PART_WTY_EXCLD_TEXT_CNTS
		               , I.WTY_EXCLD_TEXT_CNTS
	                   , I.CARY_LABOR_FEE_TEXT_CNTS
	                   , I.ONSITE_LABOR_FEE_TEXT_CNTS
	                   , I.GAS_FEE_TEXT_CNTS
	                   , I.WTY_PRL_TEXT_CNTS
	                   , I.WTY_POLICY_DESC
	                   , I.INSTALL_PRICE_TEXT_CNTS
	                   , I.INSTALL_POLICY_DESC  			   
		        FROM   SVD_WTY_INFO_M I
		        WHERE  I.SITE_CODE = #{siteCode}
		               AND I.COUNTRY_CODE = #{countryCode}
		               AND Nvl(I.USE_FLAG, 'Y') = 'Y'
		               AND I.OPEN_STATE_CODE = 'ONSERVICE'
		               AND I.LV1_CATEGORY_CODE = #{superCategoryId}
		               AND NOT EXISTS (SELECT 'Y'
		                               FROM   SVD_WTY_INFO_MODEL_R J
		                               WHERE  J.SITE_CODE = #{siteCode}
		                               AND    J.DOC_ID = I.DOC_ID)
		        ) T
		LIMIT  1
	</select>
	
	 <select id="selectExtendedwtyModelList" parameterType="com.lge.d2x.domain.support.v1.model.ExtendedwtyModelRequestVO" resultType="com.lge.d2x.domain.support.v1.model.ExtendedwtyModelDataVO">
        SELECT DISTINCT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.selectExtendedwtyModelList */
				  T.CODE 
				, T.VALUE
				, T.MODEL_IMG_PATH
				, SUBSTRING_INDEX(T.PRD_TEXT, '|', 1) as LBR_WTY_PRODUCT_TEXT_CNTS
				, SUBSTRING_INDEX(T.PRD_TEXT, '|', -1) as PART_WTY_PRODUCT_TEXT_CNTS
        		, T.LV1_CATEGORY_CODE
			    , T.LV2_CATEGORY_CODE          				
			    , T.LV3_CATEGORY_CODE
				, T.LV1_CATEGORY_NM
			    , T.LV2_CATEGORY_NM
			    , T.LV3_CATEGORY_NM
			    , T.MDMS_PRDGRP_CODE
			    , T.OBU_CODE
			    , T.MOBL_MODEL_FLAG
			    , T.USER_FRNDY_PRODUCT_NM    
		FROM   (SELECT  
					 (CASE WHEN A.OBU_CODE NOT IN 
					   <choose>
					   <when test="obuCodeList != null">
						<foreach item="item" index="index" collection="obuCodeList" open="(" close=")" separator=",">
						 #{item}
						</foreach>
						</when>
						<otherwise>
						('')
						</otherwise>
						</choose>
						THEN  A.SALES_MODEL_CODE ELSE C.SALES_CODE END) AS VALUE
				   , (SELECT F.SITE_CATEGORY_NM AS CATEGORY_NM
						FROM DSP_DISPLAY_CATEGORY_M F
					   WHERE F.SITE_CODE = #{siteCode}
						AND F.CATEGORY_CODE = B.LV1_CATEGORY_CODE
						LIMIT 1
						) AS LV1_CATEGORY_NM
				 , (SELECT CATEGORY_NM
						FROM DSP_DISPLAY_CATEGORY_M G
					   WHERE G.SITE_CODE = #{siteCode}
						AND G.CATEGORY_CODE = B.LV2_CATEGORY_CODE
						LIMIT 1
						) AS LV2_CATEGORY_NM
				 , IF(UPPER(B.LV3_CATEGORY_CODE) = 'OTHERS', 'Others', (SELECT H.SITE_CATEGORY_NM AS CATEGORY_NM
						FROM DSP_DISPLAY_CATEGORY_M H
					   WHERE H.SITE_CODE = #{siteCode}
						AND H.CATEGORY_CODE = B.LV3_CATEGORY_CODE
						LIMIT 1
						)) AS LV3_CATEGORY_NM
				,(SELECT concat(DD.LBR_WTY_PRODUCT_TEXT_CNTS, '|', DD.PART_WTY_PRODUCT_TEXT_CNTS)
					FROM SVD_WTY_INFO_M DD
					JOIN SVD_WTY_INFO_MODEL_R EE
								ON DD.DOC_ID = EE.DOC_ID
					WHERE  DD.SITE_CODE =  #{siteCode}
					AND    DD.COUNTRY_CODE = #{countryCode}
					AND    NVL(DD.USE_FLAG, 'Y') = 'Y'
					AND    DD.OPEN_STATE_CODE = 'ONSERVICE'
					AND   (EE.CUST_MODEL_CODE = C.SALES_CODE or EE.CUST_MODEL_CODE = A.BUYER_MODEL_CODE)
					UNION ALL
					SELECT concat(FF.LBR_WTY_PRODUCT_TEXT_CNTS, '|', FF.PART_WTY_PRODUCT_TEXT_CNTS)
					FROM   SVD_WTY_INFO_M FF
					WHERE  FF.SITE_CODE =  #{siteCode}
					AND    FF.COUNTRY_CODE = #{countryCode}
					AND    NVL(FF.USE_FLAG, 'Y') = 'Y'
					AND    FF.OPEN_STATE_CODE = 'ONSERVICE'
					AND    FF.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
					AND    FF.LV2_CATEGORY_CODE = NVL(NULLIF(B.LV2_CATEGORY_CODE, ''), FF.LV2_CATEGORY_CODE)
					AND    FF.LV3_CATEGORY_CODE = NVL(NULLIF(B.LV3_CATEGORY_CODE, ''), FF.LV3_CATEGORY_CODE)
					AND    NOT EXISTS (SELECT 'Y'
											FROM SVD_WTY_INFO_MODEL_R GG
										WHERE GG.SITE_CODE =  #{siteCode}
											AND GG.DOC_ID = FF.DOC_ID)
					LIMIT  1
					) AS PRD_TEXT
					,(SELECT N.USER_FRNDY_PRODUCT_NM 
					FROM DSP_PDP_M N
					WHERE 1=1
					AND N.SITE_CODE = #{siteCode}
					AND N.PDP_ID = (SELECT PDP_ID 
									FROM SVD_CUSTOMER_MODEL_M E
									WHERE 1=1
									AND E.SITE_CODE = #{siteCode}
									AND E.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
								  )
					) AS USER_FRNDY_PRODUCT_NM
				   , C.SALES_CODE AS CODE
				   , C.SALES_MODEL_FLAG    
				   , C.GSFS_USE_FLAG          
				   , A.OBU_CODE          
				   , C.MODEL_IMG_PATH 
				   , B.LV1_CATEGORY_CODE
				   , B.LV2_CATEGORY_CODE          				
				   , B.LV3_CATEGORY_CODE      				
				   , A.MDMS_PRDGRP_CODE                     
				   , C.MOBL_MODEL_FLAG
			FROM PDM_PRODUCT_SVC_D A
			JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID =B.SVD_SKU_ID 
			JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID 
			WHERE 1=1
			AND A.USE_FLAG = 'Y'
			AND B.USE_FLAG = 'Y'
			AND C.USE_FLAG = 'Y'
			AND B.SITE_CODE = #{siteCode}
			AND C.SITE_CODE = #{siteCode}	 
			AND C.SALES_MODEL_FLAG = 'Y'
			<if test="#{superCategoryId}.NOTEMPTY">
				AND B.LV1_CATEGORY_CODE = #{superCategoryId}		
			</if>
			<if test="#{superCategoryId}.NOTEMPTY">
				AND B.LV2_CATEGORY_CODE = #{categoryId}
			</if>
			<if test="categoryId neq subCategoryId">
				AND B.LV3_CATEGORY_CODE = #{subCategoryId}
			</if>
			<if test="siteCode != null and siteCode == 'IN'">
				AND C.EXT_WTY_USE_FLAG = 'Y'
			</if>
			
			<if test='tabType == "W"'>		               
				   AND EXISTS (SELECT 1
						          FROM SVD_WTY_INFO_M CWI
						         WHERE CWI.SITE_CODE =#{siteCode}
						           AND CWI.COUNTRY_CODE = #{countryCode}
						           AND CWI.OPEN_STATE_CODE = 'ONSERVICE'
						           AND CWI.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
						           AND CWI.USE_FLAG = 'Y') 
			</if>
			<if test="supportOrderByImg==null or supportOrderByImg==''">
			 ORDER BY A.BUYER_MODEL_CODE
			 </if>
		) T
		<if test="modelId != null and modelId != ''">  		
		WHERE T.VALUE = #{modelId}
		</if>
		GROUP  BY T.VALUE
		<if test='supportOrderByImg != null and supportOrderByImg == "Y"'>
				ORDER BY
					CASE WHEN T.MODEL_IMG_PATH IS NULL OR T.MODEL_IMG_PATH = '' THEN 1 ELSE 0 END,
					CASE WHEN T.USER_FRNDY_PRODUCT_NM IS NULL OR T.USER_FRNDY_PRODUCT_NM = '' THEN 1 ELSE 0 END,
					T.CODE
		</if>
		LIMIT #{countPerPage} OFFSET #{offset}
    </select>
    
    
    <select id="selectExtendedwtyModelListCnt" parameterType="com.lge.d2x.domain.support.v1.model.ExtendedwtyModelRequestVO" resultType="Integer">
    SELECT COUNT(1) AS CNT 
    FROM( 
        SELECT DISTINCT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.selectExtendedwtyModelListCnt */
				  T.CODE 
				, T.VALUE
				, T.MODEL_IMG_PATH
        		, T.LBR_WTY_PRODUCT_TEXT_CNTS
        		, T.PART_WTY_PRODUCT_TEXT_CNTS
        		, T.LV1_CATEGORY_CODE
			    , T.LV2_CATEGORY_CODE          				
			    , T.LV3_CATEGORY_CODE
				, T.LV1_CATEGORY_NM
			    , T.LV2_CATEGORY_NM
			    , T.LV3_CATEGORY_NM
			    , T.MDMS_PRDGRP_CODE
			    , T.OBU_CODE
			    , T.MOBL_MODEL_FLAG
			    , T.USER_FRNDY_PRODUCT_NM    
		FROM   (SELECT  
					 (CASE WHEN A.OBU_CODE NOT IN 
					   <choose>
					   <when test="obuCodeList != null">
						<foreach item="item" index="index" collection="obuCodeList" open="(" close=")" separator=",">
						 #{item}
						</foreach>
						</when>
						<otherwise>
						('')
						</otherwise>
						</choose>
						THEN  A.SALES_MODEL_CODE ELSE C.SALES_CODE END) AS VALUE
				   , (SELECT F.SITE_CATEGORY_NM AS CATEGORY_NM
						FROM DSP_DISPLAY_CATEGORY_M F
					   WHERE F.SITE_CODE = #{siteCode}
						AND F.CATEGORY_CODE = B.LV1_CATEGORY_CODE
						LIMIT 1
						) AS LV1_CATEGORY_NM
				 , (SELECT CATEGORY_NM
						FROM DSP_DISPLAY_CATEGORY_M G
					   WHERE G.SITE_CODE = #{siteCode}
						AND G.CATEGORY_CODE = B.LV2_CATEGORY_CODE
						LIMIT 1
						) AS LV2_CATEGORY_NM
				 , IF(UPPER(B.LV3_CATEGORY_CODE) = 'OTHERS', 'Others', (SELECT H.SITE_CATEGORY_NM AS CATEGORY_NM
						FROM DSP_DISPLAY_CATEGORY_M H
					   WHERE H.SITE_CODE = #{siteCode}
						AND H.CATEGORY_CODE = B.LV3_CATEGORY_CODE
						LIMIT 1
						)) AS LV3_CATEGORY_NM
				   , (
					SELECT O.LBR_WTY_PRODUCT_TEXT_CNTS
					FROM SVD_WTY_INFO_M O
					JOIN SVD_WTY_INFO_MODEL_R P
							 ON O.DOC_ID = P.DOC_ID
					WHERE  O.SITE_CODE =  #{siteCode}
					AND    O.COUNTRY_CODE = #{countryCode}
					AND    Nvl(O.USE_FLAG, 'Y') = 'Y'
					AND    O.OPEN_STATE_CODE = 'ONSERVICE'
					AND   (P.CUST_MODEL_CODE = C.SALES_CODE or P.CUST_MODEL_CODE = A.BUYER_MODEL_CODE)
					UNION ALL
					SELECT Q.LBR_WTY_PRODUCT_TEXT_CNTS
					FROM   SVD_WTY_INFO_M Q
					WHERE  Q.SITE_CODE =  #{siteCode}
					AND    Q.COUNTRY_CODE = #{countryCode}
					AND    Nvl(Q.USE_FLAG, 'Y') = 'Y'
					AND    Q.OPEN_STATE_CODE = 'ONSERVICE'
					AND    Q.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
					AND    Q.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					AND    Q.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
					AND    NOT EXISTS (SELECT 'Y'
										 FROM SVD_WTY_INFO_MODEL_R R
										WHERE R.SITE_CODE =  #{siteCode}
										  AND R.DOC_ID = Q.DOC_ID)
					UNION ALL
					SELECT S.LBR_WTY_PRODUCT_TEXT_CNTS
					FROM   SVD_WTY_INFO_M S
					WHERE  S.SITE_CODE =  #{siteCode}
						   AND S.COUNTRY_CODE = #{countryCode}
						   AND Nvl(S.USE_FLAG, 'Y') = 'Y'
						   AND S.OPEN_STATE_CODE = 'ONSERVICE'
						   AND S.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
						   AND S.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
						   AND NOT EXISTS (SELECT 'Y'
										   FROM   SVD_WTY_INFO_MODEL_R T
										   WHERE  T.SITE_CODE =  #{siteCode}
										   AND T.DOC_ID = S.DOC_ID)
					UNION ALL
					SELECT U.LBR_WTY_PRODUCT_TEXT_CNTS
					FROM   SVD_WTY_INFO_M U
					WHERE  U.SITE_CODE =#{siteCode}
					AND    U.COUNTRY_CODE =#{countryCode}
					AND    Nvl(U.USE_FLAG, 'Y') = 'Y'
					AND    U.OPEN_STATE_CODE = 'ONSERVICE'
					AND    U.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
					AND    NOT EXISTS (SELECT 'Y'
										 FROM  SVD_WTY_INFO_MODEL_R V
										WHERE  V.SITE_CODE =  #{siteCode}
										  AND  V.DOC_ID = U.DOC_ID)
					LIMIT  1
					) AS LBR_WTY_PRODUCT_TEXT_CNTS		   
					, (
					SELECT W.PART_WTY_PRODUCT_TEXT_CNTS
					FROM   SVD_WTY_INFO_M W
					JOIN   SVD_WTY_INFO_MODEL_R X
					ON     W.DOC_ID = X.DOC_ID
					WHERE  W.SITE_CODE = #{siteCode}
					AND W.COUNTRY_CODE = #{countryCode}
					AND Nvl(W.USE_FLAG, 'Y') = 'Y'
					AND W.OPEN_STATE_CODE = 'ONSERVICE'
					AND (X.CUST_MODEL_CODE = C.SALES_CODE or X.CUST_MODEL_CODE = A.BUYER_MODEL_CODE)
					UNION ALL
					SELECT Y.PART_WTY_PRODUCT_TEXT_CNTS
					FROM   SVD_WTY_INFO_M Y
					WHERE  Y.SITE_CODE =  #{siteCode}
					AND    Y.COUNTRY_CODE = #{countryCode}
					AND    Nvl(Y.USE_FLAG, 'Y') = 'Y'
					AND    Y.OPEN_STATE_CODE = 'ONSERVICE'
					AND    Y.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
					AND    Y.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					AND    Y.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
					AND    NOT EXISTS (SELECT 'Y'
										 FROM  SVD_WTY_INFO_MODEL_R Z
										WHERE  Z.SITE_CODE =  #{siteCode}
										  AND  Z.DOC_ID = Y.DOC_ID)
					UNION ALL
					SELECT D.PART_WTY_PRODUCT_TEXT_CNTS
					FROM   SVD_WTY_INFO_M D
					WHERE  D.SITE_CODE =  #{siteCode}
					AND    D.COUNTRY_CODE = #{countryCode}
					AND    Nvl(D.USE_FLAG, 'Y') = 'Y'
					AND    D.OPEN_STATE_CODE = 'ONSERVICE'
					AND    D.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
					AND    D.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					AND    NOT EXISTS (SELECT 'Y'
									 FROM SVD_WTY_INFO_MODEL_R BB
									WHERE BB.SITE_CODE = #{siteCode}
										  AND BB.DOC_ID = D.DOC_ID)
					UNION ALL
					SELECT E.PART_WTY_PRODUCT_TEXT_CNTS
					FROM   SVD_WTY_INFO_M E
					WHERE  E.SITE_CODE =  #{siteCode}
					AND    E.COUNTRY_CODE = #{countryCode}
					AND    Nvl(E.USE_FLAG, 'Y') = 'Y'
					AND    E.OPEN_STATE_CODE = 'ONSERVICE'
					AND    E.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
					AND    NOT EXISTS (SELECT 'Y'
										 FROM  SVD_WTY_INFO_MODEL_R DD
										WHERE  DD.SITE_CODE =  #{siteCode}
										  AND  DD.DOC_ID = E.DOC_ID)
					LIMIT  1
					) AS PART_WTY_PRODUCT_TEXT_CNTS	   
					,(SELECT N.USER_FRNDY_PRODUCT_NM 
					FROM DSP_PDP_M N
					WHERE 1=1
					AND N.SITE_CODE = #{siteCode}
					AND N.PDP_ID = (SELECT PDP_ID 
									FROM SVD_CUSTOMER_MODEL_M E
									WHERE 1=1
									AND E.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
									AND E.SITE_CODE = #{siteCode}
								  )
					) AS USER_FRNDY_PRODUCT_NM
				   , C.SALES_CODE AS CODE
				   , C.SALES_MODEL_FLAG    
				   , C.GSFS_USE_FLAG          
				   , A.OBU_CODE          
				   , C.MODEL_IMG_PATH 
				   , B.LV1_CATEGORY_CODE
				   , B.LV2_CATEGORY_CODE          				
				   , B.LV3_CATEGORY_CODE      				
				   , A.MDMS_PRDGRP_CODE                     
				   , C.MOBL_MODEL_FLAG
			FROM PDM_PRODUCT_SVC_D A
			JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID =B.SVD_SKU_ID 
			JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID 
			WHERE 1=1
			AND A.USE_FLAG = 'Y'
			AND B.USE_FLAG = 'Y'
			AND C.USE_FLAG = 'Y'
			AND B.SITE_CODE = #{siteCode}
			AND C.SITE_CODE = #{siteCode}	 
			AND C.SALES_MODEL_FLAG = 'Y'
			<if test="#{superCategoryId}.NOTEMPTY">
				AND B.LV1_CATEGORY_CODE = #{superCategoryId}		
			</if>
			<if test="#{superCategoryId}.NOTEMPTY">
				AND B.LV2_CATEGORY_CODE = #{categoryId}
			</if>
			<if test="categoryId neq subCategoryId">
				AND B.LV3_CATEGORY_CODE = #{subCategoryId}
			</if>
			<if test="siteCode != null and siteCode == 'IN'">
				AND C.EXT_WTY_USE_FLAG = 'Y'
			</if>
			
			<if test='tabType == "W"'>		               
				   AND EXISTS (SELECT 1
						          FROM SVD_WTY_INFO_M CWI
						         WHERE CWI.SITE_CODE =#{siteCode}
						           AND CWI.COUNTRY_CODE = #{countryCode}
						           AND CWI.OPEN_STATE_CODE = 'ONSERVICE'
						           AND CWI.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
						           AND CWI.USE_FLAG = 'Y') 
			</if>
		) T
		<if test="modelId != null and modelId != ''">  		
		WHERE T.VALUE = #{modelId}
		</if>
		GROUP  BY T.VALUE
	) Z 
    </select>
    
	<select id="retrieveGenuineWtyConutryList" resultType="com.lge.d2x.domain.support.v1.model.ProductValidInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductValidInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGenuineWtyConutryList */
			DISTINCT A.COUNTRY_NM
		FROM  SVD_GENUINE_WTY_VALID_D A
		WHERE A.SITE_CODE = #{siteCode}
		AND   A.USE_FLAG = 'Y'
		ORDER BY A.COUNTRY_NM
    </select>
    
	<select id="retrieveGenuineWtyProductCombo" resultType="com.lge.d2x.domain.support.v1.model.ProductValidInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductValidInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGenuineWtyProductCombo */ 
			CONCAT(#{countryNm}, '^', A.PRODUCT_NM) AS CODE, 
		       A.PRODUCT_NM AS VALUE
		  FROM SVD_GENUINE_WTY_VALID_PRODUCT_D A
		 WHERE A.SITE_CODE = #{siteCode}
		   AND A.LANG_CODE = #{langCode}
		   and A.COUNTRY_NM = #{countryNm}
		   AND A.USE_FLAG = 'Y'
		 ORDER BY A.PRODUCT_NM
    </select>
    
	<select id="retrieveGenuineWtyProductList" resultType="com.lge.d2x.domain.support.v1.model.ProductValidInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductValidInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGenuineWtyProductList */
			   A.PRODUCT_NM,
			   A.SALES_MODEL_CODE,
		<if test='hasSerialNumber eq "Y"'>
			   A.SERIAL_NO AS SALES_MODEL_SUFFIX_CODE,
		</if>
		<if test='hasSerialNumber eq "N"'>
			A.SALES_MODEL_SUFFIX_CODE,
		</if>  
			   A.IMG_FILE_NM,
			   A.IMG_ALT_TEXT_CNTS,
			   A.MODEL_DESC
		  FROM SVD_GENUINE_WTY_VALID_PRODUCT_D A
		 WHERE A.USE_FLAG = 'Y'
		   AND A.LANG_CODE = #{langCode}
		   AND A.SITE_CODE = #{siteCode}
		<if test='hasCountry eq "Y"'>
		   AND A.COUNTY_NM = #{countryNm}
		</if>   
		<if test="productNm != null and productNm != ''">
			AND A.PRODUCT_NM = #{productNm}
			LIMIT 1
		</if>
    </select>
    
	<select id="retrieveValidationModel" resultType="com.lge.d2x.domain.support.v1.model.ProductValidInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.ProductValidInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveValidationModel */
			   A.SALES_MODEL_CODE,
		<if test='hasSerialNumber eq "Y"'>
		       A.SERIAL_NO ,
		</if>
		<if test='hasSerialNumber eq "N"'>
			   A.SALES_MODEL_SUFFIX_CODE,
		</if>
			   A.BU_NM,
			   A.DEPT_NM,
			   A.CLS_NM
		  FROM SVD_GENUINE_WTY_VALID_D A
		 WHERE A.USE_FLAG = 'Y'
		   AND A.SITE_CODE = #{siteCode}
		   <if test="model != null and model != ''">
		   AND A.SALES_MODEL_CODE = #{model}
		   </if>
		<if test='hasSerialNumber eq "Y"'>
		   AND A.SERIAL_NO = #{suffix}
		</if>
		<if test='hasSerialNumber eq "N"'>
		   AND A.SALES_MODEL_SUFFIX_CODE = #{suffix}
		</if> 
		<if test='hasCountry eq "Y"'>
		   AND A.COUNTRY_NM = #{countryNm}
		</if>
		LIMIT 1
	</select>

	<select id="retrieveGpGsfsCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpGsfsCategoryList */
		 			   DISTINCT F.LV2_CATEGORY_CODE
			         , F.CATEGORY_NM
			         , F.CS_DSP_SEQ
		             , IFNULL(F.STICKY_IMG_URL, '') AS STICKY_IMG_URL
                     , IFNULL(F.STICKY_HOVER_ICON_URL, '') AS STICKY_HOVER_ICON_URL
					 <if test ="pageFlag == 'locateRepairCenter'">
                     , SERVICE_TYPE
					 </if>
		FROM (SELECT
				   DISTINCT C.LV2_CATEGORY_CODE
		         , D.SITE_CATEGORY_NM AS CATEGORY_NM
                 , C.LV3_CATEGORY_CODE
                 , A.BUYER_MODEL_CODE
		         , D.CS_DSP_SEQ
		         , K.STICKY_IMG_URL
                 , K.STICKY_HOVER_ICON_URL   
                 <if test ="pageFlag == 'locateRepairCenter'">
					, CASE WHEN C.LV2_CATEGORY_CODE IN
					<if test='mobileCategoryList != null'>
						<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
						    #{item}
						</foreach>	
					</if>
					<if test="mobileCategoryList == null">('')</if> THEN 'MC'
					ELSE (SELECT IF(COUNT(*) = 0, 'CI', 'IH')
							   FROM SVD_SERVICE_TYPE_R B
							  WHERE B.SITE_CODE   = #{localeCode}
								AND B.ONSITE_USE_FLAG   = 'Y'
								AND B.USE_FLAG          = 'Y'
								AND B.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
								AND (B.LV3_CATEGORY_CODE IS NULL OR B.LV3_CATEGORY_CODE = ''))
				   END SERVICE_TYPE
				</if>
		FROM   PDM_PRODUCT_SVC_D A
	   JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
	   JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND B.SITE_CODE = C.SITE_CODE
	   JOIN DSP_DISPLAY_CATEGORY_M D
          ON  D.SITE_CODE = C.SITE_CODE
          AND D.CATEGORY_CODE = C.LV2_CATEGORY_CODE
       JOIN DSP_DISPLAY_CATEGORY_D K
          ON  K.SITE_CODE = C.SITE_CODE
          AND K.CATEGORY_CODE = D.CATEGORY_CODE
          AND K.SHOP_CODE = 'D2C'
		<choose>
			<when test='b2bUseFlag eq "Y"'>
				<choose>
				<when test='b2bDivisionUseFlag eq "Y"'>
					AND D.BIZ_TYPE_CODE = #{divisionBizType}
				</when>
				<otherwise>
					AND D.BIZ_TYPE_CODE IN ('B2C','B2B') 
				</otherwise>
				</choose>
			</when>
			<otherwise>
				AND D.BIZ_TYPE_CODE = 'B2C'
			</otherwise>
		</choose>
		<if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
        JOIN SVD_CUSTOMER_MODEL_M E
          ON E.SITE_CODE = B.SITE_CODE
         AND E.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
         AND E.CREATION_DATE >= #{euEcoCategoryDt}
         AND E.USE_FLAG = 'Y'
        </if>
		WHERE  1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		AND D.SUPP_USE_FLAG = 'Y'
		<if test="localeCode != null and localeCode != ''">
		  AND B.SITE_CODE = #{localeCode}
		  AND C.SITE_CODE = #{localeCode}
		</if>
		
	<if test="superCategoryId != null and superCategoryId != ''">
		AND C.LV1_CATEGORY_CODE = #{superCategoryId}
	</if>
	<if test="pageFlag == 'repair' and localeCode == 'BR'">
		<choose>
		<when test='onlyMobileFlag eq "Y"'>
			<if test='mobileCategoryList != null'>
				AND C.LV2_CATEGORY_CODE IN
				<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
				    #{item}
				</foreach>
			</if>
		</when>
		<otherwise>
		<if test='mobileCategoryList != null'>
			AND C.LV2_CATEGORY_CODE NOT IN
			<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
			    #{item}
			</foreach>	
		</if>
		</otherwise>
		</choose>
	</if>
	<if test='ecoCategoryFlag == "Y" and categoryIdList != null'>
		<foreach collection="categoryIdList" item="c" index="idx" open="AND C.LV2_CATEGORY_CODE IN (" separator="," close=")">
			#{c}
		</foreach>
	</if>
	<if test="objetModelList != null and objetModelList.length > 0">
		AND A.SALES_CODE IN
		<foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
			#{objetModels}
		</foreach>
	</if>
	) F  
	WHERE 1=1
	<if test="pageFlag == 'locateRepairCenter' and hiddenCategoryList != null">
		AND F.LV2_CATEGORY_CODE NOT IN
		<foreach item="item" index="index" collection="hiddenCategoryList" open="(" close=")" separator=",">
		    #{item}
		</foreach>
	</if>
	<if test='ecoCategoryFlag != "Y"'>
	AND  EXISTS (SELECT 1
				   FROM SVD_SERVICE_TYPE_R G
				   WHERE G.SITE_CODE = #{localeCode}
				   AND G.USE_FLAG = 'Y'
				   AND G.LV2_CATEGORY_CODE = F.LV2_CATEGORY_CODE
				   AND (G.LV3_CATEGORY_CODE IS NULL OR G.LV3_CATEGORY_CODE = '')
				   AND (G.CUST_MODEL_CODE IS NULL OR G.CUST_MODEL_CODE = '')
					
					<if test="localeCode == 'BR'">
						<if test="pageType == 'common'">
							AND (G.ONSITE_USE_FLAG = 'Y' OR G.MC_COLLECT_FLAG = 'Y')
						</if>
						<if test="pageType == 'install'">
							AND G.INSTALL_USE_FLAG = 'Y'
						</if>
						<if test="pageType == 'carryin'">
							AND G.CARY_USE_FLAG = 'Y'
						</if>
					</if>
					<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode =='MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
						<if test="pageType == 'common'"> 
							AND G.ONSITE_USE_FLAG = 'Y' 
						</if> 
						<if test="pageType == 'install'"> 
							AND G.INSTALL_USE_FLAG = 'Y' 
						</if> 
					</if>
					<!--	LGCOMCO-200		-->
					<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
						<![CDATA[
							AND G.MAINTENANCE_USE_FLAG = 'Y'
						]]>
					</if>
				   UNION ALL
				   SELECT 1
				   FROM SVD_SERVICE_TYPE_R H
				   WHERE H.SITE_CODE = #{localeCode}
				   AND H.USE_FLAG = 'Y'
				   AND H.LV2_CATEGORY_CODE = F.LV2_CATEGORY_CODE
				   AND H.LV3_CATEGORY_CODE = F.LV3_CATEGORY_CODE
				   AND (H.CUST_MODEL_CODE IS NULL OR H.CUST_MODEL_CODE = '')
				   <if test="localeCode == 'BR'">
						<if test="pageType == 'common'">
							AND (H.ONSITE_USE_FLAG = 'Y' OR H.MC_COLLECT_FLAG = 'Y')
						</if>

						<if test="pageType == 'install'">
							AND H.INSTALL_USE_FLAG = 'Y'
						</if>
						<if test="pageType == 'carryin'">
							AND H.CARY_USE_FLAG = 'Y'
						</if>
					</if>
					<!-- GP1SI-428 -->
					<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
						<if test="pageType == 'common'"> 
							AND H.ONSITE_USE_FLAG = 'Y' 
						</if> 
						<if test="pageType == 'install'"> 
							AND H.INSTALL_USE_FLAG = 'Y' 
						</if> 
					</if>
					<!--	LGCOMCO-200		-->
					<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
						<![CDATA[
							AND H.MAINTENANCE_USE_FLAG = 'Y'
						]]>
					</if>
				   UNION ALL
				   SELECT 1
				   FROM SVD_SERVICE_TYPE_R I
				   WHERE I.SITE_CODE = #{localeCode}
				   AND I.USE_FLAG = 'Y'
				   AND I.LV2_CATEGORY_CODE = F.LV2_CATEGORY_CODE
				   AND I.LV3_CATEGORY_CODE = F.LV3_CATEGORY_CODE
				   AND I.CUST_MODEL_CODE = F.BUYER_MODEL_CODE
					<if test="localeCode == 'BR'">
						<if test="pageType == 'common'">
							AND (I.ONSITE_USE_FLAG = 'Y' OR I.MC_COLLECT_FLAG = 'Y')
						</if>
						<if test="pageType == 'install'">
							AND I.INSTALL_USE_FLAG = 'Y'
						</if>
						<if test="pageType == 'carryin'">
							AND I.CARY_USE_FLAG = 'Y'
						</if>
					</if>
					<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
						<if test="pageType == 'common'"> 
							AND I.ONSITE_USE_FLAG = 'Y' 
						</if> 
						<if test="pageType == 'install'"> 
							AND I.INSTALL_USE_FLAG = 'Y' 
						</if> 
					</if>
					<!--	LGCOMCO-200		-->
					<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
						<![CDATA[
							AND I.MAINTENANCE_USE_FLAG = 'Y'
						]]>
					</if>
				   UNION ALL
				   SELECT 1
				   FROM SVD_SERVICE_TYPE_R J
				   WHERE J.SITE_CODE = #{localeCode}
				   AND J.USE_FLAG = 'Y'
				   AND J.LV2_CATEGORY_CODE = F.LV2_CATEGORY_CODE
				   AND (J.LV3_CATEGORY_CODE IS NULL OR J.LV3_CATEGORY_CODE = '')
				   AND J.CUST_MODEL_CODE = F.BUYER_MODEL_CODE
					<if test="localeCode == 'BR'">
						<if test="pageType == 'common'">
							AND (J.ONSITE_USE_FLAG = 'Y' OR J.MC_COLLECT_FLAG = 'Y')
						</if>
						<if test="pageType == 'install'">
							AND J.INSTALL_USE_FLAG = 'Y'
						</if>
						<if test="pageType == 'carryin'">
							AND J.CARY_USE_FLAG = 'Y'
						</if>
					</if>
					<!-- GP1SI-428 -->
					<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
						<if test="pageType == 'common'"> 
							AND J.ONSITE_USE_FLAG = 'Y' 
						</if> 
						<if test="pageType == 'install'"> 
							AND J.INSTALL_USE_FLAG = 'Y' 
						</if> 
					</if>
					<!--	LGCOMCO-200		-->
					<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
						<![CDATA[
							AND J.MAINTENANCE_USE_FLAG = 'Y'
						]]>
					</if>
				 )
	</if>
		ORDER BY F.CS_DSP_SEQ
	</select>
	
	
	
	<select id="retrieveGpGsfsSubCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpGsfsSubCategoryList */
		     DISTINCT
			 T.LV3_CATEGORY_CODE
			,T.CATEGORY_NM
			,T.GSFS_USE_FLAG
			,T.STICKY_IMG_URL
			,T.STICKY_HOVER_ICON_URL
			,T.CS_DSP_SEQ
			FROM(
				SELECT C.LV3_CATEGORY_CODE           ,
					   D.SITE_CATEGORY_NM AS CATEGORY_NM,
					   B.SALES_MODEL_FLAG ,
					  <if test='countryCode neq "CA"'>
						'Y' AS SUPP_USE_FLAG,
					  </if>
					  <if test='countryCode eq "CA"'>
						IFNULL((
						SELECT G.SUPP_USE_FLAG FROM DSP_DISPLAY_CATEGORY_M G
						WHERE G.CATEGORY_CODE = C.LV3_CATEGORY_CODE
						  AND G.SITE_CODE = #{localeCode}),'Y') AS SUPP_USE_FLAG,
					  </if>
					   B.GSFS_USE_FLAG,
					   A.BUYER_MODEL_CODE,
					   E.STICKY_IMG_URL,
					   E.STICKY_HOVER_ICON_URL,
					   D.CS_DSP_SEQ
				FROM   PDM_PRODUCT_SVC_D A
				JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
				JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID 
				JOIN DSP_DISPLAY_CATEGORY_M D
				  ON  D.SITE_CODE = C.SITE_CODE
				  AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
				JOIN DSP_DISPLAY_CATEGORY_D E
				  ON  E.SITE_CODE = C.SITE_CODE
				  AND E.CATEGORY_CODE = D.CATEGORY_CODE
				  AND E.SHOP_CODE = 'D2C'
				<if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
					JOIN SVD_CUSTOMER_MODEL_M F
					  ON F.SITE_CODE = B.SITE_CODE
					 AND F.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
					 AND F.CREATION_DATE <![CDATA[>=]]> #{euEcoCategoryDt}
					 AND F.USE_FLAG = 'Y'
				</if>
				AND A.USE_FLAG = 'Y'
				AND B.USE_FLAG = 'Y'
				AND B.GSFS_USE_FLAG = 'Y'
				AND B.SITE_CODE = #{localeCode}
				AND C.USE_FLAG = 'Y'
				AND C.SITE_CODE = #{localeCode}
				<if test="superCategoryId != null and superCategoryId != ''">
				AND C.LV1_CATEGORY_CODE = #{superCategoryId}
				</if>
				AND C.LV2_CATEGORY_CODE = #{categoryId}
				AND C.LV3_CATEGORY_CODE IS NOT NULL
				AND C.LV3_CATEGORY_CODE <![CDATA[<>]]> 'Others'
				AND C.LV3_CATEGORY_CODE <![CDATA[<>]]> ''
				<if test='ecoCategoryFlag == "Y" and categoryIdList!=null '>
					<foreach collection="categoryIdList" item="c" index="idx" open="AND C.LV2_CATEGORY_CODE IN (" separator="," close=")">
						#{c}
					</foreach>
				</if>
				UNION ALL
				SELECT 'Others' AS LV3_CATEGORY_CODE,
					   IFNULL((SELECT L.MSG_CNTS 
							   FROM COM_MESSAGE_M L 
							   WHERE L.MSG_CODE = 'common-symptom-others' 
							   AND L.DSP_SVC_MSG_SP_CODE = 'SVC'
							   AND L.SHOP_CODE = 'D2C' 
							   AND L.LOCALE_CODE = #{javaLocaleCode} 
							   AND L.USE_FLAG = 'Y'), 'Others') AS CATEGORY_NM ,
					   I.SALES_MODEL_FLAG ,
					  <if test='countryCode neq "CA"'>
						'Y' AS SUPP_USE_FLAG,
					  </if>
					  <if test='countryCode eq "CA"'>
						IFNULL((
						SELECT M.SUPP_USE_FLAG FROM DSP_DISPLAY_CATEGORY_M M
						WHERE M.CATEGORY_CODE = J.LV3_CATEGORY_CODE
						  AND M.SITE_CODE = #{localeCode}),'Y') AS SUPP_USE_FLAG,
					  </if>
					   I.GSFS_USE_FLAG,
					   H.BUYER_MODEL_CODE,
					   '/content/dam/lge/common/common-icon/support-icons/icon-other_normal.svg' AS STICKY_IMG_URL,
					   '/content/dam/lge/common/common-icon/support-icons/icon-other_active.svg' AS STICKY_HOVER_ICON_URL,
					   9999 AS CS_DSP_SEQ
				FROM   PDM_PRODUCT_SVC_D H
				JOIN SVD_PRODUCT_SVC_MODEL_D I ON H.SVD_SKU_ID = I.SVD_SKU_ID
				JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R J ON H.SVD_SKU_ID = J.SVD_SKU_ID 
				<if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
				JOIN SVD_CUSTOMER_MODEL_M K
				  ON K.SITE_CODE = #{localeCode}
				 AND K.CUST_MODEL_CODE = H.BUYER_MODEL_CODE
				 AND K.CREATION_DATE <![CDATA[>=]]> #{euEcoCategoryDt}
				 AND K.USE_FLAG = 'Y'
				</if>
				AND H.USE_FLAG = 'Y'
				AND I.USE_FLAG = 'Y'
				AND J.USE_FLAG = 'Y'
				AND I.GSFS_USE_FLAG = 'Y'
				AND I.SITE_CODE = #{localeCode}
				AND J.SITE_CODE = #{localeCode}
				<if test="superCategoryId != null and superCategoryId != ''">
				AND J.LV1_CATEGORY_CODE = #{superCategoryId}
				</if>
				AND J.LV2_CATEGORY_CODE = #{categoryId}
				AND J.LV3_CATEGORY_CODE = 'Others'
				<if test='ecoCategoryFlag == "Y" and categoryIdList!=null '>
					<foreach collection="categoryIdList" item="c" index="idx" open="AND I.LV2_CATEGORY_CODE IN (" separator="," close=")">
						#{c}
					</foreach>
				</if>
				<if test="objetModelList != null and objetModelList.length > 0">
					AND A.SALES_CODE IN
					<foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
						#{objetModels}
					</foreach>
				</if>
			)T 				
		 WHERE 1=1
                <if test='countryCode eq "CA"'>
				AND    T.SUPP_USE_FLAG = 'Y'
				</if>
				<!-- GP1SI-428 -->
				<if test='localeCode != "BR" and (localeCode != "EG_EN" and localeCode != "EG_AR" and localeCode != "MX" and localeCode != "CO" and localeCode != "CL" and localeCode != "CAC" and localeCode != "PA" and localeCode != "PE"  and localeCode != "EC") and pageFlag == "repair" and ecoCategoryFlag != "Y"'>
				AND  EXISTS (SELECT 1
			                   FROM SVD_SERVICE_TYPE_R N
			                   WHERE N.SITE_CODE = #{localeCode}
			                   AND N.USE_FLAG = 'Y'
			                   AND N.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (N.LV3_CATEGORY_CODE IS NULL OR N.LV3_CATEGORY_CODE = '')
			                   AND (N.CUST_MODEL_CODE IS NULL OR N.CUST_MODEL_CODE = '')
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R O
			                   WHERE O.SITE_CODE = #{localeCode}
			                   AND O.USE_FLAG = 'Y'
			                   AND O.LV2_CATEGORY_CODE = #{categoryId}
			                   AND O.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND (O.CUST_MODEL_CODE IS NULL OR O.CUST_MODEL_CODE = '')
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R P
			                   WHERE P.SITE_CODE = #{localeCode}
			                   AND P.USE_FLAG = 'Y'
			                   AND P.LV2_CATEGORY_CODE = #{categoryId}
			                   AND P.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND P.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R Q
			                   WHERE Q.SITE_CODE = #{localeCode}
			                   AND Q.USE_FLAG = 'Y'
			                   AND Q.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (Q.LV3_CATEGORY_CODE IS NULL OR Q.LV3_CATEGORY_CODE = '')
			                   AND Q.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                 )
			</if>
			<if test="localeCode == 'BR' and pageFlag == 'repair'">
				AND  EXISTS (SELECT 1
			                   FROM SVD_SERVICE_TYPE_R R
			                   WHERE R.SITE_CODE = #{localeCode}
			                   AND R.USE_FLAG = 'Y'
			                   AND R.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (R.LV3_CATEGORY_CODE IS NULL OR R.LV3_CATEGORY_CODE = '')
			                   AND (R.CUST_MODEL_CODE IS NULL OR R.CUST_MODEL_CODE = '')
			                   AND (R.ONSITE_USE_FLAG = 'Y' OR R.MC_COLLECT_FLAG = 'Y')
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R S
			                   WHERE S.SITE_CODE = #{localeCode}
			                   AND S.USE_FLAG = 'Y'
			                   AND S.LV2_CATEGORY_CODE = #{categoryId}
			                   AND S.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND (S.CUST_MODEL_CODE IS NULL OR S.CUST_MODEL_CODE = '')
			                   AND (S.ONSITE_USE_FLAG = 'Y' OR S.MC_COLLECT_FLAG = 'Y')
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R U
			                   WHERE U.SITE_CODE = #{localeCode}
			                   AND U.USE_FLAG = 'Y'
			                   AND U.LV2_CATEGORY_CODE = #{categoryId}
			                   AND U.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND U.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND (U.ONSITE_USE_FLAG = 'Y' OR U.MC_COLLECT_FLAG = 'Y')
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R V
			                   WHERE V.SITE_CODE = #{localeCode}
			                   AND V.USE_FLAG = 'Y'
			                   AND V.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (V.LV3_CATEGORY_CODE IS NULL OR V.LV3_CATEGORY_CODE = '')
			                   AND V.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND (V.ONSITE_USE_FLAG = 'Y' OR V.MC_COLLECT_FLAG = 'Y')
			                 )
			</if>
			<if test="localeCode == 'BR' and pageFlag == 'carryin'">
				AND  EXISTS (SELECT 1
			                   FROM SVD_SERVICE_TYPE_R W
			                   WHERE W.SITE_CODE = #{localeCode}
			                   AND W.USE_FLAG = 'Y'
			                   AND W.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (W.LV3_CATEGORY_CODE IS NULL OR W.LV3_CATEGORY_CODE = '')
			                   AND (W.CUST_MODEL_CODE IS NULL OR W.CUST_MODEL_CODE = '')
			                   AND W.CARY_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R X
			                   WHERE X.SITE_CODE = #{localeCode}
			                   AND X.USE_FLAG = 'Y'
			                   AND X.LV2_CATEGORY_CODE = #{categoryId}
			                   AND X.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND (X.CUST_MODEL_CODE IS NULL OR X.CUST_MODEL_CODE = '')
			                   AND X.CARY_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R Y
			                   WHERE Y.SITE_CODE = #{localeCode}
			                   AND Y.USE_FLAG = 'Y'
			                   AND Y.LV2_CATEGORY_CODE = #{categoryId}
			                   AND Y.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND Y.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND Y.CARY_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R Z
			                   WHERE Z.SITE_CODE = #{localeCode}
			                   AND Z.USE_FLAG = 'Y'
			                   AND Z.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (Z.LV3_CATEGORY_CODE IS NULL OR Z.LV3_CATEGORY_CODE = '')
			                   AND Z.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND Z.CARY_USE_FLAG = 'Y'
			                 )
			</if>
			<!-- GP1SI-428 -->
			<if test="(localeCode == 'BR' or localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC') and pageFlag == 'install'">
				AND  EXISTS (SELECT 1
			                   FROM SVD_SERVICE_TYPE_R AA
			                   WHERE AA.SITE_CODE = #{localeCode}
			                   AND AA.USE_FLAG = 'Y'
			                   AND AA.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (AA.LV3_CATEGORY_CODE IS NULL OR AA.LV3_CATEGORY_CODE = '')
			                   AND (AA.CUST_MODEL_CODE IS NULL OR AA.CUST_MODEL_CODE = '')
			                   AND AA.INSTALL_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R BB
			                   WHERE BB.SITE_CODE = #{localeCode}
			                   AND BB.USE_FLAG = 'Y'
			                   AND BB.LV2_CATEGORY_CODE = #{categoryId}
			                   AND BB.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND (BB.CUST_MODEL_CODE IS NULL OR BB.CUST_MODEL_CODE = '')
			                   AND BB.INSTALL_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R CC
			                   WHERE CC.SITE_CODE = #{localeCode}
			                   AND CC.USE_FLAG = 'Y'
			                   AND CC.LV2_CATEGORY_CODE = #{categoryId}
			                   AND CC.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND CC.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND CC.INSTALL_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R DD
			                   WHERE DD.SITE_CODE = #{localeCode}
			                   AND DD.USE_FLAG = 'Y'
			                   AND DD.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (DD.LV3_CATEGORY_CODE IS NULL OR DD.LV3_CATEGORY_CODE = '')
			                   AND DD.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND DD.INSTALL_USE_FLAG = 'Y'
			                 )
			</if>
			<!-- GP1SI-428 -->
			<if test="(localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC') and pageFlag == 'repair'">
				AND  EXISTS (SELECT 1
			                   FROM SVD_SERVICE_TYPE_R EE
			                   WHERE EE.SITE_CODE = #{localeCode}
			                   AND EE.USE_FLAG = 'Y'
			                   AND EE.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (EE.LV3_CATEGORY_CODE IS NULL OR EE.LV3_CATEGORY_CODE = '')
			                   AND (EE.CUST_MODEL_CODE IS NULL OR EE.CUST_MODEL_CODE = '')
			                   AND EE.ONSITE_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R FF
			                   WHERE FF.SITE_CODE = #{localeCode}
			                   AND FF.USE_FLAG = 'Y'
			                   AND FF.LV2_CATEGORY_CODE = #{categoryId}
			                   AND FF.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND (FF.CUST_MODEL_CODE IS NULL OR FF.CUST_MODEL_CODE = '')
			                   AND FF.ONSITE_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R GG
			                   WHERE GG.SITE_CODE = #{localeCode}
			                   AND GG.USE_FLAG = 'Y'
			                   AND GG.LV2_CATEGORY_CODE = #{categoryId}
			                   AND GG.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND GG.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND GG.ONSITE_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R HH
			                   WHERE HH.SITE_CODE = #{localeCode}
			                   AND HH.USE_FLAG = 'Y'
			                   AND HH.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (HH.LV3_CATEGORY_CODE IS NULL OR HH.LV3_CATEGORY_CODE = '')
			                   AND HH.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND HH.ONSITE_USE_FLAG = 'Y'
			                 )
			</if>
			<!-- LGCOMCO-200 -->
			<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
				AND  EXISTS (SELECT 1
			                   FROM SVD_SERVICE_TYPE_R II
			                   WHERE II.SITE_CODE = #{localeCode}
			                   AND II.USE_FLAG = 'Y'
			                   AND II.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (II.LV3_CATEGORY_CODE IS NULL OR II.LV3_CATEGORY_CODE = '')
			                   AND (II.CUST_MODEL_CODE IS NULL OR II.CUST_MODEL_CODE = '')
			                   AND II.MAINTENANCE_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R JJ
			                   WHERE JJ.SITE_CODE = #{localeCode}
			                   AND JJ.USE_FLAG = 'Y'
			                   AND JJ.LV2_CATEGORY_CODE = #{categoryId}
			                   AND JJ.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND (JJ.CUST_MODEL_CODE IS NULL OR JJ.CUST_MODEL_CODE = '')
			                   AND JJ.MAINTENANCE_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R KK
			                   WHERE KK.SITE_CODE = #{localeCode}
			                   AND KK.USE_FLAG = 'Y'
			                   AND KK.LV2_CATEGORY_CODE = #{categoryId}
			                   AND KK.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
			                   AND KK.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND KK.MAINTENANCE_USE_FLAG = 'Y'
			                   UNION ALL
			                   SELECT 1
			                   FROM SVD_SERVICE_TYPE_R LL
			                   WHERE LL.SITE_CODE = #{localeCode}
			                   AND LL.USE_FLAG = 'Y'
			                   AND LL.LV2_CATEGORY_CODE = #{categoryId}
			                   AND (LL.LV3_CATEGORY_CODE IS NULL OR LL.LV3_CATEGORY_CODE = '')
			                   AND LL.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
			                   AND LL.MAINTENANCE_USE_FLAG = 'Y'
			                 )
			</if>
	<if test="pageFlag == 'call' and callSubCateId != null and callSubCateId != ''">
		AND T.LV3_CATEGORY_CODE = #{callSubCateId}
	</if>
    ORDER BY T.CS_DSP_SEQ
    </select>
    
    
    
    
    <select id="retrievePreferenceCountryList" resultType="com.lge.d2x.domain.support.v1.model.PreferenceCountryResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.PreferenceCountryRequestVO">
	    SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrievePreferenceCountryList */
	    		A.COUNTRY_CODE
		      ,(CASE IFNULL(B.MSG_CNTS, '')
			    WHEN '' 
			    THEN A.COUNTRY_NM 
			    ELSE B.MSG_CNTS
			    END ) COUNTRY_NAME
		FROM COM_SVD_PREFERENCE_M A
		LEFT OUTER JOIN COM_MESSAGE_M B ON B.MSG_CODE  =
		<choose>
			<when test="type == 'ext'">
				CONCAT('acc-common-country-name-', A.COUNTRY_CODE)
				AND B.LOCALE_CODE = #{localeCode}
			</when>
			<when test="type == 'com'">
				CONCAT('CS_COMMON_COUNTRY_NAME_', A.COUNTRY_CODE)
				AND B.LOCALE_CODE = #{localeCode}
			</when>
			<otherwise>
				CONCAT('emp_country_name_', A.COUNTRY_CODE)
				AND B.LOCALE_CODE = #{localeCode}
			</otherwise>
		</choose>
		WHERE  A.SITE_CODE = #{siteCode}
		AND    A.USE_FLAG = 'Y'
		<if test='csType eq "r"'>
				AND    A.REPAIR_REQUEST_USE_FLAG = 'Y'
		</if>
		<if test='csType eq "c"'>
				AND    A.REQUESTA_USE_FLAG = 'Y'
		</if>
		<if test='type == "ext" or addJavaLocaleFlag == "Y" or siteCode eq "UZ_RU" or siteCode eq "UZ" '>
			AND	   (B.LOCALE_CODE  IS NULL OR B.LOCALE_CODE  = #{localeCode})
		</if>
		
		ORDER BY B.MSG_CNTS
	</select>
	
	
	<select id="retrievePreferenceGpCountryList" resultType="com.lge.d2x.domain.support.v1.model.PreferenceCountryResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.PreferenceCountryRequestVO">
		SELECT A.GCM_COUNTRY_CODE AS CODE,
		<choose>
			<when test='siteCode eq "RU"'>
				(CASE WHEN A.GCM_COUNTRY_CODE = #{localeCode}
			 		THEN '1' WHEN A.GCM_COUNTRY_CODE != #{localeCode}
					THEN '2' ELSE '1' END
				)  AS ruOrder,
			</when>
			<when test='siteCode eq "AE" or siteCode eq "AE_AR"'>
				(CASE WHEN A.GCM_COUNTRY_CODE = 'AE'
			 		THEN '1' WHEN A.GCM_COUNTRY_CODE != 'AE'
					THEN '2' ELSE '1' END
				)  AS aeOrder,
			</when>
		</choose>
		A.COUNTRY_NM     AS VALUE
		FROM COM_SVD_PREFERENCE_M A
		WHERE A.SITE_CODE	= #{siteCode}
		  AND A.USE_FLAG   	= 'Y'
		<choose>
			<when test='siteCode eq "CAC" or siteCode eq "PA" or siteCode eq "EC"'>
			 	AND A.COUNTRY_CODE NOT IN ('CB')
			 			  ORDER BY VALUE
			</when>
			<when test='siteCode eq "HK" or siteCode eq "HK_EN"'>
			 	UNION ALL
				SELECT 'MO' AS CODE
				     , 'Macau' AS VALUE
				FROM DUAL
			</when>
			<when test='siteCode eq "RU"'>
				ORDER BY ruOrder, value
			</when>
			<when test='siteCode eq "AE" or siteCode eq "AE_AR"'>
				ORDER BY aeOrder, value
			</when>
		</choose>
	</select>
		
		
		
	
	<select id="retrieveGpCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpCategoryList */
			   DISTINCT C.LV2_CATEGORY_CODE
	         , D.SITE_CATEGORY_NM AS CATEGORY_NM
             , IFNULL(E.STICKY_IMG_URL, '') AS STICKY_IMG_URL
             , IFNULL(E.STICKY_HOVER_ICON_URL, '') AS STICKY_HOVER_ICON_URL   
		FROM   PDM_PRODUCT_SVC_D A
		JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID
		JOIN DSP_DISPLAY_CATEGORY_M D
		  ON  D.SITE_CODE = C.SITE_CODE
		  AND D.CATEGORY_CODE = C.LV2_CATEGORY_CODE
		JOIN DSP_DISPLAY_CATEGORY_D E
		  ON  E.SITE_CODE = C.SITE_CODE
		  AND E.CATEGORY_CODE = D.CATEGORY_CODE
		  AND E.SHOP_CODE = 'D2C'
		<choose>
		<when test='b2bUseFlag eq "Y"'>
			<choose>
			<when test='b2bDivisionUseFlag eq "Y"'>
				AND D.BIZ_TYPE_CODE = #{divisionBizType}
			</when>
			<otherwise>
				AND D.BIZ_TYPE_CODE IN ('B2C','B2B') 
			</otherwise>
			</choose>
		</when>
		<otherwise>
			AND D.BIZ_TYPE_CODE = 'B2C'
		</otherwise>
		</choose>
		<if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
        JOIN SVD_CUSTOMER_MODEL_M EE
          ON EE.SITE_CODE = B.SITE_CODE
         AND EE.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
         AND EE.CREATION_DATE >= #{euEcoCategoryDt}
         AND EE.USE_FLAG = 'Y'
        </if>
		WHERE  1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		AND D.SUPP_USE_FLAG = 'Y'
		<if test="localeCode != null and localeCode != ''">
		  AND B.SITE_CODE = #{localeCode}
		  AND C.SITE_CODE = #{localeCode}
		</if>
		<if test="superCategoryId != null and superCategoryId != ''">
			AND C.LV1_CATEGORY_CODE = #{superCategoryId}
		</if>

		<if test='ecoCategoryFlag == "Y" and categoryIdList!=null'>
			<if test='categoryIdList.size()>0'>
			<foreach collection="categoryIdList" item="c" index="idx" open="AND C.LV2_CATEGORY_CODE IN (" separator="," close=")">
			    #{c}
			</foreach>
			</if>
		</if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND A.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>
		<if test='ecoCategoryFlag != "Y"'>
		
		AND  EXISTS (SELECT 1
					   FROM SVD_SERVICE_TYPE_R G
					   WHERE G.SITE_CODE = #{localeCode}
					   AND G.USE_FLAG = 'Y'
					   AND G.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
					   AND (G.LV3_CATEGORY_CODE IS NULL OR G.LV3_CATEGORY_CODE = '')
					   AND (G.CUST_MODEL_CODE IS NULL OR G.CUST_MODEL_CODE = '')
						<if test="localeCode == 'BR'">
							<if test="pageType == 'common'">
								AND (G.ONSITE_USE_FLAG = 'Y' OR G.MC_COLLECT_FLAG = 'Y')
							</if>
							<if test="pageType == 'install'">
								AND G.INSTALL_USE_FLAG = 'Y'
							</if>
							<if test="pageType == 'carryin'">
								AND G.CARY_USE_FLAG = 'Y'
							</if>
						</if>
						<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode =='MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
							<if test="pageType == 'common'"> 
								AND G.ONSITE_USE_FLAG = 'Y' 
							</if> 
							<if test="pageType == 'install'"> 
								AND G.INSTALL_USE_FLAG = 'Y' 
							</if> 
						</if>
	                   UNION ALL
					   SELECT 1
					   FROM SVD_SERVICE_TYPE_R H
					   WHERE H.SITE_CODE = #{localeCode}
					   AND H.USE_FLAG = 'Y'
					   AND H.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
					   AND H.LV3_CATEGORY_CODE = C.LV3_CATEGORY_CODE
					   AND (H.CUST_MODEL_CODE IS NULL OR H.CUST_MODEL_CODE = '')
					   <if test="localeCode == 'BR'">
							<if test="pageType == 'common'">
								AND (H.ONSITE_USE_FLAG = 'Y' OR H.MC_COLLECT_FLAG = 'Y')
							</if>

							<if test="pageType == 'install'">
								AND H.INSTALL_USE_FLAG = 'Y'
							</if>
							<if test="pageType == 'carryin'">
								AND H.CARY_USE_FLAG = 'Y'
							</if>
						</if>
						<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
							<if test="pageType == 'common'"> 
								AND H.ONSITE_USE_FLAG = 'Y' 
							</if> 
							<if test="pageType == 'install'"> 
								AND H.INSTALL_USE_FLAG = 'Y' 
							</if> 
						</if>
	                   UNION ALL
					   SELECT 1
					   FROM SVD_SERVICE_TYPE_R I
					   WHERE I.SITE_CODE = #{localeCode}
					   AND I.USE_FLAG = 'Y'
					   AND I.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
					   AND I.LV3_CATEGORY_CODE = C.LV3_CATEGORY_CODE
					   AND I.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
						<if test="localeCode == 'BR'">
							<if test="pageType == 'common'">
								AND (I.ONSITE_USE_FLAG = 'Y' OR I.MC_COLLECT_FLAG = 'Y')
							</if>
							<if test="pageType == 'install'">
								AND I.INSTALL_USE_FLAG = 'Y'
							</if>
							<if test="pageType == 'carryin'">
								AND I.CARY_USE_FLAG = 'Y'
							</if>
						</if>
						<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
							<if test="pageType == 'common'"> 
								AND I.ONSITE_USE_FLAG = 'Y' 
							</if> 
							<if test="pageType == 'install'"> 
								AND I.INSTALL_USE_FLAG = 'Y' 
							</if> 
						</if>
					   UNION ALL
					   SELECT 1
					   FROM SVD_SERVICE_TYPE_R J
					   WHERE J.SITE_CODE = #{localeCode}
					   AND J.USE_FLAG = 'Y'
					   AND J.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
					   AND (J.LV3_CATEGORY_CODE IS NULL OR J.LV3_CATEGORY_CODE = '')
					   AND J.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
						<if test="localeCode == 'BR'">
							<if test="pageType == 'common'">
								AND (J.ONSITE_USE_FLAG = 'Y' OR J.MC_COLLECT_FLAG = 'Y')
							</if>
							<if test="pageType == 'install'">
								AND J.INSTALL_USE_FLAG = 'Y'
							</if>
							<if test="pageType == 'carryin'">
								AND J.CARY_USE_FLAG = 'Y'
							</if>
						</if>
						<!-- GP1SI-428 -->
						<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
							<if test="pageType == 'common'"> 
								AND J.ONSITE_USE_FLAG = 'Y' 
							</if> 
							<if test="pageType == 'install'"> 
								AND J.INSTALL_USE_FLAG = 'Y' 
							</if> 
						</if>
	                 )
	    </if>
	</select>
	
	<select id="retrieveCustomerProductList" resultType="com.lge.d2x.domain.support.v1.model.GpModelDetailResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpModelDetailRequestVO">
	SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveCustomerProductList */
		DISTINCT C.SVD_SKU_ID                              			
		  ,      C.SALES_CODE                                      
		  , 	 C.SITE_CODE
		  ,      A.MDMS_PRDGRP_CODE                  
		  ,     (CASE WHEN A.OBU_CODE NOT IN 
               <choose>
               <when test="obuCodeList != null and obuCodeList.size()>0">
               	<foreach item="item" index="index" collection="obuCodeList" open="(" close=")" separator=",">
		   	     #{item}
		        </foreach>
		        </when>
		        <otherwise>
		        ('')
		        </otherwise>
		        </choose>      
		        THEN  A.SALES_MODEL_CODE ELSE C.SALES_CODE END)  AS CUST_MODEL_CODE
		  ,      C.MOBL_MODEL_FLAG                                 
		  ,      C.MODEL_IMG_PATH       
		  ,	 	 C.GSFS_USE_FLAG
		  ,      B.LV1_CATEGORY_CODE                                
		  ,      B.LV2_CATEGORY_CODE                                       
		  ,      B.LV3_CATEGORY_CODE
		  , 	(SELECT CATEGORY_NM
				FROM DSP_DISPLAY_CATEGORY_M
				WHERE 1=1
				AND SITE_CODE = #{siteCode}
				AND CATEGORY_CODE = B.LV1_CATEGORY_CODE
				AND CATEGORY_LV_NO = 1
				LIMIT 1
				) AS LV1_CATEGORY_NM 
		  , 	(SELECT CATEGORY_NM
				FROM DSP_DISPLAY_CATEGORY_M
				WHERE 1=1
				AND SITE_CODE = #{siteCode}
				AND CATEGORY_CODE = B.LV2_CATEGORY_CODE
				AND CATEGORY_LV_NO = 2
				LIMIT 1
				) AS LV2_CATEGORY_NM 
		  , 	(SELECT CATEGORY_NM
				FROM DSP_DISPLAY_CATEGORY_M
				WHERE 1=1
				AND SITE_CODE = #{siteCode}
				AND CATEGORY_CODE = B.LV3_CATEGORY_CODE
				AND CATEGORY_LV_NO = 3
				LIMIT 1
				) AS LV3_CATEGORY_NM 
		  ,		H.USER_FRNDY_PRODUCT_NM
		  ,		H.PDP_ID
		  ,		H.PRODUCT_STATE_CODE
		FROM PDM_PRODUCT_SVC_D A
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID = B.SVD_SKU_ID  AND B.SITE_CODE = #{siteCode}
		JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode}
		LEFT OUTER JOIN ( SELECT F.PDP_ID 
						 , F.USER_FRNDY_PRODUCT_NM 
						 , G.PRODUCT_STATE_CODE 
					FROM DSP_PDP_M F 
					JOIN DSP_PDP_D G 
					ON F.PDP_ID = G.PDP_ID 
					AND G.SHOP_CODE = 'D2C' 
					AND G.SITE_CODE = F.SITE_CODE
					WHERE 1=1
					AND F.SITE_CODE = #{siteCode}
					) H ON H.PDP_ID =(SELECT PDP_ID FROM SVD_CUSTOMER_MODEL_M WHERE 1=1 AND CUST_MODEL_CODE= A.BUYER_MODEL_CODE AND SITE_CODE = #{siteCode})
		where 1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		<if test="modelNum != null and modelNum != ''">
				AND (C.SALES_CODE = UPPER(#{modelNum}) OR A.BUYER_MODEL_CODE = UPPER(#{modelNum}))
		</if>
		<if test="siteCode eq 'IN'">
			AND C.EXT_WTY_USE_FLAG = 'Y'
		</if>
		<choose>
			<when test="siteCode eq 'CA_EN' or siteCode eq 'CA_FR'">
				<choose>
					<when test="editMyProductYn != null">
					AND    B.SITE_CODE = #{selectedLocaleCode}
					AND    C.SITE_CODE = #{selectedLocaleCode}
					</when>
					<otherwise>
					AND    B.SITE_CODE in('CA_EN', 'CA_FR')
					AND    C.SITE_CODE in('CA_EN', 'CA_FR')
					</otherwise>
				</choose>
			</when>
			<when test="siteCode eq 'CH_DE' or siteCode eq 'CH_FR'">
				<choose>
					<when test="editMyProductYn != null">
					AND    B.SITE_CODE = #{selectedLocaleCode}
					AND    C.SITE_CODE = #{selectedLocaleCode}
					</when>
					<otherwise>
					AND    B.SITE_CODE in('CH_DE', 'CH_FR')
					AND    C.SITE_CODE in('CH_DE', 'CH_FR')
					</otherwise>
				</choose>
			</when>
			<when test="siteCode eq 'AFRICA' or siteCode eq 'AFRICA_FR'">
				<choose>
					<when test="editMyProductYn != null">
					AND    B.SITE_CODE = #{selectedLocaleCode}
					AND    C.SITE_CODE = #{selectedLocaleCode}
					</when>
					<otherwise>
					AND    B.SITE_CODE in('AFRICA', 'AFRICA_FR')
					AND    C.SITE_CODE in('AFRICA', 'AFRICA_FR')
					</otherwise>
				</choose>
			</when>
			<when test="siteCode eq 'UZ' or siteCode eq 'UZ_RU'">
				<choose>
					<when test="editMyProductYn != null">
					AND    B.SITE_CODE = #{selectedLocaleCode}
					AND    C.SITE_CODE = #{selectedLocaleCode}
					</when>
					<otherwise>
					AND    B.SITE_CODE in('UZ', 'UZ_RU')
					AND    C.SITE_CODE in('UZ', 'UZ_RU')
					</otherwise>
				</choose>
			</when>
			<otherwise>
				AND    B.SITE_CODE = #{siteCode}
				AND    C.SITE_CODE = #{siteCode}
			</otherwise>
		</choose>
		<if test="mode eq 'index'">
			LIMIT 2
		</if>
	</select>
	
	
	<select id="retrieveSvdPurchasePlaceMList" resultType="com.lge.d2x.domain.support.v1.model.PurchasePlaceInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.PurchasePlaceInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSvdPurchasePlaceMList */
			 CORP_CODE AS subsidiaryCode
			,SVD_CUST_CODE AS customerCode
			,FIRSTNAME AS customerName
			,USE_FLAG AS useYn
		FROM SVD_PURCHASE_PLACE_M
		WHERE 1=1
		AND CORP_CODE = #{corpCode}
		AND USE_FLAG = 'Y'
		ORDER BY FIRSTNAME
	</select>
	
	<select id = "retrieveBasicPromotionWtyExpireList" parameterType="com.lge.d2x.domain.support.v1.model.BasicPromotionWtyExpireRequestVO" resultType="com.lge.d2x.domain.support.v1.model.BasicPromotionWtyExpireResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveBasicPromotionWtyExpireList */
			 A.PRODUCT_REG_NO
			,A.CORP_CODE
			,A.SALES_MODEL_CODE
			,A.SERIAL_NO
			,A.DEFAULT_WTY_BEGIN_DD_VAL
			,A.DEFAULT_WTY_END_DD_VAL
			,A.PROMOTION_WTY_BEGIN_DD_VAL
			,A.PROMOTION_WTY_END_DD_VAL
			,A.WTY_EXPR_DD_VAL
			,A.EXT_WTY_NCNT
			,A.SITE_CODE
			,A.CUST_ID
		FROM SVD_BASIC_PROMOTION_WTY_EXPIRE_D A
		WHERE 1=1
		AND A.SITE_CODE = #{siteCode}
		AND A.CUST_ID = #{customerNo}
		AND A.PRODUCT_REG_NO = #{seqNo}
	</select>	
	
	<select id="retrievePdmProductList" parameterType="com.lge.d2x.domain.support.v1.model.PdmProductInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.PdmProductInfoResponseVO">
	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrievePdmProductList */
		 SALES_MODEL_CODE 
		,LV1_PRODUCT_CODE 
		,LV2_PRODUCT_CODE 
		,LV3_PRODUCT_CODE 
	FROM PDM_PRODUCT_M A 
	JOIN PDM_PRODUCT_D B
	WHERE 1=1
	AND A.SALES_MODEL_CODE = #{salesModelCode}
	AND A.USE_FLAG = 'Y'
	AND A.SKU_ID = B.SKU_ID
	AND B.LOCALE_CODE = #{javaLocaleCode}
	AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
	</select>
	
	<select id="retrieveGpRepairServiceTypeInfo" parameterType="com.lge.d2x.domain.support.v1.model.GpServiceTypeInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.GpServiceTypeInfoResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpRepairServiceTypeInfo */
		 		  D.SHIPIN_USE_FLAG
		 		, D.CARY_USE_FLAG AS CARRYIN_USE_FLAG
				, D.INSTALL_USE_FLAG AS INSTALLATION_USE_FLAG
				, D.ONSITE_USE_FLAG
				, D.VIP_USE_FLAG
				, D.GUIDE_USE_FLAG
				, D.MAINTENANCE_USE_FLAG
		  		, D.MC_COLLECT_FLAG
		  FROM (
		    SELECT A.SHIPIN_USE_FLAG
			 	 , A.CARY_USE_FLAG
				 , A.INSTALL_USE_FLAG
				 , A.ONSITE_USE_FLAG
				 , A.VIP_USE_FLAG
				 , A.GUIDE_USE_FLAG
				 , A.MAINTENANCE_USE_FLAG
		  		 , A.MC_COLLECT_FLAG
				 , '1' AS ORDER_NO
			  FROM SVD_SERVICE_TYPE_R A
		     WHERE UPPER(A.CUST_MODEL_CODE) = UPPER(#{custModelCode})
		       AND A.USE_FLAG = 'Y'
		       AND A.SITE_CODE = #{siteCode}
		    UNION
		    SELECT B.SHIPIN_USE_FLAG
			     , B.CARY_USE_FLAG
				 , B.INSTALL_USE_FLAG
				 , B.ONSITE_USE_FLAG
				 , B.VIP_USE_FLAG
				 , B.GUIDE_USE_FLAG
				 , B.MAINTENANCE_USE_FLAG
		  		 , B.MC_COLLECT_FLAG
				 , '2' AS ORDER_NO
			  FROM SVD_SERVICE_TYPE_R B
		     WHERE B.LV3_CATEGORY_CODE = #{lv3CategoryCode}
		       AND B.USE_FLAG = 'Y'
		       AND B.SITE_CODE =  #{siteCode}
		       AND (B.CUST_MODEL_CODE IS NULL OR B.CUST_MODEL_CODE = '')
		       AND (B.LV3_CATEGORY_CODE <![CDATA[<>]]> '' AND B.LV3_CATEGORY_CODE = #{lv3CategoryCode})
		    UNION
		    SELECT C.SHIPIN_USE_FLAG
			     , C.CARY_USE_FLAG
				 , C.INSTALL_USE_FLAG
				 , C.ONSITE_USE_FLAG
				 , C.VIP_USE_FLAG
				 , C.GUIDE_USE_FLAG
				 , C.MAINTENANCE_USE_FLAG
		  		 , C.MC_COLLECT_FLAG
				 , '3' AS ORDER_NO
			   FROM SVD_SERVICE_TYPE_R C
		     WHERE C.LV2_CATEGORY_CODE = #{lv2CategoryCode}
		       AND (C.LV3_CATEGORY_CODE IS NULL OR C.LV3_CATEGORY_CODE = '')
		       AND (C.CUST_MODEL_CODE IS NULL OR C.CUST_MODEL_CODE = '')
		       AND C.USE_FLAG = 'Y'
		       AND C.SITE_CODE = #{siteCode}
			) D
		ORDER BY D.ORDER_NO
		LIMIT 1
	</select>
	
	<select id="retrieveGameAppList" parameterType="com.lge.d2x.domain.support.v1.model.GameAppInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.GameAppInfoResponseVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGameAppList */
				 A.TVGAME_TITLE
		       , A.THUMBNAIL_URL
		       , A.TVGAME_DESC
		       , A.TVGAME_FILE_SIZE
		       , A.TVGAME_DOWNLOAD_URL
		       , A.ALT_TEXT_CNTS
		       , B.CS_CUST_MODEL_CODE
			FROM SVD_TVGAME_APP_M A 
			JOIN SVD_TVGAME_MODEL_LIST_D B
			  ON (A.TV_GAMEAPP_ID = B.TV_GAME_MODEL_LIST_ID)
		   WHERE A.USE_FLAG = 'Y'
			 AND B.CS_CUST_MODEL_CODE = (SELECT DISTINCT C.BUYER_MODEL_CODE 
													FROM PDM_PRODUCT_SVC_D C
													JOIN SVD_PRODUCT_SVC_MODEL_D D ON C.SVD_SKU_ID = D.SVD_SKU_ID AND D.SITE_CODE = #{siteCode}
													WHERE 1=1
													AND (D.SALES_CODE = UPPER(#{custModelCode}) OR C.BUYER_MODEL_CODE = UPPER(#{custModelCode}))
													AND C.USE_FLAG = 'Y'
													AND D.USE_FLAG = 'Y'
													LIMIT 1
										)
			 AND A.SITE_CODE = #{siteCode}
    </select>
    
	<select id="retrieveSymptomList" parameterType="com.lge.d2x.domain.support.v1.model.SympTomInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.SympTomInfoResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSymptomList */
			A.SYMPTOM_CODE
		  , A.SYMPTOM_NM
		  , A.CORP_CODE
		  , A.LANG_CODE
		  , A.MDMS_PRDGRP_CODE
		  , A.TRANSLATION_STATE_CODE
		  , A.LGEAI_FLAG
		  , A.CONSUMER_FLAG
		  , A.DSP_FLAG
		FROM SVD_GSCS_SYMP_M A
		WHERE 1=1
		AND A.USE_FLAG          = 'Y'
		AND A.CONSUMER_FLAG     = 'Y'
		AND A.DSP_FLAG    		= 'Y'
		<if test="corpCode != null and corpCode != ''">
			AND A.CORP_CODE  = #{corpCode}
		</if>
		<if test="langCode != null and langCode != ''">
			AND A.LANG_CODE    = #{langCode}
		</if>
		<if test="mdmsPrdgrpCode != null and mdmsPrdgrpCode != ''">
			AND A.MDMS_PRDGRP_CODE  = #{mdmsPrdgrpCode}
		</if>
		<if test="mode == 'getName'">
			AND A.SYMPTOM_CODE 		= #{issueTopic}
		</if>
		ORDER BY SYMPTOM_NM
	</select>
    
	<select id="retrieveSymptomList3Depth" parameterType="com.lge.d2x.domain.support.v1.model.SympTomInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.SympTomInfoResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSymptomList3Depth */
            SYMPTOM_CODE AS CODE
          , SYMPTOM_NM AS NAME
          , D.SYMPTOM_CODE
          , D.SYMPTOM_NM
         FROM SVD_SYMP_1DEPTH_D A
        WHERE A.CORP_CODE         = #{corporationCode}
          AND A.LANGUAGE_CODE     = #{languageCode}
          AND A.USE_FLAG          = 'Y'
          AND A.MDMS_PRDGRP_CODE  = #{csMdmsProductCode}
          AND A.CONSUMER_FLAG     = 'Y'
          AND A.DSP_FLAG          = 'Y'
        <if test="mode == 'getName'">
          AND A.SYMPTOM_CODE      = #{issueTopic}
        </if>
        ORDER BY SYMPTOM_NM
    </select>
	
	<select id="retrieveSubSymptomList" parameterType="com.lge.d2x.domain.support.v1.model.SympTomInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.SympTomInfoResponseVO">
		SELECT	/* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSubSymptomList */
			 A.CORP_CODE
			,A.LANG_CODE
			,A.SYMPTOM_CODE
			,A.MDMS_PRDGRP_CODE
			,A.SUB_SYMPTOM_CODE
			,A.SUB_SYMPTOM_NM
			,A.REL_EXPRESSION_TEXT_CNTS
			,A.TRANSLATION_STATE_CODE
			,A.ARTICLE_FLAG
			,A.SYMPTOM_TYPE_CODE
			,A.CONSUMER_FLAG
	  	FROM  SVD_GSCS_SYMP_D A
		WHERE 1=1
		AND A.USE_FLAG          = 'Y'
		AND A.CONSUMER_FLAG     = 'Y'
	    AND A.DSP_FLAG		    = 'Y'
		<if test="corpCode != null and corpCode != ''">
			AND A.CORP_CODE    = #{corpCode}
		</if>
		<if test="langCode != null and langCode != ''">
			AND A.LANG_CODE    = #{langCode}
		</if>
		<if test="issueTopic != null and issueTopic != ''">
			AND A.SYMPTOM_CODE = #{issueTopic}
		</if>
		<if test="mdmsPrdgrpCode != null and mdmsPrdgrpCode != ''">
			AND A.MDMS_PRDGRP_CODE = #{mdmsPrdgrpCode}
		</if>
	    <if test="mode == 'getName'">
			AND A.SUB_SYMPTOM_CODE 		= #{issueSubtopic}
		</if>
	</select>
    
	<select id="retrieveSubSymptomList3Depth" parameterType="com.lge.d2x.domain.support.v1.model.SympTomInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.SympTomInfoResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSubSymptomList3Depth */
            D.SUB_SYMPTOM_CODE    AS CODE
          , D.SUB_SYMPTOM_NM    AS NAME
		  , D.SUB_SYMPTOM_CODE
          , D.SUB_SYMPTOM_NM
          FROM  SVD_SYMP_3DEPTH_D D
         WHERE D.CORP_CODE  = #{corporationCode}
           AND D.LANGUAGE_CODE     = #{languageCode}
           AND D.USE_FLAG          = 'Y'
           AND D.SYMPTOM_CODE      = #{issueTopic}
           AND D.MDMS_PRDGRP_CODE  = #{csMdmsProductCode}
           AND D.CONSUMER_FLAG     = 'Y'
           AND D.DSP_FLAG          = 'Y'
        <if test="mode == 'getName'">
            AND A.SUB_SYMPTOM_CODE     = #{issueSubtopic}
        </if>
    </select>
	
	<select id="retrieveSubCategoryBR" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSubCategoryBR */ 
			DISTINCT
			       P.LV3_CATEGORY_CODE AS CS_SUB_CATEGORY_ID
			     , P.CATEGORY_NM AS CS_SUB_CATEGORY_NAME
			     , P.LV3_CATEGORY_CODE AS CODE
			     , P.CATEGORY_NM AS VALUE
			     , P.CS_DSP_SEQ AS CS_SUB_DISPLAY_ORDER_NO
			     , P.GSFS_USE_FLAG AS GSFS_FLAG
	          	 <if test="pageFlag eq 'locateRepairCenter'">
			     , (CASE WHEN (P.SERVICE_TYPE_CNT = 0 AND P.SUB_SERVICE_TYPE != 'MC') THEN P.CATE_SERVICE_TYPE
	                     ELSE P.SUB_SERVICE_TYPE END) AS SERVICE_TYPE
	             </if>
			FROM (
				<if test="pageFlag eq 'locateRepairCenter'">
				  SELECT O.LV3_CATEGORY_CODE
			           , O.CATEGORY_NM
			           , O.CS_DSP_SEQ
			           , O.SALES_MODEL_FLAG
			           , O.SUPP_USE_FLAG
			           , O.GSFS_USE_FLAG
			           , (SELECT COUNT(1)
					        FROM SVD_SERVICE_TYPE_R Q
					       WHERE Q.SITE_CODE = #{localeCode}
					 	     AND Q.USE_FLAG = 'Y'
					 	     AND Q.LV2_CATEGORY_CODE = #{categoryId}
					 	     AND Q.LV3_CATEGORY_CODE = O.LV3_CATEGORY_CODE) AS SERVICE_TYPE_CNT
		               , CASE WHEN #{categoryId} IN 
									<if test="mobileCategoryList != null and mobileCategoryList.size()>0">
										<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
										    #{item}
										</foreach>
									</if>
									<if test="mobileCategoryList == null or mobileCategoryList.size()==0 ">('')</if>
									 THEN 'MC'
		                      ELSE (SELECT CASE WHEN COUNT(*) = 0 THEN 'CI' ELSE 'IH' END
							          FROM SVD_SERVICE_TYPE_R R
							         WHERE R.SITE_CODE = #{localeCode}
								       AND R.ONSITE_USE_FLAG = 'Y'
								       AND R.USE_FLAG = 'Y'
								       AND R.LV2_CATEGORY_CODE = #{categoryId}
								       AND (R.LV3_CATEGORY_CODE IS NULL OR R.LV3_CATEGORY_CODE = '')) 
			              END CATE_SERVICE_TYPE
		               , CASE WHEN #{categoryId} IN
									<if test="mobileCategoryList != null and mobileCategoryList.size()>0">
										<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
										    #{item}
										</foreach>
									</if>
									<if test="mobileCategoryList == null or mobileCategoryList.size()==0">('')</if> THEN 'MC'
		                      ELSE (SELECT CASE 
		                      				WHEN COUNT(*) = 0 THEN 'CI' 
		                      				ELSE 'IH' END
							          FROM SVD_SERVICE_TYPE_R S
							         WHERE S.SITE_CODE = #{localeCode}
								       AND S.ONSITE_USE_FLAG = 'Y'
								       AND S.USE_FLAG = 'Y'
								       AND S.LV2_CATEGORY_CODE = #{categoryId}
								       AND S.LV3_CATEGORY_CODE = O.LV3_CATEGORY_CODE) 
			              END SUB_SERVICE_TYPE
			FROM
			(
			</if>
				SELECT 
					 C.LV3_CATEGORY_CODE
					,D.SITE_CATEGORY_NM AS CATEGORY_NM
					,D.CS_DSP_SEQ
					,B.SALES_MODEL_FLAG
					,D.SUPP_USE_FLAG
					,B.GSFS_USE_FLAG
					,A.BUYER_MODEL_CODE
				FROM PDM_PRODUCT_SVC_D A
				INNER JOIN SVD_PRODUCT_SVC_MODEL_D B
					ON B.SITE_CODE = #{localeCode}
					AND A.SVD_SKU_ID = B.SVD_SKU_ID
				INNER JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C
					ON C.SITE_CODE = #{localeCode}
					AND A.SVD_SKU_ID = C.SVD_SKU_ID
				INNER JOIN DSP_DISPLAY_CATEGORY_M D
					ON D.SITE_CODE = C.SITE_CODE
					AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
				INNER JOIN DSP_DISPLAY_CATEGORY_D E
					ON E.SITE_CODE = D.SITE_CODE
					AND E.CATEGORY_CODE = D.CATEGORY_CODE
					AND E.SHOP_CODE = 'D2C'
				WHERE 1=1
				AND A.USE_FLAG = 'Y'
				AND B.USE_FLAG = 'Y'
				AND C.USE_FLAG = 'Y'
				AND B.GSFS_USE_FLAG = 'Y'
				AND D.SUPP_USE_FLAG = 'Y'
				<if test="superCategoryId != null and superCategoryId != ''">
					AND C.LV1_CATEGORY_CODE = #{superCategoryId}
				</if>
				AND C.LV2_CATEGORY_CODE = #{categoryId}
				AND C.LV3_CATEGORY_CODE NOT IN ('CT20096025','CT20096030' ,'Others')
				AND C.LV3_CATEGORY_CODE IS NOT NULL
			    UNION ALL
			    SELECT 
					 L.LV3_CATEGORY_CODE
					,M.SITE_CATEGORY_NM AS CATEGORY_NM
					,M.CS_DSP_SEQ
					,L.SALES_MODEL_FLAG
					,M.SUPP_USE_FLAG
					,L.GSFS_USE_FLAG
					,L.BUYER_MODEL_CODE
			      FROM (SELECT K.CATE_CODE as LV3_CATEGORY_CODE
			                 , K.SALES_MODEL_FLAG
			                 , K.GSFS_USE_FLAG
			                 , K.BUYER_MODEL_CODE
			    	      FROM (SELECT /*+ NO_MERGE LEADING(AA) */ DISTINCT
			    	      <![CDATA[
			    	      				CASE WHEN NO = 1 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',1),',',-1)
			    	         			     WHEN NO = 2 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 1 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',2),',',-1)
			    	         			     WHEN NO = 3 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 2 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',3),',',-1)
			    	         			     WHEN NO = 4 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 3 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',4),',',-1)
			    	         			     WHEN NO = 5 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 4 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',5),',',-1)
			    	         			     WHEN NO = 6 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 5 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',6),',',-1)
			    	         			     WHEN NO = 7 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 6 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',7),',',-1)
			    	         			     WHEN NO = 8 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 7 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',8),',',-1)
			    	         			     WHEN NO = 9 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 8 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',9),',',-1)
			    	         			     WHEN NO = 10 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 9 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',10),',',-1)
			    	         			     WHEN NO = 11 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 10 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',11),',',-1)
			    	         			     WHEN NO = 12 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 11 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',12),',',-1)
			    	         			     WHEN NO = 13 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 12 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',13),',',-1)
			    	         			     WHEN NO = 14 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 13 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',14),',',-1)
			    	         			     WHEN NO = 15 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 14 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',15),',',-1)
			    	         			     WHEN NO = 16 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 15 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',16),',',-1)
			    	         			     WHEN NO = 17 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 16 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',17),',',-1)
			    	         			     WHEN NO = 18 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 17 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',18),',',-1)
			    	         			     WHEN NO = 19 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 18 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',19),',',-1)
			    	         			     WHEN NO = 20 && CHAR_LENGTH(MULTI_CATE)-CHAR_LENGTH(REPLACE(MULTI_CATE,',','')) >= 19 THEN SUBSTRING_INDEX(SUBSTRING_INDEX(MULTI_CATE,',',20),',',-1)
			    	         			     ]]>
					                        ELSE NULL
						                END AS CATE_CODE
						             , G.SALES_MODEL_FLAG
						             , G.GSFS_USE_FLAG
						             , F.BUYER_MODEL_CODE
									FROM PDM_PRODUCT_SVC_D F
									INNER JOIN SVD_PRODUCT_SVC_MODEL_D G
									ON  G.SITE_CODE  = #{localeCode}
									AND F.SVD_SKU_ID = G.SVD_SKU_ID
									INNER JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R H
									ON  H.SITE_CODE  = #{localeCode}
									AND F.SVD_SKU_ID = H.SVD_SKU_ID
									INNER JOIN (
										SELECT 
											 GROUP_CONCAT(IFNULL(CATEGORY_CODE,'') SEPARATOR ',') AS MULTI_CATE
											,SITE_CODE
											,CUST_MODEL_CODE
											,CATEGORY_LV_NO
											,USE_FLAG
										FROM SVD_CATEGORY_MODEL_R
										WHERE 1=1 
										AND USE_FLAG = 'Y'
										AND SITE_CODE = #{localeCode}
										AND CATEGORY_LV_NO  = '4'
									) I ON I.CUST_MODEL_CODE = F.BUYER_MODEL_CODE
									AND I.USE_FLAG = 'Y'
									CROSS JOIN (SELECT SEQ AS NO FROM SEQ_1_TO_20) J
									WHERE 1=1
									AND F.USE_FLAG = 'Y'
									AND G.USE_FLAG = 'Y'
									AND H.USE_FLAG = 'Y'
									AND G.GSFS_use_FLAG = 'Y'
									<if test="superCategoryId != null and superCategoryId != ''">
										AND H.LV1_CATEGORY_CODE = #{superCategoryId}
									</if>
									AND H.LV2_CATEGORY_CODE = #{categoryId}
									AND H.LV3_CATEGORY_CODE !='Others'
									AND I.MULTI_CATE IS NOT NULL
				               ) K 
			             WHERE 1=1 
						 AND K.CATE_CODE IS NOT NULL 
						 AND K.CATE_CODE != ''
			        ) L 
					INNER JOIN DSP_DISPLAY_CATEGORY_M M
					ON  M.SITE_CODE = #{localeCode}
					AND M.CATEGORY_CODE = L.LV3_CATEGORY_CODE
					INNER JOIN DSP_DISPLAY_CATEGORY_D N
					ON  N.SITE_CODE     = M.SITE_CODE
					AND N.CATEGORY_CODE = M.CATEGORY_CODE
					AND N.SHOP_CODE = 'D2C'
			  <if test="pageFlag eq 'locateRepairCenter'">
			  ) O
			  </if>
			) P
			ORDER by IF(#{localeCode} = #{localeCode}, P.CS_DSP_SEQ, LPAD(P.CS_DSP_SEQ, 3, '0') )
		</select>
		
		<select id="retrieveGpRepairableProduct" parameterType="com.lge.d2x.domain.support.v1.model.GpRepairableProductInfoRequestVO" resultType="com.lge.d2x.domain.support.v1.model.GpRepairableProductInfoResponseVO">
			WITH RECURSIVE CTE AS
					 (SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpRepairableProduct */
					         T.CATEGORY_CODE AS P_CATEGORY
					        ,T.CATEGORY_CODE
					        ,T.SITE_CATEGORY_NM AS CATEGORY_NM
					        ,T.CATEGORY_LV_NO
					        ,T.SITE_CODE
					        ,T.SUPP_USE_FLAG
					        ,T.BIZ_TYPE_CODE
					        ,T.CS_DSP_SEQ
					        ,T.HIGH_LV_CATEGORY_CODE
					    FROM DSP_DISPLAY_CATEGORY_M AS T
					   WHERE T.CATEGORY_LV_NO = 1
					     AND T.SITE_CODE = #{siteCode}
					     AND T.CATEGORY_CODE NOT IN ('CT10000003', 'CT10000027', 'CT10000026', 'CT10000025')
					  UNION ALL
					  SELECT
					         B.P_CATEGORY
					        ,A.CATEGORY_CODE
					        ,A.SITE_CATEGORY_NM AS CATEGORY_NM
					        ,A.CATEGORY_LV_NO
					        ,A.SITE_CODE
					        ,A.SUPP_USE_FLAG
					        ,A.BIZ_TYPE_CODE
					        ,A.CS_DSP_SEQ
					        ,A.HIGH_LV_CATEGORY_CODE
					    FROM DSP_DISPLAY_CATEGORY_M AS A
					   INNER JOIN CTE AS B
					      ON A.HIGH_LV_CATEGORY_CODE = B.CATEGORY_CODE
					     AND A.SITE_CODE = B.SITE_CODE)
					SELECT C.CATEGORY_CODE AS CODE
					      ,C.CATEGORY_NM AS VALUE
					      ,C.CATEGORY_LV_NO AS LEVEL_NO
					  FROM CTE C
					  JOIN DSP_DISPLAY_CATEGORY_M B
					    ON (B.CATEGORY_CODE = C.HIGH_LV_CATEGORY_CODE
					    AND B.SITE_CODE = C.SITE_CODE)
					 WHERE C.SITE_CODE = #{siteCode}
					   AND C.CATEGORY_LV_NO IN (1, 2)
					   AND C.SUPP_USE_FLAG = 'Y'
					   AND C.BIZ_TYPE_CODE ='B2C'
					   <if test="modelCodeList != null and modelCodeList.size() > 0">
							AND C.CATEGORY_CODE NOT IN
							<foreach item="item" index="index" collection="modelCodeList" open="(" close=")" separator=",">
							    #{item}
							</foreach>
						</if>
				<![CDATA[		
					ORDER BY IF(C.CATEGORY_LV_NO = 1, C.CS_DSP_SEQ, B.CS_DSP_SEQ) ASC /* GROUP_DISPLAY_ORDER_NO */
							,C.CATEGORY_LV_NO ASC
							,C.CS_DSP_SEQ ASC
							,C.CATEGORY_NM ASC
				]]>
		</select>
		
		
	<select id="retrieveGpAscListDistanceBr" parameterType="com.lge.d2x.domain.support.v1.model.GpAscListDistanceBrRequestVO" resultType="com.lge.d2x.domain.support.v1.model.GpAscListDistanceBrResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpAscListDistanceBr */
			     CASE 
		           <![CDATA[WHEN CALC_DISTANCE>=0 AND CALC_DISTANCE<30 THEN '30']]>
		           <![CDATA[WHEN CALC_DISTANCE>=30 AND CALC_DISTANCE<100 THEN '100']]>
		           <![CDATA[WHEN CALC_DISTANCE>=100 AND CALC_DISTANCE<150 THEN '150']]>
		           <![CDATA[WHEN CALC_DISTANCE>=150 AND CALC_DISTANCE<300 THEN '300']]>
		            ELSE '0'
		            END as INIT_DISTANCE 
		         FROM (
                   WITH asc_m as (
					   SELECT 
					     *
					   FROM 
					   (
						SELECT 
							A.REPAIR_POSS_PRODUCT_CODE AS PRODUCT_CODES
							, ROUND(6371 * ACOS(COS(RADIANS(CAST(#{P_SEARCH_LATITUDE_VALUE} AS DECIMAL(10,6)))) * COS(RADIANS(A.ASC_LTD_VA)) * COS(RADIANS(A.ASC_LNG_VAL) - RADIANS(CAST(#{P_SEARCH_LONGITUDE_VALUE} AS DECIMAL(10,6)))) + SIN(RADIANS(CAST(#{P_SEARCH_LATITUDE_VALUE} AS DECIMAL(10,6)))) * SIN(RADIANS(A.ASC_LTD_VA))), 1) AS CALC_DISTANCE
						FROM SVD_ASC_M A
						WHERE 1=1 
						AND A.USE_FLAG ='Y'
						AND A.COUNTRY_CODE =#{P_GCM_COUNTRY_CODE}
						AND A.REPAIR_POSS_PRODUCT_CODE IS NOT NULL
						AND A.ASC_ID NOT IN (SELECT  D.ASC_ID
												FROM SVD_ASC_EXCEPT_CATEGORY_D D
												WHERE D.SITE_CODE = #{siteCode}
												<choose>
													<when test="siteCode == 'BR'">
														AND D.CATEGORY_ID in ('xxxxxxxx'
																		<if test="P_CATEGORY_ID != null and P_CATEGORY_ID != ''">
																		, #{P_CATEGORY_ID}
																		</if>
																		<if test="P_SUPER_CATEGORY_ID != null and P_SUPER_CATEGORY_ID != ''">
																		, #{P_SUPER_CATEGORY_ID}
																		</if>
																		) 
													</when>
													<otherwise>
													AND D.CATEGORY_ID = #{P_CATEGORY_ID}
													</otherwise>
												</choose>
												AND D.USE_FLAG = 'Y'
												)
						) B
					)
					, ASC_PRODUCT AS
		            ( SELECT distinct SUBSTRING_INDEX(SUBSTRING_INDEX(T.PRODUCTS, ',', N.N), ',', -1) PRODUCT_CODE
						FROM (SELECT  #{P_PRODUCT_CODE} AS PRODUCTS) T
					   CROSS JOIN (SELECT SEQ AS N FROM SEQ_1_TO_100) N
		            )
		            SELECT * 
		            FROM asc_m m
		            WHERE EXISTS (
			            			   SELECT *
			            			     FROM asc_product s
			            			  <![CDATA[  WHERE CONCAT(',',m.PRODUCT_CODES,',') LIKE CONCAT('%,',s.product_code,',%') )]]>
			            AND PRODUCT_CODES IS NOT NULL
			         )T
			         WHERE  CALC_DISTANCE BETWEEN 0 AND 300
	    			 ORDER BY CALC_DISTANCE ASC
	                 LIMIT 1
	</select>
	
	<select id="retrieveGpAscListAll" parameterType="com.lge.d2x.domain.support.v1.model.GpAscAllRequestVO" resultType="com.lge.d2x.domain.support.v1.model.GpAscAllResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpAscListAll */
				 T.ASC_ID
				,T.ASC_NAME
				,T.STATE_NAME
				,T.CITY_NAME
				,T.ASC_POSTAL_CODE
				,T.ASC_PHONE_NO
				,T.DISTANCE
				,T.ADDRESS1
				,T.ADDRESS2
				,T.ADDRESS3
				,T.ADDRESS
				,T.ASC_FAX_NO
				,T.EMAIL_ADDR
				,T.SITE_ADDR
				,T.ELITE_NAME
				,T.ASC_LONGITUDE_VALUE
				,T.ASC_LATITUDE_VALUE
				,T.LONGITUDE
				,T.ASC_ROUTE_MAP
				,T.PRODUCT_CODES
				,T.ASC_DESC
				,T.P_SC_DIV
				,T.ASC_IMAGE_ADDR
				,T.COUNTRY_CODE
				,T.OPERATION_TIME_DESC
				,T.CORPORATION_CODE
				,T.LOCALE_CODE
				,T.CATEGORY_ID
				,T.SEARCH_LATITUDE_VALUE
				,T.SEARCH_LONGITUDE_VALUE
				,T.REPAIRABLE_PRODUCT_CODE
				,T.CALC_DISTANCE
				<if test='brTypeLocaleFlag == "Y" or siteCode == "AU"'>
				,T.ASC_PHONE_TXT
				</if>
				<if test='brTypeLocaleFlag == "Y"'>
				,T.ASC_FAX_TXT
				,T.BUTTON_TXT
				,T.CALC_DISTANCE_TXT
				,T.WHATSAPP
				</if>
				<if test="siteCode == 'AU'">
				,T.DIRECT_SERVICE_FLAG
				,T.DIRECT_SERVICE_TITLE
				,T.DIRECT_SERVICE_URL
				,T.DIRECT_SERVICE_NAME
				,T.DIRECT_SERVICE_PHONE
				,T.EVALUATION_POINT_NO
				</if>
		  FROM (
				<if test="P_PRODUCT_CODE != null and P_PRODUCT_CODE != ''">
				WITH asc_m AS (
				</if>
				SELECT
					   A.ASC_ID AS ASC_ID
					 , CASE WHEN #{prefValue} = 'Y' THEN NVL(A.ASC_PET_NM, A.ASC_NM) 
							ELSE A.ASC_NM END AS ASC_NAME
				<choose>
					<when test="siteCode == 'BR'">
						, A.ASC_PROVINCE_NM AS STATE_NAME
						, A.ASC_LG_SVC_FLAG
					</when>
					<when test="siteCode == 'AR'">	
						, A.temp_attr6 AS STATE_NAME
					</when>
					<when test="siteCode == 'CAC' or siteCode == 'PA' or siteCode == 'PE'">
						, NVL(A.ASC_PROVINCE_NM, (SELECT P.COUNTRY_NM
												   FROM COM_SVD_PREFERENCE_M P
												  WHERE P.SITE_CODE = #{siteCode}
												    AND P.USE_FLAG = 'Y'
												    AND P.GCM_COUNTRY_CODE = A.COUNTRY_CODE LIMIT 1) ) AS STATE_NAME
	
					</when>
					<otherwise>	
					 , A.ASC_PROVINCE_NM AS STATE_NAME	
					</otherwise>
				</choose>
					 , A.ASC_CITY_NM AS CITY_NAME
					 , A.ASC_POSTAL_NO AS ASC_POSTAL_CODE
				<choose>
					 <when test='siteCode == "ES" and esASCNumberFlag == "Y"'>   <!-- GP1SI-283 -->
		        		,#{ascPhoneNo}			AS 	ASC_PHONE_NO  
		        	</when>
		        	<otherwise>
						<![CDATA[,CASE A.COUNTRY_CODE
							WHEN 'ID' THEN '1500 140' 
							WHEN 'IT' THEN '02 8148 5454'
							WHEN 'CO' THEN '01-8000-910-683'
							WHEN 'FR' THEN CASE  WHEN A.ASC_ID = '70003410-S' OR
														   A.ASC_ID = 'FR010872-S' OR
														   A.ASC_ID = '108626'
													  THEN '3220'
												ELSE A.ASC_TEL_NO
												END
							ELSE A.ASC_TEL_NO END]]>      								 AS ASC_PHONE_NO
					</otherwise>	    
				</choose>    
					,A.ASC_FAX_NO                                                                               		AS DISTANCE
					,A.ASC_ADDR1                                                                                		AS ADDRESS1
					,A.ASC_ADDR2                                                                                		AS ADDRESS2
					,A.ASC_ADDR3                                                                                		AS ADDRESS3
					<![CDATA[,CASE WHEN A.COUNTRY_CODE = 'CZ' THEN CONCAT(A.ASC_ADDR1, ( CASE WHEN A.ASC_ADDR2 IS NOT NULL THEN CONCAT('&nbsp;&nbsp;', A.ASC_ADDR2) ELSE '' END))
						WHEN A.COUNTRY_CODE = 'SK' THEN CONCAT(A.ASC_ADDR1, ( CASE WHEN A.ASC_ADDR2 IS NOT NULL THEN CONCAT('&nbsp;&nbsp;', A.ASC_ADDR2) ELSE '' END))
						WHEN A.COUNTRY_CODE = 'BR' THEN CONCAT(A.ASC_ADDR1, ( CASE WHEN A.ASC_ADDR2 IS NOT NULL THEN CONCAT('&nbsp;&nbsp;', A.ASC_ADDR2) ELSE '' END), ( CASE  WHEN A.ASC_ADDR3 IS NOT NULL THEN CONCAT('&nbsp;&nbsp;', A.ASC_ADDR3) ELSE '' END))
						WHEN A.COUNTRY_CODE = 'FR'
						THEN CASE WHEN A.ASC_ID = '70003410-S' OR
									   A.ASC_ID = 'FR010872-S'
								  THEN CONCAT(#{frAddressPart1},' ', #{frAddressPart2})
								  WHEN  A.ASC_ID = '108626'     OR
										A.ASC_ID = '70003390-S' OR
										A.ASC_ID = 'FR009237-S'
								  THEN #{frAddressPart2}
								  ELSE CONCAT(A.ASC_ADDR1, (CASE WHEN A.ASC_ADDR2 IS NOT NULL THEN CONCAT(' ', A.ASC_ADDR2) ELSE '' END))
								  END
						 ELSE CONCAT(A.ASC_ADDR1, (CASE WHEN A.ASC_ADDR2 IS NOT NULL THEN CONCAT(' ', A.ASC_ADDR2) ELSE '' END)) end]]> AS ADDRESS
					,A.ASC_FAX_NO                                                                                		AS ASC_FAX_NO
					,A.EMAIL_ADDR                                                                                		AS EMAIL_ADDR
					,A.WEBSITE_URL                                                                             		AS SITE_ADDR
					,''                                                                                          		AS ELITE_NAME
					,A.ASC_LNG_VAL                                                                       		AS ASC_LONGITUDE_VALUE
					,A.ASC_LTD_VAL    																			AS ASC_LATITUDE_VALUE
					<choose>
						 <when test="siteCode == 'BR'">
							 <![CDATA[ ,nvl(A.asc_img_addr, '/lg5-common-gp/images/support/find-service-center/service-center-default.jpg')]]>   AS LONGITUDE 
						 </when>
						 <otherwise>
							  ,A.ASC_IMG_ADDR                                                                    AS LONGITUDE 
						</otherwise>
					</choose>
					,A.ASC_MAP_ADDR                                                                              		AS ASC_ROUTE_MAP
					,A.REPAIR_POSS_PRODUCT_CODE                                                                   		AS PRODUCT_CODES
					,A.ASC_DESC                                                                                  		AS ASC_DESC
					,A.TEMP_ATTR2                                                                                      		AS P_SC_DIV
					,A.ASC_IMG_ADDR                                                                            		AS ASC_IMAGE_ADDR
					,A.COUNTRY_CODE                                                                              		AS COUNTRY_CODE
					,A.WORK_TIME_DESC                                                                       		AS OPERATION_TIME_DESC
					,A.CORP_CODE                                                                          		AS CORPORATION_CODE
					,#{siteCode}                                                                     					AS LOCALE_CODE
					,#{P_CATEGORY_ID}                                                                          			AS CATEGORY_ID              
					,CAST(#{P_SEARCH_LATITUDE_VALUE} AS DECIMAL(10,6))                                                  AS SEARCH_LATITUDE_VALUE
					,CAST(#{P_SEARCH_LONGITUDE_VALUE} AS DECIMAL(10,6))                                                 AS SEARCH_LONGITUDE_VALUE
					<choose>
						<when test='brTypeLocaleFlag == "Y"'>
							,#{ascPhoneTxt}																						AS ASC_PHONE_TXT
							,#{ascFaxTxt}																						AS ASC_FAX_TXT
							,#{buttonTxt}																						AS BUTTON_TXT
							,#{calcDistanceTxt}																				    AS CALC_DISTANCE_TXT
						</when>
						<when test="siteCode == 'AU'">
							,#{ascPhoneTxt}																								AS ASC_PHONE_TXT
							,#{directServiceFlag}																						AS DIRECT_SERVICE_FLAG
							,#{directServiceTitle}																						AS DIRECT_SERVICE_TITLE
							,#{directServiceUrl}																						AS DIRECT_SERVICE_URL
							,#{directServiceName}																						AS DIRECT_SERVICE_NAME
							,#{directServicePhone}																						AS DIRECT_SERVICE_PHONE
						</when>
						<otherwise>
							,#{ascPhoneTxt}																				AS ASC_PHONE_TXT 
						</otherwise>
					</choose>
					 ,REPAIR_POSS_PRODUCT_CODE                                     AS REPAIRABLE_PRODUCT_CODE
					<choose>
						<when test="P_DISTANCE_TYPE != 'km'">
										, ROUND(3958.8 * ACOS(COS(RADIANS(CAST(#{P_SEARCH_LATITUDE_VALUE} AS DECIMAL(10,6)))) * COS(RADIANS(A.ASC_LTD_VAL)) * COS(RADIANS(A.ASC_LNG_VAL) - RADIANS(CAST(#{P_SEARCH_LONGITUDE_VALUE} AS DECIMAL(10,6)))) + SIN(RADIANS(CAST(#{P_SEARCH_LATITUDE_VALUE} AS DECIMAL(10,6)))) * SIN(RADIANS(A.ASC_LTD_VAL))), 1) AS CALC_DISTANCE
						</when>
						<otherwise>
										, ROUND(6371 * ACOS(COS(RADIANS(CAST(#{P_SEARCH_LATITUDE_VALUE} AS DECIMAL(10,6)))) * COS(RADIANS(A.ASC_LTD_VAL)) * COS(RADIANS(A.ASC_LNG_VAL) - RADIANS(CAST(#{P_SEARCH_LONGITUDE_VALUE} AS DECIMAL(10,6)))) + SIN(RADIANS(CAST(#{P_SEARCH_LATITUDE_VALUE} AS DECIMAL(10,6)))) * SIN(RADIANS(A.ASC_LTD_VAL))), 1) AS CALC_DISTANCE
						</otherwise>
					</choose>
					<if test="siteCode == 'RU'">
						,IFNULL(A.eval_point_no,0) AS EVALUATION_POINT_NO
					</if>
					<if test='brTypeLocaleFlag == "Y"'>  
					   , A.APP_NO as WHATSAPP 
					</if>
	          		FROM SVD_ASC_M A
	         		WHERE A.COUNTRY_CODE = #{P_GCM_COUNTRY_CODE}
	           		AND A.USE_FLAG ='Y'
	           		AND A.repair_poss_product_code IS NOT NULL
           			<choose>
						<when test="siteCode == 'AR' ">
							<if test="P_STATE_CODE != null and P_STATE_CODE != '' ">
								 AND UPPER(A.TEMP_ATTR6) = UPPER(#{P_STATE_CODE}) 
							</if>
						</when>
						<when test="siteCode == 'CAC' or siteCode == 'PA' or siteCode == 'PE'">
							<if test="P_STATE_CODE != null and P_STATE_CODE != '' ">
								 AND A.COUNTRY_CODE = #{P_STATE_CODE} 
							</if>
						</when>
						<otherwise>
							<if test="P_PROVINCE_NAME != null and P_PROVINCE_NAME != '' ">
								 AND a.ASC_PROVINCE_NM = #{P_PROVINCE_NAME} 
							</if>
							<if test="#{P_PROVINCE_NAME}.EMPTY ">
								 AND a.ASC_PROVINCE_NM is not null 
							</if>
							<if test="P_STATE_CODE != null and P_STATE_CODE != '' ">
								 AND UPPER(A.ASC_PROVINCE_NM) = UPPER(#{P_STATE_CODE}) 
							</if>
						</otherwise>
					</choose>
					<if test="P_CITY_NAME != null and P_CITY_NAME != ''">
						AND UPPER(A.asc_city_nm) = UPPER(#{P_CITY_NAME})
					</if>
					<if test="P_CITY_NAME eq 'MACAU' and P_GCM_COUNTRY_CODE eq 'HK'">
						AND UPPER(A.asc_city_nm) = UPPER('MACAU')
					</if>
					<!-- {#11}-->
					<choose>
						<when test="siteCode == 'BR'">
							<if test="(P_CATEGORY_ID != null and P_CATEGORY_ID != '') or (P_SUPER_CATEGORY_ID != null and P_SUPER_CATEGORY_ID != '')">
								AND A.ASC_ID NOT IN (SELECT  D.ASC_ID
													 FROM SVD_ASC_EXCEPT_CATEGORY_D D
													 WHERE D.SITE_CODE = #{siteCode}
													   AND D.CATEGORY_ID IN ('xxxxxxxx'
																			<if test="P_CATEGORY_ID != null and P_CATEGORY_ID != ''">
																			, #{P_CATEGORY_ID}
																			</if>
																			<if test="P_SUPER_CATEGORY_ID != null and P_SUPER_CATEGORY_ID != ''">
																			, #{P_SUPER_CATEGORY_ID}
																			</if>
																			)
													   AND d.use_flag = 'Y')
							</if>
						</when>
						<otherwise>
							<if test="P_CATEGORY_ID != null and P_CATEGORY_ID != ''">
								AND A.ASC_ID NOT IN (SELECT  D.ASC_ID
													 FROM SVD_ASC_EXCEPT_CATEGORY_D D
													 WHERE D.SITE_CODE = #{siteCode}
													   AND D.CATEGORY_ID = #{P_CATEGORY_ID}
													   AND D.USE_FLAG = 'Y')
							</if>
						</otherwise>
					</choose>
            	<if test="P_STEP_CITY != null and P_STEP_CITY != '' and P_STEP_CITY !='Others'  ">
         			AND A.ASC_CITY_NM = #{P_STEP_CITY}
            	</if>
            	 ORDER BY A.DSP_SEQ
            	<if test="P_PRODUCT_CODE != null and P_PRODUCT_CODE != ''">
	            	)
		            , ASC_PRODUCT AS
		            ( SELECT distinct SUBSTRING_INDEX(SUBSTRING_INDEX(T.PRODUCTS, ',', N.N), ',', -1) PRODUCT_CODE
						FROM (SELECT #{P_PRODUCT_CODE} AS PRODUCTS) T
					   CROSS JOIN (SELECT SEQ AS N FROM SEQ_1_TO_100) N
		            )
		            SELECT   M.ASC_ID
							,M.ASC_NAME
							,M.STATE_NAME
							,M.CITY_NAME
							,M.ASC_POSTAL_CODE
							,M.ASC_PHONE_NO
							,M.DISTANCE
							,M.ADDRESS1
							,M.ADDRESS2
							,M.ADDRESS3
							,M.ADDRESS
							,M.ASC_FAX_NO
							,M.EMAIL_ADDR
							,M.SITE_ADDR
							,M.ELITE_NAME
							,M.ASC_LONGITUDE_VALUE
							,M.ASC_LATITUDE_VALUE
							,M.LONGITUDE
							,M.ASC_ROUTE_MAP
							,M.PRODUCT_CODES
							,M.ASC_DESC
							,M.P_SC_DIV
							,M.ASC_IMAGE_ADDR
							,M.COUNTRY_CODE
							,M.OPERATION_TIME_DESC
							,M.CORPORATION_CODE
							,M.LOCALE_CODE
							,M.CATEGORY_ID
							,M.SEARCH_LATITUDE_VALUE
							,M.SEARCH_LONGITUDE_VALUE
							<if test='brTypeLocaleFlag == "Y" or siteCode == "AU"'>
							,M.ASC_PHONE_TXT
							</if>
							<if test='brTypeLocaleFlag == "Y"'>
							,M.ASC_FAX_TXT
							,M.BUTTON_TXT
							,M.CALC_DISTANCE_TXT
							,M.WHATSAPP
							</if>
							<if test="siteCode == 'AU'">
							,M.DIRECT_SERVICE_FLAG
							,M.DIRECT_SERVICE_TITLE
							,M.DIRECT_SERVICE_URL
							,M.DIRECT_SERVICE_NAME
							,M.DIRECT_SERVICE_PHONE
							,M.EVALUATION_POINT_NO
							</if>
							,M.REPAIRABLE_PRODUCT_CODE
							,M.CALC_DISTANCE
		           <![CDATA[ FROM asc_m M
		            WHERE EXISTS (
		            			   SELECT *
		            			     FROM asc_product S
		            			    WHERE CONCAT(',',M.PRODUCT_CODES,',') LIKE CONCAT('%,',S.PRODUCT_CODE,',%') )
		            AND PRODUCT_CODES IS NOT NULL]]>
    			</if>
    			)T
    			<if test="(P_DISTANCE_TYPE != null and P_DISTANCE_TYPE != '') and (P_DISTANCE_VALUE != null and P_DISTANCE_VALUE != '') and (P_GCM_COUNTRY_CODE !='CN')">
   				<![CDATA[WHERE CALC_DISTANCE <= CAST(#{P_DISTANCE_VALUE} AS UNSIGNED)]]>
    			<choose>	
	          		<when test="P_GCM_COUNTRY_CODE == 'AU'">
                     	<![CDATA[ORDER BY CASE WHEN INSTR(ASC_ID,'lgDirectService') = 1 THEN 0 ELSE 1 END ,CALC_DISTANCE ASC]]> 
            		</when>
            		<when test="P_GCM_COUNTRY_CODE == 'RU'">
                     	ORDER BY EVALUATION_POINT_NO DESC, CALC_DISTANCE ASC , P_SC_DIV DESC 
            		</when>
            		<otherwise>
            		 	ORDER BY CALC_DISTANCE ASC , P_SC_DIV DESC 
            		</otherwise>
            	</choose>
            	</if>
	</select>
	
	

	<select id="retrieveGpCategoryNameByKMProductCode" parameterType="com.lge.d2x.domain.support.v1.model.KmProductRequestVO" resultType="com.lge.d2x.domain.support.v1.model.KmProductResponseVO">
			SELECT /*! STRAIGHT_JOIN */ /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpCategoryNameByKMProductCode */
					DISTINCT
			<choose>
				<when test="siteCode == 'BR'">
					  C.CATEGORY_CODE AS CATEGORY_ID
					, C.SITE_CATEGORY_NM AS CATEGORY_NAME
				</when>
				<otherwise>
					 F.LV2_CATEGORY_CODE AS CATEGORY_ID
					,C.SITE_CATEGORY_NM AS CATEGORY_NAME
				</otherwise>
			</choose>
			  FROM SVD_MDMS_GSCS_KM_PROD_R A
			 inner join PDM_PRODUCT_SVC_D B
			    on B.MDMS_PRDGRP_CODE = A.MDMS_PRDGRP_CODE
			 inner join SVD_PRODUCT_SVC_MODEL_CATEGORY_R F
			    on F.SVD_SKU_ID = B.SVD_SKU_ID
			 inner join SVD_PRODUCT_SVC_MODEL_D G
			    on G.SVD_SKU_ID = F.SVD_SKU_ID
			 inner join DSP_DISPLAY_CATEGORY_M C
			    on C.SITE_CODE = F.SITE_CODE
			   and C.CATEGORY_CODE = F.LV2_CATEGORY_CODE
			 inner join DSP_DISPLAY_CATEGORY_D D
			    on D.CATEGORY_CODE = C.CATEGORY_CODE
          WHERE B.USE_FLAG = 'Y'
		   AND F.USE_FLAG = 'Y'
		   AND G.USE_FLAG = 'Y'
		   AND F.LV2_CATEGORY_CODE IS NOT NULL
		   AND F.SITE_CODE = #{siteCode}
		   AND G.SITE_CODE = #{siteCode}
		   AND D.SHOP_CODE = 'D2C'
           <if test="siteCode != 'BR'">
           	AND C.CATEGORY_CODE = F.LV2_CATEGORY_CODE
           </if>
           <if test="siteCode == 'BR'">
           	AND C.HIGH_LV_CATEGORY_CODE = D.CATEGORY_CODE
		   </if>
         <![CDATA[
           AND A.KM_PRODUCT_CODE IN (	SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(STR, ',', N.N+1), ',' , -1),2,3)  AS PRODUCT_CODE
										FROM (SELECT #{kmProductCodes} AS STR) T CROSS JOIN
										(
										SELECT A.N + B.N * 10 + 1 N
										FROM
										(SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) A
										,(SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) B
										ORDER BY N
										) N
										WHERE N.N <= (LENGTH(T.STR) - LENGTH(REPLACE(T.STR, ',',''))-1))
          AND NOT EXISTS (SELECT E.ASC_ID
				           FROM SVD_ASC_EXCEPT_CATEGORY_D E
				           WHERE E.SITE_CODE = #{siteCode}
		]]>
						<choose>
							 <when test="siteCode == 'BR'">
		        				AND  (C.CATEGORY_CODE = E.CATEGORY_ID OR C.HIGH_LV_CATEGORY_CODE = E.CATEGORY_CODE)
							 </when>
							 <otherwise>
	              				AND   C.CATEGORY_CODE = E.CATEGORY_ID
	              			</otherwise>
	              		</choose>
		<![CDATA[
				           AND   E.ASC_ID = #{ascId}
				           AND   E.USE_FLAG = 'Y')
           AND  C.SUPP_USE_FLAG='Y'
		]]>
		<choose>
			<when test="siteCode == 'BR'">
				ORDER BY D.CATEGORY_NM ASC
			</when>
			<otherwise>
				ORDER BY C.CATEGORY_NM ASC
			</otherwise>
		</choose>
	</select>
	
	<select id="retrieveSvdSiteModelList" parameterType="com.lge.d2x.domain.support.v1.model.SvdSiteModelListRequestVO" resultType="com.lge.d2x.domain.support.v1.model.SvdSiteModelListResponseVO">	
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSvdSiteModelList */
		 B.SITE_CODE 
		,C.SALES_CODE 
		FROM PDM_PRODUCT_SVC_D A
		, SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
		, SVD_PRODUCT_SVC_MODEL_D C
		WHERE 1=1 
		AND B.SITE_CODE = #{siteCode}
		AND C.SITE_CODE = #{siteCode}
		AND B.SVD_SKU_ID = A.SVD_SKU_ID
		AND C.SVD_SKU_ID = B.SVD_SKU_ID
		ORDER BY C.SALES_CODE
	</select>
	
	<select id="retrieveSuperCategoryOne" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveSuperCategoryOne */ 
			DISTINCT A.LV1_CATEGORY_CODE
		FROM   SVD_PRODUCT_SVC_MODEL_CATEGORY_R A
		<if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
		JOIN PDM_PRODUCT_SVC_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.USE_FLAG = 'Y'
        JOIN SVD_CUSTOMER_MODEL_M C
          ON C.SITE_CODE = A.SITE_CODE
         AND C.CUST_MODEL_CODE = B.BUYER_MODEL_CODE
         AND C.CREATION_DATE >= #{euEcoCategoryDt}
         AND C.USE_FLAG = 'Y'
        </if>
		WHERE  1=1
			AND A.USE_FLAG = 'Y'
		    AND A.SITE_CODE = #{localeCode}
	        AND A.LV2_CATEGORY_CODE = #{categoryId}
		LIMIT  1
	</select>
	
	
	
	<select id="retrieveGpModelList" resultType="com.lge.d2x.domain.support.v1.model.GpModelListResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpModelListRequestVO">		
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpModelList */
				 T.CODE
			   , T.VALUE
			   , T.SALES_MODEL_FLAG
			   , T.GSFS_USE_FLAG
			   , T.OBU_CODE
			   , T.MDMS_PRDGRP_CODE                     
			   , T.MOBL_MODEL_FLAG
			   , T.SITE_CODE
			   , T.MODEL_IMG_PATH
			   , T.LV1_CATEGORY_CODE
			   , T.LV2_CATEGORY_CODE
			   , T.LV3_CATEGORY_CODE
			   , T.LV1_CATEGORY_NM
			   , T.LV2_CATEGORY_NM
			   , T.LV3_CATEGORY_NM
			   , LV1_STICKY_IMG_URL
			   , LV2_STICKY_IMG_URL
			   , LV3_STICKY_IMG_URL
			   , LV1_STICKY_HOVER_ICON_URL
			   , LV2_STICKY_HOVER_ICON_URL
			   , LV3_STICKY_HOVER_ICON_URL
			   , SUBSTRING_INDEX(T.PRD_TEXT, '|', 1) AS LBR_WTY_PRD_TEXT
			   , SUBSTRING_INDEX(T.PRD_TEXT, '|', -1) AS PART_WTY_PRD_TEXT
			   , T.USER_FRIENDLY_NAME
			   FROM (
					SELECT   C.SALES_CODE  AS CODE
			               , (CASE WHEN A.OBU_CODE NOT IN 
			               <choose>
			               <when test="obuCodeList != null and obuCodeList != '' and obuCodeList.size()>0">
			               	<foreach item="item" index="index" collection="obuCodeList" open="(" close=")" separator=",">
					   	     #{item}
					        </foreach>
					        </when>
					        <otherwise>
					        ('')
					        </otherwise>
					        </choose>
			                THEN  A.SALES_MODEL_CODE ELSE C.SALES_CODE END) AS VALUE
			               , C.SALES_MODEL_FLAG
			               , C.GSFS_USE_FLAG
			               , A.OBU_CODE
	                       , A.MDMS_PRDGRP_CODE                     
	                       , C.MOBL_MODEL_FLAG
	                       , B.SITE_CODE
						   , C.MODEL_IMG_PATH
						   , B.LV1_CATEGORY_CODE
						   , B.LV2_CATEGORY_CODE
						   , B.LV3_CATEGORY_CODE
						   , CC.CATEGORY_NM AS LV1_CATEGORY_NM
						   , DD.CATEGORY_NM AS LV2_CATEGORY_NM
						   , EE.CATEGORY_NM AS LV3_CATEGORY_NM
						   , CC.STICKY_IMG_URL AS LV1_STICKY_IMG_URL
						   , DD.STICKY_IMG_URL AS LV2_STICKY_IMG_URL
						   , EE.STICKY_IMG_URL AS LV3_STICKY_IMG_URL
						   , CC.STICKY_HOVER_ICON_URL AS LV1_STICKY_HOVER_ICON_URL
						   , DD.STICKY_HOVER_ICON_URL AS LV2_STICKY_HOVER_ICON_URL
						   , EE.STICKY_HOVER_ICON_URL AS LV3_STICKY_HOVER_ICON_URL
						   ,(SELECT concat(DD.LBR_WTY_PRODUCT_TEXT_CNTS, '|', DD.PART_WTY_PRODUCT_TEXT_CNTS)
							   FROM SVD_WTY_INFO_M DD
							   JOIN SVD_WTY_INFO_MODEL_R EE
								 ON DD.DOC_ID = EE.DOC_ID
							  WHERE  DD.SITE_CODE =  #{siteCode}
								AND    DD.COUNTRY_CODE = #{countryCode}
								AND    NVL(DD.USE_FLAG, 'Y') = 'Y'
								AND    DD.OPEN_STATE_CODE = 'ONSERVICE'
								AND   (EE.CUST_MODEL_CODE = C.SALES_CODE or EE.CUST_MODEL_CODE = A.BUYER_MODEL_CODE)
							  UNION ALL
							 SELECT concat(FF.LBR_WTY_PRODUCT_TEXT_CNTS, '|', FF.PART_WTY_PRODUCT_TEXT_CNTS)
							   FROM   SVD_WTY_INFO_M FF
							  WHERE  FF.SITE_CODE =  #{siteCode}
								AND    FF.COUNTRY_CODE = #{countryCode}
								AND    NVL(FF.USE_FLAG, 'Y') = 'Y'
								AND    FF.OPEN_STATE_CODE = 'ONSERVICE'
								AND    FF.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
								AND    FF.LV2_CATEGORY_CODE = NVL(NULLIF(B.LV2_CATEGORY_CODE, ''), FF.LV2_CATEGORY_CODE)
								AND    FF.LV3_CATEGORY_CODE = NVL(NULLIF(B.LV3_CATEGORY_CODE, ''), FF.LV3_CATEGORY_CODE)
								AND    NOT EXISTS (SELECT 'Y'
													 FROM SVD_WTY_INFO_MODEL_R GG
													WHERE GG.SITE_CODE =  #{siteCode}
													  AND GG.DOC_ID = FF.DOC_ID)
								LIMIT  1
								) AS PRD_TEXT
						,(SELECT TT.USER_FRNDY_PRODUCT_NM 
										FROM DSP_PDP_M TT
										WHERE 1=1
										AND TT.SITE_CODE = #{siteCode}
										AND TT.PDP_ID = (SELECT PDP_ID 
														FROM SVD_CUSTOMER_MODEL_M UU
														WHERE 1=1
														AND UU.SITE_CODE = #{siteCode}
														AND UU.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
													  )
										) AS USER_FRIENDLY_NAME
			        FROM PDM_PRODUCT_SVC_D A
					JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID = B.SVD_SKU_ID
					JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID
					LEFT OUTER JOIN
					(
						SELECT AA.SITE_CATEGORY_NM AS CATEGORY_NM
							  ,AA.CATEGORY_CODE
							  ,BB.STICKY_IMG_URL
							  ,BB.STICKY_HOVER_ICON_URL
						FROM DSP_DISPLAY_CATEGORY_M AA 
						INNER JOIN DSP_DISPLAY_CATEGORY_D BB
						ON  AA.SITE_CODE = BB.SITE_CODE
						AND AA.CATEGORY_CODE = BB.CATEGORY_CODE
						AND BB.SHOP_CODE = 'D2C'
						WHERE 1=1 
						AND AA.SITE_CODE = #{siteCode}
					) CC ON CC.CATEGORY_CODE = B.LV1_CATEGORY_CODE
					LEFT OUTER JOIN
					(
						SELECT AA.SITE_CATEGORY_NM AS CATEGORY_NM
							  ,AA.CATEGORY_CODE
							  ,BB.STICKY_IMG_URL
							  ,BB.STICKY_HOVER_ICON_URL
						FROM DSP_DISPLAY_CATEGORY_M AA 
						INNER JOIN DSP_DISPLAY_CATEGORY_D BB
						ON  AA.SITE_CODE = BB.SITE_CODE
						AND AA.CATEGORY_CODE = BB.CATEGORY_CODE
						AND BB.SHOP_CODE = 'D2C'
						WHERE 1=1 
						AND AA.SITE_CODE = #{siteCode}
					) DD ON DD.CATEGORY_CODE = B.LV2_CATEGORY_CODE
					LEFT OUTER JOIN
					(
						SELECT AA.SITE_CATEGORY_NM AS CATEGORY_NM
							  ,AA.CATEGORY_CODE
							  ,BB.STICKY_IMG_URL
							  ,BB.STICKY_HOVER_ICON_URL
						FROM DSP_DISPLAY_CATEGORY_M AA 
						INNER JOIN DSP_DISPLAY_CATEGORY_D BB
						ON  AA.SITE_CODE = BB.SITE_CODE
						AND AA.CATEGORY_CODE = BB.CATEGORY_CODE
						AND BB.SHOP_CODE = 'D2C'
						WHERE 1=1 
						AND AA.SITE_CODE = #{siteCode}
					) EE ON EE.CATEGORY_CODE = B.LV3_CATEGORY_CODE
			        <if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
					JOIN SVD_CUSTOMER_MODEL_M E
					  ON E.SITE_CODE = B.SITE_CODE
					 AND E.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
					 AND E.CREATION_DATE <![CDATA[>=]]> #{euEcoCategoryDt}
					 AND E.USE_FLAG = 'Y'
					</if>
					WHERE 1=1
					AND A.USE_FLAG = 'Y'
					AND B.USE_FLAG = 'Y'
				    AND C.USE_FLAG = 'Y'
			        AND B.SITE_CODE = #{siteCode}
				    AND C.SITE_CODE = #{siteCode}
					AND B.LV1_CATEGORY_CODE = #{superCategoryId}
					<if test="objetModelList != null and objetModelList.length > 0">
						AND A.SALES_CODE IN
						<foreach collection="objetModelList" item="c" index="idx" open=" (" separator="," close=")"><![CDATA[
															#{c}
														]]></foreach>
					</if>
					<if test='searchCateType == "LV2"'>
						<if test='ecoCategoryFlag == "Y" and categoryIdList!=null'>
							<foreach collection="categoryIdList" item="c" index="idx" open="AND B.LV2_CATEGORY_CODE IN (" separator="," close=")">
								#{c}
							</foreach>
						</if>
						AND B.LV2_CATEGORY_CODE = #{categoryId}
					</if>
					<if test='searchCateType == "LV3"'>
						<if test='ecoCategoryFlag == "Y" and categoryIdList!=null'>
							<foreach collection="categoryIdList" item="c" index="idx" open="AND B.LV2_CATEGORY_CODE IN (" separator="," close=")">
							    #{c}
							</foreach>
						</if>
					   AND (B.LV2_CATEGORY_CODE = #{subCategoryId} OR B.LV3_CATEGORY_CODE = #{subCategoryId})
					</if>
					<if test='siteCode != "BR" and pageFlag == "repair" and ecoCategoryFlag != "Y"'>
						AND  EXISTS (SELECT 1
									   FROM SVD_SERVICE_TYPE_R D
									   WHERE D.SITE_CODE = #{siteCode}
									   AND D.USE_FLAG = 'Y'
									   AND D.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
									   AND (D.LV3_CATEGORY_CODE IS NULL OR D.LV3_CATEGORY_CODE = '')
									   AND (D.CUST_MODEL_CODE IS NULL OR D.CUST_MODEL_CODE = '')
					                   UNION ALL
					                   FROM SVD_SERVICE_TYPE_R F
									   WHERE F.SITE_CODE = #{siteCode}
									   AND F.USE_FLAG = 'Y'
									   AND F.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
									   AND F.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
									   AND (F.CUST_MODEL_CODE IS NULL OR F.CUST_MODEL_CODE = '')
					                   UNION ALL
					                   SELECT 1
									   FROM SVD_SERVICE_TYPE_R G
									   WHERE G.SITE_CODE = #{siteCode}
									   AND G.USE_FLAG = 'Y'
									   AND G.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
									   AND G.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
									   AND G.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
					                   union all
					                   SELECT 1
									   FROM SVD_SERVICE_TYPE_R H
									   WHERE H.SITE_CODE = #{siteCode}
									   AND H.USE_FLAG = 'Y'
									   AND H.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
									   AND (H.LV3_CATEGORY_CODE IS NULL OR H.LV3_CATEGORY_CODE = '')
									   AND H.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
					                 )
					</if>
					<if test="(siteCode == 'MX' or siteCode == 'CO' or siteCode == 'CL' or siteCode == 'CAC' or siteCode == 'PA' or siteCode == 'PE' or siteCode == 'EC') and pageFlag == 'install'">
						AND  EXISTS (SELECT 1
					                   FROM SVD_SERVICE_TYPE_R I
					                   WHERE I.SITE_CODE = #{siteCode}
					                   AND I.USE_FLAG = 'Y'
					                   AND I.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					                   AND (I.SUB_CATEGORY_ID IS NULL OR I.SUB_CATEGORY_ID = '')
					                   AND (I.CUST_MODEL_CODE IS NULL OR I.CUST_MODEL_CODE = '')
					                   AND I.INSTALL_USE_FLAG = 'Y'
					                   union all
					                   SELECT 1
					                   FROM SVD_SERVICE_TYPE_R J
					                   WHERE J.SITE_CODE = #{siteCode}
					                   AND J.USE_FLAG = 'Y'
					                   AND J.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					                   AND J.LV2_CATEGORY_CODE = B.LV3_CATEGORY_CODE
					                   AND (J.CUSTOMER_MODEL_CODE IS NULL OR J.CUSTOMER_MODEL_CODE = '')
					                   AND J.INSTALL_USE_FLAG = 'Y'
					                   union all
					                   SELECT 1
					                   FROM SVD_SERVICE_TYPE_R K
					                   WHERE K.SITE_CODE = #{siteCode}
					                   AND K.USE_FLAG = 'Y'
					                   AND K.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					                   AND K.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
					                   AND K.CUSTOMER_MODEL_CODE = A.BUYER_MODEL_CODE
					                   AND K.INSTALL_USE_FLAG = 'Y'
					                   union all
					                   SELECT 1
					                   FROM SVD_SERVICE_TYPE_R L
					                   WHERE L.SITE_CODE = #{siteCode}
					                   AND L.USE_FLAG = 'Y'
					                   AND L.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					                   AND (L.LV3_CATEGORY_CODE IS NULL OR L.LV3_CATEGORY_CODE = '')
					                   AND L.CUSTOMER_MODEL_CODE = A.BUYER_MODEL_CODE
					                   AND L.INSTALL_USE_FLAG = 'Y'
					                 )
					</if>
					<if test="siteCode == 'CO' and pageFlag == 'maintenance'">
						AND  EXISTS (SELECT 1
					                   FROM SVD_SERVICE_TYPE_R N
					                   WHERE N.SITE_CODE = #{siteCode}
					                   AND N.USE_FLAG = 'Y'
					                   AND N.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					                   AND (N.SUB_CATEGORY_ID IS NULL OR N.SUB_CATEGORY_ID = '')
					                   AND (N.CUST_MODEL_CODE IS NULL OR N.CUST_MODEL_CODE = '')
					                   AND N.MAINTENANCE_USE_FLAG = 'Y'
					                   union all
					                   SELECT 1
					                   FROM SVD_SERVICE_TYPE_R M
					                   WHERE M.SITE_CODE = #{siteCode}
					                   AND M.USE_FLAG = 'Y'
					                   AND M.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					                   AND M.LV2_CATEGORY_CODE = B.LV3_CATEGORY_CODE
					                   AND (M.CUSTOMER_MODEL_CODE IS NULL OR M.CUSTOMER_MODEL_CODE = '')
					                   AND M.MAINTENANCE_USE_FLAG = 'Y'
					                   union all
					                   SELECT 1
					                   FROM SVD_SERVICE_TYPE_R O
					                   WHERE O.SITE_CODE = #{siteCode}
					                   AND O.USE_FLAG = 'Y'
					                   AND O.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					                   AND O.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
					                   AND O.CUSTOMER_MODEL_CODE = A.BUYER_MODEL_CODE
					                   AND O.MAINTENANCE_USE_FLAG = 'Y'
					                   union all
					                   SELECT 1
					                   FROM SVD_SERVICE_TYPE_R P
					                   WHERE P.SITE_CODE = #{siteCode}
					                   AND P.USE_FLAG = 'Y'
					                   AND P.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
					                   AND (P.LV3_CATEGORY_CODE IS NULL OR P.LV3_CATEGORY_CODE = '')
					                   AND P.CUSTOMER_MODEL_CODE = A.BUYER_MODEL_CODE
					                   AND P.MAINTENANCE_USE_FLAG = 'Y'
					                 )
					</if>
			) T
			GROUP  BY T.VALUE
			ORDER  BY T.CODE	
			<if test="page != null and page != ''">
			LIMIT #{countPerPage} OFFSET #{offset}
			</if>
	</select>
	
	<select id="retrieveGpGsfsSuperCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">		
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpGsfsSuperCategoryList */
		DISTINCT   C.LV1_CATEGORY_CODE  
		         , D.SITE_CATEGORY_NM AS CATEGORY_NM       
		         , D.CS_DSP_SEQ
		         , COALESCE(E.STICKY_IMG_URL, '') 		AS STICKY_IMG_URL
				 , COALESCE(E.STICKY_HOVER_ICON_URL, '') AS STICKY_HOVER_ICON_URL
		FROM   PDM_PRODUCT_SVC_D A
		JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID 
		JOIN DSP_DISPLAY_CATEGORY_M D
		  ON  D.SITE_CODE = C.SITE_CODE
		  AND D.CATEGORY_CODE = C.LV1_CATEGORY_CODE
		JOIN DSP_DISPLAY_CATEGORY_D E
		  ON  E.SITE_CODE = C.SITE_CODE
		  AND E.CATEGORY_CODE = D.CATEGORY_CODE
		  AND E.SHOP_CODE = 'D2C'
		<choose>
			<when test='b2bUseFlag eq "Y"'>
				<choose>
					<when test='b2bDivisionUseFlag eq "Y"'>
						AND D.BIZ_TYPE_CODE = #{divisionBizType}
					</when>
					<otherwise>
						AND D.BIZ_TYPE_CODE IN ('B2C','B2B') 
					</otherwise>
				</choose>
			</when>
			<otherwise>
				AND D.BIZ_TYPE_CODE = 'B2C'
			</otherwise>
		</choose>
		<if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
			JOIN SVD_CUSTOMER_MODEL_M F
			  ON F.SITE_CODE = B.SITE_CODE
			 AND F.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
			 AND F.CREATION_DATE >= #{euEcoCategoryDt}
			 AND F.USE_FLAG = 'Y'
		</if>
		
		WHERE  1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		AND D.SUPP_USE_FLAG = 'Y'
		<if test="localeCode != null and localeCode != ''">
		  AND B.SITE_CODE = #{localeCode}
		  AND C.SITE_CODE = #{localeCode}
		</if>
		<if test="superCategoryId != null and superCategoryId != ''">
			AND C.LV1_CATEGORY_CODE = #{superCategoryId}
		</if>
		<if test="pageFlag == 'repair' and localeCode == 'BR'">
			<choose>
				<when test='onlyMobileFlag eq "Y" and mobileCategoryList!=null and mobileCategoryList.size()>0'>
					AND C.LV2_CATEGORY_CODE IN
					<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
						#{item}
					</foreach>
				</when>
				<otherwise>
					<if test="mobileCategoryList!=null and mobileCategoryList.size()>0">
						AND C.LV2_CATEGORY_CODE NOT IN
						<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
							#{item}
						</foreach>
					</if>
				</otherwise>
			</choose>
		</if>
		<if test='ecoCategoryFlag == "Y" and categoryIdList!=null and categoryIdList.size()>0'>
			<foreach collection="categoryIdList" item="c" index="idx" open="AND C.LV2_CATEGORY_CODE IN (" separator="," close=")">
			    #{c}
			</foreach>
		</if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND A.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>

		<if test='ecoCategoryFlag != "Y"'>
		AND  EXISTS (SELECT 1
	                   FROM SVD_SERVICE_TYPE_R G
	                   WHERE G.SITE_CODE = #{localeCode}
	                   AND G.USE_FLAG = 'Y'
	                   AND G.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
	                   AND (G.LV3_CATEGORY_CODE IS NULL OR G.LV3_CATEGORY_CODE = '')
	                   AND (G.CUST_MODEL_CODE IS NULL OR G.CUST_MODEL_CODE = '')
					   <if test="localeCode == 'BR'">
							<if test="pageType == 'common'">
								AND (G.ONSITE_USE_FLAG = 'Y' OR G.MC_COLLECT_FLAG = 'Y')
							</if>
							<if test="pageType == 'install'">
								AND G.INSTALL_USE_FLAG = 'Y'
							</if>
							<if test="pageType == 'carryin'">
								AND G.CARY_USE_FLAG = 'Y'
							</if>
						</if>
						<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode =='MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
							<if test="pageType == 'common'"> 
								AND G.ONSITE_USE_FLAG = 'Y' 
							</if> 
							<if test="pageType == 'install'"> 
								AND G.INSTALL_USE_FLAG = 'Y' 
							</if>
						</if>
						<!--	LGCOMCO-200		-->
						<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
							<![CDATA[
								AND G.MAINTENANCE_USE_FLAG = 'Y'
							]]>
						</if>
	                   UNION ALL
	                   SELECT 1
	                   FROM SVD_SERVICE_TYPE_R H
	                   WHERE H.SITE_CODE = #{localeCode}
	                   AND H.USE_FLAG = 'Y'
	                   AND H.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
	                   AND H.LV3_CATEGORY_CODE = C.LV3_CATEGORY_CODE
	                   AND (H.CUST_MODEL_CODE IS NULL OR H.CUST_MODEL_CODE = '')
						<if test="localeCode == 'BR'">
							<if test="pageType == 'common'">
								AND (H.ONSITE_USE_FLAG = 'Y' OR H.MC_COLLECT_FLAG = 'Y')
							</if>
							<if test="pageType == 'install'">
								AND H.INSTALL_USE_FLAG = 'Y'
							</if>
							<if test="pageType == 'carryin'">
								AND H.CARY_USE_FLAG = 'Y'
							</if>
						</if>
						<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
							<if test="pageType == 'common'"> 
								AND H.ONSITE_USE_FLAG = 'Y' 
							</if> 
							<if test="pageType == 'install'"> 
								AND H.INSTALL_USE_FLAG = 'Y' 
							</if>
						</if>
						<!--	LGCOMCO-200		-->
						<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
							<![CDATA[
								AND H.MAINTENANCE_USE_FLAG = 'Y'
							]]>
						</if>
	                   UNION ALL
	                   SELECT 1
	                   FROM SVD_SERVICE_TYPE_R I
	                   WHERE I.SITE_CODE = #{localeCode}
	                   AND I.USE_FLAG = 'Y'
	                   AND I.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
	                   AND I.LV3_CATEGORY_CODE = C.LV3_CATEGORY_CODE
	                   AND I.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
						<if test="localeCode == 'BR'">
							<if test="pageType == 'common'">
								AND (I.ONSITE_USE_FLAG = 'Y' OR I.MC_COLLECT_FLAG = 'Y')
							</if>
							<if test="pageType == 'install'">
								AND I.INSTALL_USE_FLAG = 'Y'
							</if>
							<if test="pageType == 'carryin'">
								AND I.CARY_USE_FLAG = 'Y'
							</if>
						</if>
						<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
							<if test="pageType == 'common'"> 
								AND I.ONSITE_USE_FLAG = 'Y' 
							</if> 
							<if test="pageType == 'install'"> 
								AND I.INSTALL_USE_FLAG = 'Y' 
							</if> 
						</if>
						<!--	LGCOMCO-200		-->
						<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
							<![CDATA[
								AND I.MAINTENANCE_USE_FLAG = 'Y'
							]]>
						</if>
	                   UNION ALL
	                   SELECT 1
	                   FROM SVD_SERVICE_TYPE_R J
	                   WHERE J.SITE_CODE = #{localeCode}
	                   AND J.USE_FLAG = 'Y'
	                   AND J.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
	                   AND (J.LV3_CATEGORY_CODE IS NULL OR J.LV3_CATEGORY_CODE = '')
	                   AND J.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
						<if test="localeCode == 'BR'">
							<if test="pageType == 'common'">
								AND (J.ONSITE_USE_FLAG = 'Y' OR J.MC_COLLECT_FLAG = 'Y')
							</if>
							<if test="pageType == 'install'">
								AND J.INSTALL_USE_FLAG = 'Y'
							</if>
							<if test="pageType == 'carryin'">
								AND J.CARY_USE_FLAG = 'Y'
							</if>
						</if>
						<if test="localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
							<if test="pageType == 'common'"> 
								AND J.ONSITE_USE_FLAG = 'Y' 
							</if> 
							<if test="pageType == 'install'"> 
								AND J.INSTALL_USE_FLAG = 'Y' 
							</if> 
						</if>
						<!--	LGCOMCO-200		-->
						<if test="localeCode == 'CO' and pageFlag == 'maintenance'">
							<![CDATA[
								AND J.MAINTENANCE_USE_FLAG = 'Y'
							]]>
						</if>
	                 )
	    </if>
		ORDER BY D.CS_DSP_SEQ
	</select>
		
	<select id="retrieveCallSuperCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveCallSuperCategoryList */
		   DISTINCT
		    C.LV1_CATEGORY_CODE
		  , D.SITE_CATEGORY_NM AS CATEGORY_NM
		  , COALESCE(E.STICKY_IMG_URL, '') 		AS STICKY_IMG_URL
		  , COALESCE(E.STICKY_HOVER_ICON_URL, '') AS STICKY_HOVER_ICON_URL
		  , D.CS_DSP_SEQ
		FROM   PDM_PRODUCT_SVC_D A
		JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID 
		JOIN DSP_DISPLAY_CATEGORY_M D
		  ON  D.SITE_CODE = C.SITE_CODE
		  AND D.CATEGORY_CODE = C.LV1_CATEGORY_CODE
		JOIN DSP_DISPLAY_CATEGORY_D E
		  ON  E.SITE_CODE = C.SITE_CODE
		  AND E.CATEGORY_CODE = D.CATEGORY_CODE
		  AND E.SHOP_CODE = 'D2C'
		  
		WHERE 1 = 1
			<choose>
				<when test='b2bUseFlag eq "Y" and categoryIdList!=null and categoryIdList.size()>0'>
					AND (D.BIZ_TYPE_CODE = 'B2C' or (D.BIZ_TYPE_CODE = 'B2B' AND C.LV1_CATEGORY_CODE IN
					<foreach collection="categoryIdList" item="c" index="idx" open=" (" separator="," close=")">
						#{c}
					</foreach>
					))
				</when>
				<otherwise>
					AND D.BIZ_TYPE_CODE = 'B2C' 
				</otherwise>
			</choose>
			AND A.USE_FLAG = 'Y'
			AND B.USE_FLAG = 'Y'
			AND C.USE_FLAG = 'Y'
			<if test="localeCode != null and localeCode != ''">
				AND B.SITE_CODE = #{localeCode}
				AND C.SITE_CODE = #{localeCode}
			</if>
			<if test="localeCode != null and localeCode == 'IN'">
				<if test='tabType == "E"'>
				AND B.EXT_WTY_USE_FLAG = 'Y'
				</if>
			</if>
			AND D.SUPP_USE_FLAG = 'Y'
			<if test="pageFlag eq 'call' and callSubCateId != null and callSubCateId != ''">
			AND D.CATEGORY_CODE =  (SELECT T1.HIGH_LEVEL_CATEGORY_ID
		                      		FROM DSP_DISPLAY_CATEGORY_M T1
		                     	   WHERE T1.CATEGORY_CODE IN (SELECT T2.HIGH_LV_CATEGORY_CODE
		                                                      FROM DSP_DISPLAY_CATEGORY_M T2
		                                                     WHERE 1=1
															   AND T2.CATEGORY_CODE = #{callSubCateId}
		                                                       AND T2.CATEGORY_LV_NO = 3
		                                                       AND T2.SITE_CODE = #{localeCode})
									)
			</if>
			<if test="objetModelList != null and objetModelList.length > 0">
				AND A.SALES_CODE IN
				<foreach collection="objetModelList" item="c" index="idx" open=" (" separator="," close=")"><![CDATA[
													#{c}
												]]></foreach>
			</if>
			ORDER BY CS_DSP_SEQ
	</select>
	
	<select id="retrieveCallCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveCallCategoryList */
		   DISTINCT
		   C.LV2_CATEGORY_CODE 
		  , D.SITE_CATEGORY_NM AS CATEGORY_NM
		  , COALESCE(E.STICKY_IMG_URL, '') 		AS STICKY_IMG_URL
		  , COALESCE(E.STICKY_HOVER_ICON_URL, '') AS STICKY_HOVER_ICON_URL
		  , D.CS_DSP_SEQ
		FROM   PDM_PRODUCT_SVC_D A
		JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID 
		JOIN DSP_DISPLAY_CATEGORY_M D
		  ON  D.SITE_CODE = C.SITE_CODE
		  AND D.CATEGORY_CODE = C.LV2_CATEGORY_CODE
		JOIN DSP_DISPLAY_CATEGORY_D E
		  ON  E.SITE_CODE = C.SITE_CODE
		  AND E.CATEGORY_CODE = D.CATEGORY_CODE
		  AND E.SHOP_CODE = 'D2C'
		WHERE 1 = 1
		AND A.USE_FLAG = 'Y'
			AND B.USE_FLAG = 'Y'
			AND C.USE_FLAG = 'Y'
		<choose>
		<when test='b2bUseFlag eq "Y"'>
			<choose>
			<when test='b2bDivisionUseFlag eq "Y"'>
				AND D.BIZ_TYPE_CODE = #{divisionBizType}
			</when>
			<otherwise>
				AND D.BIZ_TYPE_CODE IN ('B2C','B2B') 
			</otherwise>
			</choose>
		</when>
		<otherwise>
			AND D.BIZ_TYPE_CODE = 'B2C' 
		</otherwise>
		</choose>
		<if test="localeCode != null and localeCode != ''">
			AND B.SITE_CODE = #{localeCode}
			AND C.SITE_CODE = #{localeCode}
		</if>
		<if test="superCategoryId != null and superCategoryId != ''">
			AND C.LV1_CATEGORY_CODE = #{superCategoryId}
		</if>
		AND D.SUPP_USE_FLAG = 'Y'
		<if test="pageFlag == 'call' and callSubCateId != null and callSubCateId != ''">
			AND D.CATEGORY_CODE =  (SELECT T2.HIGH_LV_CATEGORY_CODE
									FROM DSP_DISPLAY_CATEGORY_M T2
									WHERE 1=1
									AND T2.CATEGORY_CODE = #{callSubCateId}
									AND T2.CATEGORY_LV_NO = 3
									AND T2.SITE_CODE = #{localeCode})
		</if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND A.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>
		ORDER BY D.CS_DSP_SEQ
	</select>
	
	<select id="retrieveCallSubCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveCallSubCategoryList */
				DISTINCT C.LV3_CATEGORY_CODE
			   , D.SITE_CATEGORY_NM AS CATEGORY_NM
			   , B.SALES_MODEL_FLAG
			   , D.SUPP_USE_FLAG 
			   , B.GSFS_USE_FLAG
			   , A.BUYER_MODEL_CODE
			   , COALESCE(E.STICKY_IMG_URL, '/content/dam/lge/common/common-icon/support-icons/icon-other_normal.svg') 		AS STICKY_IMG_URL
			   , COALESCE(E.STICKY_HOVER_ICON_URL, '/content/dam/lge/common/common-icon/support-icons/icon-other_active.svg') AS STICKY_HOVER_ICON_URL
			   , COALESCE(D.CS_DSP_SEQ,'999') AS CS_DSP_SEQ
		FROM   PDM_PRODUCT_SVC_D A
		JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
		JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID 
		JOIN DSP_DISPLAY_CATEGORY_M D
		  ON  D.SITE_CODE = C.SITE_CODE
		  AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
		JOIN DSP_DISPLAY_CATEGORY_D E
		  ON  E.SITE_CODE = C.SITE_CODE
		  AND E.CATEGORY_CODE = D.CATEGORY_CODE
		  AND E.SHOP_CODE = 'D2C'
		<if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
		JOIN SVD_CUSTOMER_MODEL_M E
		  ON E.SITE_CODE = B.SITE_CODE
		 AND E.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
		 AND E.CREATION_DATE >= #{euEcoCategoryDt}
		 AND E.USE_FLAG = 'Y'
		</if>
		WHERE 1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		AND B.GSFS_USE_FLAG = 'Y'
		AND B.SITE_CODE = #{localeCode}
		AND C.SITE_CODE = #{localeCode}
		AND C.LV2_CATEGORY_CODE = #{categoryId}
		AND C.LV3_CATEGORY_CODE IS NOT NULL
		<if test='ecoCategoryFlag == "Y" and categoryIdList!=null and categoryIdList.size()>0'>
			<foreach collection="categoryIdList" item="c" index="idx" open="AND C.LV2_CATEGORY_CODE IN (" separator="," close=")">
				#{c}
			</foreach>
		</if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND A.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>
		<if test="pageFlag == 'call' and callSubCateId != null and callSubCateId != ''">
			AND LV3_CATEGORY_CODE = #{callSubCateId}
		</if>
		ORDER BY D.CS_DSP_SEQ
	</select>
	
	
	
	<select id="retrieveGpSubCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpSubCategoryList */
		         DISTINCT LV3_CATEGORY_CODE 
		         ,CATEGORY_NM
	             ,GSFS_USE_FLAG
				 ,BUYER_MODEL_CODE
		         ,STICKY_IMG_URL
		         ,STICKY_HOVER_ICON_URL
	             ,CS_DSP_SEQ
		FROM     (
	                SELECT  C.LV3_CATEGORY_CODE
	                       ,D.SITE_CATEGORY_NM AS CATEGORY_NM
	                       ,B.SALES_MODEL_FLAG 
	                       ,D.SUPP_USE_FLAG	 AS SUPPORT_USE_FLAG 	 
	                       ,B.GSFS_USE_FLAG
						   ,A.BUYER_MODEL_CODE
	                       ,E.STICKY_IMG_URL
	                       ,E.STICKY_HOVER_ICON_URL
	                       ,D.CS_DSP_SEQ
	                FROM   PDM_PRODUCT_SVC_D A
					JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
					JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID
					JOIN DSP_DISPLAY_CATEGORY_M D
					  ON  D.SITE_CODE = C.SITE_CODE
					  AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
					JOIN DSP_DISPLAY_CATEGORY_D E
					  ON  E.SITE_CODE = C.SITE_CODE
					  AND E.CATEGORY_CODE = D.CATEGORY_CODE
					  AND E.SHOP_CODE = 'D2C'
	                <if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
					JOIN SVD_CUSTOMER_MODEL_M F
					  ON F.SITE_CODE = B.SITE_CODE
					 AND F.CUST_MODEL_CODE = A.BUYER_MODEL_CODE
					 AND F.CREATION_DATE >= #{euEcoCategoryDt}
					 AND F.USE_FLAG = 'Y'
					</if>
					WHERE 1=1
					AND    A.USE_FLAG = 'Y'
					AND    B.USE_FLAG = 'Y'
					AND    C.USE_FLAG = 'Y'
					AND    B.GSFS_USE_FLAG = 'Y'
					AND    B.SITE_CODE = #{localeCode}
					AND    C.SITE_CODE = #{localeCode}
					AND    D.SUPP_USE_FLAG= 'Y'
					AND    C.LV2_CATEGORY_CODE = #{categoryId}
					AND    C.LV3_CATEGORY_CODE IS NOT NULL
	                <if test='ecoCategoryFlag == "Y" and categoryIdList!=null and categoryIdList.size()>0'>
						<foreach collection="categoryIdList" item="c" index="idx" open="AND A.CS_CATEGORY_ID IN (" separator="," close=")"><![CDATA[
						    #{c}
						]]></foreach>
					</if>
					<if test='tabType == "W"'>
						AND EXISTS (SELECT 1
						          FROM SVD_WTY_INFO_M G
						         WHERE G.SITE_CODE = #{localeCode}
						           AND G.COUNTRY_CODE = #{countryCode}
						           AND G.OPEN_STATE_CODE = 'ONSERVICE'
						           AND G.LV1_CATEGORY_CODE = C.LV1_CATEGORY_CODE
						           AND G.USE_FLAG = 'Y') 
			        </if>
					<if test="objetModelList != null and objetModelList.length > 0">
						AND A.SALES_CODE IN
						<foreach collection="objetModelList" item="c" index="idx" open=" (" separator="," close=")"><![CDATA[
															#{c}
														]]></foreach>
					</if>
			        UNION ALL
			        SELECT   J.LV3_CATEGORY_CODE
	                       , IFNULL((SELECT L.MSG_CNTS 
							   FROM COM_MESSAGE_M L 
							   WHERE L.MSG_CODE = 'common-symptom-others' 
							   AND L.DSP_SVC_MSG_SP_CODE = 'SVC'
							   AND L.SHOP_CODE = 'D2C' 
							   AND L.LOCALE_CODE = #{javaLocaleCode} 
							   AND L.USE_FLAG = 'Y'), 'Others') AS CATEGORY_NM 
	                       , I.SALES_MODEL_FLAG
	                       , 'Y' AS SUPPORT_USE_FLAG			 
	                       , I.GSFS_USE_FLAG
						   , H.BUYER_MODEL_CODE
	                       , '/content/dam/lge/common/common-icon/support-icons/icon-other_normal.svg' AS STICKY_IMG_URL
	                       , '/content/dam/lge/common/common-icon/support-icons/icon-other_active.svg' AS STICKY_HOVER_ICON_URL
	                       , 9999 AS CS_ORDER
	                FROM   PDM_PRODUCT_SVC_D H
					JOIN SVD_PRODUCT_SVC_MODEL_D I ON H.SVD_SKU_ID = I.SVD_SKU_ID
					JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R J ON H.SVD_SKU_ID = J.SVD_SKU_ID
	                <if test="euEcoCategoryDt != null and euEcoCategoryDt != ''">
					JOIN SVD_CUSTOMER_MODEL_M F
					  ON F.SITE_CODE = I.SITE_CODE
					 AND F.CUST_MODEL_CODE = H.BUYER_MODEL_CODE
					 AND F.CREATION_DATE >= #{euEcoCategoryDt}
					 AND F.USE_FLAG = 'Y'
					</if>
					WHERE  1=1
					AND    H.USE_FLAG = 'Y'
					AND    I.USE_FLAG = 'Y'
					AND    J.USE_FLAG = 'Y'
					AND    I.GSFS_USE_FLAG = 'Y'
					AND    J.LV3_CATEGORY_CODE = 'Others'
					AND    I.SITE_CODE = #{localeCode}
					AND    J.SITE_CODE = #{localeCode}
					AND    J.LV2_CATEGORY_CODE = #{categoryId}
					AND    J.LV3_CATEGORY_CODE IS NOT NULL
	                <if test='ecoCategoryFlag == "Y"'>
						<foreach collection="categoryIdList" item="c" index="idx" open="AND J.LV2_CATEGORY_CODE IN (" separator="," close=")"><![CDATA[
						    #{c}
						]]></foreach>
					</if>
					<if test='tabType == "W"'>
						AND EXISTS (SELECT 1
						          FROM SVD_WTY_INFO_M K
						         WHERE K.SITE_CODE = #{localeCode}
						           AND K.COUNTRY_CODE = #{countryCode}
						           AND K.OPEN_STATE_CODE = 'ONSERVICE'
						           AND K.LV1_CATEGORY_CODE = J.LV1_CATEGORY_CODE
						           AND K.USE_FLAG = 'Y') 
			        </if>
					<if test="objetModelList != null and objetModelList.length > 0">
						AND H.SALES_CODE IN
						<foreach collection="objetModelList" item="c" index="idx" open=" (" separator="," close=")"><![CDATA[
															#{c}
														]]></foreach>
					</if>
					) T
	                WHERE 1=1
					<if test='localeCode != "BR" and (localeCode != "EG_EN" and localeCode != "EG_AR" and localeCode != "MX" and localeCode != "CO" and localeCode != "CL" and localeCode != "CAC" and localeCode != "PA" and localeCode != "PE"  and localeCode != "EC") and pageFlag == "repair" and ecoCategoryFlag != "Y"'>
					AND  EXISTS (SELECT 1
				                   FROM SVD_SERVICE_TYPE_R L
				                   WHERE L.SITE_CODE = #{localeCode}
				                   AND L.USE_FLAG = 'Y'
				                   AND L.LV2_CATEGORY_CODE = #{categoryId}
				                   AND (L.LV3_CATEGORY_CODE IS NULL OR L.LV3_CATEGORY_CODE = '')
				                   AND (L.CUST_MODEL_CODE IS NULL OR L.CUST_MODEL_CODE = '')
				                   union all
				                   SELECT 1
				                   FROM SVD_SERVICE_TYPE_R M
				                   WHERE M.SITE_CODE = #{localeCode}
				                   AND M.USE_FLAG = 'Y'
				                   AND M.LV2_CATEGORY_CODE = #{categoryId}
				                   AND M.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
				                   AND (M.CUST_MODEL_CODE IS NULL OR M.CUST_MODEL_CODE = '')
				                   union all
				                   SELECT 1
				                   FROM SVD_SERVICE_TYPE_R N
				                   WHERE N.SITE_CODE = #{localeCode}
				                   AND N.USE_FLAG = 'Y'
				                   AND N.LV2_CATEGORY_CODE = #{categoryId}
				                   AND N.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
				                   AND N.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
				                   union all
				                   SELECT 1
				                   FROM SVD_SERVICE_TYPE_R O
				                   WHERE O.SITE_CODE = #{localeCode}
				                   AND O.USE_FLAG = 'Y'
				                   AND O.LV2_CATEGORY_CODE = #{categoryId}
				                   AND (O.LV3_CATEGORY_CODE IS NULL OR O.LV3_CATEGORY_CODE = '')
				                   AND O.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
				                 )
					</if>
					<if test="localeCode == 'BR' and pageFlag == 'repair'">
					AND  EXISTS (SELECT 1
								   FROM SVD_SERVICE_TYPE_R P
								   WHERE P.SITE_CODE = #{localeCode}
								   AND P.USE_FLAG = 'Y'
								   AND P.LV2_CATEGORY_CODE = #{categoryId}
								   AND (P.LV3_CATEGORY_CODE IS NULL OR P.LV3_CATEGORY_CODE = '')
								   AND (P.CUST_MODEL_CODE IS NULL OR P.CUST_MODEL_CODE = '')
								   AND (P.ONSITE_USE_FLAG = 'Y' OR P.MC_COLLECT_FLAG = 'Y')
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R Q
								   WHERE Q.SITE_CODE = #{localeCode}
								   AND Q.USE_FLAG = 'Y'
								   AND Q.LV2_CATEGORY_CODE = #{categoryId}
								   AND Q.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
								   AND (Q.CUST_MODEL_CODE IS NULL OR Q.CUST_MODEL_CODE = '')
								   AND (Q.ONSITE_USE_FLAG = 'Y' OR Q.MC_COLLECT_FLAG = 'Y')
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R R
								   WHERE R.SITE_CODE = #{localeCode}
								   AND R.USE_FLAG = 'Y'
								   AND R.LV2_CATEGORY_CODE = #{categoryId}
								   AND R.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
								   AND R.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
								   AND (R.ONSITE_USE_FLAG = 'Y' OR R.MC_COLLECT_FLAG = 'Y')
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R S
								   WHERE S.SITE_CODE = #{localeCode}
								   AND S.USE_FLAG = 'Y'
								   AND S.LV2_CATEGORY_CODE = #{categoryId}
								   AND (S.LV3_CATEGORY_CODE IS NULL OR S.LV3_CATEGORY_CODE = '')
								   AND S.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
								   AND (S.ONSITE_USE_FLAG = 'Y' OR S.MC_COLLECT_FLAG = 'Y')
								 )
				</if>
				<if test="localeCode == 'BR' and pageFlag == 'carryin'">
					AND  EXISTS (SELECT 1
								   FROM SVD_SERVICE_TYPE_R U
								   WHERE U.SITE_CODE = #{localeCode}
								   AND U.USE_FLAG = 'Y'
								   AND U.LV2_CATEGORY_CODE = #{categoryId}
								   AND (U.LV3_CATEGORY_CODE IS NULL OR U.LV3_CATEGORY_CODE = '')
								   AND (U.CUST_MODEL_CODE IS NULL OR U.CUST_MODEL_CODE = '')
								   AND U.CARY_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R V
								   WHERE V.SITE_CODE = #{localeCode}
								   AND V.USE_FLAG = 'Y'
								   AND V.LV2_CATEGORY_CODE = #{categoryId}
								   AND V.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
								   AND (V.CUST_MODEL_CODE IS NULL OR V.CUST_MODEL_CODE = '')
								   AND V.CARY_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R W
								   WHERE W.SITE_CODE = #{localeCode}
								   AND W.USE_FLAG = 'Y'
								   AND W.LV2_CATEGORY_CODE = #{categoryId}
								   AND W.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
								   AND W.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
								   AND W.CARY_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R X
								   WHERE X.SITE_CODE = #{localeCode}
								   AND X.USE_FLAG = 'Y'
								   AND X.LV2_CATEGORY_CODE = #{categoryId}
								   AND (X.LV3_CATEGORY_CODE IS NULL OR X.LV3_CATEGORY_CODE = '')
								   AND X.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
								   AND X.CARY_USE_FLAG = 'Y'
								 )
				</if>
				<if test="(localeCode == 'BR' or localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC') and pageFlag == 'install'">
					AND  EXISTS (SELECT 1
								   FROM SVD_SERVICE_TYPE_R Y
								   WHERE Y.SITE_CODE = #{localeCode}
								   AND Y.USE_FLAG = 'Y'
								   AND Y.LV2_CATEGORY_CODE = #{categoryId}
								   AND (Y.LV3_CATEGORY_CODE IS NULL OR Y.LV3_CATEGORY_CODE = '')
								   AND (Y.CUST_MODEL_CODE IS NULL OR Y.CUST_MODEL_CODE = '')
								   AND Y.INSTALL_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R Z
								   WHERE Z.SITE_CODE = #{localeCode}
								   AND Z.USE_FLAG = 'Y'
								   AND Z.LV2_CATEGORY_CODE = #{categoryId}
								   AND Z.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
								   AND (Z.CUST_MODEL_CODE IS NULL OR Z.CUST_MODEL_CODE = '')
								   AND Z.INSTALL_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R AA
								   WHERE AA.SITE_CODE = #{localeCode}
								   AND AA.USE_FLAG = 'Y'
								   AND AA.LV2_CATEGORY_CODE = #{categoryId}
								   AND AA.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
								   AND AA.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
								   AND AA.INSTALL_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R BB
								   WHERE BB.SITE_CODE = #{localeCode}
								   AND BB.USE_FLAG = 'Y'
								   AND BB.LV2_CATEGORY_CODE = #{categoryId}
								   AND (BB.LV3_CATEGORY_CODE IS NULL OR BB.LV3_CATEGORY_CODE = '')
								   AND BB.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
								   AND BB.INSTALL_USE_FLAG = 'Y'
								 )
				</if>
				<!-- GP1SI-428 -->
				<if test="(localeCode == 'EG_EN' or localeCode == 'EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC') and pageFlag == 'repair'">
					AND  EXISTS (SELECT 1
								   FROM SVD_SERVICE_TYPE_R CC
								   WHERE CC.SITE_CODE = #{localeCode}
								   AND CC.USE_FLAG = 'Y'
								   AND CC.LV2_CATEGORY_CODE = #{categoryId}
								   AND (CC.LV3_CATEGORY_CODE IS NULL OR CC.LV3_CATEGORY_CODE = '')
								   AND (CC.CUST_MODEL_CODE IS NULL OR CC.CUST_MODEL_CODE = '')
								   AND CC.ONSITE_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R DD
								   WHERE DD.SITE_CODE = #{localeCode}
								   AND DD.USE_FLAG = 'Y'
								   AND DD.LV2_CATEGORY_CODE = #{categoryId}
								   AND DD.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
								   AND (DD.CUST_MODEL_CODE IS NULL OR DD.CUST_MODEL_CODE = '')
								   AND DD.ONSITE_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R EE
								   WHERE EE.SITE_CODE = #{localeCode}
								   AND EE.USE_FLAG = 'Y'
								   AND EE.LV2_CATEGORY_CODE = #{categoryId}
								   AND EE.LV3_CATEGORY_CODE = T.LV3_CATEGORY_CODE
								   AND EE.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
								   AND EE.ONSITE_USE_FLAG = 'Y'
								   union all
								   SELECT 1
								   FROM SVD_SERVICE_TYPE_R FF
								   WHERE FF.SITE_CODE = #{localeCode}
								   AND FF.USE_FLAG = 'Y'
								   AND FF.LV2_CATEGORY_CODE = #{categoryId}
								   AND (FF.LV3_CATEGORY_CODE IS NULL OR FF.LV3_CATEGORY_CODE = '')
								   AND FF.CUST_MODEL_CODE = T.BUYER_MODEL_CODE
								   AND FF.ONSITE_USE_FLAG = 'Y'
								 )
				</if>
					
			<if test="pageFlag == 'call' and callSubCateId != null and callSubCateId != ''">
				AND T.LV3_CATEGORY_CODE = #{callSubCateId}
			</if>
				GROUP BY T.LV3_CATEGORY_CODE
				ORDER BY T.CS_DSP_SEQ
	</select>
	
	
	
	<select id="retrieveGpServiceType" resultType="com.lge.d2x.domain.support.v1.model.GpServiceTypeResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpServiceTypeRequestVO">
		SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveGpServiceType */
		         SHIPIN_USE_FLAG
		       , ONSITE_USE_FLAG
		       , CARRYIN_USE_FLAG
		       , INSTALLATION_USE_FLAG
		       , MAINTENANCE_USE_FLAG
		       , MC_COLLECT_FLAG
		       , SA.ORDER_NO
		FROM   (SELECT SHIPIN_USE_FLAG
		               , ONSITE_USE_FLAG
		               , CARY_USE_FLAG as CARRYIN_USE_FLAG
		               , INSTALL_USE_FLAG as INSTALLATION_USE_FLAG
					   , MAINTENANCE_USE_FLAG
		               , MC_COLLECT_FLAG
		               , '1' AS ORDER_NO
		        FROM   SVD_SERVICE_TYPE_R TA
		        WHERE  TA.CUST_MODEL_CODE = (SELECT DISTINCT A.BUYER_MODEL_CODE FROM PDM_PRODUCT_SVC_D A
															JOIN SVD_PRODUCT_SVC_MODEL_D B 
															 ON A.SVD_SKU_ID = B.SVD_SKU_ID 
															 AND B.SITE_CODE = #{localeCode}
															WHERE (A.BUYER_MODEL_CODE = #{customerModel}
															OR B.SALES_CODE = #{customerModel})
															AND A.USE_FLAG = 'Y'
															AND B.USE_FLAG = 'Y'
															LIMIT 1
														)
		               AND TA.USE_FLAG = 'Y'
		               AND TA.SITE_CODE = #{localeCode}
		               <choose>
		               <when test='b2bUseFlag eq "Y"'>
		                   AND TA.BIZ_TYPE_CODE IN ('B2C', 'B2B')
		               </when>
		               <otherwise>
			               AND TA.BIZ_TYPE_CODE = #{bizType}
		               </otherwise>
		               </choose>
		               <!-- GP1SI-428 -->
		               <if test="localeCode == 'EG_EN' or localeCode =='EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'"> 
			               <if test="pageType == 'common'">
                                <!-- LGCOMCO-200 -->
                                <choose>
                                    <when test='localeCode == "CO"'>
                                        <![CDATA[
                                           AND (TA.ONSITE_USE_FLAG = 'Y' OR TA.MAINTENANCE_USE_FLAG = 'Y')
                                        ]]>
                                    </when>
                                    <otherwise>
                                        <![CDATA[
                                           AND TA.ONSITE_USE_FLAG = 'Y'
                                        ]]>
                                    </otherwise>
                                </choose>
						   </if> 
						    
						   <if test="pageType == 'install'"> 
								AND TA.INSTALL_USE_FLAG = 'Y'  
						   </if>	 
					   </if> 
		        UNION
		        SELECT SHIPIN_USE_FLAG
		               , ONSITE_USE_FLAG
		               , CARY_USE_FLAG as CARRYIN_USE_FLAG
		               , INSTALL_USE_FLAG as INSTALLATION_USE_FLAG
					   , MAINTENANCE_USE_FLAG
		               , MC_COLLECT_FLAG
		               , '2' AS ORDER_NO
		        FROM   SVD_SERVICE_TYPE_R TB
		        WHERE  1=1
		               <choose>
		               <when test="subCategoryId != null and subCategoryId != ''">
		                   AND TB.LV3_CATEGORY_CODE = #{subCategoryId}
		               </when>
		               <otherwise>
		                   AND TB.LV2_CATEGORY_CODE = #{categoryId}
		                   AND (TB.LV3_CATEGORY_CODE IS NULL OR TB.LV3_CATEGORY_CODE = '')
		               </otherwise>
		               </choose>
		               AND TB.USE_FLAG = 'Y'
		               AND TB.SITE_CODE = #{localeCode}
		               AND (TB.CUST_MODEL_CODE IS NULL OR TB.CUST_MODEL_CODE = '')
		               <choose>
		               <when test='b2bUseFlag eq "Y"'>
		                   AND TB.BIZ_TYPE_CODE IN ('B2C', 'B2B')
		               </when>
		               <otherwise>
			               AND TB.BIZ_TYPE_CODE = #{bizType}
		               </otherwise>
		               </choose>
		               <!-- GP1SI-428 -->
		               <if test="localeCode == 'EG_EN' or localeCode =='EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'">
			               <if test="pageType == 'common'">
						    	<!-- LGCOMCO-200 -->
                               <choose>
                                   <when test='localeCode == "CO"'>
                                       <![CDATA[
                                               AND (TB.ONSITE_USE_FLAG = 'Y' OR TB.MAINTENANCE_USE_FLAG = 'Y')
                                       ]]>
                                   </when>
                                   <otherwise>
                                       <![CDATA[
                                            AND TB.ONSITE_USE_FLAG = 'Y'
                                       ]]>
                                   </otherwise>
                               </choose>
						   </if> 
						    
						   <if test="pageType == 'install'"> 
								AND TB.INSTALL_USE_FLAG = 'Y'  
						   </if>	 
					   </if> 
		        UNION
		        SELECT SHIPIN_USE_FLAG
		               , ONSITE_USE_FLAG
		               , CARY_USE_FLAG as CARRYIN_USE_FLAG
		               , INSTALL_USE_FLAG as INSTALLATION_USE_FLAG
					   , MAINTENANCE_USE_FLAG
		               , MC_COLLECT_FLAG
		               , '3' AS ORDER_NO
		        FROM   SVD_SERVICE_TYPE_R TC
		        WHERE  TC.LV2_CATEGORY_CODE = #{categoryId}
		               AND (TC.LV3_CATEGORY_CODE IS NULL OR TC.LV3_CATEGORY_CODE = '')
		               AND (TC.CUST_MODEL_CODE IS NULL OR TC.CUST_MODEL_CODE = '')
		               AND TC.USE_FLAG = 'Y'
		               AND TC.SITE_CODE = #{localeCode}

		               AND TC.BIZ_TYPE_CODE = #{bizType}
		               <choose>
		               <when test='b2bUseFlag eq "Y"'>
		                   AND TC.BIZ_TYPE_CODE IN ('B2C', 'B2B')
		               </when>
		               <otherwise>
			               AND TC.BIZ_TYPE_CODE = #{bizType}
		               </otherwise>
		               </choose>
		               <!-- GP1SI-428 -->
		               <if test="localeCode == 'EG_EN' or localeCode =='EG_AR' or localeCode == 'MX' or localeCode == 'CO' or localeCode == 'CL' or localeCode == 'CAC' or localeCode == 'PA' or localeCode == 'PE' or localeCode == 'EC'">
			               <if test="pageType == 'common'">
                               <!-- LGCOMCO-200 -->
                               <choose>
                                   <when test='localeCode == "CO"'>
                                       <![CDATA[
                                               AND (TC.ONSITE_USE_FLAG = 'Y' OR TC.MAINTENANCE_USE_FLAG = 'Y')
                                       ]]>
                                   </when>
                                   <otherwise>
                                       <![CDATA[
                                            AND TC.ONSITE_USE_FLAG = 'Y'
                                       ]]>
                                   </otherwise>
                               </choose>
						   </if> 
						    
						   <if test="pageType == 'install'"> 
								AND TC.INSTALL_USE_FLAG = 'Y'  
						   </if>	 
					   </if> 
		       ) SA
		ORDER  BY SA.ORDER_NO
		LIMIT  1
    </select>
	
	<select id="retrieveTopFindCategoryList" parameterType="com.lge.d2x.domain.support.v1.model.FindModelNumberRequestVO" resultType="com.lge.d2x.domain.support.v1.model.FindModelNumberResponseVO">
	   SELECT DISTINCT /* com.lge.cst.api.findmymodel.service.FindMyModelService.retrieveTopFindCategoryList */
			  SUPER_CATEGORY_ID 
			, SUPER_CATEGORY_NAME
			, STICKY_IMAGE_ADDR
			, ICON_PATH_HOVER
			FROM (
				SELECT T1.CATEGORY_CODE AS SUPER_CATEGORY_ID
				     , T1.SITE_CATEGORY_NM AS SUPER_CATEGORY_NAME
				     , T1.HIGH_LV_CATEGORY_CODE AS HIGH_LEVEL_CATEGORY_ID
				     , T1.SUPP_USE_FLAG AS SUPPORT_USE_FLAG
				     , T1.CS_DSP_SEQ AS CS_ORDER
				     , T2.STICKY_IMG_URL AS STICKY_IMAGE_ADDR
				     , T2.STICKY_HOVER_ICON_URL AS ICON_PATH_HOVER
				FROM DSP_DISPLAY_CATEGORY_M T1
				JOIN DSP_DISPLAY_CATEGORY_D T2
					ON  (T1.SITE_CODE = T2.SITE_CODE
					AND T1.CATEGORY_CODE = T2.CATEGORY_CODE
					AND T2.SHOP_CODE = 'D2C')
				WHERE T1.CATEGORY_LV_NO = 1
				AND T1.SITE_CODE = #{localeCode}
				AND T1.SUPP_USE_FLAG = 'Y') A
			, SVD_FIND_MODEL_NUMBER B
		WHERE 1=1
			AND B.SITE_CODE = #{localeCode}
			AND B.USE_FLAG = 'Y'
		<choose>
		<when test="findType != null and findType != ''">
		<![CDATA[
			AND B.FIND_TYPE_CODE = #{findType}
		]]>
		</when>
		<otherwise>
		<![CDATA[
			AND B.FIND_TYPE_CODE = 'SN'
		]]>
		</otherwise>
		</choose>
		<if test='onlyMobileFlag == "Y"'>
			AND A.SUPER_CATEGORY_ID = #{mcSuperCategoryId}
		</if>
			AND A.SUPER_CATEGORY_ID = B.LV1_CATEGORY_CODE
		ORDER BY A.CS_ORDER
	</select>    
    
	<select id="retrieveFindCategoryList" parameterType="com.lge.d2x.domain.support.v1.model.FindModelNumberRequestVO" resultType="com.lge.d2x.domain.support.v1.model.FindModelNumberResponseVO">
	  <choose>
	     <when test="localeCode == 'IN' and  findCategory != '' and findCategory != null">
	      <![CDATA[
	      SELECT 'ALL' AS CATEGORY_ID  /* com.lge.cst.api.findmymodel.service.FindMyModelService.retrieveFindCategoryList */
	                 , 'ALL' AS CATEGORY_NAME
	                 , '/content/dam/lge/common/common-icon/support-icons/icon-other_normal.svg' AS STICKY_IMAGE_ADDR
	                 , '/content/dam/lge/common/common-icon/support-icons/icon-other_active.svg' AS ICON_PATH_HOVER
	                 , 1 AS CS_ORDER
	              FROM dual
	            UNION ALL
	      SELECT DISTINCT  CATEGORY_ID
		                 , CATEGORY_NAME
		                 , STICKY_IMAGE_ADDR
		                 , ICON_PATH_HOVER
		                 , 2 as CS_ORDER
	            ]]>
	     </when>
	     <otherwise>
	      <![CDATA[
	      SELECT DISTINCT  CATEGORY_ID  /* com.lge.cst.api.findmymodel.service.FindMyModelService.retrieveFindCategoryList */
		                 , CATEGORY_NAME
		                 , STICKY_IMAGE_ADDR
		                 , ICON_PATH_HOVER
		                 , CS_ORDER
	   ]]>
	     </otherwise>
	 </choose>
	      <![CDATA[
	              FROM (SELECT T1.CATEGORY_CODE AS CATEGORY_ID
	                         , T1.SITE_CATEGORY_NM AS CATEGORY_NAME
	 	   ]]>
	        <choose>
	        <when test="localeCode == 'IN' and findCategory != '' and findCategory != null">
	         ,#{findCategory} as HIGH_LEVEL_CATEGORY_ID
	        </when>
	        <otherwise>
	           ,#{productCategory} AS HIGH_LEVEL_CATEGORY_ID
	        </otherwise>
	    </choose>
	 <![CDATA[
	                         , T1.SUPP_USE_FLAG AS SUPPORT_USE_FLAG
	                         , T1.CS_DSP_SEQ AS CS_ORDER
	                         , T2.STICKY_IMG_URL AS STICKY_IMAGE_ADDR
	                         , T2.STICKY_HOVER_ICON_URL AS ICON_PATH_HOVER
                             , T1.SITE_CODE AS LOCALE_CODE
	                      FROM DSP_DISPLAY_CATEGORY_M T1
					      JOIN DSP_DISPLAY_CATEGORY_D T2
							ON (T1.SITE_CODE = T2.SITE_CODE
						   AND T1.CATEGORY_CODE = T2.CATEGORY_CODE
						   AND T2.SHOP_CODE = 'D2C')
	                     WHERE T1.HIGH_LV_CATEGORY_CODE IN (SELECT T1.CATEGORY_CODE AS CATEGORY_ID
	                                                           FROM DSP_DISPLAY_CATEGORY_M T1
														       JOIN DSP_DISPLAY_CATEGORY_D T2
																 ON (T1.SITE_CODE = T2.SITE_CODE
															    AND T1.CATEGORY_CODE = T2.CATEGORY_CODE
																AND T2.SHOP_CODE = 'D2C')
	 ]]>
	              <choose>
	                 <when test="localeCode == 'IN' and findCategory != '' and findCategory != null">
		                  WHERE T1.HIGH_LV_CATEGORY_CODE = #{findCategory}
		               		AND T1.CATEGORY_LV_NO = 3
		               		AND T1.SITE_CODE = #{localeCode})
	                 </when>
	                 <otherwise>
		                  WHERE T1.HIGH_LV_CATEGORY_CODE = #{highLevelCategoryId}
		                    AND T1.CATEGORY_LV_NO = 2
		                    AND T1.SITE_CODE = #{localeCode})
	                 </otherwise>
	             </choose>
	 <![CDATA[
	                       AND T1.SUPP_USE_FLAG = 'Y'
	                    UNION ALL
	                    SELECT T1.CATEGORY_CODE AS CATEGORY_ID
	                         , T1.SITE_CATEGORY_NM AS CATEGORY_NAME
	                         , T1.HIGH_LV_CATEGORY_CODE AS HIGH_LEVEL_CATEGORY_ID
	                         , T1.SUPP_USE_FLAG AS SUPPORT_USE_FLAG
	                         , T1.CS_DSP_SEQ AS CS_ORDER
	                         , T2.STICKY_IMG_URL AS STICKY_IMAGE_ADDR
	                         , T2.STICKY_HOVER_ICON_URL AS ICON_PATH_HOVER
                             , T1.SITE_CODE AS LOCALE_CODE
	                      FROM DSP_DISPLAY_CATEGORY_M T1
					      JOIN DSP_DISPLAY_CATEGORY_D T2
							ON  (T1.SITE_CODE = T2.SITE_CODE
						   AND T1.CATEGORY_CODE = T2.CATEGORY_CODE
						   AND T2.SHOP_CODE = 'D2C')
	 ]]>
	      <choose>
	       <when test="localeCode == 'IN' and findCategory != '' and findCategory != null">
	        WHERE T1.HIGH_LV_CATEGORY_CODE = #{findCategory}
	       </when>
	       <otherwise>
	          WHERE T1.HIGH_LV_CATEGORY_CODE = #{highLevelCategoryId}
	       </otherwise>
	   </choose>
	 <![CDATA[
	                       AND SUPP_USE_FLAG   = 'Y'
		                   AND T1.SITE_CODE = #{localeCode}) A
	                 , SVD_FIND_MODEL_NUMBER B
	       WHERE B.SITE_CODE = #{localeCode}
	         AND B.USE_FLAG = 'Y'
	 ]]>
	  <choose>
	    <when test="findType != null and findType != ''">
	    <![CDATA[
	         AND B.FIND_TYPE_CODE = #{findType}
	    ]]>
	    </when>
	    <otherwise>
	    <![CDATA[
	         AND B.FIND_TYPE_CODE = 'SN'
	    ]]>
	    </otherwise>
	  </choose>
	  <choose>
	    <when test="localeCode == 'IN' and findCategory != '' and findCategory != null">
	         AND A.HIGH_LEVEL_CATEGORY_ID = B.LV2_CATEGORY_CODE
	         AND A.CATEGORY_ID = B.LV3_CATEGORY_CODE
	         AND A.LOCALE_CODE = B.SITE_CODE
	    ORDER BY CS_ORDER, CATEGORY_NAME
	    </when>
	    <otherwise>
	         AND A.HIGH_LEVEL_CATEGORY_ID = B.LV1_CATEGORY_CODE
	         AND A.CATEGORY_ID = B.LV2_CATEGORY_CODE
	         AND A.LOCALE_CODE = B.SITE_CODE
	    ORDER BY A.CATEGORY_NAME
	    </otherwise>
	  </choose>
	</select>
	
	<select id="retrieveFindmymodelNumber" parameterType="com.lge.d2x.domain.support.v1.model.FindModelNumberRequestVO" resultType="com.lge.d2x.domain.support.v1.model.FindModelNumberResponseVO">
		<if test="localeCode == 'IN' and subCategoryId != null and subCategoryId != ''">
			SELECT * FROM (
		</if>
			<![CDATA[
			SELECT /* com.lge.cst.api.findmymodel.service.FindMyModelService.retrieveFindmymodelNumber */
			       A.SITE_CODE
			,      A.LV1_CATEGORY_CODE AS SUPER_CATEGORY_ID
			,      A.LV2_CATEGORY_CODE AS CATEGORY_ID
			,      A.FIND_MODEL_IMG_PATH_NM AS MODEL_IMAGE_PATH_NAME
			,      A.FIND_MODEL_DESC AS MODEL_NUMBER_DESC
			,      B.SITE_CATEGORY_NM AS CATEGORY_NAME
			,      A.FIND_MODEL_IMG_ALT_NM AS MODEL_IMAGE_ALT_NAME
			FROM   SVD_FIND_MODEL_NUMBER A
			JOIN   (
				SELECT T1.SITE_CODE
				     , T1.CATEGORY_CODE
				     , T1.SITE_CATEGORY_NM
				FROM DSP_DISPLAY_CATEGORY_M T1
				JOIN DSP_DISPLAY_CATEGORY_D T2
					ON  (T1.SITE_CODE = T2.SITE_CODE
					AND T1.CATEGORY_CODE = T2.CATEGORY_CODE
					AND T2.SHOP_CODE = 'D2C')
				WHERE 1=1
				AND T1.SITE_CODE = #{localeCode}
				AND T1.SUPP_USE_FLAG = 'Y') B
			ON     (A.LV2_CATEGORY_CODE   = B.CATEGORY_CODE
			AND    A.SITE_CODE = B.SITE_CODE)
			WHERE  A.SITE_CODE = #{localeCode}
		]]>
		<choose>
		  <when test="findType != null and findType != ''">
		  <![CDATA[
			AND    A.FIND_TYPE_CODE = #{findType}
		  ]]>
		  </when>
		  <otherwise>
		  <![CDATA[
			AND    A.FIND_TYPE_CODE = 'SN'
			]]>
		  </otherwise>
		</choose>
		<![CDATA[
			AND	   B.SITE_CODE = #{localeCode}
			AND    A.LV1_CATEGORY_CODE = #{superCategoryId}
			AND    A.LV2_CATEGORY_CODE = #{categoryId}
			AND    A.USE_FLAG  = 'Y'
			]]>
		<if test="localeCode == 'IN' and subCategoryId != null and subCategoryId != ''">
			AND A.LV3_CATEGORY_CODE = #{subCategoryId}
			ORDER BY IF(A.LV3_CATEGORY_CODE = 'ALL',2,1) ) Z
				 limit 1
		</if>
	</select>

	<select id="retrieveFindServiceCenterSuperCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveFindServiceCenterSuperCategoryList */
               DISTINCT C.LV1_CATEGORY_CODE AS CS_SUPER_CATEGORY_ID
             , C.LV1_CATEGORY_CODE AS CODE
             , D.SITE_CATEGORY_NM AS CS_SUPER_CATEGORY_NAME
             , D.SITE_CATEGORY_NM AS VALUE
             , IFNULL(E.STICKY_IMG_URL, '') AS STICKY_IMAGE_ADDR
             , NULL AS PICTOGRAM_IMAGE_ADDR
             , D.cs_dsp_seq AS DISPLAY_ORDER_NO
        FROM   PDM_PRODUCT_SVC_D A
        JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
        JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID
        JOIN DSP_DISPLAY_CATEGORY_M D
          ON  D.SITE_CODE = C.SITE_CODE
          AND D.CATEGORY_CODE = C.LV1_CATEGORY_CODE
        JOIN DSP_DISPLAY_CATEGORY_D E
          ON  E.SITE_CODE = C.SITE_CODE
          AND E.CATEGORY_CODE = D.CATEGORY_CODE
          AND E.SHOP_CODE = 'D2C'
        WHERE  1=1
        AND A.USE_FLAG = 'Y'
        AND B.USE_FLAG = 'Y'
        AND C.USE_FLAG = 'Y'
        AND D.SUPP_USE_FLAG = 'Y'
		<if test="localeCode != null and localeCode != ''">
		  AND B.SITE_CODE = #{localeCode}
		  AND C.SITE_CODE = #{localeCode}
		</if>
        ORDER BY D.cs_dsp_seq
	</select>

	<select id="retrieveFindServiceCenterCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveFindServiceCenterCategoryList */
		 			   DISTINCT F.LV2_CATEGORY_CODE AS CS_CATEGORY_ID
					 , F.LV2_CATEGORY_CODE AS CODE
			         , F.CATEGORY_NM AS CS_CATEGORY_NAME
			         , F.CATEGORY_NM AS VALUE
			         , F.DSP_SEQ AS CS_DISPLAY_ORDER_NO
			         , F.CS_DSP_SEQ AS CS_ORDER
                     , SERVICE_TYPE
		FROM (SELECT
				   DISTINCT C.LV2_CATEGORY_CODE
		         , D.SITE_CATEGORY_NM AS CATEGORY_NM
                 , C.LV3_CATEGORY_CODE
                 , A.BUYER_MODEL_CODE
		         , D.CS_DSP_SEQ
		         , D.DSP_SEQ
		         , K.STICKY_IMG_URL
                 , K.STICKY_HOVER_ICON_URL   
                 <if test ="pageFlag == 'locateRepairCenter'">
					, CASE WHEN C.LV2_CATEGORY_CODE IN
					<if test="mobileCategoryList != null">
						<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
						    #{item}
						</foreach>	
					</if>
					<if test="mobileCategoryList == null">('')</if> THEN 'MC'
					ELSE (SELECT IF(COUNT(*) = 0, 'CI', 'IH')
							   FROM SVD_SERVICE_TYPE_R B
							  WHERE B.SITE_CODE   = #{localeCode}
								AND B.ONSITE_USE_FLAG   = 'Y'
								AND B.USE_FLAG          = 'Y'
								AND B.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
								AND (B.LV3_CATEGORY_CODE IS NULL OR B.LV3_CATEGORY_CODE = ''))
				   END SERVICE_TYPE
				</if>
		FROM   PDM_PRODUCT_SVC_D A
	   JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
	   JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND B.SITE_CODE = C.SITE_CODE
	   JOIN DSP_DISPLAY_CATEGORY_M D
          ON  D.SITE_CODE = C.SITE_CODE
          AND D.CATEGORY_CODE = C.LV2_CATEGORY_CODE
       JOIN DSP_DISPLAY_CATEGORY_D K
          ON  K.SITE_CODE = C.SITE_CODE
          AND K.CATEGORY_CODE = D.CATEGORY_CODE
          AND K.SHOP_CODE = 'D2C'
		WHERE  1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		AND D.SUPP_USE_FLAG = 'Y'
		<if test="localeCode != null and localeCode != ''">
		  AND B.SITE_CODE = #{localeCode}
		  AND C.SITE_CODE = #{localeCode}
		</if>
		<if test="superCategoryId != null and superCategoryId != ''">
		  AND C.LV1_CATEGORY_CODE = #{superCategoryId}
		</if>
	) F  
	WHERE 1=1
	<if test="pageFlag == 'locateRepairCenter' and hiddenCategoryList != null">
		AND F.LV2_CATEGORY_CODE NOT IN
		<foreach item="item" index="index" collection="hiddenCategoryList" open="(" close=")" separator=",">
		    #{item}
		</foreach>
	</if>
		ORDER BY F.CS_DSP_SEQ
	</select>


	<select id="retrieveLocateRepairCenterCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveLocateRepairCenterCategoryList */
		 			   DISTINCT F.LV2_CATEGORY_CODE
			         , F.CATEGORY_NM
			         , F.CS_DSP_SEQ
		             , IFNULL(F.STICKY_IMG_URL, '') AS STICKY_IMG_URL
                     , IFNULL(F.STICKY_HOVER_ICON_URL, '') AS STICKY_HOVER_ICON_URL
                     , SERVICE_TYPE
		FROM (SELECT
				   DISTINCT C.LV2_CATEGORY_CODE
		         , D.SITE_CATEGORY_NM AS CATEGORY_NM
                 , C.LV3_CATEGORY_CODE
                 , A.BUYER_MODEL_CODE
		         , D.CS_DSP_SEQ
		         , K.STICKY_IMG_URL
                 , K.STICKY_HOVER_ICON_URL   
                 <if test ="pageFlag == 'locateRepairCenter'">
					, CASE WHEN 
					<if test='mobileCategoryList != null'>
						C.LV2_CATEGORY_CODE IN
						<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
						    #{item}
						</foreach>	
					</if>
					<if test="mobileCategoryList == null">('')</if> THEN 'MC'
					ELSE (SELECT IF(COUNT(*) = 0, 'CI', 'IH')
							   FROM SVD_SERVICE_TYPE_R B
							  WHERE B.SITE_CODE   = #{localeCode}
								AND B.ONSITE_USE_FLAG   = 'Y'
								AND B.USE_FLAG          = 'Y'
								AND B.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
								AND (B.LV3_CATEGORY_CODE IS NULL OR B.LV3_CATEGORY_CODE = ''))
				   END SERVICE_TYPE
				</if>
		FROM   PDM_PRODUCT_SVC_D A
	   JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
	   JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND B.SITE_CODE = C.SITE_CODE
	   JOIN DSP_DISPLAY_CATEGORY_M D
          ON  D.SITE_CODE = C.SITE_CODE
          AND D.CATEGORY_CODE = C.LV2_CATEGORY_CODE
       JOIN DSP_DISPLAY_CATEGORY_D K
          ON  K.SITE_CODE = C.SITE_CODE
          AND K.CATEGORY_CODE = D.CATEGORY_CODE
          AND K.SHOP_CODE = 'D2C'
		WHERE  1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		AND D.SUPP_USE_FLAG = 'Y'
		<if test="localeCode != null and localeCode != ''">
		  AND B.SITE_CODE = #{localeCode}
		  AND C.SITE_CODE = #{localeCode}
		</if>
	<if test="superCategoryId != null and superCategoryId != ''">
		AND C.LV1_CATEGORY_CODE = #{superCategoryId}
	</if>
	) F  
	WHERE 1=1
	<if test="pageFlag == 'locateRepairCenter' and hiddenCategoryList != null">
		AND F.LV2_CATEGORY_CODE NOT IN
		<foreach item="item" index="index" collection="hiddenCategoryList" open="(" close=")" separator=",">
		    #{item}
		</foreach>
	</if>
		ORDER BY F.CS_DSP_SEQ
	</select>


	<select id="retrieveLocateRepairCenterSubCategoryList" resultType="com.lge.d2x.domain.support.v1.model.GpCateInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.GpCateInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveLocateRepairCenterSubCategoryList */
		 			   DISTINCT F.LV2_CATEGORY_CODE
			         , F.CATEGORY_NM
			         , F.CS_DSP_SEQ
		             , IFNULL(F.STICKY_IMG_URL, '') AS STICKY_IMG_URL
                     , IFNULL(F.STICKY_HOVER_ICON_URL, '') AS STICKY_HOVER_ICON_URL
                     , SERVICE_TYPE
		FROM (SELECT
				   DISTINCT C.LV2_CATEGORY_CODE
		         , D.SITE_CATEGORY_NM AS CATEGORY_NM
                 , C.LV3_CATEGORY_CODE
                 , A.BUYER_MODEL_CODE
		         , D.CS_DSP_SEQ
		         , K.STICKY_IMG_URL
                 , K.STICKY_HOVER_ICON_URL   
                 <if test ="pageFlag == 'locateRepairCenter'">
					, CASE WHEN 
					<if test='mobileCategoryList != null'>
						AND C.LV2_CATEGORY_CODE NOT IN
						<foreach item="item" index="index" collection="mobileCategoryList" open="(" close=")" separator=",">
						    #{item}
						</foreach>	
					</if>
					<if test="mobileCategoryList == null">('')</if> THEN 'MC'
					ELSE (SELECT IF(COUNT(*) = 0, 'CI', 'IH')
							   FROM SVD_SERVICE_TYPE_R B
							  WHERE B.SITE_CODE   = #{localeCode}
								AND B.ONSITE_USE_FLAG   = 'Y'
								AND B.USE_FLAG          = 'Y'
								AND B.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
								AND (B.LV3_CATEGORY_CODE IS NULL OR B.LV3_CATEGORY_CODE = ''))
				   END SERVICE_TYPE
				</if>
		FROM   PDM_PRODUCT_SVC_D A
	   JOIN SVD_PRODUCT_SVC_MODEL_D B ON A.SVD_SKU_ID = B.SVD_SKU_ID
	   JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND B.SITE_CODE = C.SITE_CODE
	   JOIN DSP_DISPLAY_CATEGORY_M D
          ON  D.SITE_CODE = C.SITE_CODE
          AND D.CATEGORY_CODE = C.LV1_CATEGORY_CODE
       JOIN DSP_DISPLAY_CATEGORY_D K
          ON  K.SITE_CODE = C.SITE_CODE
          AND K.CATEGORY_CODE = D.CATEGORY_CODE
          AND K.SHOP_CODE = 'D2C'
		WHERE  1=1
		AND A.USE_FLAG = 'Y'
		AND B.USE_FLAG = 'Y'
		AND C.USE_FLAG = 'Y'
		AND D.SUPP_USE_FLAG = 'Y'
		<if test="localeCode != null and localeCode != ''">
		  AND B.SITE_CODE = #{localeCode}
		  AND C.SITE_CODE = #{localeCode}
		</if>
	<if test="superCategoryId != null and superCategoryId != ''">
		AND C.LV1_CATEGORY_CODE = #{superCategoryId}
	</if>
	) F  
	WHERE 1=1
	<if test="pageFlag == 'locateRepairCenter' and hiddenCategoryList != null">
		AND F.LV2_CATEGORY_CODE NOT IN
		<foreach item="item" index="index" collection="hiddenCategoryList" open="(" close=")" separator=",">
		    #{item}
		</foreach>
	</if>
		ORDER BY F.CS_DSP_SEQ
	</select>
	
	<select id="retrieveRepairCityListBR" resultType="com.lge.d2x.domain.support.v1.model.AscInfoResponseVO" parameterType="com.lge.d2x.domain.support.v1.model.AscInfoRequestVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.support.v1.service.SupportService.retrieveRepairCityListBR */
			   DISTINCT A.ASC_CITY_NM
		  FROM SVD_ASC_M A
		 WHERE A.USE_FLAG = 'Y'
	<choose>
	<when test="siteCode == 'CAC' or siteCode == 'PA' or siteCode == 'PE'">
	<![CDATA[
		   AND A.COUNTRY_CODE in (SELECT COUNTRY_CODE FROM COM_SVD_PREFERENCE_M
								   WHERE SITE_CODE = #{siteCode}
								     AND USE_FLAG='Y')
		   AND A.COUNTRY_CODE = #{state}
	]]>
	</when>
	<when test="siteCode == 'AR' ">
	<![CDATA[
		   AND A.COUNTRY_CODE = #{siteCode}
		   AND A.TEMP_ATTR6 = #{state}
	]]>
	</when>
	<when test="siteCode == 'CL' ">
	<![CDATA[
		   AND A.COUNTRY_CODE = #{siteCode}
	]]>
	</when>
	<otherwise>
	<![CDATA[
		   AND A.COUNTRY_CODE = #{siteCode}
		   AND A.ASC_PROVINCE_NM = #{state}
	]]>
	</otherwise>
	</choose>
	<![CDATA[
		   AND A.ASC_CITY_NM is not null
		 ORDER BY A.ASC_CITY_NM
	]]>
	</select>
	
	<select id="retrieveGpTypeOfInquiryList" resultType="com.lge.d2x.domain.support.v1.model.TypeOfInquiryProductResponseVO" parameterType="com.lge.d2xfrm.model.common.CachedComLocaleCodeVO">
		SELECT CODE , VALUE, SORT_NO, REQUIRED FROM( 
			SELECT 
					B.COMMON_CODE_VAL AS CODE ,
					M.MSG_CNTS AS VALUE,
					IF(M.LOCALE_CODE = 'cs_CZ',
					case
						when B.COMMON_CODE_VAL = '011' then '999'
					else case when s.MSG_CODE is not null
							then cast(s.MSG_CNTS as int)
							else B.SORT_SEQ
						end
				end,
				case when s.MSG_CODE is not null
					then cast(s.MSG_CNTS as int)
					else B.SORT_SEQ
				end) AS SORT_NO
				, R.MSG_CNTS as REQUIRED
		FROM COM_COMMON_CODE_D B
		JOIN COM_COMMON_CODE_M C ON C.COMMON_CODE = B.COMMON_CODE
		LEFT OUTER JOIN COM_MESSAGE_M M ON (M.MSG_CODE = B.CODE_VAL_NM AND M.LOCALE_CODE = #{localeCode})
		LEFT OUTER JOIN COM_MESSAGE_M S
					ON S.MSG_CODE = concat(B.CODE_VAL_NM,'.sort')
					AND S.LOCALE_CODE = #{localeCode}
					AND S.USE_FLAG = 'Y'
		LEFT OUTER JOIN COM_MESSAGE_M R
					ON R.MSG_CODE = concat(B.CODE_VAL_NM,'.required')
					AND R.LOCALE_CODE = #{localeCode}
					AND R.USE_FLAG = 'Y'
		WHERE C.COMMON_CODE_NM = 'CS03'
		AND B.COMMON_CODE_VAL != C.COMMON_CODE_NM
		AND TRIM(M.MSG_CNTS) IS NOT NULL
		AND M.USE_FLAG ='Y'
		) Z
		ORDER BY CAST(Z.SORT_NO AS unsigned), CODE
	</select>
</mapper>

