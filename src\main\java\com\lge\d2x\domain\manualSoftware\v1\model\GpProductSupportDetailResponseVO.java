package com.lge.d2x.domain.manualSoftware.v1.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class GpProductSupportDetailResponseVO {
    private String csSalesCode;
    private String salesModelCode;
    private String salesSuffixCode;
    private String csCustomerMappingModel;
    private String csMatchedModelCode;
    private String csMdmsProductCode;
    private String csSuperCategoryId;
    private String csSuperCategoryName;
    private String superStickyImageAddr;
    private String csCategoryId;
    private String csCategoryName;
    private String stickyImageAddr;
    private String csSubCategoryId;
    private String csSubCategoryName;
    private String subStickyImageAddr;
    private String csModelImagePath;
    private String csProductPageLinkStatus;
    private String csProductPageLink;
    private String csMobileModelFlag;
    private String customerModelCode;
    private String csLinkCustModelCode;
    private String csMatchedModelDisplay;
    private String gsfsFlag;
    private String MktPageLink;
    private String swTabOption;
    private String csCustomerModel;
    private String csSalesModelCode;
    private String orderNumber;
    private String modelReleaseDate;
    private String salesModelFlag;
    private String obuCode;
    private String userFriendlyName;
    private String lbrWtyPrdText;
    private String partWtyPrdText;
    private String localeCode;
}
