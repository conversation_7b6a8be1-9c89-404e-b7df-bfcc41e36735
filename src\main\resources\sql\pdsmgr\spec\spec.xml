<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.spec.v1.repository.pdsmgr.SpecRepository">
	<select id="selectProductStandardSpecs" parameterType="com.lge.d2x.domain.spec.v1.model.ProductStandardSpecRequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.ProductStandardSpecResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectProductStandardSpecs */
		       DISTINCT A.SKU_ID
		     , A.LOCALE_CODE
		     , D.SPEC_CODE AS lv1SpecCode
		     , G.SPEC_LOCAL_NM AS lv1SpecLocalNm
		     , D.SPEC_NM AS lv1SpecNm
		     , C.SPEC_CODE AS lv2SpecCode
		     , F.SPEC_LOCAL_NM AS lv2SpecLocalNm
		     , C.SPEC_NM AS lv2SpecNm
		     , IF(A.SPEC_VAL = 'Definable Spec Only in LG.COM', H.SPEC_VAL, A.SPEC_VAL) AS specVal
		     , A.MDMS_PRDGRP_CODE AS lv3ProductCode
		     , J.LEVEL1_ORDER_NO AS lv1SpecExpSeq
		     , H.DSP_SEQ
		  FROM PDM_PRODUCT_SPEC_R A
		 INNER JOIN DSP_PRODUCT_PDR_SPEC_R H
		    ON H.PDP_ID = #{pdpId}
		   AND A.SPEC_CODE = H.SPEC_CODE
		   AND H.USE_FLAG = 'Y'
		 INNER JOIN (SELECT I.HIGH_SPEC_CODE
		                  , ROW_NUMBER() OVER (ORDER BY I.DISPLAY_ORDER_NO ASC) AS LEVEL1_ORDER_NO
		               FROM (SELECT HIGH_SPEC_CODE
		                          , MIN(DSP_SEQ) AS DISPLAY_ORDER_NO
		                       FROM DSP_PRODUCT_PDR_SPEC_R
		                      WHERE PDP_ID = #{pdpId}
		                        AND USE_FLAG = 'Y'
		                      GROUP BY HIGH_SPEC_CODE
		                      ORDER BY DISPLAY_ORDER_NO
		                    ) I
		            ) J
		    ON H.HIGH_SPEC_CODE = J.HIGH_SPEC_CODE
		 INNER JOIN PDM_PRODUCT_M B
		    ON A.SKU_ID = B.SKU_ID
		   AND B.USE_FLAG = 'Y'
		 INNER JOIN PDM_SPEC_M C
		    ON A.SPEC_CODE = C.SPEC_CODE
		   AND C.SPEC_LV_NO = 2
		   AND C.USE_FLAG = 'Y'
		 INNER JOIN PDM_SPEC_M D
		    ON C.HIGH_SPEC_CODE = D.SPEC_CODE
		   AND D.SPEC_LV_NO = 1
		   AND D.USE_FLAG = 'Y'
		 INNER JOIN PDM_MDMS_SPEC_D E
		    ON A.SPEC_CODE = E.SPEC_CODE
		   AND A.MDMS_PRDGRP_CODE = E.MDMS_PRDGRP_CODE
		  LEFT OUTER JOIN PDM_SPEC_LANG_D F
		    ON A.SPEC_CODE = F.SPEC_CODE
		   AND A.LOCALE_CODE = F.LOCALE_CODE
		   AND F.USE_FLAG = 'Y'
		  LEFT OUTER JOIN PDM_SPEC_LANG_D G
		    ON D.SPEC_CODE = G.SPEC_CODE
		   AND A.LOCALE_CODE = G.LOCALE_CODE
		   AND G.USE_FLAG = 'Y'
		 WHERE A.LOCALE_CODE = #{localeCode}
		   AND A.SKU_ID = #{skuId}
		   AND A.USE_FLAG = 'Y'
		 ORDER BY lv1SpecExpSeq, H.DSP_SEQ
	</select>

	<select id="selectProductLgcomSpecs" parameterType="com.lge.d2x.domain.spec.v1.model.ProductLgcomSpecRequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.ProductLgcomSpecResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectProductLgcomSpecs */
		       A.PDP_ID
		     , A.SITE_CODE
		     , E.SPEC_ID AS lv1SpecCode
		     , E.SPEC_NM AS lv1SpecNm
		     , C.SPEC_ID AS lv2SpecCode
		     , C.SPEC_NM AS lv2SpecNm
		     , D.SPEC_VAL
		     , G.LV1_SPEC_ORDER_NO AS lv1SpecExpSeq
		     , A.DSP_SEQ AS lv3SpecExpSeq
		  FROM DSP_PRODUCT_SPEC_R A
		 INNER JOIN DSP_PDP_M B
		    ON A.PDP_ID = B.PDP_ID
		   AND A.SITE_CODE = B.SITE_CODE
		   AND A.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
		   AND B.USE_FLAG = 'Y'
		 INNER JOIN DSP_SPEC_M C
		    ON A.SITE_CODE = C.SITE_CODE
		   AND A.SPEC_ID = C.SPEC_ID
		   AND A.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		   AND C.USE_FLAG = 'Y'
		 INNER JOIN DSP_SPEC_D D
		    ON A.SITE_CODE = D.SITE_CODE
		   AND A.SPEC_ID = D.SPEC_ID
		   AND A.SPEC_VAL_ID = D.SPEC_VAL_ID
		   AND A.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
		   AND D.USE_FLAG = 'Y'
		 INNER JOIN DSP_SPEC_M E
		    ON A.SITE_CODE = E.SITE_CODE
		   AND C.HIGH_LV_SPEC_ID = E.SPEC_ID
		   AND A.BIZ_TYPE_CODE = E.BIZ_TYPE_CODE
		   AND E.USE_FLAG = 'Y'
		 INNER JOIN (SELECT MIN(F.DSP_SEQ) AS LV1_SPEC_ORDER_NO
		                  , F.HIGH_SPEC_ID AS LV1_SPEC_ID
		               FROM DSP_PRODUCT_SPEC_R F
		              WHERE F.USE_FLAG = 'Y'
		                AND F.SITE_CODE = #{siteCode}
		                AND F.PDP_ID = #{pdpId}
		           GROUP BY F.HIGH_SPEC_ID) AS G
		    ON E.SPEC_ID = G.LV1_SPEC_ID
		 WHERE A.USE_FLAG = 'Y'
		   AND A.SITE_CODE = #{siteCode}
		   AND A.PDP_ID = #{pdpId}
		 ORDER BY G.LV1_SPEC_ORDER_NO /* 1레벨 정렬순 */
		        , G.LV1_SPEC_ID /* 1레벨 동률일 경우 상위 스펙코드 우선 */
		        , A.DSP_SEQ /* 3레벨 정렬순 */
	</select>

	<select id="selectKeySpecs" parameterType="com.lge.d2x.domain.spec.v1.model.KeySpecRequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.KeySpecResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectKeySpecs */
		       DISTINCT CASE WHEN E.SPEC_LOCAL_NM IS NOT NULL THEN E.SPEC_LOCAL_NM ELSE B.SPEC_NM END AS specNm
		     , A.SPEC_VAL AS specVal
		     , C.DSP_SEQ AS dspSeq
			 , E.SPEC_DEFAULT_NM AS engSpecName
		  FROM PDM_PRODUCT_SPEC_R A
		 INNER JOIN PDM_SPEC_M B
		    ON A.SPEC_CODE = B.SPEC_CODE
		   AND B.USE_FLAG = 'Y'
		 INNER JOIN PDM_KEYSPEC_D C
		    ON C.SPEC_CODE = B.SPEC_CODE
		   AND C.SITE_CODE = #{siteCode}
		   AND C.USE_FLAG = 'Y'
		 INNER JOIN PDM_PRODUCT_M D
		    ON D.SKU_ID = A.SKU_ID
		   AND D.USE_FLAG = 'Y'
		   AND D.LV2_PRODUCT_CODE = C.LV2_PRODUCT_CODE
		<if test='lv3ProductCodeNotUseFlag == "N"'>
		   AND D.LV3_PRODUCT_CODE = C.LV3_PRODUCT_CODE
		</if>
		 INNER JOIN DSP_PRODUCT_PDR_SPEC_R F
		    ON C.SITE_CODE = F.SITE_CODE
		   AND A.SPEC_CODE = F.SPEC_CODE
		   AND F.PDP_ID = #{pdpId}
		   AND F.USE_FLAG = 'Y'
		  LEFT JOIN PDM_SPEC_LANG_D E
		    ON E.SPEC_LV_NO = 2
		   AND E.SPEC_CODE = B.SPEC_CODE
		   AND E.LOCALE_CODE = A.LOCALE_CODE
		 WHERE A.SKU_ID = #{skuId}
		   AND A.LOCALE_CODE = #{localeCode}
		   AND A.USE_FLAG = 'Y'
		 ORDER BY C.DSP_SEQ
	</select>

	<select id="selectProductSpecDms" parameterType="com.lge.d2x.domain.spec.v1.model.ProductSpecDmsRequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.ProductSpecDmsResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectProductSpecDms */
		       A.SPEC_DMS_ID
		     , A.PDP_ID
		     , A.SITE_CODE
		     , A.BIZ_TYPE_CODE
		     , A.DMS_TITLE
		     , A.DMS_CNTS
		     , B.ALT_TEXT_CNTS
		     , B.COMMENT_CNTS
		     , B.IMG_PATH_URL
		     , B.IMG_WIDTH_VAL
		     , B.IMG_HGT_VAL
		     , B.MDMS_PRDGRP_CODE
		     , B.MDMS_PRDGRP_LV_NO
		     , B.MOBL_IMG_PATH
		  FROM DSP_PRODUCT_SPEC_DMS_D A
		 INNER JOIN DSP_SPEC_DMS_M B
		    ON B.SPEC_DMS_ID = A.SPEC_DMS_ID
		   AND B.SITE_CODE = A.SITE_CODE
		   AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		   AND B.USE_FLAG = 'Y'
		 WHERE A.SITE_CODE = #{siteCode}
		   AND A.PDP_ID = #{pdpId}
		   AND A.USE_FLAG = 'Y'
		 ORDER BY B.LAST_UPDATE_DATE DESC
		 LIMIT 1
	</select>

	<select id="selectProductSpecsSmr" parameterType="com.lge.d2x.domain.spec.v1.model.ProductSpecSmrRequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.ProductSpecSmrResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectProductSpecsSmr */
		       A.SPEC_SMR_ID
		     , A.PDP_ID
		     , A.SITE_CODE
		     , A.BIZ_TYPE_CODE
		     , A.SPEC_SMR_NM
		     , A.SPEC_SMR_VAL
		     , A.SPEC_CODE
		     , A.SMR_INPUT_TYPE_CODE
		     , A.DSP_SEQ
		     , A.PRODUCT_CODE
		     , A.PRODUCT_CODE_LV_NO
		     , A.PDR_SMR_FLAG
		     , B.SPEC_SMR_NM as SPEC_SMR_NM_M
		  FROM DSP_PRODUCT_SPEC_SMR_R A
		 INNER JOIN DSP_SPEC_SMR_M B
		    ON B.SITE_CODE = A.SITE_CODE
		   AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		   AND B.SPEC_SMR_ID = A.SPEC_SMR_ID
		   AND B.USE_FLAG = 'Y'
		 WHERE A.PDP_ID = #{pdpId}
		   AND A.SITE_CODE = #{siteCode}
		   AND A.USE_FLAG = 'Y'
		 ORDER BY A.PDP_ID ASC, B.EXP_SEQ ASC
	</select>

	<select id="selectDefaultKeySpecList" parameterType="com.lge.d2x.domain.spec.v1.model.DefaultKeySpecRequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.DefaultKeySpecResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectDefaultKeySpecList */
		       DISTINCT CASE WHEN E.SPEC_LOCAL_NM IS NOT NULL
		       				 THEN E.SPEC_LOCAL_NM
		       				 ELSE B.SPEC_NM
		       			 END AS specNm
		     , CASE WHEN LEFT(C.LV2_PRODUCT_CODE, 2) IN ('AV','AO')
		     		THEN NULL
		          	ELSE LEFT(C.LV2_PRODUCT_CODE, 2)
		        END AS lv2ProductCode
		     , C.DSP_SEQ AS dspSeq
		  FROM PDM_PRODUCT_SPEC_R A
		 INNER JOIN PDM_SPEC_M B
		    ON A.SPEC_CODE = B.SPEC_CODE
		   AND B.USE_FLAG = 'Y'
		 INNER JOIN PDM_KEYSPEC_D C
		    ON C.SPEC_CODE = B.SPEC_CODE
		   AND C.SITE_CODE = #{siteCode}
		   AND C.USE_FLAG = 'Y'
		 INNER JOIN PDM_PRODUCT_M D
		    ON D.SKU_ID = A.SKU_ID
		   AND D.USE_FLAG = 'Y'
		   AND D.LV2_PRODUCT_CODE = C.LV2_PRODUCT_CODE
		<if test='lv3ProductCodeNotUseFlag == "N"'>
		   AND D.LV3_PRODUCT_CODE = C.LV3_PRODUCT_CODE
		</if>
		  LEFT JOIN PDM_SPEC_LANG_D E
		    ON E.SPEC_LV_NO = 2
		   AND E.SPEC_CODE = B.SPEC_CODE
		   AND E.LOCALE_CODE = A.LOCALE_CODE
		 WHERE A.LOCALE_CODE = #{localeCode}
		   AND A.USE_FLAG = 'Y'
		 <if test="skuIdList != null">
		   AND A.SKU_ID IN <foreach item="item" index="index" collection="skuIdList" open="(" separator="," close=")">
                 #{item}
             </foreach>
		 </if>
		 ORDER BY C.LV2_PRODUCT_CODE, C.DSP_SEQ
	</select>

	<select id="selectComparePdrSpecSchemaPart2" parameterType="com.lge.d2x.domain.spec.v1.model.PdrSpecSchemaPart2RequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.PdrSpecSchemaPart2ResponseVO">
		WITH RECURSIVE SPEC_SCHEMA (
			  SPEC_ID
			, SPEC_LEVEL_NO
			, SPEC_NAME
			, HIGH_LEVEL_SPEC_ID
			, DISPLAY_ORDER_NO
			, SPEC_VALUE_NAME
			)
		AS (
				SELECT /* system.pds.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectComparePdrSpecSchemaPart2 */
				       A.SPEC_CODE AS SPEC_ID
					 , A.SPEC_LV_NO AS SPEC_LEVEL_NO
					 , H.SPEC_LOCAL_NM AS SPEC_NAME
					 , A.SPEC_CODE AS HIGH_LEVEL_SPEC_ID
					 , 0 AS DISPLAY_ORDER_NO
					 , C.SPEC_VAL AS SPEC_VALUE_NAME
				  FROM PDM_SPEC_M A
				  LEFT OUTER JOIN (
						SELECT B.SPEC_CODE
							 , B.SPEC_VAL
						  FROM PDM_PRODUCT_SPEC_R B
						 WHERE B.LOCALE_CODE = #{localeCode}
						   AND B.USE_FLAG = 'Y'
				           AND B.SKU_ID = #{skuId}
						) C
				    ON C.SPEC_CODE = A.SPEC_CODE
				  LEFT OUTER JOIN PDM_SPEC_LANG_D H
				    ON H.SPEC_CODE = A.SPEC_CODE
				   AND H.LOCALE_CODE = #{localeCode}
				   AND H.USE_FLAG = 'Y'
				 WHERE A.USE_FLAG = 'Y'
				   AND A.SPEC_CODE = #{specCode}
				 UNION
				SELECT G.SPEC_ID
					 , G.SPEC_LEVEL_NO
					 , G.SPEC_NAME
					 , SPEC_SCHM.HIGH_LEVEL_SPEC_ID
					 , G.DISPLAY_ORDER_NO
					 , G.SPEC_VALUE_NAME
				FROM SPEC_SCHEMA SPEC_SCHM
				INNER JOIN (
							SELECT
							       D.SPEC_CODE AS SPEC_ID
							     , D.SPEC_LV_NO AS SPEC_LEVEL_NO
							     , F.SPEC_LOCAL_NM AS SPEC_NAME
							     , E.SPEC_CODE AS HIGH_LEVEL_SPEC_ID
							     , IFNULL(D.SCREEN_EXP_SEQ, ROW_NUMBER() OVER()) AS DISPLAY_ORDER_NO
								 , C.SPEC_VAL AS SPEC_VALUE_NAME
							  FROM PDM_PRODUCT_SPEC_R C
							 INNER JOIN PDM_SPEC_M D
								ON D.USE_FLAG = 'Y'
							   AND D.SPEC_CODE = C.SPEC_CODE
							 INNER JOIN PDM_SPEC_M E
						        ON E.USE_FLAG = 'Y'
						       AND E.SPEC_CODE = D.HIGH_SPEC_CODE
						      LEFT OUTER JOIN PDM_SPEC_LANG_D F
						        ON F.SPEC_CODE = C.SPEC_CODE
						       AND F.LOCALE_CODE = C.LOCALE_CODE
						       AND F.USE_FLAG = 'Y'
							 WHERE C.USE_FLAG = 'Y'
						       AND C.LOCALE_CODE = #{localeCode}
						       AND C.SKU_ID = #{skuId}
						       AND E.SPEC_CODE = #{specCode}
							) G
					ON SPEC_SCHM.SPEC_ID = G.HIGH_LEVEL_SPEC_ID
					)
				SELECT * FROM SPEC_SCHEMA
				ORDER BY DISPLAY_ORDER_NO
	</select>

	<select id="selectCompareSpecSchemaPart2" parameterType="com.lge.d2x.domain.spec.v1.model.SpecSchemaPart2RequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.SpecSchemaPart2ResponseVO">
		WITH RECURSIVE SPEC_SCHEMA (
			  SPEC_ID
			, SPEC_LEVEL_NO
			, SPEC_NAME
			, HIGH_LEVEL_SPEC_ID
			, DISPLAY_ORDER_NO
			, SPEC_VALUE_NAME
			, SPEC_DESC_TEXT
			, SPEC_DESC_LINK_FLAG
			, SPEC_DESC_LINK_URL
			, SPEC_DESC_LINK_TARGET
			, SPEC_DESC_LINK_ALT
			)
		AS (
				SELECT /* system.pds.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectCompareSpecSchemaPart2 */
				       A.SPEC_ID
					 , A.SPEC_LV_NO AS SPEC_LEVEL_NO
					 , A.SPEC_NM AS SPEC_NAME
					 , A.SPEC_ID AS HIGH_LEVEL_SPEC_ID
					 , 0 AS DISPLAY_ORDER_NO
					 , C.SPEC_VAL AS SPEC_VALUE_NAME
					 , A.DESC_TEXT_CNTS AS SPEC_DESC_TEXT
					 , IFNULL(A.DESC_LINK_FLAG, 'N') AS DESC_LINK_FLAG
					 , A.DESC_LINK_URL
					 , A.DESC_LINK_SBJ_CODE AS DESC_LINK_TARGET
					 , A.DESC_LINK_ALT_TEXT_CNTS AS DESC_LINK_ALT
				  FROM DSP_SPEC_M A
				  LEFT OUTER JOIN (
						SELECT B.SPEC_ID
							 , B.SPEC_VAL
						  FROM DSP_PRODUCT_SPEC_R B
						 WHERE B.SITE_CODE = #{siteCode}
						   AND B.USE_FLAG = 'Y'
				           AND B.PDP_ID = #{pdpId}
						) C
				    ON C.SPEC_ID = A.SPEC_ID
				 WHERE A.USE_FLAG = 'Y'
				   AND A.SPEC_ID = #{specId}
				UNION
				SELECT F.SPEC_ID
					 , F.SPEC_LEVEL_NO
					 , F.SPEC_NAME
					 , SPEC_SCHM.HIGH_LEVEL_SPEC_ID
					 , F.DISPLAY_ORDER_NO
					 , F.SPEC_VALUE_NAME
					 , F.DESC_TEXT
					 , F.DESC_LINK_FLAG
					 , F.DESC_LINK_URL
					 , F.DESC_LINK_TARGET
					 , F.DESC_LINK_ALT
				FROM SPEC_SCHEMA SPEC_SCHM
				INNER JOIN (
							SELECT
							       D.SPEC_ID
							     , D.SPEC_LV_NO AS SPEC_LEVEL_NO
							     , D.SPEC_NM AS SPEC_NAME
							     , E.SPEC_ID AS HIGH_LEVEL_SPEC_ID
							     , IFNULL(D.EXP_SEQ, ROW_NUMBER() OVER()) AS DISPLAY_ORDER_NO
								 , C.SPEC_VAL AS SPEC_VALUE_NAME
								 , IFNULL(D.DESC_LINK_FLAG, 'N') AS DESC_LINK_FLAG
								 , D.DESC_TEXT_CNTS AS DESC_TEXT
								 , D.DESC_LINK_URL
								 , D.DESC_LINK_SBJ_CODE AS DESC_LINK_TARGET
								 , D.DESC_LINK_ALT_TEXT_CNTS AS DESC_LINK_ALT
							  FROM DSP_PRODUCT_SPEC_R C
							 INNER JOIN DSP_SPEC_M D
								ON D.USE_FLAG = 'Y'
							   AND D.SPEC_ID = C.SPEC_ID
							 INNER JOIN DSP_SPEC_M E
						        ON E.USE_FLAG = 'Y'
						       AND E.SPEC_ID = D.HIGH_LV_SPEC_ID
							 WHERE C.USE_FLAG = 'Y'
						       AND C.SITE_CODE = #{siteCode}
						       AND C.PDP_ID = #{pdpId}
						       AND E.SPEC_ID = #{specId}
							) F
					ON SPEC_SCHM.SPEC_ID = F.HIGH_LEVEL_SPEC_ID
			)
		SELECT * FROM SPEC_SCHEMA
		ORDER BY DISPLAY_ORDER_NO
	</select>

	<select id="selectMtsProductLgcomSpecs" parameterType="com.lge.d2x.domain.spec.v1.model.MtsProductLgcomSpecRequestVO"
		resultType="com.lge.d2x.domain.spec.v1.model.ProductLgcomSpecResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.spec.v1.service.SpecService.selectMtsProductLgcomSpecs */
		       A.PDP_ID
		     , A.SITE_CODE
		     , F.SPEC_ID AS lv1SpecCode
		     , F.SPEC_NM AS lv1SpecNm
		     , D.SPEC_ID AS lv2SpecCode
		     , D.SPEC_NM AS lv2SpecNm
		     , E.SPEC_VAL
		     , H.LV1_SPEC_ORDER_NO AS lv1SpecExpSeq
		     , A.DSP_SEQ AS lv3SpecExpSeq
		  FROM DSP_PRODUCT_SPEC_R A
		 INNER JOIN DSP_PDP_M B
		    ON B.PDP_ID = A.PDP_ID
		   AND B.SITE_CODE = A.SITE_CODE
		   AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		 INNER JOIN DSP_PDP_D C
		    ON C.PDP_ID = B.PDP_ID
		   AND C.SITE_CODE = B.SITE_CODE
		   AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
		   AND C.SHOP_CODE = #{shopCode}
		   AND C.AEM_PUBL_FLAG = 'Y'
		   AND C.USE_FLAG = 'Y'
		 INNER JOIN DSP_SPEC_M D
		    ON D.SITE_CODE = A.SITE_CODE
		   AND D.SPEC_ID = A.SPEC_ID
		   AND D.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		   AND D.USE_FLAG = 'Y'
		 INNER JOIN DSP_SPEC_D E
		    ON E.SITE_CODE = A.SITE_CODE
		   AND E.SPEC_ID = A.SPEC_ID
		   AND E.SPEC_VAL_ID = A.SPEC_VAL_ID
		   AND E.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		   AND E.USE_FLAG = 'Y'
		INNER JOIN DSP_SPEC_M F
		    ON F.SITE_CODE = A.SITE_CODE
		   AND F.SPEC_ID = D.HIGH_LV_SPEC_ID
		   AND F.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		   AND F.USE_FLAG = 'Y'
	    INNER JOIN (SELECT MIN(G.DSP_SEQ) AS LV1_SPEC_ORDER_NO
		                  , G.HIGH_SPEC_ID AS LV1_SPEC_ID
		               FROM DSP_PRODUCT_SPEC_R G
		              WHERE G.USE_FLAG = 'Y'
		                AND G.SITE_CODE = #{siteCode}
		                AND G.PDP_ID = #{pdpId}
		           GROUP BY G.HIGH_SPEC_ID) AS H
		    ON H.LV1_SPEC_ID = F.SPEC_ID
		 WHERE A.USE_FLAG = 'Y'
		   AND A.SITE_CODE = #{siteCode}
		   AND A.PDP_ID = #{pdpId}
		 ORDER BY H.LV1_SPEC_ORDER_NO /* 1레벨 정렬순 */
		        , H.LV1_SPEC_ID /* 1레벨 동률일 경우 상위 스펙코드 우선 */
		        , A.DSP_SEQ /* 3레벨 정렬순 */
	</select>
</mapper>
