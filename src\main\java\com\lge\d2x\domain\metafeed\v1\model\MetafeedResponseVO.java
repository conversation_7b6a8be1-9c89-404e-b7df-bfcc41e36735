package com.lge.d2x.domain.metafeed.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class MetafeedResponseVO {
    @Schema(description = "제품그룹 아이디")
    private String itemGroupId;

    @Schema(description = "아이디")
    private String id;

    @Schema(description = "제목")
    private String title;

    @Schema(description = "설명")
    private String description;

    @Schema(description = "경로")
    private String link;

    @Schema(description = "이미지 경로")
    private String imageLink;

    @Schema(description = "유통표준코드")
    private String gtin;

    @Schema(description = "MSRP")
    private String msrp;

    @Schema(description = "OBS 판매 여부")
    private String obsSellFlag;

    @Schema(description = "SKU")
    private String sku;

    @Schema(description = "브랜드")
    private String brand;

    @Schema(description = "상태")
    private String condition;

    @Schema(description = "구글 제품 카테고리")
    private String googleProductCategory;

    @Schema(description = "에너지효율 클래스")
    private String energyEfficiencyClass;

    @Schema(description = "색상")
    private String color;

    @Schema(description = "번들 여부")
    private String isBundle;

    @Schema(description = "재고 여부", defaultValue = "Out of stock")
    private String availability;

    @Schema(description = "가격", defaultValue = "")
    private String price;

    @Schema(description = "할인 가격", defaultValue = "")
    private String salePrice;

    @Schema(description = "최저 가격", defaultValue = "")
    private String cheaperPrice;

    @Schema(description = "통화")
    private String currency;

    @Schema(description = "할인 적용날짜")
    private String salesPriceEffectiveDate;

    @Schema(description = "배송비")
    private String shippingCost;

    @Schema(description = "Lv1 카테고리명")
    private String categoryLv1;

    @Schema(description = "Lv2 카테고리명")
    private String categoryLv2;

    @Schema(description = "Lv3 카테고리명")
    private String categoryLv3;

    @Schema(description = "Lv4 카테고리명")
    private String categoryLv4;
}
