package com.lge.d2x.domain.productList.v1.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductListModelVO {
    private String modelId;
    private String sku;
    private String modelName;
    private String modelType;
    private String bizType;
    private String brandType;
    private String modelUrlPath;
    private int totalCount;
    private String superCategoryId;
    private String categoryId;
    private String subCategoryId;
    private String categoryName;
    private String retailerPricingFlag;
    private String modelStatusCode;
    private String salesModelCode;
    private String salesSuffixCode;
    private String productLevel1Code;
    private String defaultProductTag;
    private String productTag1;
    private String productTag1BeginDay;
    private String productTag1EndDay;
    private String productTag2;
    private String productTag2BeginDay;
    private String productTag2EndDay;
    private String productTag1UserType;
    private String productTag2UserType;
    private String promotionText;
    private String imageAltText;
    private String mediumImageAddr;
    private String smallImageAddr;
    private String userFriendlyName;
    private String bundlePlpDisplayFlag;
    private String reviewType;
    private int pCount;
    private int ratingPercent;
    private int sRating;
    private BigDecimal sRating2;
    @JsonIgnore private BigDecimal sRating3;
    private String wtbUseFlag;
    private String obsSellFlag;
    private String ecommerceTarget;
    private BigDecimal msrp;
    private String productSupportFlag;
    private String wtbExternalLinkUseFlag;
    private String wtbExternalLinkName;
    private String wtbExternalLinkUrl;
    private String wtbExternalLinkSelfFlag;
    private String inquiryToBuyFlag;
    private String findTheDealerFlag;
    private String buyNowUrl;
    private String productSupportUrl;
    private String siblingGroupCode;
    private String seriesName;
    private String siblingCode;
    private String defaultSiblingModelFlag;
    private String plpHighlightModelFlag;
    private String siblingLocalValue;
    private String target;
    private String siblingType;
    private List<Map<String, Object>> siblingModels;
    private String buyNowUseFlag;
    private String energyLabel;
    private String energyLabelFileName;
    private String energyLabelOriginalName;
    private String productFicheFileName;
    private String productFicheOriginalName;
    private String energyLabelDocId;
    private String productFicheDocId;
    private String energyLabelImageAddr;
    private String energyLabelName;
    private String energyLabelCategory;
    private String buName1;
    private String buName2;
    private String buName3;
    private String superCategoryName;
    private String categoryEngName;
    private String signatureFlag;
    private String fEnergyLabelDocId;
    private String fEnergyLabelFileName;
    private String fEnergyLabelOriginalName;
    private String localeCode;
    private String modelYear;
    private String limitSaleUseFlag;
    private String promotionLinkUrl;
    private String externalLinkTarget;
    private String energyLabelproductLeve1Code;
    private String productFicheproductLeve1Code;
    private String fenergyLabelproductLeve1Code;
    private String washTowerFlag;
    private String secondEnergyLabel;
    private String secondEnergyLabelCategory;
    private String secondEnergyLabelDocId;
    private String secondEnergyLabelFileName;
    private String secondEnergyLabelOriginalName;
    private String secondEnergyLabelproductLeve1Code;
    private String secondProductFicheDocId;
    private String secondProductFicheFileName;
    private String secondProductFicheOriginalName;
    private String secondProductFicheproductLeve1Code;
    private String secondFEnergyLabelDocId;
    private String secondFEnergyLabelFileName;
    private String secondFEnergyLabelOriginalName;
    private String secondFEnergyLabelproductLeve1Code;
    private String secondEnergyLabelImageAddr;
    private String secondEnergyLabelName;
    private String themeType;
    private String exclusionModel;
    private String classificationFlagLv1;
    private String classificationFlagLv2;
    private String classificationFlagLv3;
    private String classificationFlagLv4;
    private String repairModelAreaYn;
    private List<Map<String, Object>> labelRepairMap;
    private String labelUseFlag;
    private String repairabilityIndexFlag;
    private String taxInformationUseFlag;
    private String elType;
    private String secondElType;
    private String secondPisDocType;
    private String secondPfCode;
    private String pisDocType;
    private String pisDocOldFlag;
    private String epsIncludeCharger;
    private String epsMinPower;
    private String epsMaxPower;
    private String epsUsbPd;
    private String lgPickOrderNo;
    private String mtsModelUrlPath;
}
