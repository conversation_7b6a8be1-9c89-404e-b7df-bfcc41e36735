package com.lge.d2x.interfaces.system.pds.support.client;

import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsBundleGsriFileInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsKmProductRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsKmProductResponseVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsPdfGsriSpecInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsPdfGsriSpecInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductManualSoftwareInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductManualSoftwareInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.support.url.SystemPdsSupportUrlConstants;
import com.lge.d2xfrm.annotation.ClientCaching;
import com.lge.d2xfrm.client.configuration.D2xFeignClientConfiguration;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(
        name = "SystemPdsSupportClient",
        url = "${client.url.system.pds}",
        configuration = D2xFeignClientConfiguration.class)
public interface SystemPdsSupportClient {
    @GetMapping(value = SystemPdsSupportUrlConstants.GET_SUPPORT_V1_KM_PRODUCT_CODE_LIST)
    List<SystemPdsKmProductResponseVO> getKmProductCodeList(
            @SpringQueryMap SystemPdsKmProductRequestVO requestVO);

    @ClientCaching(name = "getProductGsriFileInfo", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsSupportUrlConstants.GET_SUPPORT_V1_PRODUCT_GSRI_FILE_INFO)
    SystemPdsProductGsriFileInfoResponseVO getProductGsriFileInfo(
            @SpringQueryMap SystemPdsProductGsriFileInfoRequestVO requestVO);

    @ClientCaching(name = "getPdfDownloadInfo", ttlMinutesExpiredAt = 3)
    @GetMapping(value = SystemPdsSupportUrlConstants.GET_SUPPORT_V1_PDF_GSRI_SPEC_INFO)
    SystemPdsPdfGsriSpecInfoResponseVO getPdfDownloadInfo(
            @SpringQueryMap SystemPdsPdfGsriSpecInfoRequestVO requestVO);

    @ClientCaching(name = "getProductManualSoftwareInfo", ttlMinutesExpiredAt = 7)
    @GetMapping(value = SystemPdsSupportUrlConstants.GET_SUPPORT_V1_PRODUCT_SUPPORT_INFO)
    SystemPdsProductManualSoftwareInfoResponseVO getProductManualSoftwareInfo(
            @SpringQueryMap SystemPdsProductManualSoftwareInfoRequestVO requestVO);

    @ClientCaching(name = "getBundleGsriFileInfo", ttlMinutesExpiredAt = 5)
    @GetMapping(value = SystemPdsSupportUrlConstants.GET_SUPPORT_V1_BUNDLE_GSRI_FILE_INFO)
    List<SystemPdsBundleGsriFileInfoResponseVO> getBundleGsriFileInfo(
            @SpringQueryMap SystemPdsProductGsriFileInfoRequestVO requestVO);
}
