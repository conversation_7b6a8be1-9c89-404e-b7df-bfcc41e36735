<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.metafeed.v1.repository.pdsmgr.MetafeedRepository">
	<select id="retrieveMetaProductFeedList" parameterType="com.lge.d2x.domain.metafeed.v1.model.MetafeedRequestVO" resultType="com.lge.d2x.domain.metafeed.v1.model.MetafeedResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.metafeed.v1.service.MetafeedService.retrieveMetaProductFeedList */
		       CONCAT(C.SALES_MODEL_CODE, '.', C.SALES_MODEL_SUFFIX_CODE) AS item_group_id
		     , MCM.PDP_ID AS id
		     , IFNULL(MT.seo_page_title,'') AS title
		     , NVL(MT.seo_desc, (SELECT NVL(REPLACE(REPLACE(CMM.MSG_CNTS, '{0}', IFNULL(NULLIF(C.NEW_MKT_PRODUCT_NM, ''), MM.USER_FRNDY_PRODUCT_NM)), '{1}', MM.PRODUCT_NM), '')
		                           FROM COM_MESSAGE_M CMM
		                          WHERE 1=1
		                            AND CMM.LOCALE_CODE = #{localeCode}
		                            AND CMM.MSG_CODE = CONCAT('metadata_', C.LV1_CATEGORY_CODE, '_description'))) AS description
		     , CONCAT(#{serverDomainName}, MD.PDP_URL) AS link
		     , IFNULL(CONCAT(#{serverDomainName}, #{serverImageDomainName}, MD.BIG_IMG_URL), '') AS image_link
		     , CASE WHEN IFNULL(C.MODEL_EAN_CODE, '') = ''
		            THEN IFNULL((SELECT AA.MODEL_EAN_CODE
		                           FROM PDM_PRODUCT_M AA
		                           JOIN DSP_PDP_M BB
		                             ON AA.SKU_ID = BB.SKU_ID
		                            AND BB.PDP_ID = (SELECT BUNDLE_PDP_ID
		                                               FROM DSP_PRODUCT_BUNDLE_D CC
		                                              WHERE CC.PDP_ID = MM.PDP_ID
		                                                AND CC.DSP_SEQ = 1)
		                       ), '')
		            ELSE C.MODEL_EAN_CODE 
		            END AS gtin
		     , IFNULL(mm.MSRP_SALES_PRICE,0) AS msrp
		     , CASE WHEN IFNULL(ESD.STOCK_STATE_CODE, '') <![CDATA[<>]]> '' THEN 'Y'
		            ELSE 'N'
		            END AS obsSellFlag
		     , MM.LGCOM_SKU_ID AS sku
		     , 'LG' AS brand
		     , 'new' AS 'condition'
		     , IFNULL(MGPTM.GGPRODUCT_TYPE_CODE, '') AS google_product_category
		     , IFNULL(E3.ENERGY_LABEL_NAME, '') AS energy_efficiency_class
		     , IFNULL(S1.color, '') AS color
		     , CASE WHEN (SELECT COUNT(*)
		                    FROM DSP_PRODUCT_BUNDLE_D BD
		                   WHERE BD.PDP_ID = MD.PDP_ID
		                     AND BD.SITE_CODE = MD.SITE_CODE
		                     AND BD.USE_FLAG = 'Y') <![CDATA[>]]> 0 THEN 'Y'
		            ELSE 'N'
		            END AS is_bundle
		     , EAM.CURRENCY_CODE AS currency
		     , EAD.SALES_PRICE_EFFECTIVE_DATE_CNTS AS sales_price_effective_date
		     , EPD.ORIGINALPRICE AS price
		     , EPD.PRODUCT_PRICE AS sale_price
		     , EPD.CHEAPER_PRICE AS cheaper_price
		     , IF(ESD.STOCK_STATE_CODE = 'IN_STOCK', 'in stock', 'out of stock') AS availability
		     , C.LV1_CATEGORY_NM AS categoryLv1
		     , C.LV2_CATEGORY_NM AS categoryLv2
		     , C.LV3_CATEGORY_NM AS categoryLv3
		     , C.LV4_CATEGORY_NM AS categoryLv4
		<choose>
		    <when test='categoryStandardFlag == "Y"'>
		  FROM DSP_PDP_CATEGORY_R MCM
		    </when>
		    <otherwise>
		  FROM DSP_OLD_PDP_CATEGORY_R MCM
		    </otherwise>
		</choose>
		 INNER JOIN DSP_PDP_M MM
		    ON MM.PDP_ID = MCM.PDP_ID
		   AND MM.SITE_CODE = MCM.SITE_CODE
		   AND MM.BIZ_TYPE_CODE = MCM.BIZ_TYPE_CODE
		 INNER JOIN DSP_PDP_D MD
		    ON MD.PDP_ID = MCM.PDP_ID
		   AND MD.SITE_CODE = MCM.SITE_CODE
		   AND MD.BIZ_TYPE_CODE = MCM.BIZ_TYPE_CODE
		   AND MD.SHOP_CODE = 'D2C'
		 INNER JOIN (SELECT M.SITE_CODE
		                  , M.PDP_URL
		                  , M.SEO_DESC
		                  , M.LAST_UPDATE_DATE
		                  , M.SEO_PAGE_TITLE
		               FROM DSP_PRODUCT_SEO_INFO_M M
		              WHERE 1=1
		                AND M.SITE_CODE = #{siteCode}) MT
		    ON MT.SITE_CODE = MCM.SITE_CODE
		   AND MT.PDP_URL = MD.PDP_URL
		 <choose>
		    <when test='localeCode == "HK" or localeCode == "HK_EN"'>
		 INNER JOIN (SELECT PPM.SKU_ID
		                  , PPM.BIZ_TYPE_CODE
		                  , PPM.SALES_MODEL_CODE
		                  , PPM.SALES_MODEL_SUFFIX_CODE
		                  , PPM.MODEL_EAN_CODE
		                  , PPM.LV1_PRODUCT_CODE
		                  , PPM.LV2_PRODUCT_CODE
		                  , PPM.LV4_PRODUCT_CODE
		                  , PPD.NEW_MKT_PRODUCT_NM
		                  , IFNULL(PPCR1.LV1_CATEGORY_CODE, PPCR2.LV1_CATEGORY_CODE) AS LV1_CATEGORY_CODE
		                  , IFNULL(PPCR1.LV2_CATEGORY_CODE, PPCR2.LV2_CATEGORY_CODE) AS LV2_CATEGORY_CODE
		                  , IFNULL(PPCR1.LV3_CATEGORY_CODE, PPCR2.LV3_CATEGORY_CODE) AS LV3_CATEGORY_CODE
		                  , IFNULL(PPCR1.LV4_CATEGORY_CODE, PPCR2.LV4_CATEGORY_CODE) AS LV4_CATEGORY_CODE
		                  , IFNULL(PC1.CATEGORY_NM, PC2.CATEGORY_NM) AS LV1_CATEGORY_NM
		                  , IFNULL(PC3.CATEGORY_NM, PC4.CATEGORY_NM) AS LV2_CATEGORY_NM
		                  , IFNULL(PC5.CATEGORY_NM, PC6.CATEGORY_NM) AS LV3_CATEGORY_NM
		                  , IFNULL(PC7.CATEGORY_NM, PC8.CATEGORY_NM) AS LV4_CATEGORY_NM
		                  , PPM.CREATION_DATE
		                  , PPM.LAST_UPDATE_DATE
		                  , PPM.USE_FLAG
		               FROM PDM_PRODUCT_M PPM
		               LEFT JOIN PDM_PRODUCT_CATEGORY_R PPCR1
		                 ON PPM.SKU_ID = PPCR1.SKU_ID
		                AND PPM.USE_FLAG = PPCR1.USE_FLAG
		                AND PPCR1.LOCALE_CODE = #{localeCode1}
		               LEFT JOIN PDM_PRODUCT_CATEGORY_R PPCR2
		                 ON PPM.SKU_ID = PPCR2.SKU_ID
		                AND PPM.USE_FLAG = PPCR2.USE_FLAG
		                AND PPCR2.LOCALE_CODE = #{localeCode2}
		               LEFT JOIN PDM_CATEGORY_M PC1
		                 ON PPCR1.LV1_CATEGORY_CODE = PC1.CATEGORY_CODE
		                AND PPM.BIZ_TYPE_CODE = PC1.BIZ_TYPE_CODE
		                AND PPCR1.LOCALE_CODE = #{localeCode1}
		               LEFT JOIN PDM_CATEGORY_M PC2
		                 ON PPCR2.LV1_CATEGORY_CODE = PC2.CATEGORY_CODE
		                AND PPM.BIZ_TYPE_CODE = PC2.BIZ_TYPE_CODE
		                AND PPCR2.LOCALE_CODE = #{localeCode2}
		               LEFT JOIN PDM_CATEGORY_M PC3
		                 ON PPCR1.LV2_CATEGORY_CODE = PC3.CATEGORY_CODE
		                AND PPM.BIZ_TYPE_CODE = PC3.BIZ_TYPE_CODE
		                AND PPCR1.LOCALE_CODE = #{localeCode1}
		               LEFT JOIN PDM_CATEGORY_M PC4
		                 ON PPCR2.LV2_CATEGORY_CODE = PC4.CATEGORY_CODE
		                AND PPM.BIZ_TYPE_CODE = PC4.BIZ_TYPE_CODE
		                AND PPCR2.LOCALE_CODE = #{localeCode2}
		               LEFT JOIN PDM_CATEGORY_M PC5
		                 ON PPCR1.LV3_CATEGORY_CODE = PC5.CATEGORY_CODE
		                AND PPM.BIZ_TYPE_CODE = PC5.BIZ_TYPE_CODE
		                AND PPCR1.LOCALE_CODE = #{localeCode1}
		               LEFT JOIN PDM_CATEGORY_M PC6
		                 ON PPCR2.LV3_CATEGORY_CODE = PC6.CATEGORY_CODE
		                AND PPM.BIZ_TYPE_CODE = PC6.BIZ_TYPE_CODE
		                AND PPCR2.LOCALE_CODE = #{localeCode2}
		               LEFT JOIN PDM_CATEGORY_M PC7
		                 ON PPCR1.LV4_CATEGORY_CODE = PC7.CATEGORY_CODE
		                AND PPM.BIZ_TYPE_CODE = PC7.BIZ_TYPE_CODE
		                AND PPCR1.LOCALE_CODE = #{localeCode1}
		               LEFT JOIN PDM_CATEGORY_M PC8
		                 ON PPCR2.LV4_CATEGORY_CODE = PC8.CATEGORY_CODE
		                AND PPM.BIZ_TYPE_CODE = PC8.BIZ_TYPE_CODE
		                AND PPCR2.LOCALE_CODE = #{localeCode2}
		               LEFT JOIN PDM_PRODUCT_D PPD
		                 ON PPM.SKU_ID = PPD.SKU_ID
		                AND PPM.BIZ_TYPE_CODE = PPD.BIZ_TYPE_CODE
		                AND PPM.USE_FLAG = PPD.USE_FLAG
		                AND PPCR1.LOCALE_CODE = PPD.LOCALE_CODE
		              WHERE PPM.SKU_ID IS NOT NULL) C
		    </when>
		    <otherwise>
		 INNER JOIN (SELECT PPM.SKU_ID
		                  , PPM.BIZ_TYPE_CODE
		                  , PPM.SALES_MODEL_CODE
		                  , PPM.SALES_MODEL_SUFFIX_CODE
		                  , PPM.MODEL_EAN_CODE
		                  , PPM.LV1_PRODUCT_CODE
		                  , PPM.LV2_PRODUCT_CODE
		                  , PPM.LV3_PRODUCT_CODE
		                  , PPM.LV4_PRODUCT_CODE
		                  , PPD.NEW_MKT_PRODUCT_NM
		                  , PPCR.LV1_CATEGORY_CODE
		                  , PPCR.LV2_CATEGORY_CODE
		                  , PPCR.LV3_CATEGORY_CODE
		                  , PPCR.LV4_CATEGORY_CODE
		    <choose>
		        <when test='localeCode == "TW"'>
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE PPCR.LV1_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV1_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE PPCR.LV2_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV2_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE PPCR.LV3_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV3_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE PPCR.LV4_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV4_CATEGORY_NM
		        </when>
		        <otherwise>
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE PPCR.LV1_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV1_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE PPCR.LV2_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV2_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE PPCR.LV3_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV3_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE PPCR.LV4_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV4_CATEGORY_NM
		        </otherwise>
		    </choose>
		                  , PPM.CREATION_DATE
		                  , PPM.LAST_UPDATE_DATE
		                  , PPM.USE_FLAG
		               FROM PDM_PRODUCT_M PPM
		    <choose>
		        <when test='localeCode == "TW"'>
		              INNER JOIN PDM_PRODUCT_CATEGORY_COUNTRY_R PPCR
		                 ON PPM.SKU_ID = PPCR.SKU_ID
		                AND PPM.USE_FLAG = PPCR.USE_FLAG
		                AND PPCR.LOCALE_CODE = #{localeCode}
		        </when>
		        <otherwise>
		              INNER JOIN PDM_PRODUCT_CATEGORY_R PPCR
		                 ON PPM.SKU_ID = PPCR.SKU_ID
		                AND PPM.USE_FLAG = PPCR.USE_FLAG
		                AND PPCR.LOCALE_CODE = #{localeCode}
		        </otherwise>
		    </choose>
		               LEFT JOIN PDM_PRODUCT_D PPD
		                 ON PPM.SKU_ID = PPD.SKU_ID
		                AND PPM.BIZ_TYPE_CODE = PPD.BIZ_TYPE_CODE
		                AND PPM.USE_FLAG = PPD.USE_FLAG
		                AND PPCR.LOCALE_CODE = PPD.LOCALE_CODE) C
		    </otherwise>
		</choose>
		    ON MM.SKU_ID = C.SKU_ID
		   AND MM.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		 INNER JOIN ECM_ATTRIBUTE_M EAM
		    ON IF(RIGHT(MM.LGCOM_SKU_ID,2) = '.C', REPLACE(MM.LGCOM_SKU_ID, '.C', ''), MM.LGCOM_SKU_ID ) = EAM.SKU_ID
		   AND EAM.STORE_CODE = MM.SITE_CODE
		 INNER JOIN ECM_PRICE_D EPD
		    ON EPD.STORE_CODE = EAM.STORE_CODE
		   AND EPD.SKU_ID = EAM.SKU_ID
		   AND EPD.CUST_GRP_CODE = 'NOT_LOGGED_IN'
		 INNER JOIN ECM_STOCK_D ESD
		    ON ESD.SKU_ID = EAM.SKU_ID
		   AND ESD.STORE_CODE = EAM.STORE_CODE
		  LEFT JOIN ECM_ATTRIBUTE_D EAD
		    ON EAM.SKU_ID = EAD.SKU_ID
		   AND EAM.STORE_CODE = EAD.STORE_CODE
		  LEFT JOIN (SELECT DISTINCT ETM.ELABEL_TYPE_CODE
		                  , ETM.ELABEL_GRD_CODE
		                  , ETM.ELABEL_IMG_PATH
		                  , ETM.BIZ_TYPE_CODE
		                  , ELC.CODE_VAL_NM AS energy_label_name
		                  , MMEPM.LV1_EL_PRODUCT_CODE
		                  , MMEPM.LV2_EL_PRODUCT_CODE
		               FROM DSP_ENERGYLABEL_TYPE_M ETM
		              INNER JOIN (SELECT CCCD.COMMON_CODE_VAL
		                               , CCCD.CODE_VAL_NM
		                            FROM COM_COMMON_CODE_M CCCM
		                            JOIN COM_COMMON_CODE_D CCCD
		                              ON CCCM.COMMON_CODE = CCCD.COMMON_CODE
		                           WHERE CCCM.COMMON_CODE = 'Energy_Label_Code'
		                             AND CCCM.USE_FLAG = 'Y') ELC
		                 ON ETM.ELABEL_GRD_CODE = ELC.COMMON_CODE_VAL
		              INNER JOIN PDM_PRODUCT_EL_PIS_R MMEPM
		                 ON ETM.ELABEL_TYPE_CODE = MMEPM.EL_TYPE_CODE
		              WHERE ETM.USE_FLAG = 'Y'
		                AND ETM.SITE_CODE = #{siteCode}) E3
		    ON MM.BIZ_TYPE_CODE = E3.BIZ_TYPE_CODE
		   AND MM.ELABEL_GRD_CODE = E3.ELABEL_GRD_CODE
		   AND C.LV1_PRODUCT_CODE = E3.LV1_EL_PRODUCT_CODE
		   AND C.LV2_PRODUCT_CODE = E3.LV2_EL_PRODUCT_CODE
		  LEFT JOIN (SELECT SM.SITE_CODE
		                  , SM.BIZ_TYPE_CODE
		                  , SV.PDP_ID
		                  , IFNULL(SM.SIBLING_LOCAL_VAL, SV.SIBLING_CODE) AS color
		               FROM DSP_SIBLING_M SM
		              INNER JOIN DSP_SIBLING_D SV
		                 ON SV.USE_FLAG = 'Y'
		                AND SV.SITE_CODE = SM.SITE_CODE
		                AND SV.BIZ_TYPE_CODE = SM.BIZ_TYPE_CODE
		                AND SV.SIBLING_TYPE_CODE = SM.SIBLING_TYPE_CODE
		                AND SV.SIBLING_CODE = SM.SIBLING_CODE
		              WHERE SM.SIBLING_TYPE_CODE = 'COLOR') S1
		    ON MM.SITE_CODE = S1.SITE_CODE
		   AND MM.BIZ_TYPE_CODE = S1.BIZ_TYPE_CODE
		   AND MM.PDP_ID = S1.PDP_ID
		  LEFT JOIN DSP_GOOGLE_PRODUCT_TYPE_D MGPTM
		    ON MGPTM.LV1_CATEGORY_CODE = C.LV1_CATEGORY_CODE
		   AND MGPTM.LV2_CATEGORY_CODE = C.LV2_CATEGORY_CODE
		   AND MGPTM.LV3_CATEGORY_CODE = C.LV3_CATEGORY_CODE
		 WHERE MCM.SITE_CODE = #{siteCode}
		   AND MCM.BIZ_TYPE_CODE = #{bizType}
		   AND MCM.DEFAULT_MAP_FLAG = 'Y'
		   AND MCM.USE_FLAG = 'Y'
		   AND MD.PRODUCT_STATE_CODE = 'ACTIVE'
		   AND IFNULL(MM.SKU_ID, '') <![CDATA[<>]]> ''
		   AND IFNULL(MD.BIG_IMG_URL, '') <![CDATA[<>]]> ''
		   AND MM.USE_FLAG = 'Y'
		   AND MD.USE_FLAG = 'Y'
		   AND MD.AEM_PUBL_FLAG = 'Y'
		   AND IFNULL(ESD.STOCK_STATE_CODE, '') <![CDATA[<>]]> ''
	</select>
</mapper>