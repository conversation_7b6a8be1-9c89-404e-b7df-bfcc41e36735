<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.svdCategory.v1.repository.pdsmgr.SvdCategoryRepository">
    <select id="selectSvdLv1CategoryList" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv1CategoryResponseVO">
        select /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv1CategoryList*/
            distinct
            b.lv1_category_code 				as superCategoryId,
            c.SITE_CATEGORY_NM 					as superCategoryName,
            coalesce(d.sticky_img_url, '') 		as superCategoryImage,
            coalesce(d.sticky_hover_icon_url, '') as supericonPathHover,
            c.cs_dsp_seq 						as csDspSeq
        from svd_product_svc_model_d a
        inner join svd_product_svc_model_category_r b
            on a.site_code = b.site_code
            and a.svd_sku_id = b.svd_sku_id
        inner join dsp_display_category_m c
            on c.site_code = b.site_code
            and c.category_code = b.lv1_category_code
        inner join dsp_display_category_d d
            on d.site_code = c.site_code
            and d.category_code = c.category_code
            and d.shop_code = 'D2C'
        where 1=1
        and a.use_flag = 'Y'
        and b.use_flag = 'Y'
        and c.supp_use_flag = 'Y'
        <choose>
            <when test='b2bUseFlag eq "Y"'>
                and (c.biz_type_code = 'B2C' or (c.biz_type_code = 'B2B' and c.category_code in
                <foreach collection="lv2CategoryCodeList" item="lv2CategoryCode" index="idx" open=" (" separator="," close=")">
                    #{lv2CategoryCode}
                </foreach>
                ))
            </when>
            <otherwise>
                and c.biz_type_code = 'B2C'
            </otherwise>
        </choose>
        <if test="siteCode != null and siteCode != ''">
            and a.site_code = #{siteCode}
        </if>
        <if test="siteCode != null and siteCode == 'IN'">
            <if test='tabType == "E"'>
            and a.ext_wty_use_flag = 'Y'
			</if>
        </if>
        <if test="pageFlag eq 'call' and callLv3CategoryCode != null and callLv3CategoryCode != ''">
            and c.category_code = (select T1.high_lv_category_code
                                    from dsp_display_category_m T1
                                    where T1.category_code in (select T1.high_lv_category_code
                                                                from dsp_display_category_m T1
                                                                where T1.category_code = #{callLv3CategoryCode} /*todo: BEFORE_CATEGORY_ID 조건 확인필요*/
                                                                and T1.category_lv_no = 3
                                                                and T1.site_code = #{siteCode}))
        </if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND A.SALES_CODE IN
                <foreach collection="objetModelList" item="objetModel" index="idx" open=" (" separator="," close=")"><![CDATA[
                    #{objetModel}
                ]]></foreach>
        </if>
        order by c.cs_dsp_seq
    </select>

    <select id="selectSvdLv1CategoryListTypeT" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv1CategoryResponseVO">
        select /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv1CategoryListTypeT*/
            DISTINCT
            B.LV1_CATEGORY_CODE 				AS superCategoryId,
            C.SITE_CATEGORY_NM					AS superCategoryName,
            COALESCE(D.STICKY_IMG_URL, '') 		AS superCategoryImage,
            COALESCE(D.STICKY_HOVER_ICON_URL, '') AS supericonPathHover,
            C.CS_DSP_SEQ 						AS csDspSeq
        FROM SVD_PRODUCT_SVC_MODEL_D A
        INNER JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
            ON A.SITE_CODE = B.SITE_CODE
            AND A.SVD_SKU_ID = B.SVD_SKU_ID
        INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.SITE_CODE = B.SITE_CODE
            AND C.CATEGORY_CODE = B.LV1_CATEGORY_CODE
        INNER JOIN DSP_DISPLAY_CATEGORY_D D
            ON D.SITE_CODE = C.SITE_CODE
            AND D.CATEGORY_CODE = C.CATEGORY_CODE
            AND D.SHOP_CODE = 'D2C'
        WHERE 1=1
        AND A.USE_FLAG = 'Y'
        AND B.USE_FLAG = 'Y'
        AND C.SUPP_USE_FLAG = 'Y'
        AND C.BIZ_TYPE_CODE = 'B2C'
        AND C.CATEGORY_LV_NO = 1
        AND A.SITE_CODE = #{siteCode}
        ORDER BY C.CS_DSP_SEQ
    </select>

    <select id="selectSvdLv1CategoryListV2TypeT" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv1CategoryResponseVO">
        SELECT /*system.pdsmgr.oln.com.lge.d2x.domain.svdcategory.v1.service.svdcategoryservice.selectSvdLv1CategoryListV2TypeT*/
            DISTINCT
            B.LV1_CATEGORY_CODE 				AS superCategoryId,
            C.SITE_CATEGORY_NM					AS superCategoryName,
            COALESCE(D.STICKY_IMG_URL, '') 		AS superCategoryImage,
            COALESCE(D.STICKY_HOVER_ICON_URL, '') AS supericonPathHover,
            C.CS_DSP_SEQ 						AS csDspSeq
        FROM SVD_PRODUCT_SVC_MODEL_D A
        INNER JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
            ON A.SITE_CODE = B.SITE_CODE
            AND A.SVD_SKU_ID = B.SVD_SKU_ID
        INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.SITE_CODE = B.SITE_CODE
            AND C.CATEGORY_CODE = B.LV1_CATEGORY_CODE
        INNER JOIN DSP_DISPLAY_CATEGORY_D D
            ON D.SITE_CODE = C.SITE_CODE
            AND D.CATEGORY_CODE = C.CATEGORY_CODE
            AND D.SHOP_CODE = 'D2C'
        WHERE 1=1
        AND A.USE_FLAG = 'Y'
        AND B.USE_FLAG = 'Y'
        AND C.SUPP_USE_FLAG = 'Y'
        AND C.BIZ_TYPE_CODE = 'B2C'
        AND C.CATEGORY_LV_NO = 1
        AND A.SITE_CODE = #{siteCode}
        <![CDATA[
	    	AND EXISTS (
			    SELECT 1 
			    FROM SVD_GSCS_CONTENTS_3DEPTH_D C 
			    WHERE C.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
			    AND C.SITE_CODE= #{siteCode}
			)
		]]>
        ORDER BY C.CS_DSP_SEQ
    </select>

    <select id="selectSvdLv2CategoryListTypeT" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv2CategoryResponseVO">
        SELECT /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv2CategoryListTypeT*/
            DISTINCT
            B.LV2_CATEGORY_CODE                   AS categoryId,
            C.SITE_CATEGORY_NM                    AS categoryName,
            COALESCE(D.STICKY_IMG_URL, '')        AS categoryImage,
            COALESCE(D.STICKY_HOVER_ICON_URL, '') AS iconPathHover,
            C.CS_DSP_SEQ                          AS csDspSeq
        FROM SVD_PRODUCT_SVC_MODEL_D A
        INNER JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
            ON A.SITE_CODE = B.SITE_CODE
            AND A.SVD_SKU_ID = B.SVD_SKU_ID
        INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.SITE_CODE = B.SITE_CODE
            AND C.CATEGORY_CODE = B.LV2_CATEGORY_CODE
        INNER JOIN DSP_DISPLAY_CATEGORY_D D
            ON D.SITE_CODE = C.SITE_CODE
            AND D.CATEGORY_CODE = C.CATEGORY_CODE
            AND D.SHOP_CODE = 'D2C'
        WHERE 1=1
        AND A.USE_FLAG = 'Y'
        AND B.USE_FLAG = 'Y'
        AND C.SUPP_USE_FLAG = 'Y'
        AND C.BIZ_TYPE_CODE = 'B2C'
        AND C.CATEGORY_LV_NO = 2
        AND A.SITE_CODE = #{siteCode}
        <if test="lv1CategoryCode != null or lv1CategoryCode != ''">
            AND B.LV1_CATEGORY_CODE = #{lv1CategoryCode}
        </if>
        ORDER BY C.CS_DSP_SEQ
    </select>

    <select id="selectSvdLv2CategoryListV2TypeT" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv2CategoryResponseVO">
        SELECT /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv2CategoryListV2TypeT*/
            DISTINCT
            B.LV2_CATEGORY_CODE                   AS categoryId,
            C.SITE_CATEGORY_NM                    AS categoryName,
            COALESCE(D.STICKY_IMG_URL, '')        AS categoryImage,
            COALESCE(D.STICKY_HOVER_ICON_URL, '') AS iconPathHover,
            C.CS_DSP_SEQ                          AS csDspSeq
        FROM SVD_PRODUCT_SVC_MODEL_D A
        INNER JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
            ON A.SITE_CODE = B.SITE_CODE
            AND A.SVD_SKU_ID = B.SVD_SKU_ID
        INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.SITE_CODE = B.SITE_CODE
            AND C.CATEGORY_CODE = B.LV2_CATEGORY_CODE
        INNER JOIN DSP_DISPLAY_CATEGORY_D D
            ON D.SITE_CODE = C.SITE_CODE
            AND D.CATEGORY_CODE = C.CATEGORY_CODE
            AND D.SHOP_CODE = 'D2C'
        WHERE 1=1
        AND A.USE_FLAG = 'Y'
        AND B.USE_FLAG = 'Y'
        AND C.SUPP_USE_FLAG = 'Y'
        AND C.BIZ_TYPE_CODE = 'B2C'
        AND C.CATEGORY_LV_NO = 2
        AND A.SITE_CODE = #{siteCode}
        <if test="lv1CategoryCode != null or lv1CategoryCode != ''">
            AND B.LV1_CATEGORY_CODE = #{lv1CategoryCode}
        </if>
        <![CDATA[
	    	AND EXISTS (
			    SELECT 1 
			    FROM SVD_GSCS_CONTENTS_3DEPTH_D C 
			    WHERE C.LV2_CATEGORY_CODE = B.LV2_CATEGORY_CODE
                AND C.LV1_CATEGORY_CODE = #{lv1CategoryCode}
			    AND C.SITE_CODE= #{siteCode}
			)
		]]>
        ORDER BY C.CS_DSP_SEQ
    </select>

    <select id="selectSvdLv2CategoryListTypeE" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv2CategoryResponseVO">
        select /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv2CategoryListTypeE*/
            distinct
            b.lv2_category_code 				as categoryId,
            c.SITE_CATEGORY_NM					as categoryName,
            coalesce(d.sticky_img_url, '') 		as categoryImage,
            coalesce(d.sticky_hover_icon_url, '') as iconPathHover,
            c.cs_dsp_seq 						as csDspSeq
        from svd_product_svc_model_d a
        inner join svd_product_svc_model_category_r b
            on a.site_code = b.site_code
            and a.svd_sku_id = b.svd_sku_id
        inner join dsp_display_category_m c
            on c.site_code = b.site_code
            and c.category_code = b.lv2_category_code
        inner join dsp_display_category_d d
            on d.site_code = c.site_code
            and d.category_code = c.category_code
            and d.shop_code = 'D2C'
        where 1=1
        and a.use_flag = 'Y'
        and b.use_flag = 'Y'
        and c.supp_use_flag = 'Y'
        and c.biz_type_code = 'B2C'
        <if test="siteCode != null and siteCode != ''">
            and a.site_code = #{siteCode}
        </if>
        <if test="siteCode != null and siteCode == 'IN'">
            and a.ext_wty_use_flag = 'Y'
        </if>
        <if test="lv1CategoryCode != null or lv1CategoryCode != ''">
            and b.lv1_category_code = #{lv1CategoryCode}
        </if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND a.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>
        order by c.cs_dsp_seq
    </select>

    <select id="selectSvdLv2CategoryListTypeMW" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv2CategoryResponseVO">
        select /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv2CategoryListTypeMW*/
            distinct
            b.lv2_category_code 				as categoryId,
            c.SITE_CATEGORY_NM					as categoryName,
            coalesce(d.sticky_img_url, '') 		as categoryImage,
            coalesce(d.sticky_hover_icon_url, '') as iconPathHover,
            c.cs_dsp_seq 						as csDspSeq
        from svd_product_svc_model_d a
        inner join svd_product_svc_model_category_r b
            on a.site_code = b.site_code
            and a.svd_sku_id = b.svd_sku_id
        inner join dsp_display_category_m c
            on c.site_code = b.site_code
            and c.category_code = b.lv2_category_code
        inner join dsp_display_category_d d
            on d.site_code = c.site_code
            and d.category_code = c.category_code
            and d.shop_code = 'D2C'
        inner join pdm_product_svc_d e
            on e.svd_sku_id = a.svd_sku_id
        where 1=1
        and a.use_flag = 'Y'
        and b.use_flag = 'Y'
        and c.supp_use_flag = 'Y'
        <choose>
            <when test='b2bUseFlag eq "Y"'>
                <choose>
                    <when test='b2bDivisionUseFlag eq "Y"'>
                        and c.biz_type_code  = #{divisionBizType}
                    </when>
                    <otherwise>
                        and c.biz_type_code in ('B2C','B2B')
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                and c.biz_type_code  = 'B2C'
            </otherwise>
        </choose>
        <if test="siteCode != null and siteCode != ''">
            and a.site_code = #{siteCode}
        </if>
        <if test="lv1CategoryCode != null or lv1CategoryCode != ''">
            and b.lv1_category_code = #{lv1CategoryCode}
        </if>
        <if test="pageFlag eq 'call' and callLv3CategoryCode != null and callLv3CategoryCode != ''">
            and c.category_code = (select T1.high_lv_category_code
                                    from dsp_display_category_m T1
                                    where T1.category_code = #{callLv3CategoryCode} /*todo: BEFORE_CATEGORY_ID 조건 확인필요*/
                                    and T1.category_lv_no = 3
                                    and T1.site_code = #{siteCode})
        </if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND a.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>
        <if test="siteCode != 'BR' and pageFlag == 'repair'">
            and exists (select 1
                        from svd_service_type_r T2
                        where T2.site_code = #{siteCode}
                        and T2.use_flag = 'Y'
                        and T2.lv2_category_code = b.lv2_category_code
                        and (T2.lv3_category_code is null or T2.lv3_category_code = '')
                        and (T2.cust_model_code is null or T2.cust_model_code = '')
                        union all
                        select 1
                        from svd_service_type_r T2
                        where T2.site_code = #{siteCode}
                        and T2.use_flag = 'Y'
                        and T2.lv2_category_code = b.lv2_category_code
                        and T2.lv3_category_code = b.lv3_category_code
                        and (T2.cust_model_code is null or T2.cust_model_code = '')
                        union all
                        select 1
                        from svd_service_type_r T2
                        where T2.site_code = #{siteCode}
                        and T2.use_flag = 'Y'
                        and T2.lv2_category_code = b.lv2_category_code
                        and T2.lv3_category_code = b.lv3_category_code
                        and T2.cust_model_code = e.buyer_model_code
                        union all
                        select 1
                        from svd_service_type_r T2
                        where T2.site_code = #{siteCode}
                        and T2.use_flag = 'Y'
                        and T2.lv2_category_code = b.lv2_category_code
                        and (T2.lv3_category_code is null or T2.lv3_category_code = '')
                        and T2.cust_model_code = e.buyer_model_code
                        )
        </if>
        order by c.cs_dsp_seq
    </select>

    <select id="selectSvdLv3CategoryList" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv3CategoryResponseVO">
        SELECT /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv3CategoryList*/
            DISTINCT
            B.LV3_CATEGORY_CODE     AS subCategoryId,
            C.CATEGORY_NM           AS subCategoryName,
            A.GSFS_USE_FLAG         AS gsfsUseFlag,
            C.STICKY_IMG_URL        AS subCategoryImage,
            C.STICKY_HOVER_ICON_URL AS subiconPathHover,
            C.CS_DSP_SEQ            AS csDspSeq
        FROM SVD_PRODUCT_SVC_MODEL_D A
        INNER JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
            ON A.SITE_CODE = B.SITE_CODE
            AND A.SVD_SKU_ID = B.SVD_SKU_ID
        INNER JOIN (SELECT
                        T1.SITE_CODE,
                        T1.CATEGORY_CODE,
                        T1.SITE_CATEGORY_NM AS CATEGORY_NM,
                        T2.STICKY_IMG_URL,
                        T2.STICKY_HOVER_ICON_URL,
                        T1.SUPP_USE_FLAG,
                        T1.CS_DSP_SEQ
                    FROM DSP_DISPLAY_CATEGORY_M T1
                    INNER JOIN DSP_DISPLAY_CATEGORY_D T2
                        ON T2.SITE_CODE = T1.SITE_CODE
                        AND T2.CATEGORY_CODE = T1.CATEGORY_CODE
                        AND T2.SHOP_CODE = 'D2C'
                    UNION ALL
                    SELECT
                        #{siteCode} AS SITE_CODE,
                        'Others' AS CATEGORY_CODE,
                        IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'common-symptom-others' AND a.LOCALE_CODE = #{localeCode} AND a.DSP_SVC_MSG_SP_CODE = 'SVC' AND a.SHOP_CODE = 'D2C' AND a.USE_FLAG = 'Y'), 'Others') AS CATEGORY_NM,
                        '/content/dam/lge/common/common-icon/support-icons/icon-other_normal.svg' AS STICKY_IMG_URL,
                        '/content/dam/lge/common/common-icon/support-icons/icon-other_active.svg' AS STICKY_HOVER_ICON_URL,
                        'Y' AS SUPP_USE_FLAG,
                        9999 AS CS_DSP_SEQ
                    ) C
            ON C.CATEGORY_CODE = B.LV3_CATEGORY_CODE 	/*Todo: site_code 조건 없어도 될 것 같음. 검토필요*/
        INNER JOIN PDM_PRODUCT_SVC_D D
            ON D.SVD_SKU_ID = A.SVD_SKU_ID
        <if test = 'euEcoCategoryDt != null'>
            INNER JOIN SVD_CUSTOMER_MODEL_M E
                ON E.SITE_CODE = A.SITE_CODE
                AND E.CUST_MODEL_CODE = D.BUYER_MODEL_CODE
                AND E.CREATION_DATE > #{euEcoCategoryDt}
                AND E.USE_FLAG = 'Y'
        </if>
        WHERE 1=1
        AND A.USE_FLAG = 'Y'
        AND B.USE_FLAG = 'Y'
        AND C.SUPP_USE_FLAG = 'Y'
        AND A.GSFS_USE_FLAG = 'Y'
        AND A.SITE_CODE = #{siteCode}
        AND B.LV2_CATEGORY_CODE = #{lv2CategoryCode}
        AND (B.LV3_CATEGORY_CODE IS NOT NULL AND B.LV3_CATEGORY_CODE <![CDATA[<>]]> '')
        <if test='ecoCategoryFlag == "Y"'>
            <foreach collection="lv2CategoryCodeList" item="lv2CategoryCodes" index="idx" open="AND B.LV2_CATEGORY_CODE IN (" separator="," close=")">
                #{lv2CategoryCodes}
            </foreach>
        </if>
        <if test='tabType == "W"'>
            AND EXISTS (SELECT 1
                        FROM SVD_WTY_INFO_M T1
                        WHERE T1.SITE_CODE = A.SITE_CODE
                        AND T1.COUNTRY_CODE = #{countryCode}
                        AND T1.OPEN_STATE_CODE = 'ONSERVICE'
                        AND T1.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
                        AND T1.USE_FLAG = 'Y')
        </if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND A.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>
        <if test='tabType == "T"'>
            <![CDATA[
                AND EXISTS (
                    SELECT 1 
                    FROM SVD_GSCS_CONTENTS_3DEPTH_D C 
                    WHERE C.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
                    AND C.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                    AND C.LV1_CATEGORY_CODE = #{lv1CategoryCode}
                    AND C.SITE_CODE = #{siteCode}
                )
            ]]>
        </if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND A.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>
        <if test='pageFlag == "repair"'>
            <if test = 'siteCode != "BR" and siteCode != "EG_EN" and siteCode != "EG_AR" and siteCode != "MX" and siteCode != "CO" and siteCode != "CL" and siteCode != "CAC" and siteCode != "PA" and siteCode != "PE"  and siteCode != "EC"'>
                <if test = 'ecoCategoryFlag != "Y"'>
                    AND EXISTS (SELECT 1
                                FROM SVD_SERVICE_TYPE_R T2
                                WHERE T2.SITE_CODE = #{siteCode}
                                AND T2.USE_FLAG = 'Y'
                                AND T2.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                                AND ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                                OR ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                                )
                </if>
            </if>
            <if test ='siteCode == "BR" or siteCode == "EG_EN" or siteCode == "EG_AR" or siteCode == "MX" or siteCode == "CO" or siteCode == "CL" or siteCode == "CAC" or siteCode == "PA" or siteCode == "PE" or siteCode == "EC"'>
                AND EXISTS (SELECT 1
                FROM SVD_SERVICE_TYPE_R T2
                WHERE T2.SITE_CODE = #{siteCode}
                AND T2.USE_FLAG = 'Y'
                AND T2.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                AND ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                OR ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                )
                <if test = 'siteCode == "BR"'>
                    AND (C.ONSITE_USE_FLAG = 'Y' OR C.MC_COLLECT_FLAG = 'Y')
                </if>
                <if test = 'siteCode == "EG_EN" or siteCode == "EG_AR" or siteCode == "MX" or siteCode == "CO" or siteCode == "CL" or siteCode == "CAC" or siteCode == "PA" or siteCode == "PE" or siteCode == "EC"'>
                    AND C.ONSITE_USE_FLAG = 'Y'
                </if>
            </if>
        </if>
        <if test = 'pageFlag == "carryin"'>
            <if test = 'siteCode == "BR"'>
                AND EXISTS (SELECT 1
                FROM SVD_SERVICE_TYPE_R T2
                WHERE T2.SITE_CODE = #{siteCode}
                AND T2.USE_FLAG = 'Y'
                AND T2.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                AND ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                OR ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                )
                AND C.CARRYIN_USE_FLAG = 'Y'
            </if>
        </if>
        <if test = 'pageFlag == "install"'>
            <if test = 'siteCode == "BR" or siteCode == "EG_EN" or siteCode == "EG_AR" or siteCode == "MX" or siteCode == "CO" or siteCode == "CL" or siteCode == "CAC" or siteCode == "PA" or siteCode == "PE" or siteCode == "EC"'>
                AND EXISTS (SELECT 1
                FROM SVD_SERVICE_TYPE_R T2
                WHERE T2.SITE_CODE = #{siteCode}
                AND T2.USE_FLAG = 'Y'
                AND T2.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                AND ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                OR ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                )
                AND C.INSTALLATION_USE_FLAG = 'Y'
            </if>
        </if>
        <if test = 'pageFlag == "call"'>
            <if test = 'lv3CategoryCode != null and lv3CategoryCode != ""'>
                AND LV3_CATEGORY_CODE = #{callLv3CategoryCode}
            </if>
        </if>
        GROUP BY B.LV3_CATEGORY_CODE, C.CATEGORY_NM, A.GSFS_USE_FLAG, C.STICKY_IMG_URL, C.STICKY_HOVER_ICON_URL, CS_DSP_SEQ
        ORDER BY C.CS_DSP_SEQ
    </select>

    <select id="selectSvdLv3CategoryListV2" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv3CategoryResponseVO">
        SELECT /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv3CategoryListV2*/
            DISTINCT
            B.LV3_CATEGORY_CODE     AS subCategoryId,
            C.CATEGORY_NM           AS subCategoryName,
            A.GSFS_USE_FLAG         AS gsfsUseFlag,
            C.STICKY_IMG_URL        AS subCategoryImage,
            C.STICKY_HOVER_ICON_URL AS subiconPathHover,
            C.CS_DSP_SEQ            AS csDspSeq
        FROM SVD_PRODUCT_SVC_MODEL_D A
        INNER JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B
            ON A.SITE_CODE = B.SITE_CODE
            AND A.SVD_SKU_ID = B.SVD_SKU_ID
        INNER JOIN (SELECT
                        T1.SITE_CODE,
                        T1.CATEGORY_CODE,
                        T1.SITE_CATEGORY_NM AS CATEGORY_NM,
                        T2.STICKY_IMG_URL,
                        T2.STICKY_HOVER_ICON_URL,
                        T1.SUPP_USE_FLAG,
                        T1.CS_DSP_SEQ
                    FROM DSP_DISPLAY_CATEGORY_M T1
                    INNER JOIN DSP_DISPLAY_CATEGORY_D T2
                        ON T2.SITE_CODE = T1.SITE_CODE
                        AND T2.CATEGORY_CODE = T1.CATEGORY_CODE
                        AND T2.SHOP_CODE = 'D2C'
                    UNION ALL
                    SELECT
                        #{siteCode} AS SITE_CODE,
                        'Others' AS CATEGORY_CODE,
                        IFNULL((SELECT a.MSG_CNTS FROM COM_MESSAGE_M a WHERE a.MSG_CODE = 'common-symptom-others' AND a.LOCALE_CODE = #{localeCode} AND a.DSP_SVC_MSG_SP_CODE = 'SVC' AND a.SHOP_CODE = 'D2C' AND a.USE_FLAG = 'Y'), 'Others') AS CATEGORY_NM,
                        '/content/dam/lge/common/common-icon/support-icons/icon-other_normal.svg' AS STICKY_IMG_URL,
                        '/content/dam/lge/common/common-icon/support-icons/icon-other_active.svg' AS STICKY_HOVER_ICON_URL,
                        'Y' AS SUPP_USE_FLAG,
                        9999 AS CS_DSP_SEQ
                    ) C
            ON (C.CATEGORY_CODE = B.LV3_CATEGORY_CODE
                AND C.SITE_CODE = A.SITE_CODE) 
        INNER JOIN PDM_PRODUCT_SVC_D D
            ON D.SVD_SKU_ID = A.SVD_SKU_ID
        <if test = 'euEcoCategoryDt != null'>
            INNER JOIN SVD_CUSTOMER_MODEL_M E
                ON E.SITE_CODE = A.SITE_CODE
                AND E.CUST_MODEL_CODE = D.BUYER_MODEL_CODE
                AND E.CREATION_DATE > #{euEcoCategoryDt}
                AND E.USE_FLAG = 'Y'
        </if>
        WHERE 1=1
        AND A.USE_FLAG = 'Y'
        AND B.USE_FLAG = 'Y'
        AND C.SUPP_USE_FLAG = 'Y'
        AND A.GSFS_USE_FLAG = 'Y'
        AND A.SITE_CODE = #{siteCode}
        AND B.LV2_CATEGORY_CODE = #{lv2CategoryCode}
        AND (B.LV3_CATEGORY_CODE IS NOT NULL AND B.LV3_CATEGORY_CODE <![CDATA[<>]]> '')
        <if test='ecoCategoryFlag == "Y"'>
            <foreach collection="lv2CategoryCodeList" item="lv2CategoryCode" index="idx" open="AND B.LV2_CATEGORY_CODE IN (" separator="," close=")">
                #{lv2CategoryCode}
            </foreach>
        </if>
        <if test='tabType == "W"'>
            AND EXISTS (SELECT 1
                        FROM SVD_WTY_INFO_M T1
                        WHERE T1.SITE_CODE = A.SITE_CODE
                        AND T1.COUNTRY_CODE = #{countryCode}
                        AND T1.OPEN_STATE_CODE = 'ONSERVICE'
                        AND T1.LV1_CATEGORY_CODE = B.LV1_CATEGORY_CODE
                        AND T1.USE_FLAG = 'Y')
        </if>
        <if test='tabType == "T"'>
            <![CDATA[
                AND EXISTS (
                    SELECT 1 
                    FROM SVD_GSCS_CONTENTS_3DEPTH_D C 
                    WHERE C.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE
                    AND C.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                    AND C.LV1_CATEGORY_CODE = #{lv1CategoryCode}
                    AND C.SITE_CODE = #{siteCode}
                )
            ]]>
        </if>
        <if test="objetModelList != null and objetModelList.length > 0">
            AND A.SALES_CODE IN
            <foreach collection="objetModelList" item="objetModels" index="idx" open=" (" separator="," close=")">
                #{objetModels}
            </foreach>
        </if>
        <if test='pageFlag == "repair"'>
            <if test = 'siteCode != "BR" and siteCode != "EG_EN" and siteCode != "EG_AR" and siteCode != "MX" and siteCode != "CO" and siteCode != "CL" and siteCode != "CAC" and siteCode != "PA" and siteCode != "PE"  and siteCode != "EC"'>
                <if test = 'ecoCategoryFlag != "Y"'>
                    AND EXISTS (SELECT 1
                                FROM SVD_SERVICE_TYPE_R T2
                                WHERE T2.SITE_CODE = #{siteCode}
                                AND T2.USE_FLAG = 'Y'
                                AND T2.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                                AND ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                                OR ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                                )
                </if>
            </if>
            <if test ='siteCode == "BR" or siteCode == "EG_EN" or siteCode == "EG_AR" or siteCode == "MX" or siteCode == "CO" or siteCode == "CL" or siteCode == "CAC" or siteCode == "PA" or siteCode == "PE" or siteCode == "EC"'>
                AND EXISTS (SELECT 1
                FROM SVD_SERVICE_TYPE_R T2
                WHERE T2.SITE_CODE = #{siteCode}
                AND T2.USE_FLAG = 'Y'
                AND T2.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                AND ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                OR ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                )
                <if test = 'siteCode == "BR"'>
                    AND (C.ONSITE_USE_FLAG = 'Y' OR C.MC_COLLECT_FLAG = 'Y')
                </if>
                <if test = 'siteCode == "EG_EN" or siteCode == "EG_AR" or siteCode == "MX" or siteCode == "CO" or siteCode == "CL" or siteCode == "CAC" or siteCode == "PA" or siteCode == "PE" or siteCode == "EC"'>
                    AND C.ONSITE_USE_FLAG = 'Y'
                </if>
            </if>
        </if>
        <if test = 'pageFlag == "carryin"'>
                AND EXISTS (SELECT 1
                FROM SVD_SERVICE_TYPE_R T2
                WHERE T2.SITE_CODE = #{siteCode}
                AND T2.USE_FLAG = 'Y'
                AND T2.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                AND ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                OR ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                )
                AND C.CARRYIN_USE_FLAG = 'Y'
        </if>
        <if test = 'pageFlag == "install"'>
            <if test = 'siteCode == "BR" or siteCode == "EG_EN" or siteCode == "EG_AR" or siteCode == "MX" or siteCode == "CO" or siteCode == "CL" or siteCode == "CAC" or siteCode == "PA" or siteCode == "PE" or siteCode == "EC"'>
                AND EXISTS (SELECT 1
                FROM SVD_SERVICE_TYPE_R T2
                WHERE T2.SITE_CODE = #{siteCode}
                AND T2.USE_FLAG = 'Y'
                AND T2.LV2_CATEGORY_CODE = #{lv2CategoryCode}
                AND ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND (T2.CUST_MODEL_CODE IS NULL OR T2.CUST_MODEL_CODE = ''))
                OR (T2.LV3_CATEGORY_CODE = B.LV3_CATEGORY_CODE AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                OR ((T2.LV3_CATEGORY_CODE IS NULL OR T2.LV3_CATEGORY_CODE = '') AND T2.CUST_MODEL_CODE = D.BUYER_MODEL_CODE)
                )
                AND C.INSTALLATION_USE_FLAG = 'Y'
            </if>
        </if>
        <if test = 'pageFlag == "call"'>
            <if test = 'lv3CategoryCode != null and lv3CategoryCode != ""'>
                AND LV3_CATEGORY_CODE = #{callLv3CategoryCode}
            </if>
        </if>
        GROUP BY B.LV3_CATEGORY_CODE, C.CATEGORY_NM, A.GSFS_USE_FLAG, C.STICKY_IMG_URL, C.STICKY_HOVER_ICON_URL, CS_DSP_SEQ
        ORDER BY C.CS_DSP_SEQ
    </select>

    <select id="selectSvdLv3CategoryListTypeE" parameterType="com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO" resultType="com.lge.d2x.domain.svdCategory.v1.model.SvdLv3CategoryResponseVO">
        select /*system.pdsmgr.oln.com.lge.d2x.domain.svdCategory.v1.service.SvdCategoryService.selectSvdLv3CategoryListTypeE*/
            distinct
            b.lv3_category_code 		as subCategoryId,
            c.category_nm 				as subCategoryName,
            a.gsfs_use_flag 			as gsfsUseFlag,
            c.sticky_img_url			as subCategoryImage,
            c.sticky_hover_icon_url 	as subiconPathHover,
            c.cs_dsp_seq                as csDspSeq
        from svd_product_svc_model_d a
        inner join svd_product_svc_model_category_r b
            on a.site_code = b.site_code
            and a.svd_sku_id = b.svd_sku_id
        inner join (select
                        T1.category_code,
                        T1.SITE_CATEGORY_NM AS category_nm,
                        T2.sticky_img_url,
                        T2.sticky_hover_icon_url,
                        T1.supp_use_flag,
                        T1.biz_type_code,
                        T1.cs_dsp_seq
                    from dsp_display_category_m T1
                    inner join dsp_display_category_d T2
                        on T2.site_code = T1.site_code
                        and T2.category_code = T1.category_code
                        and T2.shop_code = 'D2C'
                    union all
                    select
                        'Others' as category_code,
                        'Others' as category_nm,
                        '/content/dam/lge/common/common-icon/support-icons/icon-other_normal.svg' as sticky_img_url,
                        '/content/dam/lge/common/common-icon/support-icons/icon-other_active.svg' as sticky_hover_icon_url,
                        'Y' as supp_use_flag,
                        'B2C' as biz_type_code,
                        9999 as cs_dsp_seq
                    ) c
            on c.category_code = b.lv3_category_code 	/*Todo: site_code 조건 없어도 될 것 같음. 검토필요*/
        where 1=1
        and a.gsfs_use_flag = 'Y'
        and a.use_flag = 'Y'
        and b.use_flag = 'Y'
        and c.supp_use_flag = 'Y'
        and c.biz_type_code = 'B2C'
        and a.site_code = #{siteCode}
        and b.lv2_category_code = #{lv2CategoryCode}
        and b.lv3_category_code is not null
        <if test = 'siteCode != null and siteCode == "IN"'>
            and a.ext_wty_use_flag = 'Y'
        </if>
        order by c.cs_dsp_seq
    </select>
</mapper>