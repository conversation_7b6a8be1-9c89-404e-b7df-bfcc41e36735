package com.lge.d2x.domain.support.v1.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class GpServiceTypeResponseVO {
    private String shipinUseFlag;
    private String onsiteUseFlag;
    private String carryinUseFlag;
    private String installationUseFlag;
    private String maintenanceUseFlag;
    private String mcCollectFlag;
    private String orderNo;
}
