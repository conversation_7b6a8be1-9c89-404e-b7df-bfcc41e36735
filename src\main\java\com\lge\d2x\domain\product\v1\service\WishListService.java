package com.lge.d2x.domain.product.v1.service;

import com.lge.d2x.domain.product.v1.model.ProductWishlistRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductWishlistResponseVO;
import com.lge.d2x.domain.product.v1.model.WishlistRequestVO;
import com.lge.d2x.domain.product.v1.model.WishlistUpdateRequestVO;
import com.lge.d2x.domain.product.v1.model.WishlistUpdateResponseVO;
import com.lge.d2x.domain.productList.v1.model.ProductListRequestVO;
import com.lge.d2x.domain.productList.v1.model.ProductListResponseVO;
import com.lge.d2x.domain.productList.v1.service.ProductListService;
import com.lge.d2x.interfaces.system.admin.wishlist.client.SystemAdminWishlistClient;
import com.lge.d2x.interfaces.system.admin.wishlist.model.SystemAdminWishlistProductResponseVO;
import com.lge.d2x.interfaces.system.admin.wishlist.model.SystemAdminWishlistRequestVO;
import com.lge.d2x.interfaces.system.admin.wishlist.model.SystemAdminWishlistUpdateRequestVO;
import com.lge.d2x.interfaces.system.admin.wishlist.model.SystemAdminWishlistUpdateResponseVO;
import com.lge.d2xfrm.configuration.context.TokenContextHolder;
import com.lge.d2xfrm.constants.CommonCodes;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.CachedPdpIdRequestVO;
import com.lge.d2xfrm.model.common.CachedPdpIdVO;
import com.lge.d2xfrm.util.common.CachedDataUtil;
import com.lge.d2xfrm.util.common.RequestHeaderUtil;
import com.lge.d2xfrm.util.sso.SessionUserUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class WishListService {
    private final ProductListService productListService;
    private final SystemAdminWishlistClient systemAdminWishlistClient;
    private final CachedDataUtil cachedDataUtil;

    public List<ProductListResponseVO> getWishlist(WishlistRequestVO requestVO) {
        String SITE_CODE = RequestHeaderUtil.getSiteCode();

        if (StringUtils.isEmpty(SITE_CODE)) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        List<Map<String, Object>> tmpProductList = new ArrayList<Map<String, Object>>();
        Map<String, Object> tmpMap = new HashMap<>();
        tmpMap.put("tabTitle", "CUSTOMER WISH LIST");
        tmpMap.put("group", StringUtils.defaultIfBlank(requestVO.getGroup(), ""));
        tmpMap.put("listType", "WISHLIST");
        tmpProductList.add(tmpMap);

        List<ProductListResponseVO> wishProductList =
                productListService.getProductList(
                        ProductListRequestVO.builder()
                                .productList(tmpProductList)
                                .customerNo(requestVO.getCustomerNo())
                                .build());

        return wishProductList;
    }

    public List<ProductWishlistResponseVO> getProductWishlist(ProductWishlistRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU_LIST = requestVO.getSkuList();

        List<ProductWishlistResponseVO> responseVO = new ArrayList<>();

        try {
            List<String> skus = Arrays.asList(SKU_LIST.split(","));

            String token = RequestHeaderUtil.getAccessCode();
            TokenContextHolder.setToken(token);
            boolean isMembers = SessionUserUtil.isLogin();
            String customerNo = "";
            if (isMembers) {
                customerNo = SessionUserUtil.getUserInfo().getUserId();
            }

            List<SystemAdminWishlistProductResponseVO> systemAdminWishlistProductList =
                    systemAdminWishlistClient.getWishlistProductList(
                            SystemAdminWishlistRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .customerNo(customerNo)
                                    .skus(skus)
                                    .build());

            for (SystemAdminWishlistProductResponseVO systemAdminWishlistProduct :
                    systemAdminWishlistProductList) {
                systemAdminWishlistProduct.setMsrp(
                        cachedDataUtil.getMsrp(SITE_CODE, systemAdminWishlistProduct.getMsrp()));
                responseVO.add(systemAdminWishlistProduct.toProductWishlistVO());
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return responseVO;
    }

    public WishlistUpdateResponseVO updateWishlist(WishlistUpdateRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        String customerNo = "";

        SystemAdminWishlistUpdateResponseVO systemAdminWishlistUpdate =
                new SystemAdminWishlistUpdateResponseVO();

        try {
            List<CachedPdpIdVO> pdpList =
                    cachedDataUtil.getPdpIdList(
                            CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
            if (ObjectUtils.isEmpty(pdpList)) {
                throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
            }
            String PDP_ID = pdpList.get(0).getPdpId();

            String token = RequestHeaderUtil.getAccessCode();
            TokenContextHolder.setToken(token);
            boolean isMembers = SessionUserUtil.isLogin();
            if (isMembers) {
                customerNo = SessionUserUtil.getUserInfo().getUserId();
            }

            systemAdminWishlistUpdate =
                    systemAdminWishlistClient.updateWishlist(
                            SystemAdminWishlistUpdateRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .customerNo(customerNo)
                                    .sku(SKU)
                                    .modelId(PDP_ID)
                                    .isMembers(isMembers)
                                    .build());

        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return WishlistUpdateResponseVO.builder()
                .flag(systemAdminWishlistUpdate.getFlag())
                .wishCnt(systemAdminWishlistUpdate.getWishCnt())
                .sku(systemAdminWishlistUpdate.getSku())
                .build();
    }
}
