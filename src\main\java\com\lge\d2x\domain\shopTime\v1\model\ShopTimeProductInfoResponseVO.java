package com.lge.d2x.domain.shopTime.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ShopTimeProductInfoResponseVO {
    @Schema(description = "제품아이디", example = "MD00000001")
    private String pdpId;

    @Schema(description = "SKU", example = "15U50T-G.AA56G.EEDG.DE.C")
    private String sku;

    @Schema(description = "MPN", example = "15U50T-G.AA56G")
    private String mpn;

    @Schema(
            description = "상품명",
            example =
                    "LG gram Book 15,6 Zoll | Leichtes Intel® Core™ Prozessor Laptop | Windows 11, Full HD Mattes IPS Display - 15U50T-G.AA56G")
    private String title;

    @Schema(description = "상위카테고리ID", example = "CT52000817")
    private String superCategoryId;

    @Schema(description = "카테고리ID", example = "CT00008362")
    private String categoryId;

    @Schema(description = "하위카테고리ID", example = "CT52000846")
    private String subCategoryId;

    @Schema(description = "브랜드명", example = "LG")
    private String brand;

    @Schema(description = "제품이미지링크_작은이미지")
    private String smallImageAddr;

    @Schema(description = "제품이미지링크_중간이미지")
    private String mediumImageAddr;

    @Schema(description = "제품이미지링크_큰이미지")
    private String largeImageAddr;

    @Schema(
            description = "설명",
            example =
                    "Weitere Informationen über LG 15U50T-G.AA56G. Klicken Sie für Bilder, Bewertungen und technische Daten zu LG gram Book 15,6 Zoll | Leichtes Intel® Core™ Prozessor Laptop | Windows 11, Full HD Mattes IPS Display.")
    private String description;

    @Schema(description = "리뷰평가", example = "5")
    private String reviewRating;

    @Schema(description = "제품구매링크", example = "https://www.lg.com/de/notebooks/gram/15u50t-g/buy/")
    private String productBuyLink;

    @Schema(description = "단종여부", example = "N")
    private String discontinuedFlag;

    @Schema(description = "그룹제품여부", example = "N")
    private String groupProductFlag;

    @Schema(description = "생성일자", example = "2025-01-14=T12:00:00")
    private String publishDate;

    @Schema(description = "수정일자", example = "2025-01-14=T12:00:00")
    private String changeDate;

    @Schema(description = "할부개월수", example = "3")
    private String installmentMonthcnt;

    @Schema(description = "할부가격", example = "24.190000")
    private String installmentPrice;

    @Schema(description = "제품가격", example = "1548.750000")
    private String productPrice;

    @Schema(description = "정가", example = "2000.000000")
    private String originalprice;

    @Schema(description = "매진여부", example = "N")
    private String soldOutFlag;

    @Schema(description = "배송정보")
    private String shippingInfo;

    @Schema(description = "에너지효율등급", example = "F")
    private String energyEfficiencyClass;

    @Schema(description = "제품레벨코드", example = "MNOL")
    private String productLevelCode;
}
