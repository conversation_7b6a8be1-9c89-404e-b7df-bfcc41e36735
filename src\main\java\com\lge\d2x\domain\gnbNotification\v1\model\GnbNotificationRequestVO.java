package com.lge.d2x.domain.gnbNotification.v1.model;

import com.lge.d2xfrm.constants.CommonCodes;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class GnbNotificationRequestVO {
    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "로케일 코드")
    private String siteCode;

    @Schema(description = "페이지 유형")
    private String pageType;

    @Schema(description = "PLP / PDP 구분")
    private String mktType;

    @Schema(description = "Category Id")
    private String categoryCode;

    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "타임존이 적용된 오늘 날짜")
    private String today;

    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "진입 URL")
    private String originUrl;

    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "비즈니스 유형")
    private String bizTypeCode;

    @Schema(description = "LG.com에서 사용하는 sku 아이디")
    private String sku;
}
