<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.gnbNotification.v1.repository.pdsmgr.GnbNotificationRepository">
	<select id="selectGnbNotificationBar" parameterType="com.lge.d2x.domain.gnbNotification.v1.model.GnbNotificationRequestVO"
		resultType="com.lge.d2x.domain.gnbNotification.v1.model.GnbNotificationResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.gnbNotification.v1.service.getGnbNotificationBar.selectGnbNotificationBar */
		       NOTIFICATION_TYPE
		     , NOTIFICATION_MESSAGE
		     , ROW_NUMBER() OVER (ORDER BY NOTIFICATION_TYPE DESC, RN) AS ORDER_NO
		     , BUTTON_NAME
		     , BUTTON_LINK_URL
		     , CONCAT('_', BUTTON_LINK_TARGET) AS BUTTON_LINK_TARGET
		  FROM (
		        SELECT NOTIFICATION_TYPE
		             , NOTIFICATION_MESSAGE
		             , ROW_NUMBER() OVER (PARTITION BY NOTIFICATION_TYPE ORDER BY NOTIFICATION_TYPE DESC, NEW_ORDER_NO, CREATION_DATE) AS RN
		             , BUTTON_NAME
		             , BUTTON_LINK_URL
		             , BUTTON_LINK_TARGET
		          FROM (
		                SELECT MNM.NOTI_ID
		                     , MNM.URGENCY_FLAG as NOTIFICATION_TYPE
		                     , MNM.NOTI_MSG_CNTS as NOTIFICATION_MESSAGE
		                     , MNM.DSP_SEQ AS NEW_ORDER_NO
		                     , MNM.BTN_NM as BUTTON_NAME
		                     , MNM.BTN_URL as BUTTON_LINK_URL
		                     , MNM.BTN_SBJ_CODE as BUTTON_LINK_TARGET
		                     , MNM.CREATION_DATE
		                  FROM DSP_NOTI_M MNM
		                  LEFT JOIN DSP_NOTI_CATEGORY_R MNCM
		                    ON MNM.NOTI_ID = MNCM.NOTI_ID
		                  LEFT JOIN DSP_NOTI_PRODUCT_R MNMM
		                    ON MNM.NOTI_ID = MNMM.NOTI_ID
		                  LEFT JOIN PRM_PROMOTION_LIST_M MPLM
		                    ON MPLM.PROMOTION_LIST_ID = MNM.PROMOTION_LIST_ID
		                   AND MNM.PROMOTION_USE_FLAG = 'Y'
		<if test="pageType == 'MAIN'">
		                 WHERE MNM.MAIN_PAGE_EXP_FLAG = 'Y'
		</if>
		<if test="pageType == 'CART'">
		                 WHERE MNM.CART_PAGE_EXP_FLAG = 'Y'
		</if>
		<if test="pageType == 'CST'">
		                 WHERE MNM.SVD_PAGE_EXP_FLAG = 'Y'
		</if>
		<if test="pageType == 'MKT'">
		                 WHERE MNM.MKT_PAGE_EXP_FLAG = 'Y'
		    <if test="mktType != 'PLP' and mktType != 'PDP'">
		                   AND MNCM.NOTI_ID IS NULL AND MNMM.NOTI_ID IS NULL
		    </if>
		    <if test="mktType == 'PLP'">
		                   AND EXISTS (
		                               SELECT 1
		                                 FROM DSP_NOTI_CATEGORY_R MNCM
		                                INNER JOIN (
		                                            SELECT C.SITE_CODE
		                                                 , CASE WHEN C2.CATEGORY_LV_NO = 1 THEN C2.CATEGORY_CODE
		                                                   END AS superCategoryId
		                                                 , CASE WHEN C1.CATEGORY_LV_NO = 2 THEN C1.CATEGORY_CODE
		                                                        WHEN C2.CATEGORY_LV_NO = 1 THEN C2.CATEGORY_CODE
		                                                   END AS categoryId
		                                                 , CASE WHEN C.CATEGORY_LV_NO = 3 THEN C.CATEGORY_CODE
		                                                        WHEN C1.CATEGORY_LV_NO = 2 THEN C1.CATEGORY_CODE
		                                                        WHEN C2.CATEGORY_LV_NO = 1 THEN C2.CATEGORY_CODE
		                                                   END AS subCategoryId
		                                              FROM DSP_DISPLAY_CATEGORY_M AS C
		                                              LEFT JOIN DSP_DISPLAY_CATEGORY_M AS C1
		                                                ON C.HIGH_LV_CATEGORY_CODE = C1.CATEGORY_CODE
		                                               AND C.SITE_CODE = C1.SITE_CODE
		                                              LEFT JOIN DSP_DISPLAY_CATEGORY_M AS C2
		                                                ON C1.HIGH_LV_CATEGORY_CODE = C2.CATEGORY_CODE
		                                               AND C1.SITE_CODE = C2.SITE_CODE
		                                             WHERE 1=1
		                                               AND (C.CATEGORY_CODE = #{categoryCode}
		                                                    OR C1.CATEGORY_CODE = #{categoryCode}
		                                                    OR C2.CATEGORY_CODE = #{categoryCode}
		                                                   )
		                                               AND C.SITE_CODE = #{siteCode}
		                                           ) MM
		                                WHERE MNM.USE_FLAG = MNCM.USE_FLAG
		                                  AND MNM.SITE_CODE = MNCM.SITE_CODE
		                                  AND MNM.NOTI_ID = MNCM.NOTI_ID
		                                  AND MNCM.SITE_CODE = MM.SITE_CODE
		                                  AND ((MNCM.LV1_CATEGORY_CODE = MM.superCategoryId AND MNCM.LV2_CATEGORY_CODE = '' AND MNCM.LV3_CATEGORY_CODE = '')
		                                        OR (MNCM.LV1_CATEGORY_CODE = MM.superCategoryId AND MNCM.LV2_CATEGORY_CODE = MM.categoryId AND MNCM.LV3_CATEGORY_CODE = '')
		                                        OR (MNCM.LV1_CATEGORY_CODE = MM.superCategoryId AND MNCM.LV2_CATEGORY_CODE = MM.categoryId AND MNCM.LV3_CATEGORY_CODE = MM.subCategoryId)
		                                      )
		                              )
		    </if>
		    <if test="mktType == 'PDP'">
		                   AND EXISTS (
		                               SELECT 1
		                                 FROM DSP_NOTI_PRODUCT_R MNMM
		                                 JOIN DSP_PDP_M MMM ON MNMM.PDP_ID = MMM.PDP_ID
		                                WHERE MNM.NOTI_ID = MNMM.NOTI_ID
		                                  AND MNMM.SITE_CODE = MNM.SITE_CODE
		                                  AND MNMM.USE_FLAG = 'Y'
		                                  AND MMM.LGCOM_SKU_ID = #{sku}
		                                  AND MNMM.USE_FLAG = MMM.USE_FLAG
		                                  AND MNMM.SITE_CODE = MMM.SITE_CODE
		                              )
		                    OR EXISTS (
		                               SELECT 1
		                                 FROM DSP_NOTI_CATEGORY_R MNCM
		                                INNER JOIN (
		                                            SELECT MMM.SITE_CODE
		                                                 , MMCM.LV1_CATEGORY_CODE AS superCategoryId
		                                                 , MMCM.LV2_CATEGORY_CODE AS categoryId
		                                                 , MMCM.LV3_CATEGORY_CODE AS subCategoryId
		                                              FROM DSP_PDP_M MMM
		                                             INNER JOIN DSP_OLD_PDP_CATEGORY_R MMCM
		                                                ON MMCM.SITE_CODE = MMM.SITE_CODE
		                                               AND MMCM.BIZ_TYPE_CODE = MMM.BIZ_TYPE_CODE
		                                               AND MMCM.PDP_ID = MMM.PDP_ID
		                                             WHERE MMM.SITE_CODE = #{siteCode}
		                                               AND MMM.BIZ_TYPE_CODE = #{bizTypeCode}
		                                               AND MMM.LGCOM_SKU_ID = #{sku}
		                                               AND MMCM.DEFAULT_MAP_FLAG = 'Y'
		                                           ) MM
		                                WHERE MNM.USE_FLAG = MNCM.USE_FLAG
		                                  AND MNM.SITE_CODE = MNCM.SITE_CODE
		                                  AND MNM.NOTI_ID = MNCM.NOTI_ID
		                                  AND MNCM.SITE_CODE = MM.SITE_CODE
		                                  AND ((MNCM.LV1_CATEGORY_CODE = MM.superCategoryId AND MNCM.LV2_CATEGORY_CODE = '' AND MNCM.LV3_CATEGORY_CODE = '')
		                                        OR (MNCM.LV1_CATEGORY_CODE = MM.superCategoryId AND MNCM.LV2_CATEGORY_CODE = MM.categoryId AND MNCM.LV3_CATEGORY_CODE = '')
		                                        OR (MNCM.LV1_CATEGORY_CODE = MM.superCategoryId AND MNCM.LV2_CATEGORY_CODE = MM.categoryId AND MNCM.LV3_CATEGORY_CODE = MM.subCategoryId)
		                                      )
		                                  AND MNCM.LOW_PRODUCT_EXP_FLAG = 'Y'
		                              )
		    </if>
		</if>
		                   AND MNM.SITE_CODE = #{siteCode}
		                   AND MNM.BIZ_TYPE_CODE = #{bizTypeCode}
		                   AND MNM.USE_FLAG = 'Y'
		                   AND DATE_FORMAT(#{today}, '%Y%m%d') BETWEEN NVL(MPLM.PROMOTION_LIST_EXP_BEGIN_DD,MNM.NOTI_EXP_BEGIN_DATE) AND NVL(MPLM.PROMOTION_LIST_EXP_END_DD,MNM.NOTI_EXP_END_DATE)
		                 UNION
		                SELECT MNM.NOTI_ID
		                     , MNM.URGENCY_FLAG as NOTIFICATION_TYPE
		                     , MNM.NOTI_MSG_CNTS as NOTIFICATION_MESSAGE
		                     , MNM.DSP_SEQ AS NEW_ORDER_NO
		                     , MNM.BTN_NM as BUTTON_NAME
		                     , MNM.BTN_URL as BUTTON_LINK_URL
		                     , MNM.BTN_SBJ_CODE as BUTTON_LINK_TARGET
		                     , MNM.CREATION_DATE
		                  FROM DSP_NOTI_M MNM
		                 INNER JOIN DSP_NOTI_ETC_R MNEUM
		                    ON MNM.USE_FLAG = MNEUM.USE_FLAG
		                   AND MNM.SITE_CODE = MNEUM.SITE_CODE
		                   AND MNM.NOTI_ID = MNEUM.NOTI_ID
		                   AND MNEUM.ETC_URL_PATH = #{originUrl}
		                  LEFT JOIN PRM_PROMOTION_LIST_M MPLM
		                    ON MPLM.PROMOTION_LIST_ID = MNM.PROMOTION_LIST_ID
		                   AND MNM.PROMOTION_USE_FLAG = 'Y'
		                 WHERE MNM.USE_FLAG = 'Y'
		                   AND MNM.SITE_CODE = #{siteCode}
		                   AND MNM.BIZ_TYPE_CODE = #{bizTypeCode}
		                   AND DATE_FORMAT(#{today}, '%Y%m%d') BETWEEN NVL(MPLM.PROMOTION_LIST_EXP_BEGIN_DD,MNM.NOTI_EXP_BEGIN_DATE) AND NVL(MPLM.PROMOTION_LIST_EXP_END_DD,MNM.NOTI_EXP_END_DATE)
		               ) TMP
		        GROUP BY TMP.NOTI_ID
		       ) TMP2
		 WHERE (NOTIFICATION_TYPE = 'Y' AND RN = 1) OR (NOTIFICATION_TYPE = 'N' AND RN <![CDATA[<=]]> 3)
		 LIMIT 3
	</select>
</mapper>