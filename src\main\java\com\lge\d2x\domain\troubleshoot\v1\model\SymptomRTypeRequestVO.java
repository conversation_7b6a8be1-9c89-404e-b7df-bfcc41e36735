package com.lge.d2x.domain.troubleshoot.v1.model;

import com.lge.d2xfrm.constants.CommonCodes;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SymptomRTypeRequestVO {
    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "corporationCode")
    private String corporationCode;

    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "languageCode")
    private String languageCode;

    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "csMdmsProductCode")
    private String csMdmsProductCode;

    @Schema(description = "installationPageExist")
    private String installationPageExist;

    @Schema(description = "installationPageFlag")
    private String installationPageFlag;

    @Schema(description = "javaLocaleCode")
    private String javaLocaleCode;

    @Schema(description = "siteCode")
    private String siteCode;

    @Schema(description = "localeCode")
    private String localeCode;
}
