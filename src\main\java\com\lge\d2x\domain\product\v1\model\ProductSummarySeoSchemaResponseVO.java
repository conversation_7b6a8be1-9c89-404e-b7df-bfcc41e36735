package com.lge.d2x.domain.product.v1.model;

import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductSummarySeoSchemaResponseVO {
    private String enableSEOSchemaEnergy;
    private String enableSEOSchemaSize;
    private Map<String, String> size;
    private Map<String, Object> hasEnergyConsumptionDetails;
}
