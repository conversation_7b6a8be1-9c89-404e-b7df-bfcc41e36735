<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.productList.v1.repository.pdsmgr.ProductListRepository">
    <select id="selectPdpIdBySkuId" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListBySkuRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdBySkuId */
             A.PDP_ID
        FROM DSP_PDP_M A
       INNER JOIN DSP_PDP_D B
          ON B.SITE_CODE = A.SITE_CODE
         AND B.PDP_ID = A.PDP_ID
         AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		 AND B.SHOP_CODE = 'D2C'
		 AND B.USE_FLAG = 'Y'
		 AND B.AEM_PUBL_FLAG = 'Y'
	   INNER JOIN DSP_PDP_CATEGORY_R C
	      ON C.PDP_ID = A.PDP_ID
	     AND C.SITE_CODE = A.SITE_CODE
	     AND C.USE_FLAG = 'Y'
	     AND C.DEFAULT_MAP_FLAG = 'Y'
	   INNER JOIN DSP_DISPLAY_CATEGORY_M D
		  ON D.SITE_CODE = C.SITE_CODE
		 AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
		 AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		 AND D.USE_FLAG = 'Y'
	   WHERE A.USE_FLAG = 'Y'
	   	 AND A.SITE_CODE = #{siteCode}
	   	 AND A.LGCOM_SKU_ID IN (
		        <foreach item="skuId" collection="skuIds" open="" separator="," close="">
		            #{skuId}
		        </foreach>
		    	)
	   ORDER BY FIELD(A.LGCOM_SKU_ID,
	   			<foreach item="skuId" collection="skuIds" open="" separator="," close="">
			        #{skuId}
			    </foreach>
			    )
    </select>

    <select id="selectPdpIdBySkuIdNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdBySkuId */
             A.PDP_ID
        FROM DSP_PDP_M A
       INNER JOIN DSP_PDP_D B
          ON B.SITE_CODE = A.SITE_CODE
         AND B.PDP_ID = A.PDP_ID
         AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		 AND B.SHOP_CODE = 'D2C'
		 AND B.USE_FLAG = 'Y'
		 AND B.AEM_PUBL_FLAG = 'Y'
	   INNER JOIN DSP_OLD_PDP_CATEGORY_R C
	      ON C.PDP_ID = A.PDP_ID
	     AND C.SITE_CODE = A.SITE_CODE
	     AND C.USE_FLAG = 'Y'
	     AND C.DEFAULT_MAP_FLAG = 'Y'
	   INNER JOIN DSP_DISPLAY_CATEGORY_M D
		  ON D.SITE_CODE = C.SITE_CODE
		 AND D.CATEGORY_CODE = C.LV2_CATEGORY_ID
		 AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		 AND D.USE_FLAG = 'Y'
	   WHERE A.USE_FLAG = 'Y'
	   	 AND A.SITE_CODE = #{siteCode}
		 AND A.LGCOM_SKU_ID IN (
		        <foreach item="skuId" collection="skuIds" open="" separator="," close="">
		            #{skuId}
		        </foreach>
		    	)
	   ORDER BY FIELD(A.LGCOM_SKU_ID,
	   			<foreach item="skuId" collection="skuIds" open="" separator="," close="">
			        #{skuId}
			    </foreach>
			    )
    </select>

    <select id="selectNewestPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdListByListType */
             A.PDP_ID
        FROM DSP_PDP_M A
       INNER JOIN DSP_PDP_CATEGORY_R B
          ON B.PDP_ID = A.PDP_ID
         AND B.USE_FLAG = 'Y'
         AND B.SITE_CODE = #{siteCode}
         AND B.DEFAULT_MAP_FLAG = 'Y'
       <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
         AND B.LV2_CATEGORY_CODE = #{lv2CategoryCode}
       </if>
       <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
         AND B.LV3_CATEGORY_CODE in( #{lv3CategoryCode})
       </if>
       <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag == &quot;N&quot; ">
         AND B.LV3_CATEGORY_CODE != #{lv3CategoryCode}
       </if>
       <if test="lv4CategoryCode != null and lv4CategoryCode !=''">
         AND B.LV4_CATEGORY_CODE = #{lv4CategoryCode}
       </if>
       INNER JOIN DSP_DISPLAY_CATEGORY_M C
          ON C.CATEGORY_CODE = B.LV3_CATEGORY_CODE
         AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
         AND C.SITE_CODE = B.SITE_CODE
         AND C.USE_FLAG = 'Y'
       INNER JOIN DSP_PDP_D D
          ON D.PDP_ID = A.PDP_ID
         AND D.SITE_CODE = #{siteCode}
         AND D.USE_FLAG = 'Y'
         AND D.AEM_PUBL_FLAG ='Y'
         AND D.PRODUCT_STATE_CODE = 'ACTIVE'
		 AND D.SHOP_CODE = 'D2C'
       WHERE A.SITE_CODE = #{siteCode}
         AND A.USE_FLAG = 'Y'
         AND A.PDP_TYPE_CODE != 'A'
       <if test="bizTypeCode != null and bizTypeCode != ''">
         AND A.BIZ_TYPE_CODE = #{bizTypeCode}
       </if>
       <if test="activeCommerceFlag != null and activeCommerceFlag == &quot;Y&quot;">
      	 AND A.PDP_TYPE_CODE != 'O'
       </if>
       <if test="pdpIds != null and pdpIds.size != 0">
          <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
          </foreach>
       </if>
       ORDER BY A.PRODUCT_RELES_DD DESC, D.CREATION_DATE DESC, A.PRODUCT_NM ASC
       LIMIT 10
    </select>

    <select id="selectNewestPdpIdListNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_OLD_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV1_CATEGORY_ID = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode !=''">
           AND B.LV3_CATEGORY_ID = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV2_CATEGORY_ID
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
		   AND D.SHOP_CODE = 'D2C'
         WHERE A.SITE_CODE = #{siteCode}
           AND A.USE_FLAG = 'Y'
           AND A.PDP_TYPE_CODE != 'A'
         <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         </if>
      	 <if test="activeCommerceFlag != null and activeCommerceFlag == &quot;Y&quot;">
      	   AND A.PDP_TYPE_CODE != 'O'
      	 </if>
         <if test="pdpIds != null and pdpIds.size != 0">
             <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
             </foreach>
         </if>
         ORDER BY A.PRODUCT_RELES_DD DESC, D.CREATION_DATE DESC, A.PRODUCT_NM ASC
         LIMIT 10
    </select>

    <select id="selectHighlyRatedPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
    	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV2_CATEGORY_CODE = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV3_CATEGORY_CODE in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode != '' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV3_CATEGORY_CODE != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode != ''">
           AND B.LV4_CATEGORY_CODE = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV3_CATEGORY_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
           AND D.SHOP_CODE = 'D2C'
          LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D E
            ON E.PDP_ID = A.PDP_ID
           AND E.SITE_CODE = #{siteCode}
           AND E.USE_FLAG = 'Y'
         WHERE A.SITE_CODE = #{siteCode}
           AND A.USE_FLAG = 'Y'
           AND A.PDP_TYPE_CODE != 'A'
        <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
        </if>
        <if test="pdpIds != null and pdpIds.size != 0">
          <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
          </foreach>
        </if>
        ORDER BY CAST(ROUND(IFNULL(E.STRARRATING_VAL,'0'),1) AS SIGNED) DESC
               , CAST(IFNULL(E.PARTICIPANT_CNT, 0) AS SIGNED) DESC
               , A.PRODUCT_RELES_DD DESC, D.CREATION_DATE DESC, A.PRODUCT_NM ASC
         LIMIT #{hrLimit}
    </select>

    <select id="selectHighlyRatedPdpIdListNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_OLD_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV1_CATEGORY_ID = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode !=''">
           AND B.LV3_CATEGORY_ID = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV2_CATEGORY_ID
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
		   AND D.SHOP_CODE = 'D2C'
		  LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D E
            ON E.PDP_ID = A.PDP_ID
           AND E.SITE_CODE = #{siteCode}
           AND E.USE_FLAG = 'Y'
         WHERE A.SITE_CODE = #{siteCode}
           AND A.USE_FLAG = 'Y'
           AND A.PDP_TYPE_CODE != 'A'
         <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         </if>
         <if test="pdpIds != null and pdpIds.size != 0">
             <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
             </foreach>
         </if>
        ORDER BY CAST(ROUND(IFNULL(E.STRARRATING_VAL,'0'),1) AS SIGNED) DESC
               , CAST(IFNULL(E.PARTICIPANT_CNT, 0) AS SIGNED) DESC
               , A.PRODUCT_RELES_DD DESC, D.CREATION_DATE DESC, A.PRODUCT_NM ASC
         LIMIT #{hrLimit}
    </select>

    <select id="selectMostPopularPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV2_CATEGORY_CODE = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV3_CATEGORY_CODE in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode != '' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV3_CATEGORY_CODE != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode != ''">
           AND B.LV4_CATEGORY_CODE = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV3_CATEGORY_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
           AND D.SHOP_CODE = 'D2C'
         LEFT JOIN
             (
               SELECT E.FRONT_ACCESS_ID
                    , SUM(E.ACCESS_CNT) AS COUNT_NO
                    , E.SITE_CODE
                 FROM DSP_FRONT_ACCESS_COUNT_D E
                WHERE E.SITE_CODE = #{siteCode}
                  AND E.LOG_DD <![CDATA[>]]> TIMESTAMPADD(MONTH,-1, #{today})
                  AND E.USE_FLAG = 'Y'
                  AND E.PAGE_SP_CODE = 'PDP'
                <if test="bizTypeCode != null and bizTypeCode != ''">
                  AND E.BIZ_TYPE_CODE = #{bizTypeCode}
                </if>
                GROUP BY E.FRONT_ACCESS_ID, E.SITE_CODE
             ) AS F
            ON F.FRONT_ACCESS_ID = A.PDP_ID
           AND F.SITE_CODE = A.SITE_CODE
         WHERE A.SITE_CODE = #{siteCode}
           AND A.USE_FLAG = 'Y'
           AND A.PDP_TYPE_CODE != 'A'
         <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         </if>
         <if test="pdpIds != null and pdpIds.size != 0">
          <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
          </foreach>
         </if>
         ORDER BY F.COUNT_NO DESC, A.PRODUCT_RELES_DD, A.PRODUCT_NM ASC
         LIMIT 10
    </select>

    <select id="selectMostPopularPdpIdListNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_OLD_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV1_CATEGORY_ID = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode !=''">
           AND B.LV3_CATEGORY_ID = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV2_CATEGORY_ID
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
           AND D.SHOP_CODE = 'D2C'
         LEFT JOIN
             (
               SELECT E.FRONT_ACCESS_ID
                    , SUM(E.ACCESS_CNT) AS COUNT_NO
                    , E.SITE_CODE
                 FROM DSP_FRONT_ACCESS_COUNT_D E
                WHERE E.SITE_CODE = #{siteCode}
                  AND E.LOG_DD <![CDATA[>]]> TIMESTAMPADD(MONTH,-1, #{today})
                  AND E.USE_FLAG = 'Y'
                  AND E.PAGE_SP_CODE = 'PDP'
                <if test="bizTypeCode != null and bizTypeCode != ''">
                  AND E.BIZ_TYPE_CODE = #{bizTypeCode}
                </if>
                GROUP BY E.FRONT_ACCESS_ID, E.SITE_CODE
             ) AS F
            ON F.FRONT_ACCESS_ID = A.PDP_ID
           AND F.SITE_CODE = A.SITE_CODE
         WHERE A.SITE_CODE = #{siteCode}
           AND A.USE_FLAG = 'Y'
           AND A.PDP_TYPE_CODE != 'A'
         <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         </if>
         <if test="pdpIds != null and pdpIds.size != 0">
          <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
          </foreach>
         </if>
         ORDER BY F.COUNT_NO DESC, A.PRODUCT_RELES_DD, A.PRODUCT_NM ASC
         LIMIT 10
    </select>

    <select id="selectAccessoryPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdListByListType */
		       A.PDP_ID
		  FROM (
		         SELECT JSON_UNQUOTE(JSON_EXTRACT(
    					JSON_ARRAY(MAP_ACSRY_PDP_ID1, MAP_ACSRY_PDP_ID2, MAP_ACSRY_PDP_ID3, MAP_ACSRY_PDP_ID4, MAP_ACSRY_PDP_ID5, MAP_ACSRY_PDP_ID6, MAP_ACSRY_PDP_ID7, MAP_ACSRY_PDP_ID8, MAP_ACSRY_PDP_ID9, MAP_ACSRY_PDP_ID10),
    					CONCAT('$[', idx, ']')
						)) AS PDP_ID
					   , A.SITE_CODE
					FROM DSP_PRODUCT_ACCESSORY_D A
				   INNER JOIN (SELECT 0 AS idx UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
			        			SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS idxs
			 	   WHERE A.SITE_CODE = #{siteCode}
			 	     AND A.PDP_ID = #{pdpId}
			 	     AND A.USE_FLAG = 'Y'
		        ) AS A
		 INNER JOIN DSP_PDP_M B
		    on B.SITE_CODE = A.SITE_CODE
		   AND B.PDP_ID = A.PDP_ID
		   AND B.USE_FLAG = 'Y'
		 INNER JOIN DSP_PDP_D C
		    ON C.SITE_CODE = B.SITE_CODE
		   AND C.PDP_ID = B.PDP_ID
		   AND C.BIZ_TYPE_CODE  = B.BIZ_TYPE_CODE
		   AND C.SHOP_CODE = 'D2C'
		   AND C.USE_FLAG = 'Y'
		   AND C.AEM_PUBL_FLAG = 'Y'
		   AND C.PRODUCT_STATE_CODE = 'ACTIVE'
		 WHERE A.PDP_ID IS NOT NULL
		   AND A.PDP_ID <![CDATA[<>]]> ''
		 ORDER BY B.PRODUCT_RELES_DD DESC, C.CREATION_DATE DESC, B.PRODUCT_NM ASC
    </select>

    <select id="selectProductListPdpInfo" parameterType="com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoRequestVO" resultType="com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoResponseVO">
        SELECT AAA.*
		     , CASE WHEN 'HR' = #{listType}
		            THEN  CASE WHEN COUNT(AAA.PDP_ID) OVER() <![CDATA[>]]> 10
		                       THEN 10
		                       ELSE COUNT(AAA.PDP_ID) OVER()
		                    END
		            ELSE  COUNT(AAA.PDP_ID) OVER()
		        END AS TOTAL_COUNT
		  FROM
		      (
				SELECT
						ROW_NUMBER() OVER (PARTITION BY IFNULL(CCC.SIBLING_GRP_CODE, CCC.PDP_ID) ORDER BY IFNULL(CCC.PLP_HIGHLIGHT_MODEL_FLAG,'Y') DESC) AS CNT
			          <if test="listType == 'HR'">
			          , ROW_NUMBER() OVER (ORDER BY CCC.S_RATING3 DESC, CAST(IFNULL(CCC.P_COUNT, 0) AS SIGNED) DESC, FIELD(CCC.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)) AS HR_RATING_NUM
			          </if>
			          , CCC.*
			      FROM
             (
				SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectProductListPdpInfo */
				       A.PDP_ID
				     , A.LGCOM_SKU_ID
				     , A.SKU_ID
				     , A.PRODUCT_NM
				     , IFNULL(NULLIF(R.NEW_MKT_PRODUCT_NM, ''), A.USER_FRNDY_PRODUCT_NM) AS USER_FRNDY_PRODUCT_NM
				     , A.PDP_TYPE_CODE
				     , A.BIZ_TYPE_CODE
				     , O.SALES_MODEL_CODE
				     , O.SALES_MODEL_SUFFIX_CODE
				     , CASE WHEN A.SITE_CODE = 'SA' OR A.SITE_CODE = 'SA_EN' THEN ROUND(IFNULL(A.MSRP_SALES_PRICE, 0.00)) ELSE IFNULL(A.MSRP_SALES_PRICE, 0.00) END AS msrpSalesPrice
				     , IFNULL(A.WTB_USE_FLAG, 'N') AS wtbUseFlag
				     , IFNULL(A.WTB_EXTL_LINK_USE_FLAG, 'N') AS wtbExternalLinkUseFlag
				     , A.WTB_EXTL_LINK_NM
				     , A.WTB_EXTL_LINK_URL
				     , A.WTB_EXTL_LINK_SELF_SCREEN_FLAG
				     , A.ELABEL_GRD_CODE
				     , A.ELABEL_CLS_CODE
				     , A.SECOND_ELABEL_GRD_CODE
				     , A.SECOND_ELABEL_CLS_CODE
				     , A.WTOWER_PRODUCT_FLAG
				     , A.SIGNT_PRODUCT_FLAG
				     , A.ITB_USE_FLAG
				     , A.EXCL_PRODUCT_SETT_CODE
				     , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productRelesDd
				     , C.PDP_URL
				     , C.PRODUCT_STATE_CODE
				     , IFNULL(C.SML_IMG_URL, #{dummyImageUrl}) AS smlImgUrl
				     , IFNULL(C.MDM_IMG_URL, #{dummyImageUrl}) AS mdmImgUrl
				     , IFNULL(C.BIG_IMG_URL, #{dummyImageUrl}) AS bigImgUrl
				     , C.IMG_ALT_TEXT_CNTS
				     , C.CREATION_DATE
				     , C.PRODUCT_THEME_TYPE_CODE
				     , '' AS DEFAULT_PRODUCT_TAG_CODE
				     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1)
				            THEN
				                 CASE WHEN C.PRODUCT_TAG_USE_FLAG1 = 'Y'
				                      THEN C.PRODUCT_TAG_CODE1
				                      ELSE NULL
				                  END
				            ELSE NULL
				        END AS productTagCode1
				     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1)
				            THEN
				                 CASE WHEN C.PRODUCT_TAG_USE_FLAG1 = 'Y'
				                      THEN TO_CHAR(C.PRODUCT_TAG_EXP_END_DATE1, 'YYYYMMDD')
				                      ELSE NULL
				                  END
				            ELSE NULL
				        END AS productTagExpEndDate1
				     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.pRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2)
				            THEN
				                 CASE WHEN C.PRODUCT_TAG_USE_FLAG2 = 'Y'
				                      THEN C.PRODUCT_TAG_CODE2
				                      ELSE NULL
				                  END
				            ELSE NULL
				        END AS productTagCode2
				     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2)
				            THEN
				                 CASE WHEN C.PRODUCT_TAG_USE_FLAG2 = 'Y'
				                      THEN TO_CHAR(C.PRODUCT_TAG_EXP_END_DATE2, 'YYYYMMDD')
				                      ELSE NULL
				                  END
				            ELSE NULL
				        END AS productTagExpEndDate2
				     , D.LV1_CATEGORY_CODE
				     , D.LV2_CATEGORY_CODE
				     , D.LV3_CATEGORY_CODE
				     , D.LV4_CATEGORY_CODE
				     , E.SITE_CATEGORY_NM AS siteCategoryNm
				     , E.CATEGORY_NM AS categoryNm
					 , COALESCE(NULLIF(SUBSTRING_INDEX(H.CATEGORY_PAGE_URL, '/', -1), ''), G.CATEGORY_NM) AS superCategoryNm
				     , IFNULL(I.PARTICIPANT_CNT, 0) AS P_COUNT
				     , ROUND(IFNULL(I.STRARRATING_VAL, 0) * 20, 0) AS RATING_PERCENT
				     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 0) AS S_RATING
				     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 1) AS S_RATING2
				   <if test="listType == 'HR'">
                     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 2) AS S_RATING3
                   </if>
		     		 , N.SIBLING_GRP_CODE
		     		 , N.SIBLING_CODE
		     		 , N.SIBLING_GRP_NM
		     		 , IFNULL(N.DEFAULT_SIBLING_MODEL_FLAG, 'N') AS DEFAULT_SIBLING_MODEL_FLAG
		     		 , IFNULL(N.PLP_HIGHLIGHT_MODEL_FLAG, 'Y') AS PLP_HIGHLIGHT_MODEL_FLAG
		     		 , N.SIBLING_LOCAL_VAL
		     		 , N.SIBLING_TYPE_CODE
		     		 , N.SIBLING_SBJ_TYPE_CODE
     				 , Q.EXTERNAL_LINK_TARGET
     				 , D_LV1.CATEGORY_NM AS lv1CategoryCodeNm
				     , D_LV2.CATEGORY_NM AS lv2CategoryCodeNm
				     , D_LV3.CATEGORY_NM AS lv3CategoryCodeNm
				     , D_LV4.CATEGORY_NM AS lv4CategoryCodeNm
			<choose>
				<when test="pageType != null and pageType != '' and pageType == 'PLP' ">
                     , CASE WHEN A.PROMOTION_TAG_USE_ID IS NOT NULL AND A.PROMOTION_TAG_USE_ID !=''
                            THEN IFNULL((SELECT PT.PROMOTION_TAG_VAL FROM PRM_PROMOTION_M PT WHERE PT.SITE_CODE = A.SITE_CODE AND PT.PROMOTION_ID = A.PROMOTION_TAG_USE_ID AND DATE_FORMAT(#{today}, '%Y%m%d') BETWEEN PT.PROMOTION_EXP_BEGIN_DATE AND PT.PROMOTION_EXP_END_DATE),'')
                            ELSE IFNULL(Q.PROMOTION_TAG,'')
                        END AS PROMOTION_TEXT
                     , CASE WHEN A.PROMOTION_TAG_USE_ID IS NOT NULL AND A.PROMOTION_TAG_USE_ID !=''
                            THEN IFNULL((SELECT PL.PROMOTION_LINK_PATH FROM PRM_PROMOTION_LIST_M PL WHERE PL.SITE_CODE = A.SITE_CODE AND PL.PROMOTION_LIST_ID = A.PROMOTION_TAG_USE_ID),'')
                        	ELSE IFNULL(Q.PROMOTION_LINK_URL,'')
                        END AS PROMOTION_LINK_URL
               </when>
			   <otherwise>
					 , IFNULL(Q.PROMOTION_TAG,'')            AS PROMOTION_TEXT
			         , IFNULL(Q.PROMOTION_LINK_URL,'')       AS PROMOTION_LINK_URL
			   </otherwise>
			</choose>
				  FROM DSP_PDP_M A
				 INNER JOIN DSP_PDP_D C
				    ON C.SITE_CODE = A.SITE_CODE
				   AND C.PDP_ID = A.PDP_ID
				   AND C.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
				<if test="orderListFlag == null or orderListFlag == ''">
				   AND C.USE_FLAG = 'Y'
				   AND C.AEM_PUBL_FLAG ='Y'
				 </if>
				   AND C.SHOP_CODE = 'D2C'
			   <choose>
			     <when test='orderListFlag != null and orderListFlag != "" and orderListFlag eq "Y"'>
			     	AND C.PRODUCT_STATE_CODE != 'SUSPENDED'
			     </when>
			     <otherwise>
			     	<choose>
						 <when test="listType == 'ALL' or listType == 'ALLSTATUS'">
						   AND C.PRODUCT_STATE_CODE != 'SUSPENDED'
						 </when>
						 <when test='skuListFlag == "Y" and hiddenModelSearchFlag == "Y"'>
						   AND C.PRODUCT_STATE_CODE IN ('ACTIVE','HIDDEN')
						 </when>
						 <otherwise>
						   AND C.PRODUCT_STATE_CODE = 'ACTIVE'
						 </otherwise>
					 </choose>
				 </otherwise>
			   </choose>
				 INNER JOIN DSP_PDP_CATEGORY_R D
				    ON D.SITE_CODE = C.SITE_CODE
				   AND D.PDP_ID = C.PDP_ID
				   AND D.USE_FLAG = 'Y'
				   AND D.DEFAULT_MAP_FLAG = 'Y'
				 INNER JOIN DSP_DISPLAY_CATEGORY_M E
				    ON E.SITE_CODE = D.SITE_CODE
				   AND E.CATEGORY_CODE = D.LV3_CATEGORY_CODE
				 INNER JOIN DSP_DISPLAY_CATEGORY_D F
				    ON F.SITE_CODE = E.SITE_CODE
				   AND F.CATEGORY_CODE = E.CATEGORY_CODE
				   AND F.SHOP_CODE = 'D2C'
				 INNER JOIN PDM_PRODUCT_M O
				    ON O.SKU_ID  = A.SKU_ID
				 INNER JOIN PDM_PRODUCT_D R
				    ON R.SKU_ID = O.SKU_ID
				   AND R.USE_FLAG = O.USE_FLAG
				  LEFT OUTER JOIN PDM_PRODUCT_CATEGORY_R S
				    ON S.SKU_ID = R.SKU_ID
				   AND S.USE_FLAG = R.USE_FLAG
				   AND S.LOCALE_CODE = R.LOCALE_CODE
				  LEFT OUTER JOIN PDM_CATEGORY_M D_LV1
			    	ON D_LV1.CATEGORY_CODE = S.LV1_CATEGORY_CODE
			       AND D_LV1.USE_FLAG = R.USE_FLAG
                   AND A.BIZ_TYPE_CODE = D_LV1.BIZ_TYPE_CODE
			       AND D_LV1.CATEGORY_LV_NO = 1
				  LEFT OUTER JOIN PDM_CATEGORY_M D_LV2
			    	ON D_LV2.CATEGORY_CODE = S.LV2_CATEGORY_CODE
			       AND D_LV2.CATEGORY_LV_NO = 2
			       AND D_LV2.USE_FLAG = A.USE_FLAG
                   AND D_LV2.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
                   AND D_LV2.HIGH_CATEGORY_CODE = D_LV1.CATEGORY_CODE
			 	  LEFT OUTER JOIN PDM_CATEGORY_M D_LV3
			    	ON D_LV3.CATEGORY_CODE = S.LV3_CATEGORY_CODE
			       AND D_LV3.CATEGORY_LV_NO = 3
			       AND D_LV3.USE_FLAG = A.USE_FLAG
                   AND D_LV3.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
                   AND D_LV3.HIGH_CATEGORY_CODE = D_LV2.CATEGORY_CODE
				  LEFT OUTER JOIN PDM_CATEGORY_M D_LV4
			    	ON D_LV4.CATEGORY_CODE = S.LV4_CATEGORY_CODE
			       AND D_LV4.CATEGORY_LV_NO = 4
			       AND D_LV4.USE_FLAG = A.USE_FLAG
                   AND D_LV4.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
                   AND D_LV4.HIGH_CATEGORY_CODE = D_LV3.CATEGORY_CODE
				  LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M G
				    ON G.SITE_CODE = D.SITE_CODE
				   AND G.CATEGORY_CODE = D.LV2_CATEGORY_CODE
				  LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D H
				    ON H.SITE_CODE = G.SITE_CODE
				   AND H.CATEGORY_CODE = G.CATEGORY_CODE
				   AND H.SHOP_CODE = 'D2C'
				  LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D I
				    ON I.PDP_ID = A.PDP_ID
				   AND I.SITE_CODE = A.SITE_CODE
				   AND I.USE_FLAG = 'Y'
				  LEFT OUTER JOIN (
				  						 SELECT DISTINCT
		                                        K.PDP_ID
		                                      , K.SIBLING_GRP_CODE
		                                      , K.SIBLING_CODE
		                                      , K.DEFAULT_SIBLING_MODEL_FLAG
		                                      , K.PLP_HIGHLIGHT_MODEL_FLAG
		                                      , M.CATEGORY_CODE
		                                      , M.SIBLING_TYPE_CODE
		                                      , J.SIBLING_LOCAL_VAL
		                                      , K.SIBLING_GRP_NM
		                                      , K.SIBLING_SBJ_TYPE_CODE
		                                   FROM DSP_SIBLING_M J
		                                  INNER JOIN DSP_SIBLING_D K
		                                     ON K.SITE_CODE = J.SITE_CODE
		                                    AND K.SIBLING_CODE = J.SIBLING_CODE
		                                    AND K.SIBLING_TYPE_CODE = J.SIBLING_TYPE_CODE
		                                    AND K.SIBLING_GRP_USE_FLAG = 'Y'
		                                    AND K.USE_FLAG = 'Y'
		                                  <if test="bizTypeCode != null and bizTypeCode != ''">
		                                    AND K.BIZ_TYPE_CODE = #{bizTypeCode}
		                                  </if>
		                                  INNER JOIN DSP_SIBLING_EXP_SEQ_D M
		                                     ON M.SIBLING_TYPE_CODE = J.SIBLING_TYPE_CODE
		                                    AND M.BIZ_TYPE_CODE = J.BIZ_TYPE_CODE
		                                    AND M.SITE_CODE = #{siteCode}
		                                    AND M.USE_FLAG = 'Y'
		                                    AND M.PLP_DEFAULT_SIBLING_FLAG = 'Y'
		                                  WHERE J.USE_FLAG = 'Y'
		                                    AND J.SITE_CODE = #{siteCode}
	                                    <if test="bizTypeCode != null and bizTypeCode != ''">
		                                    AND J.BIZ_TYPE_CODE = #{bizTypeCode}
		                                </if>
		                                    AND K.PDP_ID IN (<foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
		                                ) N
		            ON N.PDP_ID = A.PDP_ID
		           AND N.CATEGORY_CODE = D.LV3_CATEGORY_CODE
		          LEFT OUTER JOIN
					              (
					              	SELECT P.PDP_ID
									     , P.PROMOTION_TAG
									     , P.PROMOTION_LINK_URL
									     , P.EXTERNAL_LINK_TARGET
									  FROM (
									       SELECT
										    	  A.PDP_ID
											<choose>
											  <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
											    , C.CAMPAIGN_BTN_NM AS PROMOTION_TAG
											    , C.CAMPAIGN_LINK_PATH AS PROMOTION_LINK_URL
											    , C.CAMPAIGN_BLANK_FLAG AS EXTERNAL_LINK_TARGET
											  </when>
											  <otherwise>
											    , B.PROMOTION_TAG_VAL AS PROMOTION_TAG
											    , D.PROMOTION_LINK_PATH AS PROMOTION_LINK_URL
											    , D.PROMOTION_LINK_SBJ_CODE AS EXTERNAL_LINK_TARGET
											  </otherwise>
											</choose>
											 FROM PRM_PROMOTION_PRODUCT_R A
											INNER JOIN PRM_PROMOTION_M B
											   ON B.SITE_CODE = A.SITE_CODE
											  AND B.PROMOTION_ID = A.PROMOTION_ID
											  AND B.USE_FLAG = 'Y'
											<choose>
											  <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
											INNER JOIN PRM_PROMOTION_PRODUCT_CAMP_R C
											   ON C.PROMOTION_ID = A.PROMOTION_ID
											  AND C.PDP_ID = A.PDP_ID
											  AND C.SITE_CODE = A.SITE_CODE
											  AND C.USE_FLAG = 'Y'
											  AND C.DSP_SEQ = A.PDP_PROMOTION_SEQ
											  AND C.PROMOTION_ID = #{promotionId}
											  </when>
											  <otherwise>
											 LEFT OUTER JOIN PRM_PROMOTION_LIST_M D
											   ON D.PROMOTION_LIST_ID = B.PROMOTION_ID
											  AND D.SITE_CODE = B.SITE_CODE
											  AND D.USE_FLAG = 'Y'
											  </otherwise>
											</choose>
										    WHERE A.SITE_CODE = #{siteCode}
											  AND STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PROMOTION_EXP_BEGIN_DATE AND B.PROMOTION_EXP_END_DATE
											  AND IFNULL(B.PROMOTION_NONEXP_FLAG, 'N') <![CDATA[<>]]> 'Y'
											  AND A.USE_FLAG = 'Y'
										    ORDER BY A.PDP_PROMOTION_SEQ ASC
					                          ) P
					                 GROUP BY P.PDP_ID, P.PROMOTION_TAG, P.PROMOTION_LINK_URL, P.EXTERNAL_LINK_TARGET
					              ) Q
					ON Q.PDP_ID = A.PDP_ID
				 WHERE A.SITE_CODE = #{siteCode}
				 <if test="orderListFlag == null or orderListFlag == ''">
				   AND A.USE_FLAG = 'Y'
				 </if>
				 <if test="bizTypeCode != null and bizTypeCode != ''">
				   AND A.BIZ_TYPE_CODE = #{bizTypeCode}
				 </if>
				   AND A.PDP_ID IN (<foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
				) CCC
			) AAA
		  <if test='siblingGroupFlag == "Y" and listType != "PAGINATION" '>
		    WHERE AAA.CNT = 1
		  </if>
		  <choose>
		    <when test="listType == 'HR'">
		     ORDER BY HR_RATING_NUM
		     LIMIT 10
		    </when>
		    <otherwise>
		     ORDER BY FIELD(AAA.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
			</otherwise>
		  </choose>
    </select>

	<select id="selectProductListPdpInfoNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoRequestVO" resultType="com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoResponseVO">
		SELECT AAA.*
		     , CASE WHEN 'HR' = #{listType}
		            THEN CASE WHEN COUNT(AAA.PDP_ID) OVER() <![CDATA[>]]> 10
		                      THEN 10
		                      ELSE COUNT(AAA.PDP_ID) OVER()
		                 END
		            ELSE COUNT(AAA.PDP_ID) OVER()
		       END AS TOTAL_COUNT
		  FROM
		       (
		        SELECT
		               ROW_NUMBER() OVER (PARTITION BY IFNULL(CCC.SIBLING_GRP_CODE, CCC.PDP_ID) ORDER BY IFNULL(CCC.PLP_HIGHLIGHT_MODEL_FLAG,'Y') DESC) AS CNT
		<if test="listType == 'HR'">
		             , ROW_NUMBER() OVER (ORDER BY CCC.S_RATING3 DESC, CAST(IFNULL(CCC.P_COUNT, 0) AS SIGNED) DESC, FIELD(CCC.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)) AS HR_RATING_NUM
		</if>
		             , CCC.*
		          FROM
		               (
		                SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectProductListPdpInfo */
		                       DISTINCT
		                       A.PDP_ID
		                     , A.LGCOM_SKU_ID
		                     , A.SKU_ID
		                     , A.PRODUCT_NM
		                     , IFNULL(NULLIF(R.NEW_MKT_PRODUCT_NM, ''), A.USER_FRNDY_PRODUCT_NM) AS USER_FRNDY_PRODUCT_NM
		                     , A.PDP_TYPE_CODE
		                     , A.BIZ_TYPE_CODE
		                     , O.SALES_MODEL_CODE
		                     , O.SALES_MODEL_SUFFIX_CODE
		                     , O.LV1_PRODUCT_CODE
		                     , CASE WHEN A.SITE_CODE = 'SA' OR A.SITE_CODE = 'SA_EN' THEN ROUND(IFNULL(A.MSRP_SALES_PRICE, 0.00)) ELSE IFNULL(A.MSRP_SALES_PRICE, 0.00) END AS MSRP_SALES_PRICE
		                     , IFNULL(A.WTB_USE_FLAG, 'N') AS WTB_USE_FLAG
		                     , IFNULL(A.WTB_EXTL_LINK_USE_FLAG, 'N') AS WTB_EXTL_LINK_USE_FLAG
		                     , A.WTB_EXTL_LINK_NM
		                     , A.WTB_EXTL_LINK_URL
		                     , A.WTB_EXTL_LINK_SELF_SCREEN_FLAG
		                     , A.ELABEL_GRD_CODE
		                     , A.ELABEL_CLS_CODE
		                     , T.EL_DOC_TYPE_CODE
		                     , T.PIS_DOC_TYPE_CODE
		                     , A.SECOND_ELABEL_GRD_CODE
		                     , A.SECOND_ELABEL_CLS_CODE
		                     , T.SECOND_EL_DOC_TYPE_CODE
		                     , T.SECOND_PIS_DOC_TYPE_CODE
		                     , T.SECOND_PF_CODE
		                     , T.EL_TYPE_CODE
		                     , T.SECOND_EL_TYPE_CODE
		                     , T.PIS_DOC_OLD_FLAG
		                     , A.WTOWER_PRODUCT_FLAG
		                     , A.SIGNT_PRODUCT_FLAG
                             , IFNULL(A.RETAILER_PRICING_FLAG, 'N') AS RETAILER_PRICING_FLAG
		                     , A.ITB_USE_FLAG
		                     , A.EXCL_PRODUCT_SETT_CODE
		                     , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS PRODUCT_RELES_DD
		                     , C.PDP_URL
		                     , C.PRODUCT_STATE_CODE
		                     , (SELECT CONCAT('[', GROUP_CONCAT(CONCAT('{"shop_code":"', MTSM.SHOP_CODE, '","model_url_path":"', MTSM.PDP_URL, '"}')), ']')
		                          FROM DSP_PDP_D MTSM
		                         WHERE MTSM.SITE_CODE = A.SITE_CODE
		                           AND MTSM.PDP_ID = A.PDP_ID
		                           AND MTSM.SHOP_CODE <![CDATA[<>]]> 'D2C'
		                       ) AS MTS_PDP_URL
		                     , IFNULL(C.SML_IMG_URL, #{dummyImageUrl}) AS SML_IMG_URL
		                     , IFNULL(C.MDM_IMG_URL, #{dummyImageUrl}) AS MDM_IMG_URL
		                     , IFNULL(C.BIG_IMG_URL, #{dummyImageUrl}) AS BIG_IMG_URL
		                     , C.IMG_ALT_TEXT_CNTS
		                     , C.CREATION_DATE
		                     , C.PRODUCT_THEME_TYPE_CODE
		                     , '' AS DEFAULT_PRODUCT_TAG_CODE
		                     , IFNULL(C.PRODUCT_TAG_USE_TYPE_CODE1, '') AS PRODUCT_TAG_USER_TYPE_CODE1
		                     , IFNULL(C.PRODUCT_TAG_USE_TYPE_CODE2, '') AS PRODUCT_TAG_USER_TYPE_CODE2
		                     , CASE WHEN CURDATE() BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1
		                            THEN IF(C.PRODUCT_TAG_USE_FLAG1 = 'Y', C.PRODUCT_TAG_CODE1, NULL)
		                            ELSE NULL
		                       END AS PRODUCT_TAG_CODE1
		                     , CASE WHEN CURDATE() BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1
		                            THEN IF(C.PRODUCT_TAG_USE_FLAG1 = 'Y', C.PRODUCT_TAG_EXP_BEGIN_DATE1, NULL) -- DATE_FORMAT(C.PRODUCT_TAG_EXP_BEGIN_DATE1, '%Y%m%d')
		                            ELSE NULL
		                       END AS PRODUCT_TAG_EXP_BEGIN_DATE1
		                     , CASE WHEN CURDATE() BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1 
		                            THEN IF(C.PRODUCT_TAG_USE_FLAG1 = 'Y' AND C.PRODUCT_TAG_EXP_END_DATE1 IS NOT NULL, DATE_ADD(C.PRODUCT_TAG_EXP_END_DATE1, interval 1 day), NULL)  -- DATE_FORMAT(C.PRODUCT_TAG_EXP_END_DATE1, '%Y%m%d')
		                            ELSE NULL
		                       END AS PRODUCT_TAG_EXP_END_DATE1
		                     , CASE WHEN CURDATE() BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2
		                            THEN IF(C.PRODUCT_TAG_USE_FLAG2 = 'Y', C.PRODUCT_TAG_CODE2, NULL)
		                            ELSE NULL
		                       END AS PRODUCT_TAG_CODE2
		                     , CASE WHEN CURDATE() BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2 
		                            THEN IF(C.PRODUCT_TAG_USE_FLAG2 = 'Y', C.PRODUCT_TAG_EXP_BEGIN_DATE2, NULL)  -- DATE_FORMAT(C.PRODUCT_TAG_EXP_BEGIN_DATE2, '%Y%m%d')
		                            ELSE NULL
		                       END AS PRODUCT_TAG_EXP_BEGIN_DATE2
		                     , CASE WHEN CURDATE() BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2
		                            THEN IF(C.PRODUCT_TAG_USE_FLAG2 = 'Y' AND C.PRODUCT_TAG_EXP_END_DATE2 IS NOT NULL, DATE_ADD(C.PRODUCT_TAG_EXP_END_DATE2, interval 1 day), NULL)  -- DATE_FORMAT(C.PRODUCT_TAG_EXP_END_DATE2, '%Y%m%d')
		                            ELSE NULL
		                       END AS PRODUCT_TAG_EXP_END_DATE2
		                     , D.LV1_CATEGORY_ID
		                     , D.LV2_CATEGORY_ID
		                     , D.LV3_CATEGORY_ID
		                     , E.SITE_CATEGORY_NM
		                     , E.CATEGORY_NM
		                     , G.CATEGORY_NM AS SUPER_CATEGORY_NM
		                     , IFNULL(I.PARTICIPANT_CNT, 0) AS P_COUNT
		                     , ROUND(IFNULL(I.STRARRATING_VAL, 0) * 20, 0) AS RATING_PERCENT
		                     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 0) AS S_RATING
		                     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 1) AS S_RATING2
		<if test="listType == 'HR'">
		                     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 2) AS S_RATING3
		</if>
		                     , N.SIBLING_GRP_CODE
		                     , N.SIBLING_CODE
		                     , N.SIBLING_GRP_NM
		                     , N.SIBLING_SBJ_TYPE_CODE
		                     , IFNULL(N.DEFAULT_SIBLING_MODEL_FLAG, 'N') AS DEFAULT_SIBLING_MODEL_FLAG
		                     , IFNULL(N.PLP_HIGHLIGHT_MODEL_FLAG, 'Y') AS PLP_HIGHLIGHT_MODEL_FLAG
		                     , N.SIBLING_LOCAL_VAL
		                     , N.SIBLING_TYPE_CODE
		                     , Q.EXTERNAL_LINK_TARGET
		                     , D_LV1.CATEGORY_NM AS LV1_CATEGORY_CODE_NM
		                     , D_LV2.CATEGORY_NM AS LV2_CATEGORY_CODE_NM
		                     , D_LV3.CATEGORY_NM AS LV3_CATEGORY_CODE_NM
		                     , D_LV4.CATEGORY_NM AS LV4_CATEGORY_CODE_NM
		                     , U.EPS_USE_FLAG
		                     , U.EPS_USB_PD_NM
		                     , U.EPS_MAX_VOLTAGE
		                     , U.EPS_MIN_VOLTAGE
		<choose>
		    <when test="pageType != null and pageType != '' and pageType == 'PLP' ">
		                     , CASE WHEN A.PROMOTION_TAG_USE_ID IS NOT NULL AND A.PROMOTION_TAG_USE_ID !=''
		                            THEN IFNULL((SELECT PT.PROMOTION_TAG_VAL FROM PRM_PROMOTION_M PT WHERE PT.SITE_CODE = A.SITE_CODE AND PT.PROMOTION_ID = A.PROMOTION_TAG_USE_ID AND DATE_FORMAT(#{today}, '%Y%m%d') BETWEEN PT.PROMOTION_EXP_BEGIN_DATE AND PT.PROMOTION_EXP_END_DATE),'')
		                            ELSE IFNULL(Q.PROMOTION_TAG,'')
		                       END AS PROMOTION_TEXT
		                     , CASE WHEN A.PROMOTION_TAG_USE_ID IS NOT NULL AND A.PROMOTION_TAG_USE_ID !=''
		                            THEN IFNULL((SELECT PL.PROMOTION_LINK_PATH FROM PRM_PROMOTION_LIST_M PL WHERE PL.SITE_CODE = A.SITE_CODE AND PL.PROMOTION_LIST_ID = A.PROMOTION_TAG_USE_ID),'')
		                            ELSE IFNULL(Q.PROMOTION_LINK_URL,'')
		                       END AS PROMOTION_LINK_URL
		    </when>
		    <otherwise>
		                     , IFNULL(Q.PROMOTION_TAG,'') AS PROMOTION_TEXT
		                     , IFNULL(Q.PROMOTION_LINK_URL,'') AS PROMOTION_LINK_URL
		    </otherwise>
		</choose>
		<if test="listType == 'LGPICK'">
		                     , V.SPOTLIGHTS_DSP_SEQ
		</if>
		                     , IF(W.STOCK_STATE_CODE = 'IN_STOCK', 'Y', 'N') AS OBS_SELL_FLAG
		                     , A.INQUIRY_FLAG 
		                     , A.FIND_DEALER_USE_FLAG 
		                  FROM DSP_PDP_M A
		                 INNER JOIN DSP_PDP_D C
		                    ON C.SITE_CODE = A.SITE_CODE
		                   AND C.PDP_ID = A.PDP_ID
		                   AND C.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		<if test="orderListFlag == null or orderListFlag == ''">
		                   AND C.USE_FLAG = 'Y'
		                   AND C.AEM_PUBL_FLAG ='Y'
		</if>
		                   AND C.SHOP_CODE = 'D2C'
		<choose>
		    <when test='orderListFlag != null and orderListFlag != "" and orderListFlag eq "Y"'>
		                   AND C.PRODUCT_STATE_CODE != 'SUSPENDED'
		    </when>
		    <otherwise>
		        <choose>
		            <when test="listType == 'ALL' or listType == 'ALLSTATUS'">
		                   AND C.PRODUCT_STATE_CODE != 'SUSPENDED'
		            </when>
		            <when test='skuListFlag == "Y" and hiddenModelSearchFlag == "Y"'>
		                   AND C.PRODUCT_STATE_CODE IN ('ACTIVE','HIDDEN')
		            </when>
		            <otherwise>
		                   AND C.PRODUCT_STATE_CODE = 'ACTIVE'
		            </otherwise>
		        </choose>
		    </otherwise>
		</choose>
		                 INNER JOIN DSP_OLD_PDP_CATEGORY_R D
		                    ON D.SITE_CODE = C.SITE_CODE
		                   AND D.PDP_ID = C.PDP_ID
		                   AND D.USE_FLAG = 'Y'
		                   AND D.DEFAULT_MAP_FLAG = 'Y'
		                 INNER JOIN DSP_DISPLAY_CATEGORY_M E
		                    ON E.SITE_CODE = D.SITE_CODE
		                   AND E.CATEGORY_CODE = D.LV2_CATEGORY_ID
		                   AND E.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
		                 INNER JOIN DSP_DISPLAY_CATEGORY_D F
		                    ON F.SITE_CODE = E.SITE_CODE
		                   AND F.CATEGORY_CODE = E.CATEGORY_CODE
		                   AND F.SHOP_CODE = 'D2C'
		                   AND F.USE_FLAG = 'Y'
		<if test="listType == 'LGPICK'">
		                 INNER JOIN DSP_LG_PICK_M V
		                    ON V.SITE_CODE = A.SITE_CODE
		                   AND V.LGCOM_SKU_ID = A.LGCOM_SKU_ID
		</if>
		                 INNER JOIN PDM_PRODUCT_M O
		                    ON O.SKU_ID  = A.SKU_ID
		                 INNER JOIN PDM_PRODUCT_D R
		                    ON R.SKU_ID = O.SKU_ID
		                   AND R.USE_FLAG = O.USE_FLAG
		                   AND R.LOCALE_CODE = #{localeCode}
		                  LEFT OUTER JOIN PDM_PRODUCT_CATEGORY_R S
		                    ON S.SKU_ID = R.SKU_ID
		                   AND S.USE_FLAG = R.USE_FLAG
		                   AND S.LOCALE_CODE = R.LOCALE_CODE
		                  LEFT OUTER JOIN PDM_CATEGORY_M D_LV1
		                    ON D_LV1.CATEGORY_CODE = S.LV1_CATEGORY_CODE
		                   AND D_LV1.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		                   AND D_LV1.USE_FLAG = R.USE_FLAG
		                   AND D_LV1.CATEGORY_LV_NO = 1
		                  LEFT OUTER JOIN PDM_CATEGORY_M D_LV2
		                    ON D_LV2.CATEGORY_CODE = S.LV2_CATEGORY_CODE
		                   AND D_LV2.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		                   AND D_LV2.HIGH_CATEGORY_CODE = S.LV1_CATEGORY_CODE
		                   AND D_LV2.USE_FLAG = A.USE_FLAG
		                   AND D_LV2.CATEGORY_LV_NO = 2
		                  LEFT OUTER JOIN PDM_CATEGORY_M D_LV3
		                    ON D_LV3.CATEGORY_CODE = S.LV3_CATEGORY_CODE
		                   AND D_LV3.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		                   AND D_LV3.HIGH_CATEGORY_CODE = S.LV2_CATEGORY_CODE
		                   AND D_LV3.USE_FLAG = A.USE_FLAG
		                   AND D_LV3.CATEGORY_LV_NO = 3
		                  LEFT OUTER JOIN PDM_CATEGORY_M D_LV4
		                    ON D_LV4.CATEGORY_CODE = S.LV4_CATEGORY_CODE
		                   AND D_LV4.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		                   AND D_LV4.HIGH_CATEGORY_CODE = S.LV3_CATEGORY_CODE
		                   AND D_LV4.USE_FLAG = A.USE_FLAG
		                   AND D_LV4.CATEGORY_LV_NO = 4
		                  LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M G
		                    ON G.SITE_CODE = D.SITE_CODE
		                   AND G.CATEGORY_CODE = D.LV1_CATEGORY_ID
		                  LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D H
		                    ON H.SITE_CODE = G.SITE_CODE
		                   AND H.CATEGORY_CODE = G.CATEGORY_CODE
		                   AND H.SHOP_CODE = 'D2C'
		<choose>
		    <when test="reviewType != null and reviewType != '' and reviewType == 'LGCOM' ">
		                  LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D I
		    </when>
		    <otherwise>
		                  LEFT OUTER JOIN PDSMGR_PUBL.DSP_USER_REVIEW_RATING_D I
		    </otherwise>
		</choose>
		                    ON I.PDP_ID = A.PDP_ID
		                   AND I.SITE_CODE = A.SITE_CODE
		                   AND I.USE_FLAG = 'Y'
		                  LEFT OUTER JOIN (
		                                   SELECT DISTINCT
		                                          K.PDP_ID
		                                        , K.SIBLING_GRP_CODE
		                                        , K.SIBLING_CODE
		                                        , K.DEFAULT_SIBLING_MODEL_FLAG
		                                        , K.PLP_HIGHLIGHT_MODEL_FLAG
		                                        , M.CATEGORY_CODE
		                                        , M.SIBLING_TYPE_CODE
		                                        , J.SIBLING_LOCAL_VAL
		                                        , K.SIBLING_GRP_NM
		                                        , K.SIBLING_SBJ_TYPE_CODE
		                                     FROM DSP_SIBLING_M J
		                                    INNER JOIN DSP_SIBLING_D K
		                                       ON K.SITE_CODE = J.SITE_CODE
		                                      AND K.SIBLING_CODE = J.SIBLING_CODE
		                                      AND K.SIBLING_TYPE_CODE = J.SIBLING_TYPE_CODE
		                                      AND K.SIBLING_GRP_USE_FLAG = 'Y'
		                                      AND K.USE_FLAG = 'Y'
		<if test="bizTypeCode != null and bizTypeCode != ''">
		                                      AND K.BIZ_TYPE_CODE = #{bizTypeCode}
		</if>
		                                    INNER JOIN DSP_SIBLING_EXP_SEQ_D M
		                                       ON M.SIBLING_TYPE_CODE = J.SIBLING_TYPE_CODE
		                                      AND M.BIZ_TYPE_CODE = J.BIZ_TYPE_CODE
		                                      AND M.SITE_CODE = #{siteCode}
		                                      AND M.USE_FLAG = 'Y'
		                                      AND M.PLP_DEFAULT_SIBLING_FLAG = 'Y'
		                                    WHERE J.USE_FLAG = 'Y'
		                                      AND J.SITE_CODE = #{siteCode}
		<if test="bizTypeCode != null and bizTypeCode != ''">
		                                      AND J.BIZ_TYPE_CODE = #{bizTypeCode}
		</if>
		                                      AND K.PDP_ID IN (<foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
		                                  ) N
		                    ON N.PDP_ID = A.PDP_ID
		                   AND N.CATEGORY_CODE = D.LV2_CATEGORY_ID
		                  LEFT OUTER JOIN (
		                                   SELECT P.PDP_ID
		                                        , P.PROMOTION_TAG
		                                        , P.PROMOTION_LINK_URL
		                                        , P.EXTERNAL_LINK_TARGET
		                                     FROM (
		                                           SELECT
		                                                  A.PDP_ID
		<choose>
		    <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
		                                                , C.CAMPAIGN_BTN_NM AS PROMOTION_TAG
		                                                , C.CAMPAIGN_LINK_PATH AS PROMOTION_LINK_URL
		                                                , C.CAMPAIGN_BLANK_FLAG AS EXTERNAL_LINK_TARGET
		    </when>
		    <otherwise>
		                                                , B.PROMOTION_TAG_VAL AS PROMOTION_TAG
		                                                , D.PROMOTION_LINK_PATH AS PROMOTION_LINK_URL
		                                                , D.PROMOTION_LINK_SBJ_CODE AS EXTERNAL_LINK_TARGET
		    </otherwise>
		</choose>
		                                             FROM PRM_PROMOTION_PRODUCT_R A
		                                            INNER JOIN PRM_PROMOTION_M B
		                                               ON B.SITE_CODE = A.SITE_CODE
		                                              AND B.PROMOTION_ID = A.PROMOTION_ID
		                                              AND B.USE_FLAG = 'Y'
		<choose>
		    <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
		                                            INNER JOIN PRM_PROMOTION_PRODUCT_CAMP_R C
		                                               ON C.PROMOTION_ID = A.PROMOTION_ID
		                                              AND C.PDP_ID = A.PDP_ID
		                                              AND C.SITE_CODE = A.SITE_CODE
		                                              AND C.USE_FLAG = 'Y'
		                                              AND C.DSP_SEQ = A.PDP_PROMOTION_SEQ
		                                              AND C.PROMOTION_ID = #{promotionId}
		    </when>
		    <otherwise>
		                                             LEFT OUTER JOIN PRM_PROMOTION_LIST_M D
		                                               ON D.PROMOTION_LIST_ID = B.PROMOTION_ID
		                                              AND D.SITE_CODE = B.SITE_CODE
		                                              AND D.USE_FLAG = 'Y'
		    </otherwise>
		</choose>
		                                            WHERE A.SITE_CODE = #{siteCode}
		                                              AND STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PROMOTION_EXP_BEGIN_DATE AND B.PROMOTION_EXP_END_DATE
		                                              AND IFNULL(B.PROMOTION_NONEXP_FLAG, 'N') <![CDATA[<>]]> 'Y'
		                                              AND A.USE_FLAG = 'Y'
		                                            ORDER BY A.PDP_PROMOTION_SEQ ASC
		                                          ) P
		                                    GROUP BY P.PDP_ID
		                                  ) Q
		                    ON Q.PDP_ID = A.PDP_ID
		                  LEFT OUTER JOIN PDM_PRODUCT_EL_PIS_R T
		                    ON A.SITE_CODE = T.SITE_CODE
		                   AND O.LV1_PRODUCT_CODE = T.LV1_EL_PRODUCT_CODE
		                   AND O.LV2_PRODUCT_CODE = T.LV2_EL_PRODUCT_CODE
		                  LEFT OUTER JOIN DSP_EPS_PICTOGRAM_PRODUCT_R U
		                    ON A.LGCOM_SKU_ID = U.LGCOM_SKU_ID
		                   AND A.PDP_ID = U.PDP_ID
		                   AND A.SITE_CODE = U.SITE_CODE
		                   AND U.USE_FLAG = 'Y'
		                  LEFT OUTER JOIN ECM_STOCK_D W
		                    ON A.LGCOM_SKU_ID = CONCAT(SUBSTRING_INDEX(W.SKU_ID , '.', LENGTH(W.SKU_ID) - LENGTH(REPLACE(W.SKU_ID, '.', ''))), '.', W.STORE_CODE, '.C')
		                   AND A.SITE_CODE = W.STORE_CODE
		                 WHERE A.SITE_CODE = #{siteCode}
		<if test="orderListFlag == null or orderListFlag == ''">
		                   AND A.USE_FLAG = 'Y'
		</if>
		<if test="bizTypeCode != null and bizTypeCode != ''">
		                   AND A.BIZ_TYPE_CODE = #{bizTypeCode}
		</if>
		                   AND A.PDP_ID IN (<foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
		               ) CCC
		       ) AAA
		<if test='siblingGroupFlag == "Y" and listType != "PAGINATION" '>
		 WHERE AAA.CNT = 1
		</if>
		<choose>
		    <when test="listType == 'HR'">
		 ORDER BY HR_RATING_NUM
		 LIMIT 10
		    </when>
		    <when test="listType == 'LGPICK'">
		 ORDER BY AAA.SPOTLIGHTS_DSP_SEQ
		 LIMIT 20
		    </when>
		    <otherwise>
		 ORDER BY FIELD(AAA.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
		    </otherwise>
		</choose>
	</select>

    <select id="selectProductListSiblingList" parameterType="com.lge.d2x.domain.productList.v1.model.ProductListSiblingInfoRequestVO" resultType="com.lge.d2x.domain.productList.v1.model.ProductListSiblingInfoResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectProductListSiblingList */
		     C.LGCOM_SKU_ID
		   , A.SIBLING_CODE
		   , CASE WHEN A.SIBLING_TYPE_CODE = 'SIZE' AND A.SITE_CODE = 'IN'
			      THEN REPLACE(SUBSTRING(IFNULL(A.SIBLING_LOCAL_VAL, A.SIBLING_CODE), POSITION('(' IN IFNULL(A.SIBLING_LOCAL_VAL, A.SIBLING_CODE))+1), ')', '')
			      ELSE IFNULL(A.SIBLING_LOCAL_VAL, A.SIBLING_CODE)
			  END AS SIBLING_VALUE
		<if test="pageType != null and pageType != '' and pageType == 'PLP' ">
		   , CASE WHEN C.PROMOTION_TAG_USE_ID IS NOT NULL AND C.PROMOTION_TAG_USE_ID !=''
            	  THEN IFNULL((SELECT
									  PT.PROMOTION_TAG_VAL
								 FROM PRM_PROMOTION_M PT
								WHERE PT.SITE_CODE = C.SITE_CODE
								  AND PT.PROMOTION_ID = C.PROMOTION_TAG_USE_ID
								  AND DATE_FORMAT(#{today}, '%Y%m%d') BETWEEN PT.PROMOTION_EXP_BEGIN_DATE AND PT.PROMOTION_EXP_END_DATE),'')
                  ELSE IFNULL(Q.PROMOTION_TAG,'')
              END AS PROMOTION_TEXT
           , CASE WHEN C.PROMOTION_TAG_USE_ID IS NOT NULL AND C.PROMOTION_TAG_USE_ID !=''
            	THEN IFNULL((SELECT PL.PROMOTION_LINK_PATH FROM PRM_PROMOTION_LIST_M PL WHERE PL.SITE_CODE = C.SITE_CODE AND PL.PROMOTION_LIST_ID = C.PROMOTION_TAG_USE_ID),'')
                ELSE IFNULL(Q.PROMOTION_LINK_URL,'')
              END AS PROMOTION_LINK_URL
		</if>
	    FROM DSP_SIBLING_M A
	   INNER JOIN DSP_SIBLING_D B
		  ON B.SIBLING_CODE = A.SIBLING_CODE
		 AND B.SIBLING_TYPE_CODE = A.SIBLING_TYPE_CODE
		 AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		 AND B.SITE_CODE = A.SITE_CODE
		 AND B.USE_FLAG = 'Y'
		 AND B.SIBLING_GRP_USE_FLAG = 'Y'
		 AND B.SIBLING_GRP_CODE = #{siblingGroupCode}
	   INNER JOIN DSP_PDP_M C
	      ON C.SITE_CODE = B.SITE_CODE
	     AND C.PDP_ID = B.PDP_ID
	     AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
	     AND C.USE_FLAG = 'Y'
	   <if test='bundlesOnly != null and bundlesOnly == "Y"'>
	     AND C.PDP_TYPE_CODE IN ('B', 'O')
	   </if>
	   <if test="filterModelList != null">
            <foreach item="pdpId" index="indexs" collection="filterModelList" open="AND C.PDP_ID IN (" close=")" separator=",">
            	#{pdpId}
        	</foreach>
       </if>
       INNER JOIN DSP_PDP_D D
	      ON D.SITE_CODE = C.SITE_CODE
	     AND D.PDP_ID = C.PDP_ID
	     AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
	     AND D.SHOP_CODE = 'D2C'
	     AND D.USE_FLAG = 'Y'
	  <if test='productStateCode != null and productStateCode != ""'>
	     AND D.PRODUCT_STATE_CODE  = #{productStateCode}
	  </if>
	  <choose>
	  <when test='standardFlag != "Y"'>
	   INNER JOIN DSP_OLD_PDP_CATEGORY_R E
          ON E.PDP_ID = D.PDP_ID
         AND E.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
         AND E.USE_FLAG = 'Y'
         AND E.SITE_CODE = #{siteCode}
         AND E.LV1_CATEGORY_ID  = #{superCategoryId}
      <if test='categoryId != null and categoryId != ""'>
         AND E.LV2_CATEGORY_ID = #{categoryId}
      </if>
      <if test='subCategoryId != null and subCategoryId != ""'>
         AND E.LV3_CATEGORY_ID = #{subCategoryId}
      </if>
      </when>
      <otherwise>
       INNER JOIN DSP_PDP_CATEGORY_R E
          ON E.PDP_ID = D.PDP_ID
         AND E.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
         AND E.USE_FLAG = 'Y'
         AND E.SITE_CODE = #{siteCode}
         AND E.LV2_CATEGORY_CODE  = #{superCategoryId}
       <if test='categoryId != null and categoryId != ""'>
         AND E.LV3_CATEGORY_CODE = #{categoryId}
       </if>
       <if test='subCategoryId != null and subCategoryId != ""'>
         AND E.LV4_CATEGORY_CODE = #{subCategoryId}
       </if>
      </otherwise>
      </choose>
      <if test='subCategoryId == null or subCategoryId == ""'>
         AND E.DEFAULT_MAP_FLAG = 'Y'
      </if>
      <if test='promotionsOnly != null and promotionsOnly == "Y"'>
	   INNER JOIN (SELECT G.PDP_ID
		                 FROM PRM_PROMOTION_M F
		                INNER JOIN PRM_PROMOTION_PRODUCT_R G
		                   ON G.PROMOTION_ID = F.PROMOTION_ID
		                  AND G.SITE_CODE = F.SITE_CODE
		                  AND G.USE_FLAG = 'Y'
		                  AND IFNULL(G.promotion_page_product_seq, '00') <![CDATA[<>]]> '99'
		                WHERE F.SITE_CODE = #{siteCode}
		                  AND F.USE_FLAG = 'Y'
		              	  AND TO_DATE(#{today},'%Y%m%d') BETWEEN F.PROMOTION_EXP_BEGIN_DATE AND F.PROMOTION_EXP_END_DATE
			          GROUP BY G.PDP_ID
		            ) H
		       ON C.PDP_ID = H.PDP_ID
	   </if>
	   <if test="pageType != null and pageType != '' and pageType == 'PLP' ">
			 LEFT OUTER JOIN
					              (
					              	SELECT P.PDP_ID
									     , P.PROMOTION_TAG
									     , P.PROMOTION_LINK_URL
									     , P.EXTERNAL_LINK_TARGET
									  FROM (
									       SELECT
										    	  A.PDP_ID
											    , B.PROMOTION_TAG_VAL AS PROMOTION_TAG
											    , C.PROMOTION_LINK_PATH AS PROMOTION_LINK_URL
											    , C.PROMOTION_LINK_SBJ_CODE AS EXTERNAL_LINK_TARGET
											 FROM PRM_PROMOTION_PRODUCT_R A
											INNER JOIN PRM_PROMOTION_M B
											   ON B.SITE_CODE = A.SITE_CODE
											  AND B.PROMOTION_ID = A.PROMOTION_ID
											  AND B.USE_FLAG = 'Y'
											 LEFT OUTER JOIN PRM_PROMOTION_LIST_M C
											   ON C.PROMOTION_LIST_ID = B.PROMOTION_ID
											  AND C.SITE_CODE = B.SITE_CODE
											  AND C.USE_FLAG = 'Y'
										    WHERE A.SITE_CODE = #{siteCode}
											  AND STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PROMOTION_EXP_BEGIN_DATE AND B.PROMOTION_EXP_END_DATE
											  AND IFNULL(B.PROMOTION_NONEXP_FLAG, 'N') <![CDATA[<>]]> 'Y'
											  AND A.USE_FLAG = 'Y'
										    ORDER BY A.PDP_PROMOTION_SEQ ASC
					                          ) P
					                 GROUP BY P.PDP_ID, P.PROMOTION_TAG, P.PROMOTION_LINK_URL, P.EXTERNAL_LINK_TARGET
					              ) Q
					ON Q.PDP_ID = C.PDP_ID
	   </if>
	   WHERE A.SITE_CODE = #{siteCode}
		 AND A.BIZ_TYPE_CODE = #{bizTypeCode}
		 AND A.USE_FLAG = 'Y'
	   ORDER BY B.SORT_SEQ, A.SIBLING_CODE
	   LIMIT #{siblingLimitCnt}
    </select>

    <select id="selectProductListPromotionInfo" parameterType="com.lge.d2x.domain.productList.v1.model.ProductListPromotionInfoRequestVO" resultType="com.lge.d2x.domain.productList.v1.model.ProductListPromotionInfoResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectProductListPromotionInfo */
      		 E.PDP_ID
           , E.promotionTag
           , E.promotionLinkUrl
           , E.externalLinkTarget
        FROM (
      SELECT A.PDP_ID
	  <choose>
		<when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
		   , C.CAMPAIGN_BTN_NM AS promotionTag
		   , C.CAMPAIGN_LINK_PATH AS promotionLinkUrl
		   , C.CAMPAIGN_BLANK_FLAG AS externalLinkTarget
		</when>
	    <otherwise>
		   , B.PROMOTION_TAG_VAL AS promotionTag
		   , D.PROMOTION_LINK_PATH AS promotionLinkUrl
		   , D.PROMOTION_LINK_SBJ_CODE AS externalLinkTarget
		</otherwise>
	  </choose>
		FROM PRM_PROMOTION_PRODUCT_R A
	   INNER JOIN PRM_PROMOTION_M B
		  ON B.SITE_CODE = A.SITE_CODE
		 AND B.PROMOTION_ID = A.PROMOTION_ID
		 AND B.USE_FLAG = 'Y'
	 <choose>
	   <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
	   INNER JOIN PRM_PROMOTION_PRODUCT_CAMP_R C
		  ON C.PROMOTION_ID = A.PROMOTION_ID
		 AND C.PDP_ID = A.PDP_ID
		 AND C.SITE_CODE = A.SITE_CODE
		 AND C.USE_FLAG = 'Y'
		 AND C.DSP_SEQ = A.PDP_PROMOTION_SEQ
		 AND C.PROMOTION_ID = #{promotionId}
	   </when>
	   <otherwise>
		LEFT OUTER JOIN PRM_PROMOTION_LIST_M D
		  ON D.PROMOTION_LIST_ID = B.PROMOTION_ID
		 AND D.SITE_CODE = B.SITE_CODE
		 AND D.USE_FLAG = 'Y'
		</otherwise>
	 </choose>
	   WHERE A.SITE_CODE = #{siteCode}
	     AND A.PDP_ID = #{pdpId}
		 AND STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PROMOTION_EXP_BEGIN_DATE AND B.PROMOTION_EXP_END_DATE
		 AND IFNULL(B.PROMOTION_NONEXP_FLAG, 'N') <![CDATA[<>]]> 'Y'
		 AND A.USE_FLAG = 'Y'
	   ORDER BY A.PDP_PROMOTION_SEQ ASC
	   ) AS E
	   GROUP BY E.PDP_ID
    </select>

    <select id="selectMtsPdpIdBySkuId" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListBySkuRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdBySkuId */
             A.PDP_ID
        FROM DSP_PDP_M A
       INNER JOIN DSP_PDP_D B
          ON B.SITE_CODE = A.SITE_CODE
         AND B.PDP_ID = A.PDP_ID
         AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		 AND B.SHOP_CODE = #{shopCode}
		 AND B.AEM_PUBL_FLAG = 'Y'
		 AND B.USE_FLAG = 'Y'
	   INNER JOIN DSP_PDP_CATEGORY_R C
	      ON C.PDP_ID = A.PDP_ID
	     AND C.SITE_CODE = A.SITE_CODE
	     AND C.USE_FLAG = 'Y'
	     AND C.DEFAULT_MAP_FLAG = 'Y'
	   INNER JOIN DSP_DISPLAY_CATEGORY_M D
		  ON D.SITE_CODE = C.SITE_CODE
		 AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
		 AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		 AND D.USE_FLAG = 'Y'
	   WHERE A.USE_FLAG = 'Y'
	   	 AND A.SITE_CODE = #{siteCode}
	<if test="customerGroup != null and customerGroup !=''">
         AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
	</if>
	     AND A.LGCOM_SKU_ID IN (
		        <foreach item="skuId" collection="skuIds" open="" separator="," close="">
		            #{skuId}
		        </foreach>
		    	)
	   ORDER BY FIELD(A.LGCOM_SKU_ID,
	   			<foreach item="skuId" collection="skuIds" open="" separator="," close="">
			        #{skuId}
			    </foreach>
			    )
    </select>

    <select id="selectMtsPdpIdBySkuIdNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdBySkuId */
             A.PDP_ID
        FROM DSP_PDP_M A
       INNER JOIN DSP_PDP_D B
          ON B.SITE_CODE = A.SITE_CODE
         AND B.PDP_ID = A.PDP_ID
         AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		 AND B.SHOP_CODE = #{shopCode}
		 AND B.AEM_PUBL_FLAG = 'Y'
		 AND B.USE_FLAG = 'Y'
	   INNER JOIN DSP_OLD_PDP_CATEGORY_R C
	      ON C.PDP_ID = A.PDP_ID
	     AND C.SITE_CODE = A.SITE_CODE
	     AND C.USE_FLAG = 'Y'
	     AND C.DEFAULT_MAP_FLAG = 'Y'
	   INNER JOIN DSP_DISPLAY_CATEGORY_M D
		  ON D.SITE_CODE = C.SITE_CODE
		 AND D.CATEGORY_CODE = C.LV2_CATEGORY_ID
		 AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		 AND D.USE_FLAG = 'Y'
	   WHERE A.USE_FLAG = 'Y'
	   	 AND A.SITE_CODE = #{siteCode}
	<if test="customerGroup != null and customerGroup !=''">
         AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
	</if>
	     AND A.LGCOM_SKU_ID IN (
		        <foreach item="skuId" collection="skuIds" open="" separator="," close="">
		            #{skuId}
		        </foreach>
		    	)
	   ORDER BY FIELD(A.LGCOM_SKU_ID,
	   			<foreach item="skuId" collection="skuIds" open="" separator="," close="">
			        #{skuId}
			    </foreach>
			    )
    </select>

    <select id="selectMtsNewestPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdListByListType */
             A.PDP_ID
        FROM DSP_PDP_M A
       INNER JOIN DSP_PDP_CATEGORY_R B
          ON B.PDP_ID = A.PDP_ID
         AND B.USE_FLAG = 'Y'
         AND B.SITE_CODE = #{siteCode}
         AND B.DEFAULT_MAP_FLAG = 'Y'
       <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
         AND B.LV2_CATEGORY_CODE = #{lv2CategoryCode}
       </if>
       <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
         AND B.LV3_CATEGORY_CODE in( #{lv3CategoryCode})
       </if>
       <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag == &quot;N&quot; ">
         AND B.LV3_CATEGORY_CODE != #{lv3CategoryCode}
       </if>
       <if test="lv4CategoryCode != null and lv4CategoryCode !=''">
         AND B.LV4_CATEGORY_CODE = #{lv4CategoryCode}
       </if>
       INNER JOIN DSP_DISPLAY_CATEGORY_M C
          ON C.CATEGORY_CODE = B.LV3_CATEGORY_CODE
         AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
         AND C.SITE_CODE = B.SITE_CODE
         AND C.USE_FLAG = 'Y'
       INNER JOIN DSP_PDP_D D
          ON D.PDP_ID = A.PDP_ID
         AND D.SITE_CODE = #{siteCode}
         AND D.USE_FLAG = 'Y'
         AND D.AEM_PUBL_FLAG ='Y'
         AND D.PRODUCT_STATE_CODE = 'ACTIVE'
		 AND D.SHOP_CODE = #{shopCode}
       WHERE A.SITE_CODE = #{siteCode}
         AND A.PDP_TYPE_CODE != 'A'
	    <if test="customerGroup != null and customerGroup !=''">
         AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
		</if>
       <if test="bizTypeCode != null and bizTypeCode != ''">
         AND A.BIZ_TYPE_CODE = #{bizTypeCode}
       </if>
       <if test="activeCommerceFlag != null and activeCommerceFlag == &quot;Y&quot;">
      	 AND A.PDP_TYPE_CODE != 'O'
       </if>
       <if test="pdpIds != null and pdpIds.size != 0">
          <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
          </foreach>
       </if>
       ORDER BY A.PRODUCT_RELES_DD DESC, D.CREATION_DATE DESC, A.PRODUCT_NM ASC
       LIMIT 10
    </select>

    <select id="selectMtsNewestPdpIdListNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_OLD_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV1_CATEGORY_ID = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode !=''">
           AND B.LV3_CATEGORY_ID = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV2_CATEGORY_ID
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
		   AND D.SHOP_CODE = #{shopCode}
         WHERE A.SITE_CODE = #{siteCode}
           AND A.PDP_TYPE_CODE != 'A'
		 <if test="customerGroup != null and customerGroup !=''">
         AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
		</if>
         <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         </if>
      	 <if test="activeCommerceFlag != null and activeCommerceFlag == &quot;Y&quot;">
      	   AND A.PDP_TYPE_CODE != 'O'
      	 </if>
         <if test="pdpIds != null and pdpIds.size != 0">
             <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
             </foreach>
         </if>
         ORDER BY A.PRODUCT_RELES_DD DESC, D.CREATION_DATE DESC, A.PRODUCT_NM ASC
         LIMIT 10
    </select>

    <select id="selectMtsHighlyRatedPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
    	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV2_CATEGORY_CODE = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV3_CATEGORY_CODE in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode != '' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV3_CATEGORY_CODE != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode != ''">
           AND B.LV4_CATEGORY_CODE = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV3_CATEGORY_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
           AND D.SHOP_CODE = #{shopCode}
          LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D E
            ON E.PDP_ID = A.PDP_ID
           AND E.SITE_CODE = #{siteCode}
           AND E.USE_FLAG = 'Y'
         WHERE A.SITE_CODE = #{siteCode}
           AND A.PDP_TYPE_CODE != 'A'
		<if test="customerGroup != null and customerGroup !=''">
         AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
		</if>
        <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
        </if>
        <if test="pdpIds != null and pdpIds.size != 0">
          <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
          </foreach>
        </if>
        ORDER BY CAST(ROUND(IFNULL(E.STRARRATING_VAL,'0'),1) AS SIGNED) DESC
               , CAST(IFNULL(E.PARTICIPANT_CNT, 0) AS SIGNED) DESC
               , A.PRODUCT_RELES_DD DESC, D.CREATION_DATE DESC, A.PRODUCT_NM ASC
         LIMIT #{hrLimit}
    </select>

    <select id="selectMtsHighlyRatedPdpIdListNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_OLD_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV1_CATEGORY_ID = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode !=''">
           AND B.LV3_CATEGORY_ID = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV2_CATEGORY_ID
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
		   AND D.SHOP_CODE = #{shopCode}
		  LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D E
            ON E.PDP_ID = A.PDP_ID
           AND E.SITE_CODE = #{siteCode}
           AND E.USE_FLAG = 'Y'
         WHERE A.SITE_CODE = #{siteCode}
           AND A.PDP_TYPE_CODE != 'A'
		 <if test="customerGroup != null and customerGroup !=''">
        	 AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
		 </if>
         <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         </if>
         <if test="pdpIds != null and pdpIds.size != 0">
             <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
             </foreach>
         </if>
        ORDER BY CAST(ROUND(IFNULL(E.STRARRATING_VAL,'0'),1) AS SIGNED) DESC
               , CAST(IFNULL(E.PARTICIPANT_CNT, 0) AS SIGNED) DESC
               , A.PRODUCT_RELES_DD DESC, D.CREATION_DATE DESC, A.PRODUCT_NM ASC
         LIMIT #{hrLimit}
    </select>

    <select id="selectMtsMostPopularPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV2_CATEGORY_CODE = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV3_CATEGORY_CODE in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode != '' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV3_CATEGORY_CODE != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode != ''">
           AND B.LV4_CATEGORY_CODE = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV3_CATEGORY_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
           AND D.SHOP_CODE = #{shopCode}
         LEFT OUTER JOIN
             (
               SELECT E.FRONT_ACCESS_ID
                    , SUM(E.ACCESS_CNT) AS COUNT_NO
                    , E.SITE_CODE
                 FROM DSP_MTS_FRONT_ACCESS_COUNT_D E
                WHERE E.SITE_CODE = #{siteCode}
                  AND E.LOG_DD <![CDATA[>]]> TIMESTAMPADD(MONTH,-1, #{today})
                  AND E.USE_FLAG = 'Y'
                  AND E.PAGE_SP_CODE = 'PDP'
                  AND E.SHOP_CODE = #{shopCode}
                <if test="bizTypeCode != null and bizTypeCode != ''">
                  AND E.BIZ_TYPE_CODE = #{bizTypeCode}
                </if>
                GROUP BY E.FRONT_ACCESS_ID, E.SITE_CODE
             ) AS F
            ON F.FRONT_ACCESS_ID = A.PDP_ID
           AND F.SITE_CODE = A.SITE_CODE
         WHERE A.SITE_CODE = #{siteCode}
           AND A.PDP_TYPE_CODE != 'A'
		 <if test="customerGroup != null and customerGroup !=''">
           AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
		</if>
         <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         </if>
         <if test="pdpIds != null and pdpIds.size != 0">
          <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
          </foreach>
         </if>
         ORDER BY F.COUNT_NO DESC, A.PRODUCT_RELES_DD, A.PRODUCT_NM ASC
         LIMIT 10
    </select>

    <select id="selectMtsMostPopularPdpIdListNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdListByListType */
               A.PDP_ID
          FROM DSP_PDP_M A
         INNER JOIN DSP_OLD_PDP_CATEGORY_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
           AND B.SITE_CODE = #{siteCode}
           AND B.DEFAULT_MAP_FLAG = 'Y'
         <if test="lv2CategoryCode != null and lv2CategoryCode !=''">
           AND B.LV1_CATEGORY_ID = #{lv2CategoryCode}
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag !=  &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID in( #{lv3CategoryCode})
         </if>
         <if test="lv3CategoryCode != null and lv3CategoryCode !='' and lv3CategoryCdFlag == &quot;N&quot; ">
           AND B.LV2_CATEGORY_ID != #{lv3CategoryCode}
         </if>
         <if test="lv4CategoryCode != null and lv4CategoryCode !=''">
           AND B.LV3_CATEGORY_ID = #{lv4CategoryCode}
         </if>
         INNER JOIN DSP_DISPLAY_CATEGORY_M C
            ON C.CATEGORY_CODE = B.LV2_CATEGORY_ID
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.SITE_CODE = B.SITE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = A.PDP_ID
           AND D.SITE_CODE = #{siteCode}
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG ='Y'
           AND D.PRODUCT_STATE_CODE = 'ACTIVE'
           AND D.SHOP_CODE = #{shopCode}
          LEFT OUTER JOIN
             (
               SELECT E.FRONT_ACCESS_ID
                    , SUM(E.ACCESS_CNT) AS COUNT_NO
                    , E.SITE_CODE
                 FROM DSP_MTS_FRONT_ACCESS_COUNT_D E
                WHERE E.SITE_CODE = #{siteCode}
                  AND E.LOG_DD <![CDATA[>]]> TIMESTAMPADD(MONTH,-1, #{today})
                  AND E.USE_FLAG = 'Y'
                  AND E.PAGE_SP_CODE = 'PDP'
                  AND E.SHOP_CODE = #{shopCode}
                <if test="bizTypeCode != null and bizTypeCode != ''">
                  AND E.BIZ_TYPE_CODE = #{bizTypeCode}
                </if>
                GROUP BY E.FRONT_ACCESS_ID, E.SITE_CODE
             ) AS F
            ON F.FRONT_ACCESS_ID = A.PDP_ID
           AND F.SITE_CODE = A.SITE_CODE
         WHERE A.SITE_CODE = #{siteCode}
           AND A.PDP_TYPE_CODE != 'A'
		 <if test="customerGroup != null and customerGroup !=''">
        	  AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
		 </if>
         <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         </if>
         <if test="pdpIds != null and pdpIds.size != 0">
          <foreach collection="pdpIds" item="pdpId" open="AND A.PDP_ID NOT IN (" separator="," close=")">
               #{pdpId}
          </foreach>
         </if>
         ORDER BY F.COUNT_NO DESC, A.PRODUCT_RELES_DD, A.PRODUCT_NM ASC
         LIMIT 10
    </select>

    <select id="selectMtsAccessoryPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsPdpIdListByListType */
		       A.PDP_ID
		     , A.SITE_CODE
		     , B.PRODUCT_RELES_DD
		     , C.CREATION_DATE
		     , B.PRODUCT_NM
		  FROM (
		         SELECT JSON_UNQUOTE(JSON_EXTRACT(
  							JSON_ARRAY(MAP_ACSRY_PDP_ID1, MAP_ACSRY_PDP_ID2, MAP_ACSRY_PDP_ID3, MAP_ACSRY_PDP_ID4, MAP_ACSRY_PDP_ID5, MAP_ACSRY_PDP_ID6, MAP_ACSRY_PDP_ID7, MAP_ACSRY_PDP_ID8, MAP_ACSRY_PDP_ID9, MAP_ACSRY_PDP_ID10),
  							CONCAT('$[', idx, ']')
						)) AS PDP_ID
			   		  , A.SITE_CODE
				   FROM DSP_PRODUCT_ACCESSORY_D A
				  INNER JOIN (SELECT 0 AS idx UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
			        			SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS idxs
			 	  WHERE A.SITE_CODE = #{siteCode}
			 	    AND A.PDP_ID = #{pdpId}
			 	    AND A.USE_FLAG = 'Y'
		        ) AS A
		 INNER JOIN DSP_PDP_M B
		    on B.SITE_CODE = A.SITE_CODE
		   AND B.PDP_ID = A.PDP_ID
		   AND B.USE_FLAG = 'Y'
		 INNER JOIN DSP_PDP_D C
		    ON C.SITE_CODE = B.SITE_CODE
		   AND C.PDP_ID = B.PDP_ID
		   AND C.BIZ_TYPE_CODE  = B.BIZ_TYPE_CODE
		   AND C.SHOP_CODE = #{shopCode}
		   AND C.USE_FLAG = 'Y'
		   AND C.AEM_PUBL_FLAG = 'Y'
		   AND C.PRODUCT_STATE_CODE = 'ACTIVE'
		 WHERE A.PDP_ID IS NOT NULL
		   AND A.PDP_ID <![CDATA[<>]]> ''
		   <if test="customerGroup != null and customerGroup !=''">
        	   AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	           OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
			</if>
		 ORDER BY B.PRODUCT_RELES_DD DESC, C.CREATION_DATE DESC, B.PRODUCT_NM ASC
    </select>

    <select id="selectSelfSiblingDefaultUrl" parameterType="com.lge.d2x.domain.productList.v1.model.ProductListSelfSiblingDefaultRequestVO" resultType="String">
    	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectSelfSiblingDefaultUrl */
    	  	   D.PDP_URL
          FROM DSP_SIBLING_D A
         INNER JOIN DSP_SIBLING_D B
            ON B.SIBLING_GRP_CODE = A.SIBLING_GRP_CODE
           AND B.SITE_CODE = A.SITE_CODE
           AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
           AND B.DEFAULT_SIBLING_MODEL_FLAG = 'Y'
           AND B.SIBLING_GRP_USE_FLAG = 'Y'
           AND B.USE_FLAG = 'Y'
      <choose>
        <when test='standardFlag eq "Y"'>
         INNER JOIN DSP_PDP_CATEGORY_R C
            ON C.PDP_ID = B.PDP_ID
           AND C.SITE_CODE = B.SITE_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.LV2_CATEGORY_CODE = #{superCategoryId}
           AND C.USE_FLAG = 'Y'
        <if test="categoryId != null and categoryId != ''">
           AND C.LV3_CATEGORY_CODE = #{categoryId}
    	</if>
        <if test="subCategoryId != null and subCategoryId != ''">
           AND C.LV4_CATEGORY_CODE = #{subCategoryId}
        </if>
        <if test="subCategoryId == null or subCategoryId == ''">
           AND C.DEFAULT_MAP_FLAG = 'Y'
        </if>
        </when>
        <otherwise>
         INNER JOIN DSP_OLD_PDP_CATEGORY_R C
            ON C.PDP_ID = B.PDP_ID
           AND C.SITE_CODE = B.SITE_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.LV1_CATEGORY_ID = #{superCategoryId}
           AND C.USE_FLAG = 'Y'
       <if test="categoryId != null and categoryId != ''">
           AND C.LV2_CATEGORY_ID = #{categoryId}
    	</if>
        <if test="subCategoryId != null and subCategoryId != ''">
           AND C.LV3_CATEGORY_ID = #{subCategoryId}
        </if>
        <if test="subCategoryId == null or subCategoryId == ''">
           AND C.DEFAULT_MAP_FLAG = 'Y'
        </if>
        </otherwise>
       </choose>
         INNER JOIN DSP_PDP_D D
            ON D.SITE_CODE = C.SITE_CODE
           AND D.PDP_ID = C.PDP_ID
           AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
           AND D.SHOP_CODE = 'D2C'
           AND D.USE_FLAG = 'Y'
         WHERE A.SITE_CODE = #{siteCode}
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
           AND A.PDP_ID = #{pdpId}
           AND A.USE_FLAG = 'Y'
           AND A.SIBLING_GRP_USE_FLAG = 'Y'
         LIMIT 1
    </select>

    <select id="selectMtsProductListPdpInfo" parameterType="com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoRequestVO" resultType="com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoResponseVO">
        SELECT AAA.*
		     , CASE WHEN 'HR' = #{listType}
		            THEN  CASE WHEN COUNT(AAA.PDP_ID) OVER() <![CDATA[>]]> 10
		                       THEN 10
		                       ELSE COUNT(AAA.PDP_ID) OVER()
		                    END
		            ELSE  COUNT(AAA.PDP_ID) OVER()
		        END AS TOTAL_COUNT
		  FROM
		      (
				SELECT
						ROW_NUMBER() OVER (PARTITION BY IFNULL(CCC.SIBLING_GRP_CODE, CCC.PDP_ID) ORDER BY IFNULL(CCC.PLP_HIGHLIGHT_MODEL_FLAG,'Y') DESC) AS CNT
			          <if test="listType == 'HR'">
			          , ROW_NUMBER() OVER (ORDER BY CCC.S_RATING3 DESC, CAST(IFNULL(CCC.P_COUNT, 0) AS SIGNED) DESC, FIELD(CCC.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)) AS HR_RATING_NUM
			          </if>
			          , CCC.*
			      FROM
             (
				SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectProductListPdpInfo */
				       A.PDP_ID
				     , A.LGCOM_SKU_ID
				     , A.SKU_ID
				     , A.PRODUCT_NM
				     , IFNULL(NULLIF(R.NEW_MKT_PRODUCT_NM, ''), A.USER_FRNDY_PRODUCT_NM) AS USER_FRNDY_PRODUCT_NM
				     , A.PDP_TYPE_CODE
				     , A.BIZ_TYPE_CODE
				     , O.SALES_MODEL_CODE
				     , O.SALES_MODEL_SUFFIX_CODE
				     , CASE WHEN A.SITE_CODE = 'SA' OR A.SITE_CODE = 'SA_EN' THEN ROUND(IFNULL(A.MSRP_SALES_PRICE, 0.00)) ELSE IFNULL(A.MSRP_SALES_PRICE, 0.00) END AS msrpSalesPrice
				     , A.ELABEL_GRD_CODE
				     , A.ELABEL_CLS_CODE
				     , A.SECOND_ELABEL_GRD_CODE
				     , A.SECOND_ELABEL_CLS_CODE
				     , A.WTOWER_PRODUCT_FLAG
				     , A.SIGNT_PRODUCT_FLAG
				     , A.ITB_USE_FLAG
				     , A.EXCL_PRODUCT_SETT_CODE
				     , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productRelesDd
				     , C.PDP_URL
				     , C.PRODUCT_STATE_CODE
				     , IFNULL(C.SML_IMG_URL, #{dummyImageUrl}) AS smlImgUrl
				     , IFNULL(C.MDM_IMG_URL, #{dummyImageUrl}) AS mdmImgUrl
				     , IFNULL(C.BIG_IMG_URL , #{dummyImageUrl}) AS bigImgUrl
				     , C.IMG_ALT_TEXT_CNTS
				     , C.CREATION_DATE
				     , C.PRODUCT_THEME_TYPE_CODE
				     , '' AS DEFAULT_PRODUCT_TAG_CODE
				     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1)
				            THEN
				                 CASE WHEN C.PRODUCT_TAG_USE_FLAG1 = 'Y'
				                      THEN C.PRODUCT_TAG_CODE1
				                      ELSE NULL
				                  END
				            ELSE NULL
				        END AS productTagCode1
				     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1)
				            THEN
				                 CASE WHEN C.PRODUCT_TAG_USE_FLAG1 = 'Y'
				                      THEN TO_CHAR(C.PRODUCT_TAG_EXP_END_DATE1, 'YYYYMMDD')
				                      ELSE NULL
				                  END
				            ELSE NULL
				        END AS productTagExpEndDate1
				     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.pRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2)
				            THEN
				                 CASE WHEN C.PRODUCT_TAG_USE_FLAG2 = 'Y'
				                      THEN C.PRODUCT_TAG_CODE2
				                      ELSE NULL
				                  END
				            ELSE NULL
				        END AS productTagCode2
				     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2)
				            THEN
				                 CASE WHEN C.PRODUCT_TAG_USE_FLAG2 = 'Y'
				                      THEN TO_CHAR(C.PRODUCT_TAG_EXP_END_DATE2, 'YYYYMMDD')
				                      ELSE NULL
				                  END
				            ELSE NULL
				        END AS productTagExpEndDate2
				     , D.LV1_CATEGORY_CODE
				     , D.LV2_CATEGORY_CODE
				     , D.LV3_CATEGORY_CODE
				     , D.LV4_CATEGORY_CODE
				     , E.SITE_CATEGORY_NM AS siteCategoryNm
				     , E.CATEGORY_NM AS categoryNm
					 , COALESCE(NULLIF(SUBSTRING_INDEX(H.CATEGORY_PAGE_URL, '/', -1), ''), G.CATEGORY_NM) AS superCategoryNm
				     , IFNULL(I.PARTICIPANT_CNT, 0) AS P_COUNT
				     , ROUND(IFNULL(I.STRARRATING_VAL, 0) * 20, 0) AS RATING_PERCENT
				     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 0) AS S_RATING
				     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 1) AS S_RATING2
				   <if test="listType == 'HR'">
                     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 2) AS S_RATING3
                   </if>
		     		 , N.SIBLING_GRP_CODE
		     		 , N.SIBLING_CODE
		     		 , N.SIBLING_GRP_NM
		     		 , IFNULL(N.DEFAULT_SIBLING_MODEL_FLAG, 'N') AS DEFAULT_SIBLING_MODEL_FLAG
		     		 , IFNULL(N.PLP_HIGHLIGHT_MODEL_FLAG, 'Y') AS PLP_HIGHLIGHT_MODEL_FLAG
		     		 , N.SIBLING_LOCAL_VAL
		     		 , N.SIBLING_TYPE_CODE
		     		 , N.SIBLING_SBJ_TYPE_CODE
		     		 , IFNULL(Q.PROMOTION_TAG, '') AS PROMOTION_TEXT
     				 , IFNULL(Q.PROMOTION_LINK_URL, '') AS PROMOTION_LINK_URL
     				 , Q.EXTERNAL_LINK_TARGET
     				 , D_LV1.CATEGORY_NM AS lv1CategoryCodeNm
				     , D_LV2.CATEGORY_NM AS lv2CategoryCodeNm
				     , D_LV3.CATEGORY_NM AS lv3CategoryCodeNm
				     , D_LV4.CATEGORY_NM AS lv4CategoryCodeNm
				  FROM DSP_PDP_M A
				 INNER JOIN DSP_PDP_D C
				    ON C.SITE_CODE = A.SITE_CODE
				   AND C.PDP_ID = A.PDP_ID
				   AND C.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
				   AND C.USE_FLAG = 'Y'
				   AND C.AEM_PUBL_FLAG ='Y'
				   AND C.SHOP_CODE = #{shopCode}
			   <choose>
				 <when test="listType == 'ALL' or listType == 'ALLSTATUS'">
				   AND C.PRODUCT_STATE_CODE != 'SUSPENDED'
				 </when>
				 <when test='skuListFlag == "Y" and hiddenModelSearchFlag == "Y"'>
				   AND C.PRODUCT_STATE_CODE IN ('ACTIVE','HIDDEN')
				 </when>
				 <otherwise>
				   AND C.PRODUCT_STATE_CODE = 'ACTIVE'
				 </otherwise>
			   </choose>
				 INNER JOIN DSP_PDP_CATEGORY_R D
				    ON D.SITE_CODE = C.SITE_CODE
				   AND D.PDP_ID = C.PDP_ID
				   AND D.USE_FLAG = 'Y'
				   AND D.DEFAULT_MAP_FLAG = 'Y'
				 INNER JOIN DSP_DISPLAY_CATEGORY_M E
				    ON E.SITE_CODE = D.SITE_CODE
				   AND E.CATEGORY_CODE = D.LV3_CATEGORY_CODE
				 INNER JOIN DSP_DISPLAY_CATEGORY_D F
				    ON F.SITE_CODE = E.SITE_CODE
				   AND F.CATEGORY_CODE = E.CATEGORY_CODE
				   AND F.SHOP_CODE = 'D2C'
				 INNER JOIN PDM_PRODUCT_M O
				    ON O.SKU_ID  = A.SKU_ID
				 INNER JOIN PDM_PRODUCT_D R
				    ON R.SKU_ID = O.SKU_ID
				   AND R.USE_FLAG = O.USE_FLAG
				  LEFT OUTER JOIN PDM_PRODUCT_CATEGORY_R S
				    ON S.SKU_ID = R.SKU_ID
				   AND S.USE_FLAG = R.USE_FLAG
				   AND S.LOCALE_CODE = R.LOCALE_CODE
				  LEFT OUTER JOIN PDM_CATEGORY_M D_LV1
			    	ON D_LV1.CATEGORY_CODE = S.LV1_CATEGORY_CODE
			       AND D_LV1.USE_FLAG = R.USE_FLAG
			       AND D_LV1.CATEGORY_LV_NO = 1
				  LEFT OUTER JOIN PDM_CATEGORY_M D_LV2
			    	ON D_LV2.CATEGORY_CODE = S.LV2_CATEGORY_CODE
			       AND D_LV2.CATEGORY_LV_NO = 2
			       AND D_LV2.USE_FLAG = A.USE_FLAG
			 	  LEFT OUTER JOIN PDM_CATEGORY_M D_LV3
			    	ON D_LV3.CATEGORY_CODE = S.LV3_CATEGORY_CODE
			       AND D_LV3.CATEGORY_LV_NO = 3
			       AND D_LV3.USE_FLAG = A.USE_FLAG
				  LEFT OUTER JOIN PDM_CATEGORY_M D_LV4
			    	ON D_LV4.CATEGORY_CODE = S.LV4_CATEGORY_CODE
			       AND D_LV4.CATEGORY_LV_NO = 4
			       AND D_LV4.USE_FLAG = A.USE_FLAG
				  LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M G
				    ON G.SITE_CODE = D.SITE_CODE
				   AND G.CATEGORY_CODE = D.LV2_CATEGORY_CODE
				  LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D H
				    ON H.SITE_CODE = G.SITE_CODE
				   AND H.CATEGORY_CODE = G.CATEGORY_CODE
				   AND H.SHOP_CODE = #{shopCode}
				  LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D I
				    ON I.PDP_ID = A.PDP_ID
				   AND I.SITE_CODE = A.SITE_CODE
				   AND I.USE_FLAG = 'Y'
				  LEFT OUTER JOIN (
				  						 SELECT DISTINCT
		                                        K.PDP_ID
		                                      , K.SIBLING_GRP_CODE
		                                      , K.SIBLING_CODE
		                                      , K.DEFAULT_SIBLING_MODEL_FLAG
		                                      , K.PLP_HIGHLIGHT_MODEL_FLAG
		                                      , M.CATEGORY_CODE
		                                      , M.SIBLING_TYPE_CODE
		                                      , J.SIBLING_LOCAL_VAL
		                                      , K.SIBLING_GRP_NM
		                                      , K.SIBLING_SBJ_TYPE_CODE
		                                   FROM DSP_SIBLING_M J
		                                  INNER JOIN DSP_SIBLING_D K
		                                     ON K.SITE_CODE = J.SITE_CODE
		                                    AND K.SIBLING_CODE = J.SIBLING_CODE
		                                    AND K.SIBLING_TYPE_CODE = J.SIBLING_TYPE_CODE
		                                    AND K.SIBLING_GRP_USE_FLAG = 'Y'
		                                    AND K.USE_FLAG = 'Y'
		                                  <if test="bizTypeCode != null and bizTypeCode != ''">
		                                    AND K.BIZ_TYPE_CODE = #{bizTypeCode}
		                                  </if>
		                                  INNER JOIN DSP_SIBLING_EXP_SEQ_D M
		                                     ON M.SIBLING_TYPE_CODE = J.SIBLING_TYPE_CODE
		                                    AND M.BIZ_TYPE_CODE = J.BIZ_TYPE_CODE
		                                    AND M.SITE_CODE = #{siteCode}
		                                    AND M.USE_FLAG = 'Y'
		                                    AND M.PLP_DEFAULT_SIBLING_FLAG = 'Y'
		                                  WHERE J.USE_FLAG = 'Y'
		                                    AND J.SITE_CODE = #{siteCode}
		                                <if test="bizTypeCode != null and bizTypeCode != ''">
		                                    AND J.BIZ_TYPE_CODE = #{bizTypeCode}
		                                </if>
		                                    AND K.PDP_ID IN (<foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
		                                ) N
		            ON N.PDP_ID = A.PDP_ID
		           AND N.CATEGORY_CODE = D.LV3_CATEGORY_CODE
		          LEFT OUTER JOIN
					              (
					              	SELECT P.PDP_ID
									     , P.PROMOTION_TAG
									     , P.PROMOTION_LINK_URL
									     , P.EXTERNAL_LINK_TARGET
									  FROM (
									       SELECT
										    	  A.PDP_ID
											<choose>
											  <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
											    , C.CAMPAIGN_BTN_NM AS PROMOTION_TAG
											    , C.CAMPAIGN_LINK_PATH AS PROMOTION_LINK_URL
											    , C.CAMPAIGN_BLANK_FLAG AS EXTERNAL_LINK_TARGET
											  </when>
											  <otherwise>
											    , B.PROMOTION_TAG_VAL AS PROMOTION_TAG
											    , D.PROMOTION_LINK_PATH AS PROMOTION_LINK_URL
											    , D.PROMOTION_LINK_SBJ_CODE AS EXTERNAL_LINK_TARGET
											  </otherwise>
											</choose>
											 FROM PRM_PROMOTION_PRODUCT_R A
											INNER JOIN PRM_PROMOTION_M B
											   ON B.SITE_CODE = A.SITE_CODE
											  AND B.PROMOTION_ID = A.PROMOTION_ID
											  AND B.USE_FLAG = 'Y'
											<choose>
											  <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
											INNER JOIN PRM_PROMOTION_PRODUCT_CAMP_R C
											   ON C.PROMOTION_ID = A.PROMOTION_ID
											  AND C.PDP_ID = A.PDP_ID
											  AND C.SITE_CODE = A.SITE_CODE
											  AND C.USE_FLAG = 'Y'
											  AND C.DSP_SEQ = A.PDP_PROMOTION_SEQ
											  AND C.PROMOTION_ID = #{promotionId}
											  </when>
											  <otherwise>
											 LEFT OUTER JOIN PRM_PROMOTION_LIST_M D
											   ON D.PROMOTION_LIST_ID = B.PROMOTION_ID
											  AND D.SITE_CODE = B.SITE_CODE
											  AND D.USE_FLAG = 'Y'
											  </otherwise>
											</choose>
										    WHERE A.SITE_CODE = #{siteCode}
											  AND STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PROMOTION_EXP_BEGIN_DATE AND B.PROMOTION_EXP_END_DATE
											  AND IFNULL(B.PROMOTION_NONEXP_FLAG, 'N') <![CDATA[<>]]> 'Y'
											  AND A.USE_FLAG = 'Y'
										    ORDER BY A.PDP_PROMOTION_SEQ ASC
					                          ) P
					                 GROUP BY P.PDP_ID, P.PROMOTION_TAG, P.PROMOTION_LINK_URL, P.EXTERNAL_LINK_TARGET
					              ) Q
					ON Q.PDP_ID = A.PDP_ID
				 WHERE A.SITE_CODE = #{siteCode}
				   AND A.USE_FLAG = 'Y'
				   <if test="customerGroup != null and customerGroup !=''">
         			AND (NOT EXISTS (SELECT 1
	           				FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID	= A.PDP_ID
	           				 AND USE_FLAG = 'Y')
	          		 OR EXISTS (SELECT 1
	           			    FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
	           			   WHERE SHOP_CODE = #{shopCode}
	           				 AND CUST_GRP_CODE 	= #{customerGroup}
	           				 AND SITE_CODE = #{siteCode}
	           				 AND PDP_ID = A.PDP_ID
	           				 AND USE_FLAG = 'Y'))
				  </if>
				 <if test="bizTypeCode != null and bizTypeCode != ''">
				   AND A.BIZ_TYPE_CODE = #{bizTypeCode}
				 </if>
				   AND A.PDP_ID IN (<foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
				) CCC
			) AAA
		  <if test='siblingGroupFlag == "Y" and listType != "PAGINATION" '>
		    WHERE AAA.CNT = 1
		  </if>
		  <choose>
		    <when test="listType == 'HR'">
		     ORDER BY HR_RATING_NUM
		     LIMIT 10
		    </when>
		    <otherwise>
		     ORDER BY FIELD(AAA.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
			</otherwise>
		  </choose>
    </select>

    <select id="selectMtsProductListPdpInfoNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoRequestVO" resultType="com.lge.d2x.domain.productList.v1.model.ProductListPdpInfoResponseVO">
    	SELECT AAA.*
		     , CASE WHEN 'HR' = #{listType}
		            THEN  CASE WHEN COUNT(AAA.PDP_ID) OVER() <![CDATA[>]]> 10
		                       THEN 10
		                       ELSE COUNT(AAA.PDP_ID) OVER()
		                    END
		            ELSE  COUNT(AAA.PDP_ID) OVER()
		        END AS TOTAL_COUNT
		  FROM
		      (
				SELECT
					   ROW_NUMBER() OVER (PARTITION BY IFNULL(CCC.SIBLING_GRP_CODE, CCC.PDP_ID) ORDER BY IFNULL(CCC.PLP_HIGHLIGHT_MODEL_FLAG,'Y') DESC) AS CNT
			        <if test="listType == 'HR'">
			         , ROW_NUMBER() OVER (ORDER BY CCC.S_RATING3 DESC, CAST(IFNULL(CCC.P_COUNT, 0) AS SIGNED) DESC, FIELD(CCC.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)) AS HR_RATING_NUM
			        </if>
			         , CCC.*
			      FROM
             		(
						SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectProductListPdpInfo */
						       A.PDP_ID
						     , A.LGCOM_SKU_ID
						     , A.SKU_ID
						     , A.PRODUCT_NM
						     , IFNULL(NULLIF(R.NEW_MKT_PRODUCT_NM, ''), A.USER_FRNDY_PRODUCT_NM) AS USER_FRNDY_PRODUCT_NM
						     , A.PDP_TYPE_CODE
						     , A.BIZ_TYPE_CODE
						     , O.SALES_MODEL_CODE
						     , O.SALES_MODEL_SUFFIX_CODE
						     , O.LV1_PRODUCT_CODE
						     , CASE WHEN A.SITE_CODE = 'SA' OR A.SITE_CODE = 'SA_EN' THEN ROUND(IFNULL(A.MSRP_SALES_PRICE, 0.00)) ELSE IFNULL(A.MSRP_SALES_PRICE, 0.00) END AS msrpSalesPrice
						     , A.ELABEL_GRD_CODE
						     , T.EL_DOC_TYPE_CODE
						     , T.PIS_DOC_TYPE_CODE
						     , A.SECOND_ELABEL_GRD_CODE
						     , T.SECOND_EL_DOC_TYPE_CODE
						     , T.SECOND_PIS_DOC_TYPE_CODE
						     , T.SECOND_PF_CODE
						     , T.EL_TYPE_CODE
						     , T.SECOND_EL_TYPE_CODE
						     , A.WTOWER_PRODUCT_FLAG
						     , A.SIGNT_PRODUCT_FLAG
						     , A.ITB_USE_FLAG
						     , A.EXCL_PRODUCT_SETT_CODE
						     , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productRelesDd
						     , C.PDP_URL
						     , C.PRODUCT_STATE_CODE
						     , IFNULL(C.SML_IMG_URL, #{dummyImageUrl}) AS smlImgUrl
						     , IFNULL(C.MDM_IMG_URL, #{dummyImageUrl}) AS mdmImgUrl
						     , IFNULL(C.BIG_IMG_URL , #{dummyImageUrl}) AS bigImgUrl
						     , C.IMG_ALT_TEXT_CNTS
						     , C.CREATION_DATE
						     , C.PRODUCT_THEME_TYPE_CODE
						     , '' AS DEFAULT_PRODUCT_TAG_CODE
						     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1)
						            THEN
						                 CASE WHEN C.PRODUCT_TAG_USE_FLAG1 = 'Y'
						                      THEN C.PRODUCT_TAG_CODE1
						                      ELSE NULL
						                  END
						            ELSE NULL
						        END AS productTagCode1
						     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE1 AND C.PRODUCT_TAG_EXP_END_DATE1)
						            THEN
						                 CASE WHEN C.PRODUCT_TAG_USE_FLAG1 = 'Y'
						                      THEN TO_CHAR(C.PRODUCT_TAG_EXP_END_DATE1, 'YYYYMMDD')
						                      ELSE NULL
						                  END
						            ELSE NULL
						        END AS productTagExpEndDate1
						     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2)
						            THEN
						                 CASE WHEN C.PRODUCT_TAG_USE_FLAG2 = 'Y'
						                      THEN C.PRODUCT_TAG_CODE2
						                      ELSE NULL
						                  END
						            ELSE NULL
						        END AS productTagCode2
						     , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN C.PRODUCT_TAG_EXP_BEGIN_DATE2 AND C.PRODUCT_TAG_EXP_END_DATE2)
						            THEN
						                 CASE WHEN C.PRODUCT_TAG_USE_FLAG2 = 'Y'
						                      THEN TO_CHAR(C.PRODUCT_TAG_EXP_END_DATE2, 'YYYYMMDD')
						                      ELSE NULL
						                  END
						            ELSE NULL
						        END AS productTagExpEndDate2
						     , D.LV1_CATEGORY_ID
						     , D.LV2_CATEGORY_ID
						     , D.LV3_CATEGORY_ID
						     , E.SITE_CATEGORY_NM AS siteCategoryNm
						     , E.CATEGORY_NM AS categoryNm
						     , COALESCE(NULLIF(SUBSTRING_INDEX(H.CATEGORY_PAGE_URL, '/', -1), ''), G.CATEGORY_NM) AS superCategoryNm
						     , IFNULL(I.PARTICIPANT_CNT, 0) AS P_COUNT
						     , ROUND(IFNULL(I.STRARRATING_VAL, 0) * 20, 0) AS RATING_PERCENT
						     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 0) AS S_RATING
						     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 1) AS S_RATING2
						   <if test="listType == 'HR'">
		                     , ROUND(IFNULL(I.STRARRATING_VAL, 0), 2) AS S_RATING3
		                   </if>
		     				 , N.SIBLING_GRP_CODE
		     				 , N.SIBLING_CODE
		     				 , N.SIBLING_GRP_NM
		     				 , N.SIBLING_SBJ_TYPE_CODE
		     				 , IFNULL(N.DEFAULT_SIBLING_MODEL_FLAG, 'N') AS DEFAULT_SIBLING_MODEL_FLAG
		     				 , IFNULL(N.PLP_HIGHLIGHT_MODEL_FLAG, 'Y') AS PLP_HIGHLIGHT_MODEL_FLAG
		     				 , N.SIBLING_LOCAL_VAL
		     				 , N.SIBLING_TYPE_CODE
		     				 , IFNULL(Q.PROMOTION_TAG, '') AS PROMOTION_TEXT
		     				 , IFNULL(Q.PROMOTION_LINK_URL, '') AS PROMOTION_LINK_URL
		     				 , Q.EXTERNAL_LINK_TARGET
		     				 , D_LV1.CATEGORY_NM AS lv1CategoryCodeNm
						     , D_LV2.CATEGORY_NM AS lv2CategoryCodeNm
						     , D_LV3.CATEGORY_NM AS lv3CategoryCodeNm
						     , D_LV4.CATEGORY_NM AS lv4CategoryCodeNm
						  FROM DSP_PDP_M A
						 INNER JOIN DSP_PDP_D C
						    ON C.SITE_CODE = A.SITE_CODE
						   AND C.PDP_ID = A.PDP_ID
						   AND C.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
						   AND C.USE_FLAG = 'Y'
						   AND C.AEM_PUBL_FLAG ='Y'
						   AND C.SHOP_CODE = #{shopCode}
					   <choose>
						 <when test="listType == 'ALL' or listType == 'ALLSTATUS'">
						   AND C.PRODUCT_STATE_CODE != 'SUSPENDED'
						 </when>
						 <when test='skuListFlag == "Y" and hiddenModelSearchFlag == "Y"'>
						   AND C.PRODUCT_STATE_CODE IN ('ACTIVE','HIDDEN')
						 </when>
						 <otherwise>
						   AND C.PRODUCT_STATE_CODE = 'ACTIVE'
						 </otherwise>
					   </choose>
						 INNER JOIN DSP_OLD_PDP_CATEGORY_R D
						    ON D.SITE_CODE = C.SITE_CODE
						   AND D.PDP_ID = C.PDP_ID
						   AND D.USE_FLAG = 'Y'
						   AND D.DEFAULT_MAP_FLAG = 'Y'
						 INNER JOIN DSP_DISPLAY_CATEGORY_M E
						    ON E.SITE_CODE = D.SITE_CODE
						   AND E.CATEGORY_CODE = D.LV2_CATEGORY_ID
						   AND E.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
						 INNER JOIN DSP_DISPLAY_CATEGORY_D F
						    ON F.SITE_CODE = E.SITE_CODE
						   AND F.CATEGORY_CODE = E.CATEGORY_CODE
						   AND F.SHOP_CODE = #{shopCode}
						   AND F.USE_FLAG = 'Y'
						 INNER JOIN PDM_PRODUCT_M O
						    ON O.SKU_ID  = A.SKU_ID
						 INNER JOIN PDM_PRODUCT_D R
						    ON R.SKU_ID = O.SKU_ID
						   AND R.USE_FLAG = O.USE_FLAG
						  LEFT OUTER JOIN PDM_PRODUCT_CATEGORY_R S
						    ON S.SKU_ID = R.SKU_ID
						   AND S.USE_FLAG = R.USE_FLAG
						   AND S.LOCALE_CODE = R.LOCALE_CODE
						  LEFT OUTER JOIN PDM_CATEGORY_M D_LV1
					    	ON D_LV1.CATEGORY_CODE = S.LV1_CATEGORY_CODE
					       AND D_LV1.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
					       AND D_LV1.USE_FLAG = R.USE_FLAG
					       AND D_LV1.CATEGORY_LV_NO = 1
						  LEFT OUTER JOIN PDM_CATEGORY_M D_LV2
					    	ON D_LV2.CATEGORY_CODE = S.LV2_CATEGORY_CODE
					       AND D_LV2.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
						   AND D_LV2.HIGH_CATEGORY_CODE = S.LV1_CATEGORY_CODE
					       AND D_LV2.USE_FLAG = A.USE_FLAG
					       AND D_LV2.CATEGORY_LV_NO = 2
						  LEFT OUTER JOIN PDM_CATEGORY_M D_LV3
					    	ON D_LV3.CATEGORY_CODE = S.LV3_CATEGORY_CODE
					       AND D_LV3.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
						   AND D_LV3.HIGH_CATEGORY_CODE = S.LV2_CATEGORY_CODE
					       AND D_LV3.USE_FLAG = A.USE_FLAG
					       AND D_LV3.CATEGORY_LV_NO = 3
						  LEFT OUTER JOIN PDM_CATEGORY_M D_LV4
					    	ON D_LV4.CATEGORY_CODE = S.LV4_CATEGORY_CODE
					       AND D_LV4.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
						   AND D_LV4.HIGH_CATEGORY_CODE = S.LV3_CATEGORY_CODE
					       AND D_LV4.USE_FLAG = A.USE_FLAG
					       AND D_LV4.CATEGORY_LV_NO = 4
						  LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M G
						    ON G.SITE_CODE = D.SITE_CODE
						   AND G.CATEGORY_CODE = D.LV1_CATEGORY_ID
						  LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D H
						    ON H.SITE_CODE = G.SITE_CODE
						   AND H.CATEGORY_CODE = G.CATEGORY_CODE
						   AND H.SHOP_CODE = #{shopCode}
						  LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D I
						    ON I.PDP_ID = A.PDP_ID
						   AND I.SITE_CODE = A.SITE_CODE
						   AND I.USE_FLAG = 'Y'
						  LEFT OUTER JOIN (
						  						 SELECT DISTINCT
				                                        K.PDP_ID
				                                      , K.SIBLING_GRP_CODE
				                                      , K.SIBLING_CODE
				                                      , K.DEFAULT_SIBLING_MODEL_FLAG
				                                      , K.PLP_HIGHLIGHT_MODEL_FLAG
				                                      , M.CATEGORY_CODE
				                                      , M.SIBLING_TYPE_CODE
				                                      , J.SIBLING_LOCAL_VAL
				                                      , K.SIBLING_GRP_NM
				                                      , K.SIBLING_SBJ_TYPE_CODE
				                                   FROM DSP_SIBLING_M J
				                                  INNER JOIN DSP_SIBLING_D K
				                                     ON K.SITE_CODE = J.SITE_CODE
				                                    AND K.SIBLING_CODE = J.SIBLING_CODE
				                                    AND K.SIBLING_TYPE_CODE = J.SIBLING_TYPE_CODE
				                                    AND K.SIBLING_GRP_USE_FLAG = 'Y'
				                                    AND K.USE_FLAG = 'Y'
				                                  <if test="bizTypeCode != null and bizTypeCode != ''">
				                                    AND K.BIZ_TYPE_CODE = #{bizTypeCode}
				                                  </if>
				                                  INNER JOIN DSP_SIBLING_EXP_SEQ_D M
				                                     ON M.SIBLING_TYPE_CODE = J.SIBLING_TYPE_CODE
				                                    AND M.BIZ_TYPE_CODE = J.BIZ_TYPE_CODE
				                                    AND M.SITE_CODE = #{siteCode}
				                                    AND M.USE_FLAG = 'Y'
				                                    AND M.PLP_DEFAULT_SIBLING_FLAG = 'Y'
				                                  WHERE J.USE_FLAG = 'Y'
				                                    AND J.SITE_CODE = #{siteCode}
				                                  <if test="bizTypeCode != null and bizTypeCode != ''">
				                                    AND J.BIZ_TYPE_CODE = #{bizTypeCode}
				                                  </if>
		                                    		AND K.PDP_ID IN (<foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
				                                ) N
				            ON N.PDP_ID = A.PDP_ID
				           AND N.CATEGORY_CODE = D.LV2_CATEGORY_ID
				          LEFT OUTER JOIN
					              (
					              	SELECT P.PDP_ID
									     , P.PROMOTION_TAG
									     , P.PROMOTION_LINK_URL
									     , P.EXTERNAL_LINK_TARGET
									  FROM (
									       SELECT
										    	  A.PDP_ID
											<choose>
											  <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
											    , C.CAMPAIGN_BTN_NM AS PROMOTION_TAG
											    , C.CAMPAIGN_LINK_PATH AS PROMOTION_LINK_URL
											    , C.CAMPAIGN_BLANK_FLAG AS EXTERNAL_LINK_TARGET
											  </when>
											  <otherwise>
											    , B.PROMOTION_TAG_VAL AS PROMOTION_TAG
											    , D.PROMOTION_LINK_PATH AS PROMOTION_LINK_URL
											    , D.PROMOTION_LINK_SBJ_CODE AS EXTERNAL_LINK_TARGET
											  </otherwise>
											</choose>
											 FROM PRM_PROMOTION_PRODUCT_R A
											INNER JOIN PRM_PROMOTION_M B
											   ON B.SITE_CODE = A.SITE_CODE
											  AND B.PROMOTION_ID = A.PROMOTION_ID
											  AND B.USE_FLAG = 'Y'
											<choose>
											  <when test='promoProductFlag != null and promoProductFlag != "" and promoProductFlag == "Y"'>
											INNER JOIN PRM_PROMOTION_PRODUCT_CAMP_R C
											   ON C.PROMOTION_ID = A.PROMOTION_ID
											  AND C.PDP_ID = A.PDP_ID
											  AND C.SITE_CODE = A.SITE_CODE
											  AND C.USE_FLAG = 'Y'
											  AND C.DSP_SEQ = A.PDP_PROMOTION_SEQ
											  AND C.PROMOTION_ID = #{promotionId}
											  </when>
											  <otherwise>
											 LEFT OUTER JOIN PRM_PROMOTION_LIST_M D
											   ON D.PROMOTION_LIST_ID = B.PROMOTION_ID
											  AND D.SITE_CODE = B.SITE_CODE
											  AND D.USE_FLAG = 'Y'
											  </otherwise>
											</choose>
										    WHERE A.SITE_CODE = #{siteCode}
											  AND STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PROMOTION_EXP_BEGIN_DATE AND B.PROMOTION_EXP_END_DATE
											  AND IFNULL(B.PROMOTION_NONEXP_FLAG, 'N') <![CDATA[<>]]> 'Y'
											  AND A.USE_FLAG = 'Y'
										    ORDER BY A.PDP_PROMOTION_SEQ ASC
					                          ) P
					                 GROUP BY P.PDP_ID
					              ) Q
							ON Q.PDP_ID = A.PDP_ID
						  LEFT OUTER JOIN PDM_PRODUCT_EL_PIS_R T
		                    ON A.SITE_CODE = T.SITE_CODE
			  	    	   AND O.LV1_PRODUCT_CODE = T.LV1_EL_PRODUCT_CODE
			  	    	   AND O.LV2_PRODUCT_CODE = T.LV2_EL_PRODUCT_CODE
						 WHERE A.SITE_CODE = #{siteCode}
						   AND A.USE_FLAG = 'Y'
						 <if test="customerGroup != null and customerGroup !=''">
							AND (NOT EXISTS (SELECT 1
									FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
								WHERE SHOP_CODE = #{shopCode}
									AND SITE_CODE = #{siteCode}
									AND PDP_ID	= A.PDP_ID
									AND USE_FLAG = 'Y')
							OR EXISTS (SELECT 1
									FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
								WHERE SHOP_CODE = #{shopCode}
									AND CUST_GRP_CODE 	= #{customerGroup}
									AND SITE_CODE = #{siteCode}
									AND PDP_ID = A.PDP_ID
									AND USE_FLAG = 'Y'))
				 		 </if>
						 <if test="bizTypeCode != null and bizTypeCode != ''">
						   AND A.BIZ_TYPE_CODE = #{bizTypeCode}
						 </if>
						   AND A.PDP_ID IN (<foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
						 ) CCC
					) AAA
				 <if test='siblingGroupFlag == "Y" and listType != "PAGINATION" '>
				    WHERE AAA.CNT = 1
				 </if>
				 <choose>
				   <when test="listType == 'HR'">
				    ORDER BY HR_RATING_NUM
				    LIMIT 10
				   </when>
				   <otherwise>
				    ORDER BY FIELD(AAA.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
				   </otherwise>
				 </choose>
    </select>

    <select id="selectMtsOrderProductList" parameterType="com.lge.d2x.domain.productList.v1.model.OrderProductListRequestVO" resultType="com.lge.d2x.domain.productList.v1.model.OrderProductListResponseVO">
    	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsOrderProductList */
    		   I.*
    		 , CASE WHEN 'HR' = #{listType}
                    THEN IF(COUNT(I.PDP_ID) OVER() <![CDATA[>]]> 10, 10, COUNT(I.PDP_ID) OVER())
                    ELSE COUNT(I.PDP_ID) OVER()
                END AS TOTAL_COUNT
    	  FROM
            (
            SELECT A.PDP_ID
                 , A.LGCOM_SKU_ID
                 , A.SKU_ID
                 , B.SHOP_CODE
                 , B.PRODUCT_STATE_CODE
                 , B.PDP_URL
                 , B.IMG_ALT_TEXT_CNTS
                 , B.MDM_IMG_URL
                 , B.SML_IMG_URL
                 , IFNULL(NULLIF(SUBSTRING_INDEX(H.CATEGORY_PAGE_URL, '/', -1),'') , G.CATEGORY_NM) AS superCategoryName
                 , E.CATEGORY_NM
                 , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1)
                        THEN IF(B.PRODUCT_TAG_USE_FLAG1 = 'Y', B.PRODUCT_TAG_CODE1, NULL)
                        ELSE NULL
                    END AS PRODUCT_TAG1
                 , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1)
                        THEN IF(B.PRODUCT_TAG_USE_FLAG1 = 'Y', DATE_FORMAT(B.PRODUCT_TAG_EXP_END_DATE1, '%Y%m%d'), NULL)
                        ELSE NULL
                    END AS PRODUCT_TAG1_END_DAY
                 , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2)
                        THEN IF(B.PRODUCT_TAG_USE_FLAG2 = 'Y', B.PRODUCT_TAG_CODE2, NULL)
                        ELSE NULL
                    END AS PRODUCT_TAG2
                 , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND  B.PRODUCT_TAG_EXP_END_DATE2)
                        THEN IF(B.PRODUCT_TAG_USE_FLAG2 = 'Y', DATE_FORMAT(B.PRODUCT_TAG_EXP_END_DATE2, '%Y%m%d'), NULL)
                        ELSE NULL
                    END AS PRODUCT_TAG2_END_DAY
              FROM DSP_PDP_M A
         	 INNER JOIN DSP_PDP_D B
                ON B.SITE_CODE = A.SITE_CODE
               AND B.PDP_ID = A.PDP_ID
             <if test="bizTypeCode != null and bizTypeCode != ''">
               AND B.BIZ_TYPE_CODE = #{bizTypeCode}
             </if>
           	   AND B.USE_FLAG = 'Y'
	           AND B.SHOP_CODE = #{shopCode}
	           AND B.AEM_PUBL_FLAG = 'Y'
          	   AND B.PRODUCT_STATE_CODE != 'SUSPENDED'
         	 INNER JOIN DSP_PDP_CATEGORY_R D
                ON D.PDP_ID = A.PDP_ID
               AND D.USE_FLAG = 'Y'
               AND D.SITE_CODE = A.SITE_CODE
               AND D.DEFAULT_MAP_FLAG = 'Y'
          	 INNER JOIN DSP_DISPLAY_CATEGORY_M E
                ON E.CATEGORY_CODE = D.LV3_CATEGORY_CODE
               AND E.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
               AND E.SITE_CODE = D.SITE_CODE
          	 INNER JOIN DSP_DISPLAY_CATEGORY_D F
                ON F.CATEGORY_CODE = E.CATEGORY_CODE
               AND F.SITE_CODE = E.SITE_CODE
               AND F.BIZ_TYPE_CODE = E.BIZ_TYPE_CODE
               AND F.USE_FLAG = 'Y'
               AND F.SHOP_CODE = #{shopCode}
          	 INNER JOIN DSP_DISPLAY_CATEGORY_M G
                ON G.CATEGORY_CODE = D.LV2_CATEGORY_CODE
               AND G.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
               AND G.SITE_CODE = D.SITE_CODE
          	 INNER JOIN DSP_DISPLAY_CATEGORY_D H
                ON H.CATEGORY_CODE = G.CATEGORY_CODE
               AND H.BIZ_TYPE_CODE = G.BIZ_TYPE_CODE
               AND H.SITE_CODE = G.SITE_CODE
               AND H.USE_FLAG = 'Y'
               AND H.SHOP_CODE = #{shopCode}
          	 WHERE A.SITE_CODE = #{siteCode}
          	   AND A.PDP_ID IN (<foreach item="pdpId" collection="pdpIdList" open="" separator="," close="">
		            #{pdpId}
		        </foreach>)
          ) I
         ORDER BY FIELD(I.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
    </select>

    <select id="selectMtsOrderProductListNotStandard" parameterType="com.lge.d2x.domain.productList.v1.model.OrderProductListRequestVO" resultType="com.lge.d2x.domain.productList.v1.model.OrderProductListResponseVO">
    	SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectMtsOrderProductList */
    		   I.*
    		 , CASE WHEN 'HR' = #{listType}
                    THEN IF(COUNT(I.PDP_ID) OVER() <![CDATA[>]]> 10, 10, COUNT(I.PDP_ID) OVER())
                    ELSE COUNT(I.PDP_ID) OVER()
                END AS TOTAL_COUNT
    	  FROM
            (
            SELECT A.PDP_ID
                 , A.LGCOM_SKU_ID
                 , A.SKU_ID
                 , B.SHOP_CODE
                 , B.PRODUCT_STATE_CODE
                 , B.PDP_URL
                 , B.IMG_ALT_TEXT_CNTS
                 , B.MDM_IMG_URL
                 , B.SML_IMG_URL
                 , IFNULL(NULLIF(SUBSTRING_INDEX(H.CATEGORY_PAGE_URL, '/', -1),'') , G.CATEGORY_NM) AS superCategoryName
                 , E.CATEGORY_NM
                 , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1)
                        THEN IF(B.PRODUCT_TAG_USE_FLAG1 = 'Y', B.PRODUCT_TAG_CODE1, NULL)
                        ELSE NULL
                    END AS PRODUCT_TAG1
                 , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1)
                        THEN IF(B.PRODUCT_TAG_USE_FLAG1 = 'Y', DATE_FORMAT(B.PRODUCT_TAG_EXP_END_DATE1, '%Y%m%d'), NULL)
                        ELSE NULL
                    END AS PRODUCT_TAG1_END_DAY
                 , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2)
                        THEN IF(B.PRODUCT_TAG_USE_FLAG2 = 'Y', B.PRODUCT_TAG_CODE2, NULL)
                        ELSE NULL
                    END AS PRODUCT_TAG2
                 , CASE WHEN (STR_TO_DATE(#{today}, '%Y%m%d') BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND  B.PRODUCT_TAG_EXP_END_DATE2)
                        THEN IF(B.PRODUCT_TAG_USE_FLAG2 = 'Y', DATE_FORMAT(B.PRODUCT_TAG_EXP_END_DATE2, '%Y%m%d'), NULL)
                        ELSE NULL
                    END AS PRODUCT_TAG2_END_DAY
              FROM DSP_PDP_M A
         	 INNER JOIN DSP_PDP_D B
                ON B.SITE_CODE = A.SITE_CODE
               AND B.PDP_ID = A.PDP_ID
             <if test="bizTypeCode != null and bizTypeCode != ''">
               AND B.BIZ_TYPE_CODE = #{bizTypeCode}
             </if>
           	   AND B.USE_FLAG = 'Y'
	           AND B.SHOP_CODE = #{shopCode}
	           AND B.AEM_PUBL_FLAG = 'Y'
          	   AND B.PRODUCT_STATE_CODE != 'SUSPENDED'
         	 INNER JOIN DSP_OLD_PDP_CATEGORY_R D
                ON D.PDP_ID = A.PDP_ID
               AND D.USE_FLAG = 'Y'
               AND D.SITE_CODE = A.SITE_CODE
               AND D.DEFAULT_MAP_FLAG = 'Y'
          	 INNER JOIN DSP_DISPLAY_CATEGORY_M E
                ON E.CATEGORY_CODE = D.LV2_CATEGORY_ID
               AND E.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
               AND E.SITE_CODE = D.SITE_CODE
          	 INNER JOIN DSP_DISPLAY_CATEGORY_D F
                ON F.CATEGORY_CODE = E.CATEGORY_CODE
               AND F.SITE_CODE = E.SITE_CODE
               AND F.BIZ_TYPE_CODE = E.BIZ_TYPE_CODE
               AND F.USE_FLAG = 'Y'
               AND F.SHOP_CODE = #{shopCode}
          	 INNER JOIN DSP_DISPLAY_CATEGORY_M G
                ON G.CATEGORY_CODE = D.LV1_CATEGORY_ID
               AND G.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
               AND G.SITE_CODE = D.SITE_CODE
          	 INNER JOIN DSP_DISPLAY_CATEGORY_D H
                ON H.CATEGORY_CODE = G.CATEGORY_CODE
               AND H.BIZ_TYPE_CODE = G.BIZ_TYPE_CODE
               AND H.SITE_CODE = G.SITE_CODE
               AND H.USE_FLAG = 'Y'
               AND H.SHOP_CODE = #{shopCode}
          	 WHERE A.SITE_CODE = #{siteCode}
          	   AND A.PDP_ID IN (<foreach item="pdpId" collection="pdpIdList" open="" separator="," close="">
		            #{pdpId}
		        </foreach>)
          ) I
         ORDER BY FIELD(I.PDP_ID, <foreach collection="pdpIdList" item="pdpId" separator=",">#{pdpId}</foreach>)
    </select>

    <select id="selectLgPickPdpIdList" parameterType="com.lge.d2x.domain.productList.v1.model.PdpIdListRequestVO" resultType="String">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.productList.v1.service.ProductListService.selectPdpIdListByListType */
               B.PDP_ID
          FROM DSP_LG_PICK_M A
          JOIN DSP_PDP_M B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.LGCOM_SKU_ID = A.LGCOM_SKU_ID
           AND B.USE_FLAG = 'Y'
         WHERE A.SITE_CODE = #{siteCode}
         LIMIT 20
    </select>
</mapper>