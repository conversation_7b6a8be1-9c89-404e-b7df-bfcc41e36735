package com.lge.d2x.domain.svdCategory.v1.service;

import com.lge.d2x.domain.svdCategory.v1.model.SvdCategoryRequestVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv1CategoryResponseVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv2CategoryResponseVO;
import com.lge.d2x.domain.svdCategory.v1.model.SvdLv3CategoryResponseVO;
import com.lge.d2x.domain.svdCategory.v1.repository.pdsmgr.SvdCategoryRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class SvdCategoryService {

    private final SvdCategoryRepository svdCategoryRepository;

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv1CategoryResponseVO> selectSvdLv1CategoryList(SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv1CategoryList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv1CategoryResponseVO> selectSvdLv1CategoryListTypeT(
            SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv1CategoryListTypeT(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv1CategoryResponseVO> selectSvdLv1CategoryListV2TypeT(
            SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv1CategoryListV2TypeT(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv2CategoryResponseVO> selectSvdLv2CategoryListTypeT(
            SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv2CategoryListTypeT(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv2CategoryResponseVO> selectSvdLv2CategoryListV2TypeT(
            SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv2CategoryListV2TypeT(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv2CategoryResponseVO> selectSvdLv2CategoryListTypeE(
            SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv2CategoryListTypeE(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv2CategoryResponseVO> selectSvdLv2CategoryListTypeMW(
            SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv2CategoryListTypeMW(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv3CategoryResponseVO> selectSvdLv3CategoryList(SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv3CategoryList(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv3CategoryResponseVO> selectSvdLv3CategoryListV2(
            SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv3CategoryListV2(requestVO);
    }

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<SvdLv3CategoryResponseVO> selectSvdLv3CategoryListTypeE(
            SvdCategoryRequestVO requestVO) {
        return svdCategoryRepository.selectSvdLv3CategoryListTypeE(requestVO);
    }
}
