package com.lge.d2x.domain.support.v1.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class GpCateInfoResponseVO {
    private String lv1CategoryCode;
    private String lv2CategoryCode;
    private String lv3CategoryCode;
    private String salesModelFlag;
    private String suppUseFlag;
    private String gsfsUseFlag;
    private String buyerModelCode;
    private String categoryNm;
    private String csDspSeq;
    private String stickyImgUrl;
    private String stickyHoverIconUrl;
    private String serviceType;

    private String csSuperCategoryId;
    private String csSuperCategoryName;
    private String displayOrderNo;
    private String code;
    private String value;
    private String stickyImageAddr;
    private String pictogramImageAddr;

    private String csCategoryId;
    private String csCategoryName;
    private String csDisplayOrderNo;
    private String csOrder;
    private String SERVICE_TYPE;

    private String csSubCategoryId;
    private String csSubCategoryName;
    private String csSubDisplayOrderNo;

    private String[] objetModelList;
}
