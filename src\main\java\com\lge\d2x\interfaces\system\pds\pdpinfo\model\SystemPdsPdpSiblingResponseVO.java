package com.lge.d2x.interfaces.system.pds.pdpinfo.model;

import com.lge.d2x.domain.product.v1.model.PdpSiblingModelResponseVO;
import java.math.BigDecimal;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class SystemPdsPdpSiblingResponseVO {
    private String lgcomSkuId;
    private String skuId;
    private String pdpId;
    private String productNm;
    private String bizTypeCode;
    private BigDecimal msrp;
    private String userFrndyProductNm;
    private String productStateCode;
    private String pdpTypeCode;
    private String siteCode;
    private String defaultSiblingModelFlag;
    private String pdpTitle;
    private String siblingCode;
    private String siblingGrpCode;
    private String siblingValue;
    private String siblingTypeCode;
    private String pdpUrl;
    private String priceUseFlag;
    private String siblingGrpNm;
    private String siblingSbjTypeCode;
    private String displayOrderNo;
    private String priorityOrderNo;
    private String display1depth;

    public PdpSiblingModelResponseVO toVO() {
        return PdpSiblingModelResponseVO.builder()
                .sku(this.lgcomSkuId)
                .pimSku(this.skuId)
                .modelId(this.pdpId)
                .modelName(this.productNm)
                .bizType(this.bizTypeCode)
                .msrp(this.msrp)
                .userFriendlyName(this.userFrndyProductNm)
                .modelStatusCode(this.productStateCode)
                .localeCode(this.siteCode)
                .defaultModelFlag(this.defaultSiblingModelFlag)
                .pdpTitle(this.pdpTitle)
                .siblingCode(this.siblingCode)
                .siblingGroupCode(this.siblingGrpCode)
                .siblingValue(this.siblingValue)
                .siblingType(this.siblingTypeCode)
                .modelUrlPath(this.pdpUrl)
                .priceUseFlag(this.priceUseFlag)
                .seriesName(this.siblingGrpNm)
                .modelType(this.pdpTypeCode)
                .target(this.siblingSbjTypeCode)
                .displayOrderNo(this.displayOrderNo)
                .priorityOrderNo(this.priorityOrderNo)
                .display1Depth(this.display1depth)
                .build();
    }
}
