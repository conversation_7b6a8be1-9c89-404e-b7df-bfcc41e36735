package com.lge.d2x.domain.gnbNotification.v1.service;

import com.lge.d2x.domain.gnbNotification.v1.model.GnbNotificationRequestVO;
import com.lge.d2x.domain.gnbNotification.v1.model.GnbNotificationResponseVO;
import com.lge.d2x.domain.gnbNotification.v1.repository.pdsmgr.GnbNotificationRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class GnbNotificationService {
    private final GnbNotificationRepository gnbNotificationRepository;

    @Transactional(readOnly = true)
    public List<GnbNotificationResponseVO> getGnbNotificationBar(
            GnbNotificationRequestVO requestVO) {
        return gnbNotificationRepository.selectGnbNotificationBar(requestVO);
    }
}
