package com.lge.d2x.domain.product.v1.model;

import java.math.BigDecimal;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PdpSiblingModelResponseVO {
    private String sku;
    private String pimSku;
    private String modelId;
    private String modelName;
    private String bizType;
    private BigDecimal msrp;
    private String userFriendlyName;
    private String modelStatusCode;
    private String localeCode;
    private String defaultModelFlag;
    private String pdpTitle;
    private String siblingCode;
    private String siblingGroupCode;
    private String siblingValue;
    private String siblingType;
    private String modelUrlPath;
    private String priceUseFlag;
    private String seriesName;
    private String modelType;
    private String target;
    private String salesModelCode;
    private String displayOrderNo;
    private String priorityOrderNo;
    private String display1Depth;
}
