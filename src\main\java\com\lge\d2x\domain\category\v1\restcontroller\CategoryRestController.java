package com.lge.d2x.domain.category.v1.restcontroller;

import com.lge.d2x.domain.category.v1.model.CatalogueCategoryRequestVO;
import com.lge.d2x.domain.category.v1.model.CatalogueCategoryResponseVO;
import com.lge.d2x.domain.category.v1.model.CategoryRequestVO;
import com.lge.d2x.domain.category.v1.model.CategoryResponseVO;
import com.lge.d2x.domain.category.v1.model.PimCategoryRequestVO;
import com.lge.d2x.domain.category.v1.model.PimCategoryResponseVO;
import com.lge.d2x.domain.category.v1.model.PlpStickyTabRequestVO;
import com.lge.d2x.domain.category.v1.model.PlpStickyTabResponseVO;
import com.lge.d2x.domain.category.v1.model.ProductListCategoryInfoRequestVO;
import com.lge.d2x.domain.category.v1.model.ProductListCategoryInfoResponseVO;
import com.lge.d2x.domain.category.v1.model.ProductListHRCategoryInfoRequestVO;
import com.lge.d2x.domain.category.v1.model.ProductListHRCategoryInfoResponseVO;
import com.lge.d2x.domain.category.v1.model.ReviewCategoryRequestVO;
import com.lge.d2x.domain.category.v1.model.ReviewCategoryResponseVO;
import com.lge.d2x.domain.category.v1.service.CategoryService;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.D2xCommonResponseVO;
import com.lge.d2xfrm.util.common.D2xResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/category/v1")
@Tag(name = "CategoryRestController", description = "카테고리 정보를 조회하는 컨트롤러이다")
public class CategoryRestController {
    private final CategoryService categoryService;

    @GetMapping("/category-info-list")
    @Operation(summary = "카테고리 정보", description = "카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<CategoryResponseVO>>> categoryInfoList(
            @Valid CategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(categoryService.selectCategoryInfoList(req));
    }

    @GetMapping("/plp-sticky-tab-list")
    @Operation(summary = "PLP sticky tab 리스트", description = "PLP sticky tab 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PlpStickyTabResponseVO>>> plpStickyTabList(
            @Valid PlpStickyTabRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(categoryService.selectPlpStickyTabList(req));
    }

    @GetMapping("/find-my-model-super-category-list")
    @Operation(
            summary = "서포트 사용이 가능한 슈퍼 카테고리 리스트",
            description = "서포트 사용이 가능한 슈퍼 카테고리 리스트 조회를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<CategoryResponseVO>>>
            findMyModelSuperCategoryList(@Valid CategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                categoryService.selectFindMyModelSuperCategoryList(req));
    }

    @GetMapping("/mts-category-info-list")
    @Operation(summary = "멀티샵 카테고리 정보", description = "멀티샵 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<CategoryResponseVO>>> mtsCategoryInfoList(
            @Valid CategoryRequestVO req) {

        if ((ObjectUtils.isEmpty(req.getCategoryCode())
                        && ObjectUtils.isEmpty(req.getHighLvCategoryCode()))
                || ObjectUtils.isEmpty(req.getShopCode())) {
            throw new D2xBusinessException("Missing required parameters");
        }

        return D2xResponseUtil.createSuccessResponse(
                categoryService.selectMtsCategoryInfoList(req));
    }

    @GetMapping("/mts-plp-sticky-tab-list")
    @Operation(summary = "멀티샵 PLP sticky tab 리스트", description = "멀티샵 PLP sticky tab 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PlpStickyTabResponseVO>>> mtsPlpStickyTabList(
            @Valid PlpStickyTabRequestVO req) {

        if (ObjectUtils.isEmpty(req.getShopCode())) {
            throw new D2xBusinessException("Missing required parameters:shopCode");
        }

        return D2xResponseUtil.createSuccessResponse(
                categoryService.selectMtsPlpStickyTabList(req));
    }

    @GetMapping("/review-category-list")
    @Operation(summary = "Review 카테고리 리스트", description = "Review에서 사용하는 카테고리 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ReviewCategoryResponseVO>>>
            getReviewCategoryList(@Valid ReviewCategoryRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(categoryService.selectReviewCategoryList(req));
    }

    @GetMapping("/product-list-category-info")
    @Operation(summary = "Product List 카테고리 정보", description = "Product List 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<ProductListCategoryInfoResponseVO>>
            getProductListCategoryInfo(@Valid ProductListCategoryInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                categoryService.selectProductListCategoryInfo(req));
    }

    @GetMapping("/product-list-hr-category-info")
    @Operation(summary = "Product List HR 카테고리 정보", description = "Product List HR 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductListHRCategoryInfoResponseVO>>>
            getProductListHRCategoryInfo(@Valid ProductListHRCategoryInfoRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(
                categoryService.selectProductListHRCategoryInfo(req));
    }

    @GetMapping("/catalogue-category")
    @Operation(summary = "Catalogue 카테고리 정보", description = "Catalogue 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<CatalogueCategoryResponseVO>> getCatalogueCategory(
            @Valid CatalogueCategoryRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(categoryService.selectCatalogueCategory(req));
    }

    @GetMapping("/pim-category")
    @Operation(summary = "PIM 카테고리 정보", description = "PIM 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PimCategoryResponseVO>> getPimCategory(
            @Valid PimCategoryRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(categoryService.selectPimCategory(req));
    }

    @GetMapping("/lv2-category-code")
    @Operation(summary = "Lv2 카테고리 코드", description = "Lv2 카테고리 코드를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<String>> getLv2CategoryCode(
            @Valid CategoryRequestVO req) {
        return D2xResponseUtil.createSuccessResponse(categoryService.selectLv2CategoryCode(req));
    }
}
