package com.lge.d2x.domain.category.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CategoryRequestVO {
    @Schema(description = "카테고리 코드", example = "CT00008333")
    private String categoryCode;

    @Schema(description = "상위 레벨 카테고리 코드", example = "CT00008332")
    private String highLvCategoryCode;

    @Schema(description = "샵코드", example = "de_students")
    private String shopCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "사이트 코드", example = "UK")
    private String siteCode;

    @Schema(description = "비즈니스 유형 코드", example = "B2C")
    private String bizTypeCode;

    @Schema(description = "서포트 여부", example = "Y")
    private String supportFlag;

    @Schema(description = "카테고리 레벨", example = "1")
    private int categoryLvNo;

    @Schema(description = "정렬 여부", example = "Y")
    private String orderFlag;

    @Schema(description = "상위 카테고리 리스트")
    private String[] highLvCategoryList;

    @Schema(description = "카테고리 리스트")
    private String[] categoryList;
}
