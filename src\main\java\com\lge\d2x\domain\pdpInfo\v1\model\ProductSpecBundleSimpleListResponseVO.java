package com.lge.d2x.domain.pdpInfo.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductSpecBundleSimpleListResponseVO {
    @Schema(description = "제품 SKU Id")
    private String lgcomSkuId;

    @Schema(description = "제품 PIM SKU Id")
    private String pimSkuId;

    @Schema(description = "번들 SKU Id")
    private String bundleSkuId;

    @Schema(description = "번들 PIM SKU Id")
    private String bundlePimSkuId;

    @Schema(description = "제품 PDP Id")
    private String pdpId;

    @Schema(description = "번들 PDP Id")
    private String bundlePdpId;

    @Schema(description = "번들 모델명")
    private String bundleModelName;

    @Schema(description = "Large Sige Image Url")
    private String bigImgUrl;

    @Schema(description = "Medium Size Image Url")
    private String mdmImgUrl;

    @Schema(description = "Small Size Image Url")
    private String smlImgUrl;

    @Schema(description = "이미지 대체 문구")
    private String imgAltTextCnts;

    @Schema(description = "사용자 친화 제품 명")
    private String userFrndyProductNm;

    @Schema(description = "PDP URL")
    private String pdpUrl;

    @Schema(description = "WTB 사용 여부")
    private String wtbUseFlag;

    @Schema(description = "WTB 외부 링크 사용 여부")
    private String wtbExtlLinkUseFlag;

    @Schema(description = "WTB 외부 링크 명")
    private String wtbExtlLinkNm;

    @Schema(description = "WTB 외부 링크 url")
    private String wtbExtlLinkUrl;

    @Schema(description = "WTB 외부 링크 새창 여부")
    private String wtbExtlLinkSelfScreenFlag;

    @Schema(description = "제품 상태 코드")
    private String productStateCode;

    @Schema(description = "카테고리 명")
    private String siteCategoryNm;

    @Schema(description = "OBS 판매 여부")
    private String obsSellFlag;

    @Schema(description = "참여자 건수")
    private String pCount;

    @Schema(description = "별평점 퍼센트")
    private String ratingPercent;

    @Schema(description = "별평점값")
    private String sRating;

    @Schema(description = "별평점값2")
    private String sRating2;
}
