<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.category.v1.repository.pdsmgr.CategoryRepository">
	<select id="selectCategoryInfoList" parameterType="com.lge.d2x.domain.category.v1.model.CategoryRequestVO" resultType="com.lge.d2x.domain.category.v1.model.CategoryResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectCategoryInfo */
		       A.CATEGORY_CODE AS categoryCode
		     , A.BIZ_TYPE_CODE AS bizTypeCode
		     , A.SITE_CODE AS siteCode
		     , A.CATEGORY_NM AS categoryNm
		     , A.SITE_CATEGORY_NM AS siteCategoryNm
		     , A.CATEGORY_LV_NO AS categoryLvNo
		     , A.DSP_SEQ AS dspSeq
		     , A.HIGH_LV_CATEGORY_CODE AS highLvCategoryCode
		     , A.SUPP_USE_FLAG AS suppUseFlag
		     , A.CATEGORY_IMG_URL AS categoryImgUrl
		     , A.CATEGORY_MOBL_IMG_URL AS categoryMoblImgUrl
		     , A.REPAIR_USE_FLAG AS repairUseFlag
		     , A.CS_DSP_SEQ AS csDspSeq
		     , B.SHOP_CODE AS shopCode
		     , B.CATEGORY_PAGE_URL AS categoryPageUrl
		     , B.STICKY_USE_FLAG AS stickyUseFlag
		     , B.STICKY_IMG_URL AS stickyImgUrl
		     , B.STICKY_DSP_SEQ AS stickyDspSeq
		     , B.STICKY_HOVER_ICON_URL AS stickyHoverIconUrl
		  FROM DSP_DISPLAY_CATEGORY_M A
		 INNER JOIN DSP_DISPLAY_CATEGORY_D B
		    ON B.SITE_CODE = A.SITE_CODE
		   AND B.CATEGORY_CODE = A.CATEGORY_CODE
		   AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		   AND B.USE_FLAG = 'Y'
		   AND B.SHOP_CODE = 'D2C'
		<choose>
		    <when test='supportFlag == "Y"'>
		 WHERE A.SUPP_USE_FLAG = 'Y'
		    </when>
		    <otherwise>
		 WHERE A.USE_FLAG = 'Y'
		    </otherwise>
		</choose>
		   AND A.SITE_CODE = #{siteCode}
		<if test="bizTypeCode != null and bizTypeCode != ''">
		   AND A.BIZ_TYPE_CODE = #{bizTypeCode}
		</if>
		<if test="categoryCode != null and categoryCode != ''">
		   AND A.CATEGORY_CODE = #{categoryCode}
		</if>
		<if test="highLvCategoryCode != null and highLvCategoryCode != ''">
		   AND A.HIGH_LV_CATEGORY_CODE = #{highLvCategoryCode}
		</if>
		<if test="categoryLvNo != null and categoryLvNo > 0">
		   AND A.CATEGORY_LV_NO = #{categoryLvNo}
		</if>
		<if test="highLvCategoryList != null">
		    <foreach item="categoryCode" index="indexs" collection="highLvCategoryList" open="AND A.HIGH_LV_CATEGORY_CODE IN (" close=")" separator=",">
		       #{categoryCode}
		    </foreach>
		</if>
		<if test="categoryList != null">
		    <foreach item="categoryCode" index="indexs" collection="categoryList" open="AND A.CATEGORY_CODE IN (" close=")" separator=",">
		       #{categoryCode}
		    </foreach>
		</if>
		<if test='supportFlag == "Y"'>
		 ORDER BY A.CS_DSP_SEQ
		</if>
		<if test='orderFlag == "Y"'>
		 ORDER BY A.DSP_SEQ ASC
		</if>
	</select>

    <select id="selectPlpStickyTabList" parameterType="com.lge.d2x.domain.category.v1.model.PlpStickyTabRequestVO" resultType="com.lge.d2x.domain.category.v1.model.PlpStickyTabResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectPlpStickyTabList */
		     C.CATEGORY_CODE
		   , C.CATEGORY_NM
		   , C.CATEGORY_LV_NO
           , D.STICKY_USE_FLAG AS categoryStickyUseFlag
		   , A.CATEGORY_CODE AS subCategoryCode
		   , A.SITE_CATEGORY_NM AS subCategoryNm
		   , E.CATEGORY_PAGE_URL AS subCategoryPageUrl
           , E.STICKY_USE_FLAG AS subCategoryStickyUseFlag
           , E.STICKY_IMG_URL AS subCategoryStickyImgUrl
           , E.STICKY_HOVER_ICON_URL AS subCategoryHoverIconUrl
		   , C.BIZ_TYPE_CODE
		   , C.SITE_CODE
		   , C.USE_FLAG AS cateUseFlag
		   , E.USE_FLAG AS subCateUseFlag
     	   , A.DSP_SEQ AS stickyOrderNo
		FROM DSP_DISPLAY_CATEGORY_M AS A
       INNER JOIN (
                      SELECT
                             CASE
						          WHEN B.CATEGORY_LV_NO = '1' THEN B.CATEGORY_CODE
						          WHEN B.CATEGORY_LV_NO = '2' THEN B.CATEGORY_CODE
						          WHEN B.CATEGORY_LV_NO = '3' THEN B.HIGH_LV_CATEGORY_CODE
						      END AS CATEGORY_CODE
						   , B.SITE_CATEGORY_NM AS CATEGORY_NM
						   , B.CATEGORY_LV_NO
						   , B.BIZ_TYPE_CODE
						   , B.SITE_CODE
						   , B.USE_FLAG
        			    FROM DSP_DISPLAY_CATEGORY_M AS B
        			   WHERE B.BIZ_TYPE_CODE = #{bizTypeCode}
        		         AND B.CATEGORY_CODE = #{categoryCode}
        		         AND B.SITE_CODE = #{siteCode}
        		   ) AS C
          ON C.CATEGORY_CODE = A.HIGH_LV_CATEGORY_CODE
         AND C.SITE_CODE = A.SITE_CODE
         AND C.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
         AND C.USE_FLAG = A.USE_FLAG
       INNER JOIN DSP_DISPLAY_CATEGORY_D D
		  ON D.CATEGORY_CODE = C.CATEGORY_CODE
		 AND D.SITE_CODE = C.SITE_CODE
		 AND D.SHOP_CODE = 'D2C'
		 AND D.CATEGORY_PAGE_URL IS NOT NULL
	   INNER JOIN DSP_DISPLAY_CATEGORY_D E
		  ON E.CATEGORY_CODE = A.CATEGORY_CODE
		 AND E.SITE_CODE = A.SITE_CODE
		 AND E.SHOP_CODE = 'D2C'
		 AND E.CATEGORY_PAGE_URL IS NOT NULL
		 AND E.STICKY_USE_FLAG = 'Y'
	   ORDER BY A.DSP_SEQ
    </select>

    <select id="selectFindMyModelSuperCategoryList" parameterType="com.lge.d2x.domain.category.v1.model.CategoryRequestVO" resultType="com.lge.d2x.domain.category.v1.model.CategoryResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectFindMyModelSuperCategoryList */
		     A.CATEGORY_CODE	AS	categoryCode
		   , A.SITE_CATEGORY_NM	AS	categoryNm
		   , A.HIGH_LV_CATEGORY_CODE	AS	highLvCategoryCode
		FROM DSP_DISPLAY_CATEGORY_M A
	   WHERE A.SITE_CODE = #{siteCode}
	     AND A.CATEGORY_LV_NO = 1
	     AND A.SUPP_USE_FLAG = 'Y'
	   <if test="bizTypeCode != null and bizTypeCode != ''">
		 AND A.BIZ_TYPE_CODE = #{bizTypeCode}
	   </if>
	   ORDER BY A.DSP_SEQ
    </select>

    <select id="selectMtsCategoryInfoList" parameterType="com.lge.d2x.domain.category.v1.model.CategoryRequestVO" resultType="com.lge.d2x.domain.category.v1.model.CategoryResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectMtsCategoryInfo */
		     A.CATEGORY_CODE	AS	categoryCode
		   , A.BIZ_TYPE_CODE	AS	bizTypeCode
		   , A.SITE_CODE	AS	siteCode
		   , A.CATEGORY_NM	AS	categoryNm
		   , A.SITE_CATEGORY_NM	AS	siteCategoryNm
		   , A.CATEGORY_LV_NO	AS	categoryLvNo
		   , A.DSP_SEQ	AS	dspSeq
		   , A.HIGH_LV_CATEGORY_CODE	AS	highLvCategoryCode
		   , A.SUPP_USE_FLAG	AS	suppUseFlag
		   , A.CATEGORY_IMG_URL	AS	categoryImgUrl
		   , A.CATEGORY_MOBL_IMG_URL	AS	categoryMoblImgUrl
		   , A.REPAIR_USE_FLAG	AS	repairUseFlag
		   , A.CS_DSP_SEQ	AS	csDspSeq
		   , B.SHOP_CODE	AS	shopCode
		   , B.CATEGORY_PAGE_URL	AS	categoryPageUrl
		   , B.STICKY_USE_FLAG	AS	stickyUseFlag
		   , B.STICKY_IMG_URL	AS	stickyImgUrl
		   , B.STICKY_DSP_SEQ	AS	stickyDspSeq
		   , B.STICKY_HOVER_ICON_URL	AS	stickyHoverIconUrl
		FROM DSP_DISPLAY_CATEGORY_M A
	   INNER JOIN DSP_DISPLAY_CATEGORY_D B
		  ON B.SITE_CODE = A.SITE_CODE
		 AND B.CATEGORY_CODE = A.CATEGORY_CODE
		 AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		 AND B.USE_FLAG = 'Y'
		 AND B.SHOP_CODE = #{shopCode}
	   WHERE A.SITE_CODE = #{siteCode}
	   <if test="bizTypeCode != null and bizTypeCode != ''">
		 AND A.BIZ_TYPE_CODE = #{bizTypeCode}
	   </if>
	   <if test="categoryCode != null and categoryCode != ''">
		 AND A.CATEGORY_CODE = #{categoryCode}
	   </if>
	   <if test="highLvCategoryCode != null and highLvCategoryCode != ''">
		 AND A.HIGH_LV_CATEGORY_CODE = #{highLvCategoryCode}
	   </if>
	   <if test="categoryLvNo != null and categoryLvNo > 0">
		 AND A.CATEGORY_LV_NO = #{categoryLvNo}
	   </if>
    </select>

    <select id="selectMtsPlpStickyTabList" parameterType="com.lge.d2x.domain.category.v1.model.PlpStickyTabRequestVO" resultType="com.lge.d2x.domain.category.v1.model.PlpStickyTabResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectMtsPlpStickyTabList */
		     C.CATEGORY_CODE
		   , C.CATEGORY_NM
		   , C.CATEGORY_LV_NO
           , D.STICKY_USE_FLAG AS categoryStickyUseFlag
		   , A.CATEGORY_CODE AS subCategoryCode
		   , A.SITE_CATEGORY_NM AS subCategoryNm
		   , E.CATEGORY_PAGE_URL AS subCategoryPageUrl
           , E.STICKY_USE_FLAG AS subCategoryStickyUseFlag
           , E.STICKY_IMG_URL AS subCategoryStickyImgUrl
           , E.STICKY_HOVER_ICON_URL AS subCategoryHoverIconUrl
		   , C.BIZ_TYPE_CODE
		   , C.SITE_CODE
		   , C.USE_FLAG AS cateUseFlag
		   , E.USE_FLAG AS subCateUseFlag
     	   , A.DSP_SEQ AS stickyOrderNo
		FROM DSP_DISPLAY_CATEGORY_M AS A
       INNER JOIN (
                      SELECT
                             CASE
						          WHEN B.CATEGORY_LV_NO = '1' THEN B.CATEGORY_CODE
						          WHEN B.CATEGORY_LV_NO = '2' THEN B.CATEGORY_CODE
						          WHEN B.CATEGORY_LV_NO = '3' THEN B.HIGH_LV_CATEGORY_CODE
						      END AS CATEGORY_CODE
						   , B.SITE_CATEGORY_NM AS CATEGORY_NM
						   , B.CATEGORY_LV_NO
						   , B.BIZ_TYPE_CODE
						   , B.SITE_CODE
						   , B.USE_FLAG
        			    FROM DSP_DISPLAY_CATEGORY_M AS B
        			   WHERE B.BIZ_TYPE_CODE = #{bizTypeCode}
        		         AND B.CATEGORY_CODE = #{categoryCode}
        		         AND B.SITE_CODE = #{siteCode}
        		   ) AS C
          ON C.CATEGORY_CODE = A.HIGH_LV_CATEGORY_CODE
         AND C.SITE_CODE = A.SITE_CODE
         AND C.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
         AND C.USE_FLAG = A.USE_FLAG
       INNER JOIN DSP_DISPLAY_CATEGORY_D D
		  ON D.CATEGORY_CODE = C.CATEGORY_CODE
		 AND D.SITE_CODE = C.SITE_CODE
		 AND D.SHOP_CODE = #{shopCode}
		 AND D.CATEGORY_PAGE_URL IS NOT NULL
	   INNER JOIN DSP_DISPLAY_CATEGORY_D E
		  ON E.CATEGORY_CODE = A.CATEGORY_CODE
		 AND E.SITE_CODE = A.SITE_CODE
		 AND E.SHOP_CODE = #{shopCode}
		 AND E.CATEGORY_PAGE_URL IS NOT NULL
		 AND E.STICKY_USE_FLAG = 'Y'
	   ORDER BY A.DSP_SEQ
    </select>

	<select id="selectReviewCategoryList" parameterType="com.lge.d2x.domain.category.v1.model.ReviewCategoryRequestVO" resultType="com.lge.d2x.domain.category.v1.model.ReviewCategoryResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectReviewCategoryList */
			   B.CATEGORY_NM
		     , B.SITE_CATEGORY_NM
		     , B.CATEGORY_CODE
		     , A.CATEGORY_DESC
		     , A.CATEGORY_SUB_TITLE
		     , A.IMG_FILE_PATH
		     , A.IMG_ALT_TEXT_CNTS
		     , D.CATEGORY_PAGE_URL
		  FROM DSP_REVIEW_CATEGORY_D A
		 INNER JOIN DSP_DISPLAY_CATEGORY_M B
		    ON A.CATEGORY_CODE = B.CATEGORY_CODE
		   AND A.USE_FLAG = B.USE_FLAG
		   AND A.SITE_CODE = B.SITE_CODE
		 INNER JOIN DSP_REVIEW_PRODUCT_R C
		    ON A.CATEGORY_CODE = C.CATEGORY_CODE
		   AND A.USE_FLAG = C.USE_FLAG
		   AND A.SITE_CODE = B.SITE_CODE
		 INNER JOIN DSP_DISPLAY_CATEGORY_D D
		    ON B.CATEGORY_CODE = D.CATEGORY_CODE
		   AND B.USE_FLAG = D.USE_FLAG
		   AND B.SITE_CODE = D.SITE_CODE
		 WHERE A.USE_FLAG = 'Y'
		   AND A.SITE_CODE = #{siteCode}
		   AND A.BU1_CLS_NM = #{buCode}
		   AND C.BU1_CLS_NM = #{buCode}
		   AND A.REVIEW_PAGE_ID = CONCAT(#{siteCode},'X9999999')
		 GROUP BY B.CATEGORY_NM, B.CATEGORY_CODE, A.IMG_FILE_PATH, A.IMG_ALT_TEXT_CNTS
	</select>

	<select id="selectProductListCategoryInfo" parameterType="com.lge.d2x.domain.category.v1.model.ProductListCategoryInfoRequestVO" resultType="com.lge.d2x.domain.category.v1.model.ProductListCategoryInfoResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectProductListCategoryInfo */
	           CASE WHEN D.CATEGORY_LV_NO = 1 THEN D.CATEGORY_CODE
              		WHEN D.CATEGORY_LV_NO = 2 THEN A.CATEGORY_CODE
              		WHEN D.CATEGORY_LV_NO = 3 THEN A.HIGH_LV_CATEGORY_CODE
              	END SUPER_CATEGORY_CODE
             , CASE WHEN D.CATEGORY_LV_NO = 2 THEN D.CATEGORY_CODE
              		WHEN D.CATEGORY_LV_NO = 3 THEN A.CATEGORY_CODE
                END CATEGORY_CODE
             , CASE WHEN D.CATEGORY_LV_NO = 3 THEN D.CATEGORY_CODE
                END SUB_CATEGORY_CODE
	         , A.BIZ_TYPE_CODE
	      FROM DSP_DISPLAY_CATEGORY_M A
	     INNER JOIN (SELECT B.CATEGORY_CODE
	                      , B.CATEGORY_LV_NO
	                      , B.HIGH_LV_CATEGORY_CODE
	                      , B.SITE_CODE
	                   FROM DSP_DISPLAY_CATEGORY_M B
	                  INNER JOIN DSP_DISPLAY_CATEGORY_D C
                         ON C.SITE_CODE = B.SITE_CODE
                        AND C.CATEGORY_CODE = B.CATEGORY_CODE
                        AND C.USE_FLAG = 'Y'
                        AND C.SHOP_CODE = #{shopCode}
	                  WHERE B.SITE_CODE = #{siteCode}
	                    AND B.BIZ_TYPE_CODE = #{bizTypeCode}
	                    AND B.CATEGORY_CODE = #{categoryCode}
	                   <if test='shopCode == "D2C"'>
	                    AND B.USE_FLAG = 'Y'
	                   </if>
	                  ) D
	        ON A.CATEGORY_CODE = D.HIGH_LV_CATEGORY_CODE
           AND A.SITE_CODE = D.SITE_CODE
         INNER JOIN DSP_DISPLAY_CATEGORY_D E
           ON E.SITE_CODE = A.SITE_CODE
          AND E.CATEGORY_CODE = A.CATEGORY_CODE
          AND E.USE_FLAG = 'Y'
          AND E.SHOP_CODE = #{shopCode}
    </select>

    <select id="selectProductListHRCategoryInfo" parameterType="com.lge.d2x.domain.category.v1.model.ProductListHRCategoryInfoRequestVO" resultType="com.lge.d2x.domain.category.v1.model.ProductListHRCategoryInfoResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectProductListHRCategoryInfo */
	           CASE WHEN D.CATEGORY_LV_NO = 1 THEN D.CATEGORY_CODE
              		WHEN D.CATEGORY_LV_NO = 2 THEN A.CATEGORY_CODE
              		WHEN D.CATEGORY_LV_NO = 3 THEN A.HIGH_LV_CATEGORY_CODE
              	END SUPER_CATEGORY_CODE
             , CASE WHEN D.CATEGORY_LV_NO = 2 THEN D.CATEGORY_CODE
              		WHEN D.CATEGORY_LV_NO = 3 THEN A.CATEGORY_CODE
              	END CATEGORY_CODE
             , CASE WHEN D.CATEGORY_LV_NO = 3 THEN D.CATEGORY_CODE
                END SUB_CATEGORY_CODE
             , A.BIZ_TYPE_CODE
             , D.DSP_SEQ
             , F.CONFIG_VAL AS hrLimit
	      FROM DSP_DISPLAY_CATEGORY_M A
	     INNER JOIN (SELECT B.CATEGORY_CODE
	                      , CONCAT('HR_', B.CATEGORY_CODE)   AS HR_CATEGORY_CODE
	                      , B.CATEGORY_LV_NO
	                      , B.HIGH_LV_CATEGORY_CODE
	                      , B.SITE_CODE
	                      , B.DSP_SEQ
	                   FROM DSP_DISPLAY_CATEGORY_M B
	                  INNER JOIN DSP_DISPLAY_CATEGORY_D C
                         ON C.SITE_CODE = B.SITE_CODE
                        AND C.CATEGORY_CODE = B.CATEGORY_CODE
                        AND C.USE_FLAG = 'Y'
                        AND C.SHOP_CODE = #{shopCode}
	                  WHERE B.SITE_CODE = #{siteCode}
	                    AND B.BIZ_TYPE_CODE = #{bizTypeCode}
	                   <if test='shopCode == "D2C"'>
	                    AND B.USE_FLAG = 'Y'
	                   </if>
	                  ) D
	        ON A.CATEGORY_CODE = D.HIGH_LV_CATEGORY_CODE
           AND A.SITE_CODE = D.SITE_CODE
         INNER JOIN DSP_DISPLAY_CATEGORY_D E
       		ON E.SITE_CODE = A.SITE_CODE
           AND E.CATEGORY_CODE = A.CATEGORY_CODE
           AND E.USE_FLAG = 'Y'
           AND E.SHOP_CODE = #{shopCode}
    	 INNER JOIN COM_SYS_CONF_D F
            ON F.CONFIG_CODE   = HR_CATEGORY_CODE
           AND F.SITE_CODE = D.SITE_CODE
           AND F.USE_FLAG = 'Y'
         ORDER BY D.DSP_SEQ
    </select>

	<select id="selectCatalogueCategory" parameterType="com.lge.d2x.domain.category.v1.model.CatalogueCategoryRequestVO"
	resultType="com.lge.d2x.domain.category.v1.model.CatalogueCategoryResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectCatalogueCategory */
		       A.CATEGORY_CODE
		     , CONVERT(A.CATEGORY_LV_NO, CHAR) AS GNB_LEVEL
		     , CASE WHEN A.CATEGORY_LV_NO = IF(#{categoryStandardFlag} = 'Y', '4', '3')
		            THEN A.HIGH_LV_CATEGORY_CODE
		            ELSE ''
		        END CATEGORY_LEVEL2
		  FROM DSP_DISPLAY_CATEGORY_M A
		  JOIN DSP_DISPLAY_CATEGORY_D B
		    ON A.SITE_CODE = B.SITE_CODE
		   AND A.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
		   AND A.CATEGORY_CODE = B.CATEGORY_CODE
		   AND B.USE_FLAG = 'Y'
		   AND (B.CATEGORY_PAGE_URL = #{categoryPageUrl} OR B.CATEGORY_CODE = #{categoryCode})
		 WHERE A.SITE_CODE = #{siteCode}
		   AND A.BIZ_TYPE_CODE = 'B2B'
		   AND A.USE_FLAG = 'Y'
		 LIMIT 1
	</select>

	<select id="selectPimCategory" parameterType="com.lge.d2x.domain.category.v1.model.PimCategoryRequestVO"
	resultType="com.lge.d2x.domain.category.v1.model.PimCategoryResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectPimCategory */
		       B.PDP_ID
		<choose>
		    <when test='siteCode == "TW"'>
		     , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE PPCR.LV1_CATEGORY_CODE = CATEGORY_CODE AND BIZ_TYPE_CODE = #{bizTypeCode} LIMIT 1), '') AS CLASSIFICATION_FLAG_LV1
		     , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE PPCR.LV2_CATEGORY_CODE = CATEGORY_CODE AND BIZ_TYPE_CODE = #{bizTypeCode} LIMIT 1), '') AS CLASSIFICATION_FLAG_LV2
		     , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE PPCR.LV3_CATEGORY_CODE = CATEGORY_CODE AND BIZ_TYPE_CODE = #{bizTypeCode} LIMIT 1), '') AS CLASSIFICATION_FLAG_LV3
		     , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE PPCR.LV4_CATEGORY_CODE = CATEGORY_CODE AND BIZ_TYPE_CODE = #{bizTypeCode} LIMIT 1), '') AS CLASSIFICATION_FLAG_LV4
		  FROM PDM_PRODUCT_CATEGORY_COUNTRY_R A
		    </when>
		    <otherwise>
		     , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE A.LV1_CATEGORY_CODE = CATEGORY_CODE AND BIZ_TYPE_CODE = #{bizTypeCode} LIMIT 1), '') AS CLASSIFICATION_FLAG_LV1
		     , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE A.LV2_CATEGORY_CODE = CATEGORY_CODE AND BIZ_TYPE_CODE = #{bizTypeCode} LIMIT 1), '') AS CLASSIFICATION_FLAG_LV2
		     , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE A.LV3_CATEGORY_CODE = CATEGORY_CODE AND BIZ_TYPE_CODE = #{bizTypeCode} LIMIT 1), '') AS CLASSIFICATION_FLAG_LV3
		     , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE A.LV4_CATEGORY_CODE = CATEGORY_CODE AND BIZ_TYPE_CODE = #{bizTypeCode} LIMIT 1), '') AS CLASSIFICATION_FLAG_LV4
		  FROM PDM_PRODUCT_CATEGORY_R A
		    </otherwise>
		</choose>
		  JOIN DSP_PDP_M B
		    ON A.SKU_ID = B.SKU_ID
		   AND B.CATEGORY_CODE = #{categoryCode}
		   AND B.SITE_CODE = #{siteCode}
		   AND B.BIZ_TYPE_CODE = #{bizTypeCode}
		   AND B.USE_FLAG = 'Y'
		  JOIN DSP_PDP_D C
		    ON B.PDP_ID = C.PDP_ID
		   AND B.SITE_CODE = C.SITE_CODE
		   AND B.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		   AND C.SHOP_CODE = 'D2C'
		   AND C.PRODUCT_STATE_CODE = 'ACTIVE'
		   AND C.USE_FLAG = 'Y'
		   AND C.AEM_PUBL_FLAG = 'Y'
		 LIMIT 1
	</select>

    <select id="selectLv2CategoryCode" parameterType="com.lge.d2x.domain.category.v1.model.CategoryRequestVO" resultType="String">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.category.v1.service.CategoryService.selectPimCategory */
               CATEGORY_CODE
          FROM DSP_DISPLAY_CATEGORY_M
         WHERE CATEGORY_LV_NO = #{categoryLvNo}
           AND (
                 CATEGORY_CODE = #{categoryCode}
                 OR CATEGORY_CODE = (SELECT HIGH_LV_CATEGORY_CODE FROM DSP_DISPLAY_CATEGORY_M WHERE CATEGORY_CODE = #{categoryCode})
               )
    </select>
</mapper>