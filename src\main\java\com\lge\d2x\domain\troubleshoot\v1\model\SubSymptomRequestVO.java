package com.lge.d2x.domain.troubleshoot.v1.model;

import com.lge.d2xfrm.constants.CommonCodes;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SubSymptomRequestVO {
    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "siteCode")
    private String siteCode;

    @NotBlank(message = CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE)
    @Schema(description = "symptom")
    private String symptom;

    @Schema(description = "subSymptom")
    private String subSymptom;

    @Schema(description = "topicListFlag")
    private String topicListFlag;

    @Schema(description = "categoryId")
    private String categoryId;

    @Schema(description = "subCategoryId")
    private String subCategoryId;

    @Schema(description = "localeCode")
    private String localeCode;
}
