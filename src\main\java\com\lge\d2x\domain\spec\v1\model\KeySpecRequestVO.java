package com.lge.d2x.domain.spec.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class KeySpecRequestVO {
    @NotBlank(message = "Missing required parameters")
    @Schema(description = "SKU아이디", example = "ZRUN060LSS0.EWGBLEU.EEUK.B")
    private String skuId;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "로케일코드", example = "en_GB")
    private String localeCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "사이트코드", example = "UK")
    private String siteCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "PDP아이디", example = "MD07581854")
    private String pdpId;

    @Schema(description = "3레벨제품코드사용안함여부", example = "Y")
    private String lv3ProductCodeNotUseFlag;
}
