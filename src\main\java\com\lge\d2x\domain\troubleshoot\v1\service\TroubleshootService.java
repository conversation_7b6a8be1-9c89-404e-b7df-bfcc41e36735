package com.lge.d2x.domain.troubleshoot.v1.service;

import com.lge.d2x.domain.troubleshoot.v1.model.CategoryNameRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.CategoryRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.CategoryResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptomResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SuperCategoryRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SuperCategoryResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomRequestVO;
import com.lge.d2x.domain.troubleshoot.v1.model.SymptomResponseVO;
import com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class TroubleshootService {
    private final TroubleshootRepository troubleshootRepository;

    @Transactional(readOnly = true)
    public List<HelpfulArticlesResponseVO> getHelpfulArticlesList(
            HelpfulArticlesRequestVO requestVO) {
        return troubleshootRepository.selectHelpfulArticlesList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<HelpfulArticlesResponseVO> getHelpfulArticlesList3Depth(
            HelpfulArticlesRequestVO requestVO) {
        return troubleshootRepository.selectHelpfulArticlesList3Depth(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SuperCategoryResponseVO> getGpSuperCategory(SuperCategoryRequestVO requestVO) {
        return troubleshootRepository.selectGpSuperCategory(requestVO);
    }

    @Transactional(readOnly = true)
    public List<CategoryResponseVO> getGpCategory(CategoryRequestVO requestVO) {
        return troubleshootRepository.selectGpCategory(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SymptomResponseVO> getSymptom(SymptomRequestVO requestVO) {
        return troubleshootRepository.selectSymptom(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SymptomResponseVO> getSymptomT1Depth(SymptomRequestVO requestVO) {
        return troubleshootRepository.selectSymptomT1Depth(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SubSymptomResponseVO> getSubsymptom(SubSymptomRequestVO requestVO) {
        return troubleshootRepository.selectSubsymptom(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SubSymptomResponseVO> getSubSymptomT2DepthList(SubSymptomRequestVO requestVO) {
        return troubleshootRepository.selectSubsymptomT2DepthList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<DetailSymptomResponseVO> getSubsymptom3DepthTtype(SubSymptomRequestVO requestVO) {
        return troubleshootRepository.selectSubsymptom3DepthTtype(requestVO);
    }

    @Transactional(readOnly = true)
    public String getCategoryName(CategoryNameRequestVO requestVO) {
        return troubleshootRepository.selectCategoryName(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SubSymptCwTypeResponseVO> getSubsymptomCWtypeList(
            SubSymptCwTypeRequestVO requestVO) {
        return troubleshootRepository.selectSubsymptomCWtypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SubSymptCwTypeResponseVO> getSubSymptomCw2DepthTypeList(
            SubSymptCwTypeRequestVO requestVO) {
        return troubleshootRepository.selectSubSymptomCW2DepthTypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<DetailSymptCwTypeResponseVO> getSubsymptomCW3DepthTypeList(
            SubSymptCwTypeRequestVO requestVO) {
        return troubleshootRepository.selectSubsymptomCW3DepthTypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SymptCwTypeResponseVO> getSymptomCWtypeList(SymptCwTypeRequestVO requestVO) {
        return troubleshootRepository.selectSymptomCWtypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SymptCwTypeResponseVO> getSymptomCW1DepthTypeList(SymptCwTypeRequestVO requestVO) {
        return troubleshootRepository.selectSymptomCW1DepthTypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SymptomRTypeResponseVO> getSymptomRtypeList(SymptomRTypeRequestVO requestVO) {
        return troubleshootRepository.selectSymptomRtypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SymptomRTypeResponseVO> getSymptomR1DepthTypeList(SymptomRTypeRequestVO requestVO) {
        return troubleshootRepository.selectSymptomR1DepthTypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SubSymptRTypeResponseVO> getSubSymptomRtypeList(SubSymptRTypeRequestVO requestVO) {
        return troubleshootRepository.selectSubSymptomRtypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<SubSymptRTypeResponseVO> getSubSymptomR2DepthTypeList(
            SubSymptRTypeRequestVO requestVO) {
        return troubleshootRepository.selectSubSymptomR2DepthTypeList(requestVO);
    }

    @Transactional(readOnly = true)
    public List<DetailSymptRTypeResponseVO> getSubSymptomR3DepthTypeList(
            SubSymptRTypeRequestVO requestVO) {
        return troubleshootRepository.selectSubSymptomR3DepthTypeList(requestVO);
    }
}
