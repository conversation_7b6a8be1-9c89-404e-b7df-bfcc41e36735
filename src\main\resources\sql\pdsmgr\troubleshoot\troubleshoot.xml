<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository">
    <select id="selectHelpfulArticlesList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectHelpfulArticlesList */
             <if test='repairFlag == "Y"'>
			   DISTINCT
			 </if>
			   A.GSCS_ITEM_ID
		     , A.GSCS_ITEM_TITLE_NM
		     , A.LV2_CATEGORY_CODE AS CATEGORY_ID
		  FROM SVD_GSCS_CONTENTS_D A
		 WHERE A.SITE_CODE = UPPER(#{localeCode})
		   AND A.USE_FLAG = 'Y'
		   AND A.LV2_CATEGORY_CODE = #{categoryId}
		   AND A.LV3_CATEGORY_CODE = #{subCateId}
		   AND A.SYMPTOM_CODE = #{issueTopic}
		   AND A.SUB_SYMPTOM_CODE = #{issueSubtopic}
		<if test='repairFlag == "Y"'>
		   AND NOT EXISTS (SELECT 'X'
							 FROM SVD_HELP_LIBRARY_CONTENT_D CSFD
							WHERE  1 = 1
							  AND CSFD.GSCS_ITEM_ID = A.GSCS_ITEM_ID
							  AND CSFD.USE_FLAG = 'Y'
							  AND CSFD.SITE_CODE = A.SITE_CODE
							  AND ( CSFD.ATT_FILE_NM REGEXP '.mp4'
								OR CSFD.ATT_FILE_NM REGEXP '.flv'
								OR CSFD.ATT_FILE_NM REGEXP '.wmv'
								OR CSFD.ATT_FILE_NM REGEXP '.swf' ))
		 ORDER BY A.LAST_UPDATE_DATE DESC
		 </if>
    </select>

    <select id="selectHelpfulArticlesList3Depth" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.HelpfulArticlesResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectHelpfulArticlesList3Depth */
             <if test='repairFlag == "Y"'>
			   DISTINCT
			 </if>
			   A.GSCS_ITEM_ID
		     , A.GSCS_ITEM_TITLE_NM
		     , A.LV2_CATEGORY_CODE AS CATEGORY_ID
		  FROM SVD_GSCS_CONTENTS_3DEPTH_D A
		 WHERE A.SITE_CODE = UPPER(#{localeCode})
		   AND A.USE_FLAG = 'Y'
		   AND A.LV2_CATEGORY_CODE = #{categoryId}
		   AND A.LV3_CATEGORY_CODE = #{subCateId}
		   AND A.SYMPTOM_CODE = #{issueTopic}
		   AND A.SUB_SYMPTOM_CODE = #{issueSubtopic}
		<if test='repairFlag == "Y"'>
		   AND NOT EXISTS (SELECT 'X'
							 FROM SVD_HELP_LIBRARY_CONTENT_D CSFD
							WHERE  1 = 1
							  AND CSFD.GSCS_ITEM_ID = A.GSCS_ITEM_ID
							  AND CSFD.USE_FLAG = 'Y'
							  AND CSFD.SITE_CODE = A.SITE_CODE
							  AND ( CSFD.ATT_FILE_NM REGEXP '.mp4'
								OR CSFD.ATT_FILE_NM REGEXP '.flv'
								OR CSFD.ATT_FILE_NM REGEXP '.wmv'
								OR CSFD.ATT_FILE_NM REGEXP '.swf' ))
		 ORDER BY A.LAST_UPDATE_DATE DESC
		 </if>
    </select>

	<select id="selectGpSuperCategory" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SuperCategoryRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SuperCategoryResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectGpSuperCategory */
               DISTINCT
			   B.LV1_CATEGORY_CODE AS CS_SUPER_CATEGORY_ID
			 , E.SITE_CATEGORY_NM AS CS_SUPER_CATEGORY_NAME
			 , B.LV1_CATEGORY_CODE AS CODE
			 , E.SITE_CATEGORY_NM AS VALUE
			 , F.STICKY_IMG_URL AS stickyImageAddr
			 , E.CS_DSP_SEQ AS csOrder
			 , F.STICKY_HOVER_ICON_URL AS iconPathHover
		  FROM PDM_PRODUCT_SVC_D A
		  JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B ON A.SVD_SKU_ID = B.SVD_SKU_ID AND B.SITE_CODE = #{siteCode}
		  JOIN SVD_PRODUCT_SVC_MODEL_D C ON A.SVD_SKU_ID = C.SVD_SKU_ID AND C.SITE_CODE = #{siteCode}
		  JOIN DSP_DISPLAY_CATEGORY_M E
	        ON  E.SITE_CODE = #{siteCode}
	       AND E.CATEGORY_CODE = B.LV1_CATEGORY_CODE
	       AND E.SUPP_USE_FLAG = 'Y'
	      JOIN DSP_DISPLAY_CATEGORY_D F
	        ON  F.SITE_CODE = #{siteCode}
	       AND F.CATEGORY_CODE = E.CATEGORY_CODE
	       AND F.BIZ_TYPE_CODE = E.BIZ_TYPE_CODE
	       AND F.SHOP_CODE = 'D2C'
	       AND F.USE_FLAG = 'Y'
	       AND F.BIZ_TYPE_CODE = 'B2C'
		 WHERE 1=1
		   AND A.USE_FLAG = 'Y'
		   AND B.USE_FLAG = 'Y'
		   AND C.USE_FLAG = 'Y'
		 ORDER BY E.CS_DSP_SEQ
    </select>

	<select id="selectGpCategory" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.CategoryRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.CategoryResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectGpCategory */
               DISTINCT
			   B.LV2_CATEGORY_CODE AS CS_CATEGORY_ID
			 , E.SITE_CATEGORY_NM AS CS_CATEGORY_NAME
			 , B.LV2_CATEGORY_CODE AS CODE
			 , E.SITE_CATEGORY_NM AS VALUE
			 , E.CS_DSP_SEQ AS CS_DISPLAY_ORDER_NO
		  FROM PDM_PRODUCT_SVC_D A
		  JOIN SVD_PRODUCT_SVC_MODEL_CATEGORY_R B 
		    ON A.SVD_SKU_ID = B.SVD_SKU_ID 
		   AND B.SITE_CODE = #{siteCode}
		<if test="superCategoryId != null and superCategoryId != ''">
		   AND B.LV1_CATEGORY_CODE = #{superCategoryId}
		</if>
		  JOIN SVD_PRODUCT_SVC_MODEL_D C
		    ON A.SVD_SKU_ID = C.SVD_SKU_ID
		   AND C.SITE_CODE = #{siteCode}
		  JOIN DSP_DISPLAY_CATEGORY_M E
	        ON  E.SITE_CODE = #{siteCode}
	       AND E.CATEGORY_CODE = B.LV2_CATEGORY_CODE
	       AND E.SUPP_USE_FLAG = 'Y'
	      JOIN DSP_DISPLAY_CATEGORY_D F
	        ON  F.SITE_CODE = #{siteCode}
	       AND F.CATEGORY_CODE = E.CATEGORY_CODE
	       AND F.BIZ_TYPE_CODE = E.BIZ_TYPE_CODE
	       AND F.SHOP_CODE = 'D2C'
	       AND F.USE_FLAG = 'Y'
	       AND F.BIZ_TYPE_CODE = 'B2C'
		 WHERE 1=1
		   AND A.USE_FLAG = 'Y'
		   AND B.USE_FLAG = 'Y'
		   AND C.USE_FLAG = 'Y'
		 ORDER BY E.CS_DSP_SEQ
    </select>

	<select id="selectSymptom" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SymptomRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SymptomResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSymptom */
               DISTINCT 	 
					TRIM(SYMPTOM_CODE) AS SYMP_ID
					,IF(TRIM(SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(SYMPTOM_NM)),TRIM(SYMPTOM_NM)) AS SYMP_NAME
					,IF(TRIM(SYMPTOM_NM) = 'Others', 9999, 1) AS SYMPTOM_DISPLAY_ORDER_NO
					,(SELECT CD.CONFIG_VAL
						FROM COM_SYS_CONF_D CD
						WHERE CD.SITE_CODE = #{siteCode}
						AND CD.CONFIG_CODE = 'symptom_depth_setup'
						AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
			FROM SVD_GSCS_CONTENTS_D A
			WHERE 1=1
			AND USE_FLAG = 'Y'
			AND SYMPTOM_CODE IS NOT NULL
			AND SYMPTOM_NM IS NOT NULL
			AND SITE_CODE = #{siteCode}
			AND LV2_CATEGORY_CODE = #{categoryId}
			<if test="subCategoryId != null and subCategoryId != ''">
			    <![CDATA[
			    	AND LV3_CATEGORY_CODE = #{subCategoryId}
	       		]]>
	       	</if>
			AND GSCS_CONTENT_DELIMITER_CODE IN ('VIDEO', 'HOWTO')
			ORDER BY SYMPTOM_DISPLAY_ORDER_NO ASC , TRIM(SYMPTOM_NM) ASC
    </select>

	<select id="selectSymptomT1Depth" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SymptomRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SymptomResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSymptomT1Depth */
               DISTINCT 	 
					TRIM(SYMPTOM_CODE) AS SYMP_ID
					,IF(TRIM(SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(SYMPTOM_NM)),TRIM(SYMPTOM_NM)) AS SYMP_NAME
					,IF(TRIM(SYMPTOM_NM) = 'Others', 9999, 1) AS SYMPTOM_DISPLAY_ORDER_NO
					,(SELECT CD.CONFIG_VAL
						FROM COM_SYS_CONF_D CD
						WHERE CD.SITE_CODE = #{siteCode}
						AND CD.CONFIG_CODE = 'symptom_depth_setup'
						AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
			FROM SVD_GSCS_CONTENTS_3DEPTH_D A
			WHERE 1=1
			AND USE_FLAG = 'Y'
			AND SYMPTOM_CODE IS NOT NULL
			AND SYMPTOM_NM IS NOT NULL
			AND SITE_CODE = #{siteCode}
			AND LV2_CATEGORY_CODE = #{categoryId}
			<if test="subCategoryId != null and subCategoryId != ''">
			    <![CDATA[
			    	AND LV3_CATEGORY_CODE = #{subCategoryId}
	       		]]>
	       	</if>
			AND GSCS_CONTENT_DELIMITER_CODE IN ('VIDEO', 'HOWTO')
			AND (USER_TYPE1_VAL LIKE '%C%' OR USER_TYPE1_VAL LIKE '%A%')
			AND USER_TYPE_NM LIKE '%Consumer%' 
			AND SYMPTOM_CODE NOT LIKE '7%'
			AND SYMPTOM_CODE NOT LIKE '8%'
			AND SYMPTOM_CODE NOT LIKE '9%'
			ORDER BY 
			CASE 
			  WHEN SORT1_VAL IS NOT NULL THEN 1
			  ELSE 2
			END,
			SORT1_VAL ASC,
			SYMPTOM_CODE ASC
    </select>

	<select id="selectSubsymptom" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSubsymptom */
               DISTINCT
			   ,TRIM(SUB_SYMPTOM_CODE) AS SYMP_SUB_ID
			   ,IF(TRIM(SUB_SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(SUB_SYMPTOM_NM)),TRIM(SUB_SYMPTOM_NM)) AS SYMP_SUB_NAME
			 , IF(SUB_SYMPTOM_NM = 'Others', 9999, 1) AS SYMPTOM_DISPLAY_ORDER_NO
			 ,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
		  FROM SVD_GSCS_CONTENTS_D A
		 WHERE 1=1
		   AND USE_FLAG = 'Y'
		   AND SUB_SYMPTOM_CODE IS NOT NULL
		   AND SUB_SYMPTOM_NM IS NOT NULL
	<if test='topicListFlag == "Y"'>
		   AND LV2_CATEGORY_CODE = #{categoryId}
		<if test="subCategoryId != null and subCategoryId != ''">
		   AND LV3_CATEGORY_CODE = #{subCategoryId}
	    </if>
	</if>
		   AND SITE_CODE = #{siteCode}
		   AND TRIM(SYMPTOM_NM) = TRIM(#{symptom})
		   AND GSCS_CONTENT_DELIMITER_CODE IN ('VIDEO', 'HOWTO')
		 ORDER BY SYMPTOM_DISPLAY_ORDER_NO ASC , TRIM(SUB_SYMPTOM_NM) ASC
    </select>

	<select id="selectSubsymptomT2DepthList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSubsymptomT2DepthList */
               DISTINCT 	 
					TRIM(MID_SYMPTOM_CODE) AS SYMP_SUB_ID
					,IF(TRIM(REPLACE(MID_SYMPTOM_NM,'\n','')) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(REPLACE(MID_SYMPTOM_NM,'\n',''))),TRIM(REPLACE(MID_SYMPTOM_NM,'\n',''))) AS SYMP_SUB_NAME
					,IF(TRIM(MID_SYMPTOM_NM) = 'Others', 9999, 1) AS SYMPTOM_DISPLAY_ORDER_NO
					,(SELECT CD.CONFIG_VAL
						FROM COM_SYS_CONF_D CD
						WHERE CD.SITE_CODE = #{siteCode}
						AND CD.CONFIG_CODE = 'symptom_depth_setup'
						AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
			FROM SVD_GSCS_CONTENTS_3DEPTH_D A
			WHERE 1=1
			AND USE_FLAG = 'Y'
			AND MID_SYMPTOM_CODE IS NOT NULL
			AND MID_SYMPTOM_NM IS NOT NULL
			AND SITE_CODE = #{siteCode}
			AND LV2_CATEGORY_CODE = #{categoryId}
			<if test="subCategoryId != null and subCategoryId != ''">
			    <![CDATA[
			    	AND LV3_CATEGORY_CODE = #{subCategoryId}
	       		]]>
	       	</if>
			AND TRIM(SYMPTOM_NM) = TRIM(#{symptom})
			AND GSCS_CONTENT_DELIMITER_CODE IN ('VIDEO', 'HOWTO')
			AND (USER_TYPE2_VAL LIKE '%C%' OR USER_TYPE2_VAL LIKE '%A%')
			AND USER_TYPE_NM LIKE '%Consumer%' 
			ORDER BY 
			CASE 
			  WHEN SORT2_VAL IS NOT NULL THEN 1
			  ELSE 2
			END,
			SORT2_VAL ASC,
			MID_SYMPTOM_CODE ASC
    </select>

	<select id="selectSubsymptom3DepthTtype" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptomRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptomResponseVO">
        SELECT  /* com.lge.cst.api.troubleshoot.service.TroubleshootService.retrieveSubsymptom3DepthTtype */
                    DISTINCT     
                    TRIM(SUB_SYMPTOM_CODE) AS SYMP_DETAIL_ID
                    ,IF(TRIM(REPLACE(SUB_SYMPTOM_NM,'\n','')) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(REPLACE(SUB_SYMPTOM_NM,'\n',''))),TRIM(REPLACE(SUB_SYMPTOM_NM,'\n',''))) AS SYMP_DETAIL_NAME
                    ,IF(TRIM(SUB_SYMPTOM_NM) = 'Others', 9999, 1) AS SYMPTOM_DISPLAY_ORDER_NO
                    ,(SELECT D.CONFIG_VAL
                        FROM COM_SYS_CONF_D D
                        WHERE D.SITE_CODE = #{siteCode}
                        AND D.CONFIG_CODE = 'symptom_depth_setup'
                        AND D.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
            FROM SVD_GSCS_CONTENTS_3DEPTH_D A
            WHERE 1=1
            AND USE_FLAG = 'Y'
            AND SUB_SYMPTOM_CODE IS NOT NULL
            AND SUB_SYMPTOM_NM IS NOT NULL
            AND SITE_CODE = #{siteCode}
            AND LV2_CATEGORY_CODE = #{categoryId}
			<if test="subCategoryId != null and subCategoryId != ''">
			    <![CDATA[
			    	AND LV3_CATEGORY_CODE = #{subCategoryId}
	       		]]>
	       	</if>
            AND TRIM(MID_SYMPTOM_NM) = TRIM(#{subSymptom})
            AND GSCS_CONTENT_DELIMITER_CODE IN ('VIDEO', 'HOWTO')
            AND (USER_TYPE3_VAL LIKE '%C%' OR USER_TYPE3_VAL LIKE '%A%')
            AND USER_TYPE_NM LIKE '%Consumer%' 
            ORDER BY 
            CASE 
              WHEN SORT3_VAL IS NOT NULL THEN 1
              ELSE 2
            END,
            SORT3_VAL ASC,
            SUB_SYMPTOM_CODE ASC;
    </select>

	<select id="selectCategoryName" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.CategoryNameRequestVO" resultType="String">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectCategoryName */
			   DISTINCT A.SITE_CATEGORY_NM AS CS_CATEGORY_NAME
		  FROM DSP_DISPLAY_CATEGORY_M A
	     WHERE A.CATEGORY_CODE = #{categoryId}
	       AND A.SUPP_USE_FLAG = 'Y'
	</select>

	<select id="selectSubsymptomCWtypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSubsymptomCWtypeList */
			   D.SUB_SYMPTOM_CODE AS SYMP_SUB_ID
		   ,IF(TRIM(D.SUB_SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(D.SUB_SYMPTOM_NM)),TRIM(D.SUB_SYMPTOM_NM)) AS SYMP_SUB_NAME
	  	   ,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
	  	FROM  SVD_GSCS_SYMP_D D
		WHERE D.CORP_CODE      = #{corporationCode}
		AND D.LANG_CODE        = #{languageCode}
		AND D.USE_FLAG         = 'Y'
		AND D.SYMPTOM_CODE     = #{symptom}
		AND D.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
	    AND D.CONSUMER_FLAG    = 'Y'
	    AND D.DSP_FLAG		   = 'Y'
	    <if test="mode == 'getName'">
		AND D.SUB_SYMPTOM_CODE = #{issueSubtopic}
		</if>
	</select>

	<select id="selectSubSymptomCW2DepthTypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSubSymptomCW2DepthTypeList */
			   D.MID_SYMPTOM_CODE AS SYMP_SUB_ID
           ,IF(TRIM(REPLACE(MID_SYMPTOM_NM,'\n','')) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(REPLACE(MID_SYMPTOM_NM,'\n',''))),TRIM(REPLACE(MID_SYMPTOM_NM,'\n',''))) AS SYMP_SUB_NAME
           ,(SELECT CD.CONFIG_VAL
                FROM COM_SYS_CONF_D CD
                WHERE CD.SITE_CODE = 'UK'
                AND CD.CONFIG_CODE = 'symptom_depth_setup'
                AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
        FROM  SVD_SYMP_2DEPTH_D D
        WHERE D.CORP_CODE  = #{corporationCode}
        AND D.LANGUAGE_CODE     = #{languageCode}
        AND D.USE_FLAG          = 'Y'
        AND D.SYMPTOM_CODE         = #{symptom}
        AND D.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
        AND IFNULL(D.CONSUMER_FLAG,'Y')     = 'Y'
        AND D.DSP_FLAG      = 'Y'
        <if test="mode == 'getName'">
            AND D.SUB_SYMPTOM_CODE         = #{issueSubtopic}
        </if>   
        AND (D.USER_TYPE_VAL LIKE '%C%' OR D.USER_TYPE_VAL LIKE '%A%')
        ORDER BY 
        CASE 
          WHEN D.ATTR1_VAL IS NOT NULL THEN 1
          ELSE 2
        END,
        D.MID_SYMPTOM_CODE ASC
	</select>

	<select id="selectSubsymptomCW3DepthTypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptCwTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptCwTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSubsymptomCW3DepthTypeList */
			D.SUB_SYMPTOM_CODE AS SYMP_DETAIL_ID
		   ,IF(TRIM(D.SUB_SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(D.SUB_SYMPTOM_NM)),TRIM(D.SUB_SYMPTOM_NM)) AS SYMP_DETAIL_NAME
		   ,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
	  	FROM  SVD_SYMP_3DEPTH_D D
		WHERE D.CORP_CODE      = #{corporationCode}
		AND D.LANGUAGE_CODE    = #{languageCode}
		AND D.USE_FLAG         = 'Y'
        AND D.SYMPTOM_CODE     = #{symptom}
		AND D.MID_SYMPTOM_CODE = #{subSymptom}
		AND D.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
	    AND IFNULL(D.CONSUMER_FLAG,'Y') = 'Y'
	    AND D.DSP_FLAG		= 'Y'
	    <if test="mode == 'getName'">
			AND D.SUB_SYMPTOM_CODE = #{issueSubtopic}
		</if>	
		AND (D.USER_TYPE_VAL LIKE '%C%' OR D.USER_TYPE_VAL LIKE '%A%')
		ORDER BY 
		CASE 
		  WHEN D.ATTR1_VAL IS NOT NULL THEN 1
		  ELSE 2
		END,
		D.SUB_SYMPTOM_CODE ASC
	</select>

	<select id="selectSymptomCWtypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSymptomCWtypeList */
			   SYMPTOM_CODE AS SYMP_ID
			  ,IF(TRIM(SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(SYMPTOM_NM)),TRIM(SYMPTOM_NM)) AS SYMPTOM_NM
			  ,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
		FROM SVD_GSCS_SYMP_M A
		WHERE A.CORP_CODE  = #{corporationCode}
		AND A.LANGUAGE_CODE     = #{languageCode}
		AND A.USE_FLAG          = 'Y'
		AND A.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
		AND A.CONSUMER_FLAG     = 'Y'
		
		<if test='topicType eq "C"'>
		AND A.DSP_FLAG		= 'Y'
		</if>
		ORDER BY SYMPTOM_NM
	</select>

	<select id="selectSymptomCW1DepthTypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SymptCwTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSymptomCW1DepthTypeList */
			   SYMPTOM_CODE AS SYMP_ID
			  ,IF(TRIM(SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(SYMPTOM_NM)),TRIM(SYMPTOM_NM)) AS SYMP_NAME
			  ,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
		FROM SVD_SYMP_1DEPTH_D A
		WHERE A.CORP_CODE  = #{corporationCode}
		AND A.LANGUAGE_CODE     = #{languageCode}
		AND A.USE_FLAG          = 'Y'
		AND A.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
		AND IFNULL(A.CONSUMER_FLAG,'Y')     = 'Y'
		AND (A.USER_TYPE_VAL LIKE '%C%' OR A.USER_TYPE_VAL LIKE '%A%')
		AND A.SYMPTOM_CODE NOT LIKE '7%'
		AND A.SYMPTOM_CODE NOT LIKE '8%'
		AND A.SYMPTOM_CODE NOT LIKE '9%'
		<if test='topicType eq "C"'>
		AND A.DSP_FLAG		= 'Y'
		</if>
		ORDER BY 
		CASE 
		  WHEN A.ATTR1_VAL IS NOT NULL THEN 1
		  ELSE 2
		END,
		A.SYMPTOM_CODE ASC
	</select>

	<select id="selectSymptomRtypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSymptomRtypeList */
			   M.SYMPTOM_CODE AS SYMP_ID
		    ,IF(TRIM(M.SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(M.SYMPTOM_NM)),TRIM(M.SYMPTOM_NM)) AS SYMPTOM_NM
		    ,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
		FROM   SVD_GSCS_SYMP_M M
		WHERE  M.CORP_CODE = #{corporationCode}
		AND    M.LANGUAGE_CODE    = #{languageCode}
		AND    M.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
		AND    M.USE_FLAG         = 'Y'
		AND    M.CONSUMER_FLAG = 'Y'
		AND    M.DSP_FLAG = 'Y'
		<if test='installationPageExist == "Y"'>
			<if test='installationPageFlag == "Y"'>
				AND M.SYMPTOM_CODE IN 
			</if>
			<if test='installationPageFlag != "Y"'>
				AND M.SYMPTOM_CODE NOT IN 
			</if>
						   (SELECT distinct TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(A.DATA, ',', B.RN), ',', -1)) AS symp_code
							  FROM (SELECT CASE WHEN CONVERT((LENGTH(A.DATA) - LENGTH(REPLACE(A.DATA, ',', ''))) / LENGTH(','), UNSIGNED) > 0 THEN
							                         CONVERT((LENGTH(A.DATA) - LENGTH(REPLACE(A.DATA, ',', ''))) / LENGTH(',') + 1, UNSIGNED)
							                    ELSE 0
							                END AS DELIMITER_CNT
							             , A.DATA
							          FROM (SELECT A.MSG_CNTS as DATA
									          FROM COM_MESSAGE_M A
											 WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC'
											   AND A.MSG_CODE = 'request-installation-symp-code'
											   AND A.LOCALE_CODE = #{localeCode}
											   AND A.USE_FLAG = 'Y'
											 limit 1) A
							       ) A
							  JOIN (SELECT CAST(seq AS INT) as RN 
							          FROM seq_1_to_1000
							       ) B 
							    ON B.RN <![CDATA[ <= ]]> A.DELIMITER_CNT
					       )
		</if>
	</select>

	<select id="selectSymptomR1DepthTypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SymptomRTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSymptomR1DepthTypeList */
			   M.SYMPTOM_CODE AS SYMP_ID
		    ,IF(TRIM(M.SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(M.SYMPTOM_NM)),TRIM(M.SYMPTOM_NM)) AS SYMP_NAME
			,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
		FROM   SVD_SYMP_1DEPTH_D M
		WHERE  M.CORP_CODE = #{corporationCode}
		AND    M.LANGUAGE_CODE    = #{languageCode}
		AND    M.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
		AND    M.USE_FLAG         = 'Y'
		AND    IFNULL(M.CONSUMER_FLAG,'Y')     = 'Y'
		AND    M.DSP_FLAG = 'Y'
		AND    (M.USER_TYPE_VAL LIKE '%C%' OR M.USER_TYPE_VAL LIKE '%A%') 
		AND    M.SYMPTOM_CODE NOT LIKE '7%'
		AND    M.SYMPTOM_CODE NOT LIKE '8%'
		AND    M.SYMPTOM_CODE NOT LIKE '9%'
		<if test='installationPageExist == "Y"'>
			<if test='installationPageFlag == "Y"'>
				AND M.SYMPTOM_CODE IN 
			</if>
			<if test='installationPageFlag != "Y"'>
				AND M.SYMPTOM_CODE NOT IN 
			</if>
						   (SELECT distinct TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(A.DATA, ',', B.RN), ',', -1)) AS symp_code
							  FROM (SELECT CASE WHEN CONVERT((LENGTH(A.DATA) - LENGTH(REPLACE(A.DATA, ',', ''))) / LENGTH(','), UNSIGNED) > 0 THEN
							                         CONVERT((LENGTH(A.DATA) - LENGTH(REPLACE(A.DATA, ',', ''))) / LENGTH(',') + 1, UNSIGNED)
							                    ELSE 0
							                END AS DELIMITER_CNT
							             , A.DATA
							          FROM (SELECT A.MSG_CNTS as DATA
									          FROM COM_MESSAGE_M A
											 WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC'
											   AND A.MSG_CODE = 'request-installation-symp-code'
											   AND A.LOCALE_CODE = #{localeCode}
											   AND A.USE_FLAG = 'Y'
											 limit 1) A
							       ) A
							  JOIN (SELECT CAST(seq AS INT) as RN 
							          FROM seq_1_to_1000
							       ) B 
							    ON B.RN <![CDATA[ <= ]]> A.DELIMITER_CNT
					       )
		</if>
		ORDER BY 
		CASE 
		  WHEN M.ATTR1_VAL IS NOT NULL THEN 1
		  ELSE 2
		END,
		M.SYMPTOM_CODE ASC
	</select>

	<select id="selectSubSymptomRtypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSubSymptomRtypeList */
			   D.SUB_SYMPTOM_CODE AS SYMP_SUB_ID
		    ,IF(TRIM(D.SUB_SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(D.SUB_SYMPTOM_NM)),TRIM(D.SUB_SYMPTOM_NM)) AS SYMP_SUB_NAME
		    ,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
		FROM   SVD_GSCS_SYMP_D D
		WHERE 1=1
		AND    D.CORP_CODE        = #{corporationCode}
		AND    D.LANG_CODE        = #{languageCode}
		AND    D.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
		AND    D.SYMPTOM_CODE     = #{symptom}
		AND    D.USE_FLAG         = 'Y'
		AND    D.CONSUMER_FLAG    = 'Y'
	</select>

	<select id="selectSubSymptomR2DepthTypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSubSymptomR2DepthTypeList */
			 D.MID_SYMPTOM_CODE AS SYMP_SUB_ID
		     ,IF(TRIM(D.MID_SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(D.MID_SYMPTOM_NM)),TRIM(D.MID_SYMPTOM_NM)) AS SYMP_SUB_NAME
			,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
		FROM   SVD_SYMP_2DEPTH_D D
		WHERE  D.CORP_CODE = #{corporationCode}
		AND    D.LANGUAGE_CODE    = #{languageCode}
		AND    D.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
		AND    D.SYMPTOM_CODE         = #{symptom}
		AND    D.USE_FLAG         = 'Y'
		AND    IFNULL(D.CONSUMER_FLAG,'Y')     = 'Y'
		AND    D.DSP_FLAG = 'Y'
		AND    (D.USER_TYPE_VAL LIKE '%C%' OR D.USER_TYPE_VAL LIKE '%A%')
		ORDER BY 
		CASE 
		  WHEN D.ATTR1_VAL IS NOT NULL THEN 1
		  ELSE 2
		END,
		D.MID_SYMPTOM_CODE ASC
	</select>

	<select id="selectSubSymptomR3DepthTypeList" parameterType="com.lge.d2x.domain.troubleshoot.v1.model.SubSymptRTypeRequestVO" resultType="com.lge.d2x.domain.troubleshoot.v1.model.DetailSymptRTypeResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.troubleshoot.v1.repository.pdsmgr.TroubleshootRepository.selectSubSymptomR3DepthTypeList */
			 D.SUB_SYMPTOM_CODE AS SYMP_DETAIL_CODE
            ,IF(TRIM(D.SUB_SYMPTOM_NM) = 'Others',IFNULL((SELECT A.MSG_CNTS FROM COM_MESSAGE_M A WHERE A.DSP_SVC_MSG_SP_CODE = 'SVC' AND A.MSG_CODE = 'common-symptom-others' AND A.LOCALE_CODE = #{localeCode} AND A.USE_FLAG = 'Y'),TRIM(D.SUB_SYMPTOM_NM)),TRIM(D.SUB_SYMPTOM_NM)) AS SYMP_DETAIL_NAME
            ,(SELECT CD.CONFIG_VAL
				FROM COM_SYS_CONF_D CD
				WHERE CD.SITE_CODE = #{siteCode}
				AND CD.CONFIG_CODE = 'symptom_depth_setup'
				AND CD.USE_FLAG = 'Y')  AS SYMPTOM_DEPTH_SETUP
        FROM   SVD_SYMP_3DEPTH_D D
        WHERE 1=1
        AND    D.CORP_CODE  = #{corporationCode}
        AND    D.LANGUAGE_CODE     = #{languageCode}
        AND    D.MDMS_PRDGRP_CODE = #{csMdmsProductCode}
		AND    D.SYMPTOM_CODE         = #{symptom}
        AND    D.MID_SYMPTOM_CODE         = #{subSymptom}
        AND    D.USE_FLAG          = 'Y'
        AND    IFNULL(D.CONSUMER_FLAG,'Y')     = 'Y'
        AND    (D.USER_TYPE_VAL LIKE '%C%' OR D.USER_TYPE_VAL LIKE '%A%')
        ORDER BY 
        CASE 
          WHEN D.ATTR1_VAL IS NOT NULL THEN 1
          ELSE 2
        END,
        D.SUB_SYMPTOM_CODE ASC
	</select>
</mapper>