package com.lge.d2x.domain.pdpInfo.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductSpecBundleSimpleListRequestVO {
    @NotBlank(message = "Missing required parameters")
    @Schema(description = "사이트 코드", example = "UK")
    private String siteCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "pdp id", example = "**********")
    private String pdpId;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "표준화 여부", example = "Y")
    private String standardFlag;

    @Schema(description = "Business 유형 코드", example = "B2C")
    private String bizTypeCode;

    @Schema(description = "샵 코드", example = "de_students")
    private String shopCode;

    @Schema(description = "리뷰유형", example = "LGCOM")
    private String reviewType;
}
