<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.shopTime.v1.repository.pdsmgr.ShopTimeRepository">
	<select id="selectProductInfo" parameterType="com.lge.d2x.domain.shopTime.v1.model.ShopTimeProductInfoRequestVO" resultType="com.lge.d2x.domain.shopTime.v1.model.ShopTimeProductInfoResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.shopTime.v1.service.ShopTimeService.selectProductInfo */
		       B.PDP_ID
		     , B.LGCOM_SKU_ID AS sku
		     , B.PRODUCT_NM AS mpn
		     , NVL(MT.SEO_PAGE_TITLE, (SELECT NVL(REPLACE(REPLACE(CMM.MSG_CNTS, '{0}', IFNULL(NULLIF(C.NEW_MKT_PRODUCT_NM, ''), B.USER_FRNDY_PRODUCT_NM)), '{1}', B.PRODUCT_NM), '')
		                                 FROM COM_MESSAGE_M CMM
		                                WHERE 1=1
		                                  AND CMM.LOCALE_CODE = #{localeCode}
		                                  AND CMM.MSG_CODE = CONCAT('SEO_PAGE_TITLE'))) AS title
		<if test='categoryStandardFlag eq "Y"'>
		     , IFNULL(E.LV2_CATEGORY_CODE, '') AS super_category_id
		     , IFNULL(E.LV3_CATEGORY_CODE, '') AS category_id
		     , IFNULL(E.LV4_CATEGORY_CODE, '') AS sub_category_id
		</if>
		<if test='categoryStandardFlag eq "N"'>
		     , IFNULL(E.LV1_CATEGORY_ID, '') AS super_category_id
		     , IFNULL(E.LV2_CATEGORY_ID, '') AS category_id
		     , IFNULL(E.LV3_CATEGORY_ID, '') AS sub_category_id
		</if>
		     , 'LG' AS brand
		     , IFNULL(CONCAT(#{serverDomainName}, #{serverImageDomainName}, A.SML_IMG_URL), '') AS small_image_addr
		     , IFNULL(CONCAT(#{serverDomainName}, #{serverImageDomainName}, A.MDM_IMG_URL), '') AS medium_image_addr
		     , IFNULL(CONCAT(#{serverDomainName}, #{serverImageDomainName}, A.BIG_IMG_URL), '') AS large_image_addr
		     , NVL(MT.SEO_DESC, (SELECT NVL(REPLACE(REPLACE(CMM.MSG_CNTS, '{0}', IFNULL(NULLIF(C.NEW_MKT_PRODUCT_NM, ''), B.USER_FRNDY_PRODUCT_NM)), '{1}', B.PRODUCT_NM), '')
		                           FROM COM_MESSAGE_M CMM
		                          WHERE 1=1
		                            AND CMM.LOCALE_CODE = #{localeCode}
		                            AND CMM.MSG_CODE = CONCAT('SEO_PAGE_DESCRIPTION'))) AS description
		     , IFNULL(G.STRARRATING_VAL, '') AS review_rating
		     , CONCAT(#{serverDomainName}, A.PDP_URL, 'buy/') AS product_buy_link
		     , 'N' AS discontinued_flag
		     , CASE WHEN (SELECT COUNT(*)
		                    FROM DSP_SIBLING_M SM
		                   INNER JOIN DSP_SIBLING_D SV
		                      ON SV.USE_FLAG = 'Y'
		                     AND SV.SITE_CODE = SM.SITE_CODE
		                     AND SV.BIZ_TYPE_CODE = SM.BIZ_TYPE_CODE
		                     AND SV.SIBLING_TYPE_CODE = SM.SIBLING_TYPE_CODE
		                     AND SV.SIBLING_CODE = SM.SIBLING_CODE
		                   WHERE B.PDP_ID = SV.PDP_ID
		                     AND SM.USE_FLAG = 'Y') <![CDATA[>]]> 0 THEN 'Y'
		            ELSE 'N'
		             END AS group_product_flag
		     , IFNULL(C.CREATION_DATE, NULL) AS publish_date
		     , IFNULL(B.LAST_UPDATE_DATE, NULL) AS change_date
		     , J.INSTALLMENT_MONTHCNT
		     , J.INSTALLMENT_PRICE
		     , H.PRODUCT_PRICE
		     , H.ORIGINALPRICE
		     , IF(I.STOCK_STATE_CODE = 'IN_STOCK', 'N' , 'Y') AS sold_out_flag
		     , JSON_ARRAYAGG (JSON_OBJECT ('shipin_type_code', K.SHIPIN_TYPE_CODE , 'shipin_price', K.SHIPIN_PRICE)) AS shipping_info
		     , IFNULL(L.energy_label_name, '') AS energy_efficiency_class
		     , IFNULL(C.LV2_PRODUCT_CODE, IFNULL(C.LV1_PRODUCT_CODE, '')) AS product_level_code
		  FROM DSP_PDP_D A -- MCM
		 INNER JOIN DSP_PDP_M B -- MM
		    ON B.PDP_ID = A.PDP_ID
		   AND B.SITE_CODE = A.SITE_CODE
		   AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
		   AND A.SHOP_CODE = 'D2C'
		 INNER JOIN (SELECT PPM.SKU_ID
		                  , PPM.BIZ_TYPE_CODE
		                  , PPM.SALES_MODEL_CODE
		                  , PPM.SALES_MODEL_SUFFIX_CODE
		                  , PPM.MODEL_EAN_CODE
		                  , PPM.LV1_PRODUCT_CODE
		                  , PPM.LV2_PRODUCT_CODE
		                  , PPM.LV3_PRODUCT_CODE
		                  , PPM.LV4_PRODUCT_CODE
		                  , PPD.NEW_MKT_PRODUCT_NM
		                  , PPCR.LV1_CATEGORY_CODE
		                  , PPCR.LV2_CATEGORY_CODE
		                  , PPCR.LV3_CATEGORY_CODE
		                  , PPCR.LV4_CATEGORY_CODE
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE PPCR.LV1_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV1_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE PPCR.LV2_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV2_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE PPCR.LV3_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV3_CATEGORY_NM
		                  , IFNULL((SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE PPCR.LV4_CATEGORY_CODE = CATEGORY_CODE AND PPM.BIZ_TYPE_CODE = BIZ_TYPE_CODE LIMIT 1), '') AS LV4_CATEGORY_NM
		                  , PPM.CREATION_DATE
		                  , PPM.LAST_UPDATE_DATE
		                  , PPM.USE_FLAG
		               FROM PDM_PRODUCT_M PPM
		              INNER JOIN PDM_PRODUCT_CATEGORY_R PPCR
		                 ON PPM.SKU_ID = PPCR.SKU_ID
		                AND PPM.USE_FLAG = PPCR.USE_FLAG
		                AND PPCR.LOCALE_CODE = #{localeCode}
		               LEFT JOIN PDM_PRODUCT_D PPD
		                 ON PPM.SKU_ID = PPD.SKU_ID
		                AND PPM.BIZ_TYPE_CODE = PPD.BIZ_TYPE_CODE
		                AND PPM.USE_FLAG = PPD.USE_FLAG
		                AND PPCR.LOCALE_CODE = PPD.LOCALE_CODE) C
		    ON B.SKU_ID = C.SKU_ID
		   AND A.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
		 INNER JOIN ECM_ATTRIBUTE_M D
		    ON B.LGCOM_SKU_ID = CONCAT(SUBSTRING_INDEX(D.SKU_ID , '.', LENGTH(D.SKU_ID) - LENGTH(REPLACE(D.SKU_ID, '.', ''))), '.', D.STORE_CODE, '.C')
		   AND D.STORE_CODE = B.SITE_CODE
		 INNER JOIN ECM_PRICE_D H
		    ON D.SKU_ID = H.SKU_ID 
		   AND D.STORE_CODE = H.STORE_CODE
		   AND H.CUST_GRP_CODE = 'NOT_LOGGED_IN'
		 INNER JOIN ECM_STOCK_D I
		    ON D.SKU_ID = I.SKU_ID 
		   AND D.STORE_CODE = I.STORE_CODE
		<if test='categoryStandardFlag eq "Y"'>
		 INNER JOIN DSP_PDP_CATEGORY_R E
		    ON A.PDP_ID = E.PDP_ID
		   AND A.SITE_CODE = E.SITE_CODE
		   AND A.BIZ_TYPE_CODE = E.BIZ_TYPE_CODE
		</if>
		<if test='categoryStandardFlag eq "N"'>
		 INNER JOIN DSP_OLD_PDP_CATEGORY_R E
		    ON A.PDP_ID = E.PDP_ID
		   AND A.SITE_CODE = E.SITE_CODE
		   AND A.BIZ_TYPE_CODE = E.BIZ_TYPE_CODE
		</if>
		 INNER JOIN (SELECT M.SITE_CODE
		                  , M.PDP_URL
		                  , M.SEO_DESC
		                  , M.LAST_UPDATE_DATE
		                  , M.SEO_PAGE_TITLE
		               FROM DSP_PRODUCT_SEO_INFO_M M
		              WHERE 1=1
		                AND M.SITE_CODE = #{siteCode}) MT
		    ON MT.SITE_CODE = A.SITE_CODE
		   AND MT.PDP_URL = A.PDP_URL
		  LEFT JOIN DSP_USER_REVIEW_RATING_D G
		    ON A.PDP_ID = G.PDP_ID
		  LEFT JOIN ECM_ATTRIBUTE_D J
		    ON D.SKU_ID = J.SKU_ID
		   AND D.STORE_CODE = J.STORE_CODE
		  LEFT JOIN ECM_SHIPIN_INFO_D K
		    ON D.SKU_ID = K.SKU_ID
		   AND D.STORE_CODE = K.STORE_CODE
		  LEFT JOIN (SELECT DISTINCT ETM.ELABEL_TYPE_CODE
		                  , ETM.ELABEL_GRD_CODE
		                  , ETM.BIZ_TYPE_CODE
		                  , ELC.CODE_VAL_NM AS energy_label_name
		               FROM DSP_ENERGYLABEL_TYPE_M ETM
		              INNER JOIN (SELECT CCCD.COMMON_CODE_VAL
		                               , CCCD.CODE_VAL_NM
		                            FROM COM_COMMON_CODE_M CCCM
		                            JOIN COM_COMMON_CODE_D CCCD
		                              ON CCCM.COMMON_CODE = CCCD.COMMON_CODE
		                           WHERE CCCM.COMMON_CODE = 'Energy_Label_Code'
		                             AND CCCM.USE_FLAG = 'Y') ELC
		                 ON ETM.ELABEL_GRD_CODE = ELC.COMMON_CODE_VAL
		              INNER JOIN PDM_PRODUCT_EL_PIS_R MMEPM
		                 ON ETM.ELABEL_TYPE_CODE = MMEPM.EL_TYPE_CODE
		              WHERE ETM.USE_FLAG = 'Y'
		                AND ETM.SITE_CODE = #{siteCode}) L
		    ON B.BIZ_TYPE_CODE = L.BIZ_TYPE_CODE
		   AND B.ELABEL_GRD_CODE = L.ELABEL_GRD_CODE
		 WHERE A.SITE_CODE = #{siteCode}
		   AND A.BIZ_TYPE_CODE = #{bizTypeCode}
		   AND A.USE_FLAG = 'Y'
		   AND B.USE_FLAG = 'Y'
		   AND C.USE_FLAG = 'Y'
		   AND E.USE_FLAG = 'Y'
		   AND E.DEFAULT_MAP_FLAG = 'Y'
		   AND A.AEM_PUBL_FLAG = 'Y'
		   AND IFNULL(B.SKU_ID, '') <![CDATA[<>]]> ''
		   AND IFNULL(A.BIG_IMG_URL, '') <![CDATA[<>]]> ''
		   AND A.PRODUCT_STATE_CODE = 'ACTIVE'
		   AND IFNULL(I.STOCK_STATE_CODE, '') <![CDATA[<>]]> ''
		 GROUP BY B.LGCOM_SKU_ID, B.SITE_CODE
	</select>

	<select id="selectProductImageList" parameterType="com.lge.d2x.domain.shopTime.v1.model.ProductImageRequestVO" resultType="com.lge.d2x.domain.shopTime.v1.model.ProductImageResponseVO">
		SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.shopTime.v1.service.ShopTimeService.selectProductImageList */
		       PDP_ID
		     , CASE WHEN SML_IMG_URL LIKE '/content%'
		            THEN CONCAT(#{serverDomainName}, SML_IMG_URL)
		            ELSE CONCAT(#{serverDomainName}, #{serverImageDomainName}, SML_IMG_URL)
		       END AS smallGalleryImageAddr
		     , CASE WHEN BIG_IMG_URL LIKE '/content%'
		            THEN CONCAT(#{serverDomainName}, BIG_IMG_URL)
		            ELSE CONCAT(#{serverDomainName}, #{serverImageDomainName}, BIG_IMG_URL)
		       END AS largeGalleryImageAddr
		  FROM DSP_PRODUCT_IMAGE_D
		 WHERE SITE_CODE = #{siteCode}
		   AND SHOP_CODE = 'D2C'
		<foreach item="item" collection="pdpIdList" open="AND PDP_ID IN (" close=")" separator=",">
		       #{item}
		</foreach>
	</select>
</mapper>