package com.lge.d2x.domain.whereToBuy.v1.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.lge.d2x.core.constants.CommonConstants;
import com.lge.d2x.core.util.Util;
import com.lge.d2x.domain.product.v1.model.ProductSummaryRequestVO;
import com.lge.d2x.domain.product.v1.service.PdpInfoService;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbBrandShopResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbCityRequestVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbCityResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbCnComboRequestVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbCnDistributorRequestVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbCnDistributorResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbCnDistributorsInfoResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbComboResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbDistributorRequestVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbDistributorResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbInitRequestVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbInitResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbOnlineDistributorRequestVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbRetailerCnResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbRetailerResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbSettingResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbStoreCategoryResponseVO;
import com.lge.d2x.domain.whereToBuy.v1.model.WtbTrComboRequestVO;
import com.lge.d2x.interfaces.system.admin.support.client.SystemAdminSupportClient;
import com.lge.d2x.interfaces.system.admin.whereToBuy.client.SystemAdminWhereToBuyClient;
import com.lge.d2x.interfaces.system.admin.whereToBuy.model.SystemAdminCnBrandshopCityGetRequestVO;
import com.lge.d2x.interfaces.system.admin.whereToBuy.model.SystemAdminCnBrandshopCityGetResponseVO;
import com.lge.d2x.interfaces.system.admin.whereToBuy.model.SystemAdminOpenHourGetRequestVO;
import com.lge.d2x.interfaces.system.admin.whereToBuy.model.SystemAdminRetailerCnGetRequestVO;
import com.lge.d2x.interfaces.system.admin.whereToBuy.model.SystemAdminRetailerCnGetResponseVO;
import com.lge.d2x.interfaces.system.admin.whereToBuy.model.SystemAdminRetailerGetRequestVO;
import com.lge.d2x.interfaces.system.admin.whereToBuy.model.SystemAdminRetailerGetResponseVO;
import com.lge.d2x.interfaces.system.admin.whereToBuy.model.SystemAdminRetailerImageGetRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.client.SystemPdsPdpInfoClient;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListResponseVO;
import com.lge.d2x.interfaces.system.pds.support.client.SystemPdsSupportClient;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsKmProductRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsKmProductResponseVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.client.SystemPdsWhereToBuyClient;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbCategoryIdRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbCategoryIdResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbCategoryListGetRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbCategoryListGetResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbCityGetRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbCityGetResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbCnDistributorGetRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbCnDistributorGetResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbComboGetRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbComboGetResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbDistributorRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbDistributorResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbOfflineRetailerGetRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbOfflineRetailerGetResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbSettingRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbSettingResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbStateGetRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbStateGetResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbStoreCategoryListRequestVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbStoreCategoryResponseVO;
import com.lge.d2x.interfaces.system.pds.whereToBuy.model.SystemPdsWtbStoreFilterResponseVO;
import com.lge.d2xfrm.constants.CommonCodes;
import com.lge.d2xfrm.constants.enums.JobSeperateCodeEnum;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.CachedCodeRequestVO;
import com.lge.d2xfrm.model.common.CachedCommonCodeRequestVO;
import com.lge.d2xfrm.model.common.CachedPdpIdRequestVO;
import com.lge.d2xfrm.model.common.CachedPdpIdVO;
import com.lge.d2xfrm.model.paging.PageInfoAEMRequestVO;
import com.lge.d2xfrm.model.paging.PageInfoResultVO;
import com.lge.d2xfrm.service.email.EmailService;
import com.lge.d2xfrm.util.common.CachedDataUtil;
import com.lge.d2xfrm.util.common.DateUtil;
import com.lge.d2xfrm.util.common.RequestHeaderUtil;
import com.lge.d2xfrm.util.page.PagingUtil;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WhereToBuyService {
    private final SystemAdminWhereToBuyClient systemAdminWhereToBuyClient;
    private final SystemPdsWhereToBuyClient systemPdsWhereToBuyClient;
    private final SystemPdsPdpInfoClient systemPdsPdpInfoClient;
    private final SystemAdminSupportClient systemAdminSupportClient;
    private final SystemPdsSupportClient systemPdsSupportClient;
    private final PdpInfoService pdpInfoService;
    private final CachedDataUtil cachedDataUtil;
    private final EmailService emailService;
    private final Util util;

    @Value("${application.globalPlatform.serverInfo.url}")
    private String serverUrl;

    public List<WtbDistributorResponseVO> getWtbDistributorList(WtbDistributorRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final String LAT = requestVO.getLat();
        final String LNG = requestVO.getLng();

        List<WtbDistributorResponseVO> wtbDistributorList =
                new ArrayList<WtbDistributorResponseVO>();

        try {
            String PDP_ID = "";
            String BIZ_TYPE_CODE = "";

            if (StringUtils.isNotBlank(SKU)) {
                List<CachedPdpIdVO> pdpList =
                        cachedDataUtil.getPdpIdList(
                                CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
                if (ObjectUtils.isNotEmpty(pdpList)) {
                    PDP_ID = pdpList.get(0).getPdpId();
                    BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";
                }
            }

            String BUSINESS_REVIEW_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("BUSINESS_REVIEW_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_TYPE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder().keyCode("REVIEW_TYPE").build()),
                            "");

            String PROMOTION_TIME_ZONE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("promotionTimeZone")
                                            .build()),
                            CommonConstants.NULL);

            String BRANDSHOP_IMPROVING_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("BRANDSHOP_IMPROVING_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String MULTI_BRANDSHOP_IMPROVING_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("MULTI_BRANDSHOP_IMPROVING_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("category_standard_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String MKT_WTB_ORDERBY_DISTANCE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("mkt-where-to-buy-orderby-distance")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String MKT_WTB_DISTRIBUTOR_CATEGORY_USE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("mkt-wtb-distributor-category-use")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String WTB_TITLE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("wtb_title_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);
            // WTB_TITLE_FLAG = "Y";

            String GERP_DISTRIBUTOR_STOCK_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("gerp_distributor_stock_use_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String RADIUS =
                    StringUtils.defaultIfBlank(
                            requestVO.getRadius(),
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode(
                                                            SITE_CODE.toLowerCase()
                                                                    + ".findTheDealer.radius")
                                                    .build()),
                                    "50"));

            List<String> list = new ArrayList<>();
            if (ObjectUtils.isNotEmpty(requestVO.getCategoryIdList())) {
                list = Arrays.asList(requestVO.getCategoryIdList().toString().split(","));
            }

            /** Where to Buy Store 정보 조회 */
            Map<String, Object> radiusData = getRadiusData(LAT, LNG, RADIUS);
            SystemPdsWtbSettingResponseVO SystemPdsWtbSetting =
                    systemPdsWhereToBuyClient.getWtbSettingInfo(
                            SystemPdsWtbSettingRequestVO.builder().siteCode(SITE_CODE).build());

            String radiusType = SystemPdsWtbSetting.getRadiusUnitTypeCode();
            String brandshopTabFlag =
                    StringUtils.defaultIfBlank(requestVO.getBrandshopTabFlag(), "false");

            List<SystemPdsWtbDistributorResponseVO> SystemPdsWtbDistributorList =
                    systemPdsWhereToBuyClient.getWtbDistributorList(
                            SystemPdsWtbDistributorRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .pdpId(PDP_ID)
                                    .categoryStandardFlag(CATEGORY_STANDARD_FLAG)
                                    .distributorCategoryUse(MKT_WTB_DISTRIBUTOR_CATEGORY_USE)
                                    .radius(RADIUS)
                                    .radiusType(radiusType)
                                    .lat(LAT)
                                    .lng(LNG)
                                    .maxLat(radiusData.get("maxLat").toString())
                                    .minLat(radiusData.get("minLat").toString())
                                    .maxLng(radiusData.get("maxLng").toString())
                                    .minLng(radiusData.get("minLng").toString())
                                    .wtbCateId(
                                            StringUtils.defaultIfBlank(
                                                    requestVO.getWtbCateId(), CommonConstants.NULL))
                                    .signatureWTBUseFlag(
                                            StringUtils.defaultIfBlank(
                                                    requestVO.getSignatureWTBUseFlag(),
                                                    CommonConstants.NO_FLAG))
                                    .brandshopTabFlag(brandshopTabFlag)
                                    .brandShopImprovingFlag(BRANDSHOP_IMPROVING_FLAG)
                                    .orderbyDistanceLocaleYn(MKT_WTB_ORDERBY_DISTANCE)
                                    .categoryCode(
                                            StringUtils.defaultIfBlank(
                                                    requestVO.getCategoryCode(),
                                                    CommonConstants.NULL))
                                    .countryCode(
                                            StringUtils.defaultIfBlank(
                                                    requestVO.getCountryCode(),
                                                    CommonConstants.NULL))
                                    .stateCode(
                                            StringUtils.defaultIfBlank(
                                                    requestVO.getStateCode(), CommonConstants.NULL))
                                    .stateName(
                                            StringUtils.defaultIfBlank(
                                                    requestVO.getStateName(), CommonConstants.NULL))
                                    .cityName(
                                            StringUtils.defaultIfBlank(
                                                    requestVO.getCityName(), CommonConstants.NULL))
                                    .cityStateDesc(
                                            StringUtils.defaultIfBlank(
                                                    requestVO.getCityStateDesc(),
                                                    CommonConstants.NULL))
                                    .categoryIdList(list)
                                    .build());

            String currentDate = null;
            if (PROMOTION_TIME_ZONE != null) {
                currentDate =
                        DateUtil.getTimeZoneNowDateTime("yyyy-MM-dd", PROMOTION_TIME_ZONE)
                                .toString();
            } else {
                currentDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }

            /** 판매 제품 카테고리 조회 */
            boolean chkFlag = true;
            if (StringUtils.defaultIfBlank(requestVO.getWtbCateId(), CommonConstants.NULL)
                    != null) {
                chkFlag = false;
            }

            /** 판매 제품 Sub 카테고리 및 해당 국가의 B2C 카테고리 조회 */
            SystemPdsWtbCategoryIdResponseVO wtbCateData =
                    systemPdsWhereToBuyClient.getWtbCategoryId(
                            SystemPdsWtbCategoryIdRequestVO.builder()
                                    .pdpId(PDP_ID)
                                    .bizTypeCode(BIZ_TYPE_CODE)
                                    .siteCode(SITE_CODE)
                                    .categoryStandardFlag(CATEGORY_STANDARD_FLAG)
                                    .build());

            String wtbSubCateId = "";
            String wtbB2CCateId = "";
            if (wtbCateData != null) {
                wtbSubCateId =
                        StringUtils.defaultIfBlank(
                                wtbCateData.getSubCategoryId(), CommonConstants.NULL);
                wtbB2CCateId =
                        StringUtils.defaultIfBlank(
                                wtbCateData.getB2cCategoryId(), CommonConstants.NULL);
            }

            SystemPdsWtbSettingResponseVO wtbSetting =
                    systemPdsWhereToBuyClient.getWtbSettingInfo(
                            SystemPdsWtbSettingRequestVO.builder().siteCode(SITE_CODE).build());

            List<Map<String, Object>> systemAdminOpenHourList = new ArrayList<>();
            List<Map<String, Object>> systemAdminStoreImageList = new ArrayList<>();
            if (SystemPdsWtbDistributorList != null && SystemPdsWtbDistributorList.size() > 0) {
                List<String> distributorIdList =
                        SystemPdsWtbDistributorList.stream()
                                .map(vo -> vo.getDistributorId())
                                .collect(Collectors.toList());

                systemAdminOpenHourList =
                        systemAdminWhereToBuyClient.getOpenHourList(
                                SystemAdminOpenHourGetRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .distributorIdList(distributorIdList)
                                        .currentDate(currentDate)
                                        .build());

                systemAdminStoreImageList =
                        systemAdminWhereToBuyClient.getStoreImageList(
                                SystemAdminRetailerImageGetRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .distributorIdList(distributorIdList)
                                        .build());
            }

            WtbDistributorResponseVO wtbDistributor = new WtbDistributorResponseVO();
            for (int i = 0; SystemPdsWtbDistributorList.size() > i; i++) {
                List<String> listProduct = new ArrayList<String>();
                wtbDistributor = SystemPdsWtbDistributorList.get(i).toWtbDistributorVO();

                String categoryCode = wtbDistributor.getLv3CategoryCode();
                if (categoryCode != null) {
                    if (chkFlag
                            || categoryCode.equals(wtbSubCateId)
                            || categoryCode.equals(wtbB2CCateId)
                            || categoryCode.equals(
                                    StringUtils.defaultIfBlank(
                                            requestVO.getWtbSuperCateId(), CommonConstants.NULL))
                            || categoryCode.equals(
                                    StringUtils.defaultIfBlank(
                                            requestVO.getWtbCateId(), CommonConstants.NULL))) {
                        if (wtbDistributor.getCategory() != null) {
                            String[] strCategories = wtbDistributor.getCategory().split(",");
                            for (int j = 0; j < strCategories.length; j++) {
                                listProduct.add(strCategories[j]);
                            }
                            wtbDistributor.setProductCode(listProduct);
                        }
                    }
                }

                if ("Y".equals(WTB_TITLE_FLAG)) {
                    if (wtbDistributor.getCategory() != null) {
                        String[] categories = wtbDistributor.getCategory().split(",");
                        String[] superCategories = wtbDistributor.getSuperCategory().split(",");

                        Map<String, List<String>> categoryMap = new HashMap<String, List<String>>();

                        for (int j = 0; j < superCategories.length; j++) {
                            if (categoryMap.get(superCategories[j]) != null) {
                                categoryMap.get(superCategories[j]).add(categories[j]);
                            } else {
                                List<String> categoryList = new ArrayList<String>();
                                categoryList.add(categories[j]);
                                categoryMap.put(superCategories[j], categoryList);
                            }
                        }
                        wtbDistributor.setCategoryCode(categoryMap);
                    }
                }

                String distributorId =
                        StringUtils.defaultIfBlank(
                                wtbDistributor.getDistributorId(), CommonConstants.NULL);

                List<Map<String, Object>> openHourList = new ArrayList<>();
                for (int j = 0; j < systemAdminOpenHourList.size(); j++) {
                    if (distributorId.equals(systemAdminOpenHourList.get(j).get("distributorId"))) {
                        openHourList.add(systemAdminOpenHourList.get(j));
                    }
                }
                wtbDistributor.setOpenHoursList(openHourList);

                List<Map<String, Object>> storeImageList = new ArrayList<>();
                for (int j = 0; j < systemAdminStoreImageList.size(); j++) {
                    if (distributorId.equals(
                            systemAdminStoreImageList.get(j).get("distributorId"))) {
                        storeImageList.add(systemAdminStoreImageList.get(j));
                    }
                }
                wtbDistributor.setStoreImageList(storeImageList);

                wtbDistributor.setRadiusZoom1(wtbSetting.getLv1ZoomRadiusVal());
                wtbDistributor.setRadiusZoom2(wtbSetting.getLv2ZoomRadiusVal());
                wtbDistributor.setRadiusZoom3(wtbSetting.getLv3ZoomRadiusVal());
                wtbDistributor.setRadiusZoom4(wtbSetting.getLv4ZoomRadiusVal());

                wtbDistributorList.add(wtbDistributor);
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return wtbDistributorList;
    }

    public Map<String, Object> getRadiusData(String lat, String lng, String radius) {
        try {
            Map<String, Object> input = new HashMap<>();
            double milesPerDegree = 0.868976242 / 60.0 * 1.7;
            double degrees = milesPerDegree * Double.parseDouble(radius);

            input.put("maxLat", (Double.parseDouble(lat) + degrees));
            input.put("minLat", (Double.parseDouble(lat) - degrees));
            input.put("maxLng", (Double.parseDouble(lng) + degrees));
            input.put("minLng", (Double.parseDouble(lng) - degrees));

            return input;
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }
    }

    public List<WtbCityResponseVO> getWtbCity(@Valid WtbCityRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String TYPE = requestVO.getType();
        final String WTB_SUPER_CATEGORY_ID = requestVO.getWtbSuperCateId();
        final String WTB_CATEGORY_ID = requestVO.getWtbCateId();
        final String WTB_SUB_CATEGORY_ID = requestVO.getWtbSubCateId();
        final String STATE = requestVO.getState();
        final String CITY = requestVO.getCity();

        if (requestVO.getType().equals("location") && requestVO.getCity().isEmpty()) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        List<SystemPdsWtbCityGetResponseVO> systemPdsWtbCityGetRequestVO = new ArrayList<>();
        switch (TYPE) {
            case "city" -> {
                systemPdsWtbCityGetRequestVO =
                        systemPdsWhereToBuyClient.getWtbCity(
                                SystemPdsWtbCityGetRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .wtbSuperCateId(WTB_SUPER_CATEGORY_ID)
                                        .wtbCateId(WTB_CATEGORY_ID)
                                        .wtbSubCateId(WTB_SUB_CATEGORY_ID)
                                        .state(STATE)
                                        .build());
            }
            case "location" -> {
                systemPdsWtbCityGetRequestVO =
                        systemPdsWhereToBuyClient.getWtbCity(
                                SystemPdsWtbCityGetRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .wtbSuperCateId(WTB_SUPER_CATEGORY_ID)
                                        .wtbCateId(WTB_CATEGORY_ID)
                                        .wtbSubCateId(WTB_SUB_CATEGORY_ID)
                                        .state(STATE)
                                        .city(CITY)
                                        .build());
            }
        }

        List<WtbCityResponseVO> retrieveCityLocation = new ArrayList<>();
        for (SystemPdsWtbCityGetResponseVO city : systemPdsWtbCityGetRequestVO) {
            String code = city.getCode();
            String value = city.getValue();

            retrieveCityLocation.add(WtbCityResponseVO.builder().code(code).value(value).build());
        }

        return retrieveCityLocation;
    }

    public WtbInitResponseVO getWtbInit(@Valid WtbInitRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = StringUtils.defaultIfBlank(requestVO.getSku(), CommonConstants.NULL);

        WtbInitResponseVO wtbInfo = new WtbInitResponseVO();
        try {
            if (SKU != null) {
                List<CachedPdpIdVO> pdpList =
                        cachedDataUtil.getPdpIdList(
                                CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
                if (ObjectUtils.isEmpty(pdpList)) {
                    throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
                }

                String PDP_ID = pdpList.get(0).getPdpId();

                wtbInfo =
                        pdpInfoService
                                .getProductSummary(
                                        ProductSummaryRequestVO.builder().sku(SKU).build())
                                .toWtbInfoVO();

                if (wtbInfo != null) {
                    wtbInfo.setMsrp(cachedDataUtil.getMsrp(wtbInfo.getMsrp()));
                    if ("DE".equals(SITE_CODE)) {
                        if (wtbInfo.getEanCode() == null || "".equals(wtbInfo.getEanCode())) {
                            wtbInfo.setEanCode(wtbInfo.getModelEanCode());
                        }
                        if (wtbInfo.getWtbSubId() == null || "".equals(wtbInfo.getWtbSubId())) {
                            wtbInfo.setWtbSubId("lg_general");
                        }
                    }

                    PDP_ID = wtbInfo.getModelId();
                }

                String WTB_SELECTBOX_TYPE =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("wtb_selectbox_type")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String WTB_DEFAULT_ZOOM =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("wtb-default-zoom")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String SIGNATURE_WTB_USE_FLAG_YN =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("signature_wtb_use_flag_yn")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String WTB_DATA_DISPLAY_NAME =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("wtb_data_display_name")
                                                .build()),
                                "false");

                String WTB_SCROLL_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("wtb_scroll_flag")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String WTB_TITLE_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("wtb_title_flag")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String ENERGY_LABEL_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("Energy_Label_Flag")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String REPAIRABILITY_INDEX_USE_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("repairability_index_use_flag")
                                                .build()),
                                CommonConstants.NO_FLAG);

                wtbInfo.setDataDisplayName(WTB_DATA_DISPLAY_NAME);
                wtbInfo.setWtbScrollFlag(WTB_SCROLL_FLAG);
                wtbInfo.setWtbTitleFlag(WTB_TITLE_FLAG);

                WtbSettingResponseVO wtbSetting =
                        systemPdsWhereToBuyClient
                                .getWtbSettingInfo(
                                        SystemPdsWtbSettingRequestVO.builder()
                                                .siteCode(SITE_CODE)
                                                .build())
                                .toWtbSettingVO();

                wtbInfo.setWtbSetting(wtbSetting);

                if (wtbSetting != null && !"None".equals(wtbSetting.getWtbType())) {
                    /** ##.OfflineMap 영역 정보 조회 */
                    getOfflineInfo(wtbInfo);

                    /** ##.OnlineStore 정보 조회 */
                    if (!CommonConstants.NO_FLAG.equals(wtbSetting.getBuyonlineType())) {
                        getOnlineStoreInfo(wtbInfo);
                    }
                }

                getOnOffStoreYn(wtbInfo);

                /** ##.signatureWTBUseFlag 설정(Locale : TW, UA) */
                String sigWTBUseFlag = "N";
                if (CommonConstants.YES_FLAG.equals(SIGNATURE_WTB_USE_FLAG_YN)) {
                    sigWTBUseFlag = "Y";
                }
                wtbInfo.setSigWTBUseFlag(sigWTBUseFlag);

                /** ##.기본 반경 설정(Locale : FR) */
                if (!CommonConstants.NO_FLAG.equals(WTB_DEFAULT_ZOOM)) {
                    wtbInfo.setDefaultZoom(WTB_DEFAULT_ZOOM);
                }

                String WTB_DIRECTFROM_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemPreferenceCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("WTB_DIRECTFROM_FLAG")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String BRANDSHOP_IMPROVING_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemPreferenceCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("BRANDSHOP_IMPROVING_FLAG")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String MULTI_BRANDSHOP_IMPROVING_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemPreferenceCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("MULTI_BRANDSHOP_IMPROVING_FLAG")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String LABEL_USE_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemPreferenceCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("LABEL_USE_FLAG")
                                                .build()),
                                CommonConstants.NO_FLAG);

                String PROMOTION_TIME_ZONE =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemPreferenceCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("promotionTimeZone")
                                                .build()),
                                CommonConstants.NO_FLAG);

                wtbInfo.setWtbDirectfromFlag(WTB_DIRECTFROM_FLAG);
                wtbInfo.setBrandShopImprovingFlag(BRANDSHOP_IMPROVING_FLAG);
                wtbInfo.setMultiBrandShopImprovingFlag(MULTI_BRANDSHOP_IMPROVING_FLAG);

                if (CommonConstants.YES_FLAG.equals(LABEL_USE_FLAG)
                        && CommonConstants.YES_FLAG.equals(REPAIRABILITY_INDEX_USE_FLAG)) {
                    String strDate = null;
                    if (PROMOTION_TIME_ZONE != null) {
                        strDate =
                                DateUtil.getTimeZoneNowDateTime("yyyyMMdd", PROMOTION_TIME_ZONE)
                                        .toString();
                    }

                    List<Map<String, Object>> labelRepairMapList =
                            new ArrayList<Map<String, Object>>();
                    List<SystemPdsProductIconListResponseVO> sysPdsProductIconListVO =
                            systemPdsPdpInfoClient.getProductIconList(
                                    SystemPdsProductIconListRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .bizTypeCode(wtbInfo.getBizType())
                                            .today(strDate)
                                            .pdpIdList(Arrays.asList(PDP_ID))
                                            .build());

                    for (SystemPdsProductIconListResponseVO sysPdsProductIconVO :
                            sysPdsProductIconListVO) {
                        Map<String, Object> labelIconTempMap = new HashMap<>();
                        labelIconTempMap.put("modelId", sysPdsProductIconVO.getPdpId());
                        labelIconTempMap.put("TEMP_SEQ", "1");
                        labelIconTempMap.put("repOrderNo", sysPdsProductIconVO.getScreenExpSeq());
                        labelIconTempMap.put("iconId", sysPdsProductIconVO.getIconId());
                        labelIconTempMap.put("imagePathAddr", sysPdsProductIconVO.getImgPath());
                        labelIconTempMap.put("shortDesc", sysPdsProductIconVO.getIconDesc());
                        labelIconTempMap.put("altText", sysPdsProductIconVO.getImgAltTextCnts());
                        labelIconTempMap.put(
                                "shortDescType", sysPdsProductIconVO.getIconTypeCode());
                        labelIconTempMap.put("linkUrl", sysPdsProductIconVO.getLinkUrl());
                        labelIconTempMap.put("linkOpt", sysPdsProductIconVO.getLinkModeCode());
                        labelIconTempMap.put(
                                "cssFontItalic", sysPdsProductIconVO.getItalicftUseFlag());
                        labelIconTempMap.put("cssFontBold", sysPdsProductIconVO.getBoldftUseFlag());
                        labelIconTempMap.put("pdpUse", sysPdsProductIconVO.getPdpLabelUseFlag());

                        if (labelRepairMapList.size() == 0) {
                            labelRepairMapList.add(labelIconTempMap);
                        }
                    }
                    wtbInfo.setLabelUseFlag(LABEL_USE_FLAG);
                    wtbInfo.setRepairabilityIndexFlag(REPAIRABILITY_INDEX_USE_FLAG);
                    wtbInfo.setLabelRepairMap(labelRepairMapList);
                }

                String energyLabelCategory = wtbInfo.getEnergyLabelCategory();
                String energyLabel = wtbInfo.getEnergyLabel();
                String secondELabelCategory = wtbInfo.getSecondEnergyLabelCategory();
                String secondELabel = wtbInfo.getSecondEnergyLabel();
                String washTowerFlag = wtbInfo.getWashTowerFlag();

                String rsUseFlag = "N";
                if ("RS".equals(SITE_CODE)
                        && (("EL_CAT_01".equals(energyLabelCategory))
                                || ("EL_CAT_02".equals(energyLabelCategory))
                                || ("EL_CAT_03".equals(energyLabelCategory))
                                || ("EL_CAT_05".equals(energyLabelCategory)))) {
                    rsUseFlag = "Y";
                }
                wtbInfo.setFirstLabelCheckFlag("Y");

                /** 제품 GSRI(환경규제 관련 모델) 정보 조회 * */
                String docTypeCode = "";
                String energyLabelDocId = "";
                String fEnergyLabelDocId = "";
                String fEnergyLabelFileName = "";
                String fEnergyLabelOriginalName = "";
                String fEnergyLabelproductLeve1Code = "";
                String productFichelDocId = "";
                String productFicheFileName = "";
                String productFicheOriginalName = "";
                String productFicheproductLeve1Code = "";
                String energyLabelFileName = "";
                String energyLabelOriginalName = "";
                String energyLabelproductLeve1Code = "";

                if ("EL_CAT_06".equals(energyLabelCategory)
                        || "EL_CAT_07".equals(energyLabelCategory)
                        || "EL_CAT_08".equals(energyLabelCategory)
                        || "EL_CAT_09".equals(energyLabelCategory)
                        || "EL_CAT_10".equals(energyLabelCategory)
                        || "EL_CAT_11".equals(energyLabelCategory)) {
                    docTypeCode = "EL";
                } else if ("TR".equals(SITE_CODE)) {
                    docTypeCode = "TE";
                } else {
                    docTypeCode = "EN";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsProductGsriFileResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(PDP_ID)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp)
                        && ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsProductGsriFileResp.getResultMap();

                    energyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                    energyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                    energyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                    energyLabelproductLeve1Code =
                            Objects.toString(resultMap.get("productLeve1Code"), "");
                }

                wtbInfo.setEnergyLabelDocId(energyLabelDocId);
                wtbInfo.setEnergyLabelFileName(energyLabelFileName);
                wtbInfo.setEnergyLabelOriginalName(energyLabelOriginalName);
                wtbInfo.setEnergyLabelproductLeve1Code(energyLabelproductLeve1Code);

                if ("EL_CAT_06".equals(energyLabelCategory)
                        || "EL_CAT_07".equals(energyLabelCategory)
                        || "EL_CAT_08".equals(energyLabelCategory)
                        || "EL_CAT_09".equals(energyLabelCategory)
                        || "EL_CAT_10".equals(energyLabelCategory)
                        || "EL_CAT_11".equals(energyLabelCategory)) {
                    docTypeCode = "PF";
                } else if ("TR".equals(SITE_CODE) && !"EL_CAT_04".equals(energyLabelCategory)) {
                    docTypeCode = "TI";
                } else if (rsUseFlag.equals("Y")) {
                    docTypeCode = "SP";
                } else {
                    docTypeCode = "PI";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsProductFicheResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(PDP_ID)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsProductFicheResp)
                        && ObjectUtils.isNotEmpty(sysPdsProductFicheResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsProductFicheResp.getResultMap();

                    productFichelDocId = Objects.toString(resultMap.get("docId"), "");
                    productFicheFileName = Objects.toString(resultMap.get("fileName"), "");
                    productFicheOriginalName = Objects.toString(resultMap.get("originalName"), "");
                    productFicheproductLeve1Code =
                            Objects.toString(resultMap.get("productLeve1Code"), "");
                }

                wtbInfo.setProductFicheDocId(productFichelDocId);
                wtbInfo.setProductFicheFileName(productFicheFileName);
                wtbInfo.setProductFicheOriginalName(productFicheOriginalName);
                wtbInfo.setProductFicheproductLeve1Code(productFicheproductLeve1Code);

                if ("EL_CAT_06".equals(energyLabelCategory)
                        || "EL_CAT_07".equals(energyLabelCategory)
                        || "EL_CAT_08".equals(energyLabelCategory)
                        || "EL_CAT_09".equals(energyLabelCategory)
                        || "EL_CAT_10".equals(energyLabelCategory)
                        || "EL_CAT_11".equals(energyLabelCategory)) {
                    docTypeCode = "FL";
                } else {
                    docTypeCode = "FE";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsFEnergyLabelResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(PDP_ID)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp)
                        && ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsFEnergyLabelResp.getResultMap();

                    fEnergyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                    fEnergyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                    fEnergyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                    fEnergyLabelproductLeve1Code =
                            Objects.toString(resultMap.get("productLeve1Code"), "");
                }

                wtbInfo.setFEnergyLabelDocId(fEnergyLabelDocId);
                wtbInfo.setFEnergyLabelFileName(fEnergyLabelFileName);
                wtbInfo.setFEnergyLabelOriginalName(fEnergyLabelOriginalName);
                wtbInfo.setFenergyLabelproductLeve1Code(fEnergyLabelproductLeve1Code);

                SystemPdsPdpEnergyLabelInfoResponseVO energyLabelInfoResponseVO =
                        systemPdsPdpInfoClient.getEnergyLabelInfo(
                                SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                        .elabelClsCode(energyLabelCategory)
                                        .elabelGrdCode(energyLabel)
                                        .build());

                if (ObjectUtils.isNotEmpty(energyLabelInfoResponseVO)) {
                    wtbInfo.setEnergyLabelImageAddr(
                            StringUtils.defaultIfBlank(
                                    energyLabelInfoResponseVO.getElabelImgPath(), ""));
                    wtbInfo.setEnergyLabelName(
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComCommonCodeValue(
                                            CachedCommonCodeRequestVO.ofCommonCodeValue(
                                                    "Energy_Label_Code",
                                                    energyLabelInfoResponseVO.getElabelGrdCode(),
                                                    JobSeperateCodeEnum.D2C)),
                                    ""));
                }

                if (CommonConstants.YES_FLAG.equals(washTowerFlag)) {
                    if ("EL_CAT_06".equals(secondELabelCategory)
                            || "EL_CAT_07".equals(secondELabelCategory)
                            || "EL_CAT_08".equals(secondELabelCategory)
                            || "EL_CAT_09".equals(secondELabelCategory)
                            || "EL_CAT_10".equals(secondELabelCategory)
                            || "EL_CAT_11".equals(secondELabelCategory)) {
                        docTypeCode = "EL";
                    } else if ("TR".equals(SITE_CODE)) {
                        docTypeCode = "TE";
                    } else {
                        docTypeCode = "EN";
                    }

                    SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductGsriFileResp =
                            systemPdsSupportClient.getProductGsriFileInfo(
                                    SystemPdsProductGsriFileInfoRequestVO.builder()
                                            .docTypeCode(docTypeCode)
                                            .pdpId(PDP_ID)
                                            .fEnergyLabelDocId(fEnergyLabelDocId)
                                            .energyLabelDocId(energyLabelDocId)
                                            .productFichelDocId(productFichelDocId)
                                            .build());

                    if (ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp)
                            && ObjectUtils.isNotEmpty(
                                    sysPdsSecondProductGsriFileResp.getResultMap())) {
                        Map<String, Object> resultMap =
                                sysPdsSecondProductGsriFileResp.getResultMap();

                        wtbInfo.setSecondEnergyLabelDocId(
                                Objects.toString(resultMap.get("docId"), ""));
                        wtbInfo.setSecondEnergyLabelFileName(
                                Objects.toString(resultMap.get("fileName"), ""));
                        wtbInfo.setSecondEnergyLabelOriginalName(
                                Objects.toString(resultMap.get("originalName"), ""));
                        wtbInfo.setSecondEnergyLabelproductLeve1Code(
                                Objects.toString(resultMap.get("productLeve1Code"), ""));
                    }

                    if ("EL_CAT_06".equals(secondELabelCategory)
                            || "EL_CAT_07".equals(secondELabelCategory)
                            || "EL_CAT_08".equals(secondELabelCategory)
                            || "EL_CAT_09".equals(secondELabelCategory)
                            || "EL_CAT_10".equals(secondELabelCategory)
                            || "EL_CAT_11".equals(secondELabelCategory)) {
                        docTypeCode = "PF";
                    } else if ("TR".equals(SITE_CODE)
                            && !"EL_CAT_04".equals(secondELabelCategory)) {
                        docTypeCode = "TI";
                    } else if (rsUseFlag.equals("Y")) {
                        docTypeCode = "SP";
                    } else {
                        docTypeCode = "PI";
                    }

                    SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductFicheResp =
                            systemPdsSupportClient.getProductGsriFileInfo(
                                    SystemPdsProductGsriFileInfoRequestVO.builder()
                                            .docTypeCode(docTypeCode)
                                            .pdpId(PDP_ID)
                                            .fEnergyLabelDocId(fEnergyLabelDocId)
                                            .energyLabelDocId(energyLabelDocId)
                                            .productFichelDocId(productFichelDocId)
                                            .build());

                    if (ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp)
                            && ObjectUtils.isNotEmpty(
                                    sysPdsSecondProductFicheResp.getResultMap())) {
                        Map<String, Object> resultMap = sysPdsSecondProductFicheResp.getResultMap();
                        wtbInfo.setSecondProductFicheDocId(
                                Objects.toString(resultMap.get("docId"), ""));
                        wtbInfo.setSecondProductFicheFileName(
                                Objects.toString(resultMap.get("fileName"), ""));
                        wtbInfo.setSecondProductFicheOriginalName(
                                Objects.toString(resultMap.get("originalName"), ""));
                        wtbInfo.setSecondProductFicheproductLeve1Code(
                                Objects.toString(resultMap.get("productLeve1Code"), ""));
                    }

                    if ("EL_CAT_06".equals(secondELabelCategory)
                            || "EL_CAT_07".equals(secondELabelCategory)
                            || "EL_CAT_08".equals(secondELabelCategory)
                            || "EL_CAT_09".equals(secondELabelCategory)
                            || "EL_CAT_10".equals(secondELabelCategory)
                            || "EL_CAT_11".equals(secondELabelCategory)) {
                        docTypeCode = "FL";
                    } else {
                        docTypeCode = "FE";
                    }

                    SystemPdsProductGsriFileInfoResponseVO sysPdsSecondFEnergyLabelResp =
                            systemPdsSupportClient.getProductGsriFileInfo(
                                    SystemPdsProductGsriFileInfoRequestVO.builder()
                                            .docTypeCode(docTypeCode)
                                            .pdpId(PDP_ID)
                                            .fEnergyLabelDocId(fEnergyLabelDocId)
                                            .energyLabelDocId(energyLabelDocId)
                                            .productFichelDocId(productFichelDocId)
                                            .build());

                    if (ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp)
                            && ObjectUtils.isNotEmpty(
                                    sysPdsSecondFEnergyLabelResp.getResultMap())) {
                        Map<String, Object> resultMap = sysPdsSecondFEnergyLabelResp.getResultMap();
                        wtbInfo.setSecondFEnergyLabelDocId(
                                Objects.toString(resultMap.get("docId"), ""));
                        wtbInfo.setSecondFEnergyLabelFileName(
                                Objects.toString(resultMap.get("fileName"), ""));
                        wtbInfo.setSecondFEnergyLabelOriginalName(
                                Objects.toString(resultMap.get("originalName"), ""));
                        wtbInfo.setSecondFEnergyLabelproductLeve1Code(
                                Objects.toString(resultMap.get("productLeve1Code"), ""));
                    }

                    SystemPdsPdpEnergyLabelInfoResponseVO secondELabelInfoResponseVO =
                            systemPdsPdpInfoClient.getEnergyLabelInfo(
                                    SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                            .elabelClsCode(secondELabelCategory)
                                            .elabelGrdCode(secondELabel)
                                            .build());

                    if (ObjectUtils.isNotEmpty(secondELabelInfoResponseVO)) {
                        wtbInfo.setSecondEnergyLabelImageAddr(
                                StringUtils.defaultIfBlank(
                                        secondELabelInfoResponseVO.getElabelImgPath(), ""));
                        wtbInfo.setSecondEnergyLabelName(
                                StringUtils.defaultIfBlank(
                                        cachedDataUtil.getComCommonCodeValue(
                                                CachedCommonCodeRequestVO.ofCommonCodeValue(
                                                        "Energy_Label_Code",
                                                        secondELabelInfoResponseVO
                                                                .getElabelGrdCode(),
                                                        JobSeperateCodeEnum.D2C)),
                                        ""));
                    }
                }

                wtbInfo.setEnergyLabelFlag(ENERGY_LABEL_FLAG);
            } else {
                WtbSettingResponseVO wtbSetting =
                        systemPdsWhereToBuyClient
                                .getWtbSettingInfo(
                                        SystemPdsWtbSettingRequestVO.builder()
                                                .siteCode(SITE_CODE)
                                                .build())
                                .toWtbSettingVO();

                wtbInfo.setWtbSetting(wtbSetting);

                String WTB_SELECTBOX_TYPE =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("wtb_selectbox_type")
                                                .build()),
                                CommonConstants.NO_FLAG);

                /** ##.City SelectBox 목록 조회 */
                if (!CommonConstants.NO_FLAG.equals(WTB_SELECTBOX_TYPE)) {
                    Map<String, Object> input = new HashMap<>();
                    input.put("siteCode", SITE_CODE);
                    input.put("selectBoxType", WTB_SELECTBOX_TYPE);
                    wtbInfo.setSelectBoxType(WTB_SELECTBOX_TYPE);
                    wtbInfo.setSelectBoxList(getComboInfo(input));
                }
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }
        return wtbInfo;
    }

    /**
     * Offline(Map) 영역 관련 정보 조회
     *
     * @param wtbInfo - result setting
     */
    public void getOfflineInfo(WtbInitResponseVO wtbInfo) {
        try {
            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("category_standard_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            /** ##.City SelectBox 목록 조회 */
            String WTB_SELECTBOX_TYPE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("wtb_selectbox_type")
                                            .build()),
                            CommonConstants.NO_FLAG);

            /** ##.City SelectBox 목록 조회 */
            if (!CommonConstants.NO_FLAG.equals(WTB_SELECTBOX_TYPE)) {
                Map<String, Object> input = new HashMap<>();
                input.put("siteCode", wtbInfo.getLocaleCode());
                input.put("selectBoxType", WTB_SELECTBOX_TYPE);
                wtbInfo.setSelectBoxType(WTB_SELECTBOX_TYPE);
                wtbInfo.setSelectBoxList(getComboInfo(input));
            }

            switch (wtbInfo.getWtbSetting().getWtbUseFlag()) {
                case "comcon_all" -> {
                    // [WtbUseFlag] Commerce Connector(All)
                    String WTB_COMMERCE_CONNECTOR_ALL =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_commerce_connector_all")
                                                    .build()),
                                    "");

                    wtbInfo.setOfflineAreaUrl(WTB_COMMERCE_CONNECTOR_ALL);
                }
                case "iframe_all" -> {
                    // [WtbUseFlag] iframe(All)
                    String WTB_IFRAME_ALL =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_iframe_all")
                                                    .build()),
                                    "");
                    StringBuffer wtbIframeUrl = new StringBuffer();
                    String iframeUrl = WTB_IFRAME_ALL.replace("{modelInfo}", "");
                    String salesModelCode = wtbInfo.getSalesModelCode();
                    String salesSuffixCode = wtbInfo.getSalesSuffixCode();

                    wtbIframeUrl.append(iframeUrl).append(salesModelCode);
                    if (!"".equals(salesSuffixCode)) {
                        wtbIframeUrl.append(".").append(salesSuffixCode);
                    }
                    wtbInfo.setOfflineAreaUrl(wtbIframeUrl.toString());
                }
                case "yandex" -> {
                    // [BuyOnlineType] Yandex (RU)
                }
                case "lgcom_cn" -> {
                    // [BuyOnlineType] LG.com (CN)
                    Map<String, Object> tmpItem = new HashMap<String, Object>();
                    tmpItem.put("code", "all");
                    tmpItem.put("value", "全部");

                    /** 1Lv Category 조회 */
                    List<Map<String, Object>> superCateList = new ArrayList<Map<String, Object>>();
                    List<SystemPdsWtbCategoryListGetResponseVO> systemWtbSuperCateList =
                            systemPdsWhereToBuyClient.getCategoryList(
                                    SystemPdsWtbCategoryListGetRequestVO.builder()
                                            .siteCode(wtbInfo.getLocaleCode())
                                            .bizTypeCode(wtbInfo.getBizType())
                                            .categoryLvNo(
                                                    CommonConstants.YES_FLAG.equals(
                                                                    CATEGORY_STANDARD_FLAG)
                                                            ? 2
                                                            : 1)
                                            .build());

                    for (SystemPdsWtbCategoryListGetResponseVO systemWtbSuperCategory :
                            systemWtbSuperCateList) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("code", systemWtbSuperCategory.getCode());
                        map.put("value", systemWtbSuperCategory.getValue());
                        superCateList.add(map);
                    }
                    superCateList.add(0, tmpItem);

                    wtbInfo.setSuperCategoryList(superCateList);

                    /** 2Lv Category 조회 */
                    List<Map<String, Object>> cateList = new ArrayList<Map<String, Object>>();
                    List<SystemPdsWtbCategoryListGetResponseVO> systemWtbCateList =
                            systemPdsWhereToBuyClient.getCategoryList(
                                    SystemPdsWtbCategoryListGetRequestVO.builder()
                                            .siteCode(wtbInfo.getLocaleCode())
                                            .bizTypeCode(wtbInfo.getBizType())
                                            .type("category")
                                            .superCategory(wtbInfo.getWtbSuperCateId())
                                            .categoryLvNo(
                                                    CommonConstants.YES_FLAG.equals(
                                                                    CATEGORY_STANDARD_FLAG)
                                                            ? 3
                                                            : 2)
                                            .build());

                    for (SystemPdsWtbCategoryListGetResponseVO systemWtbCategory :
                            systemWtbCateList) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("code", systemWtbCategory.getCode());
                        map.put("value", systemWtbCategory.getValue());
                        cateList.add(map);
                    }
                    cateList.add(0, tmpItem);

                    wtbInfo.setCategoryList(cateList);

                    /** 상세 도시 정보 초기 값 설정 */
                    List<Map<String, Object>> siteList = new ArrayList<Map<String, Object>>();
                    siteList.add(0, tmpItem);
                    wtbInfo.setSiteList(siteList);
                }
                default -> {
                    // [WtbUseFlag] None
                }
            }

            if ("TR".equals(wtbInfo.getLocaleCode())) {
                /** 2Lv Category 조회 */
                List<Map<String, Object>> cateList = new ArrayList<Map<String, Object>>();
                List<SystemPdsWtbCategoryListGetResponseVO> systemWtbCateList =
                        systemPdsWhereToBuyClient.getCategoryList(
                                SystemPdsWtbCategoryListGetRequestVO.builder()
                                        .siteCode(wtbInfo.getLocaleCode())
                                        .bizTypeCode(wtbInfo.getBizType())
                                        .categoryLvNo(
                                                CommonConstants.YES_FLAG.equals(
                                                                CATEGORY_STANDARD_FLAG)
                                                        ? 3
                                                        : 2)
                                        .build());

                for (SystemPdsWtbCategoryListGetResponseVO systemWtbCategory : systemWtbCateList) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("code", systemWtbCategory.getCode());
                    map.put("value", systemWtbCategory.getValue());
                    cateList.add(map);
                }
                wtbInfo.setCategoryList(cateList);
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }
    }

    /**
     * 국가별 Buy Online 관련 정보 조회
     *
     * @param wtbInfo - result setting
     */
    public void getOnlineStoreInfo(WtbInitResponseVO wtbInfo) {
        String localeCode = wtbInfo.getLocaleCode();
        String modelId = wtbInfo.getModelId();

        try {
            if ("CA_EN".equals(localeCode) || "CA_FR".equals(localeCode)) {
                wtbInfo.getWtbSetting().setBuyonlineType("price_spider");
            }
            switch (wtbInfo.getWtbSetting().getBuyonlineType()) {
                case "channel_ad" -> {
                    // [BuyOnlineType] Channel Advisor
                    String WTB_CHANNEL_ADVISOR =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_channel_advisor")
                                                    .build()),
                                    "");

                    String WTB_CHANNEL_ADVISOR_MODELINFO =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_channel_advisor_modelInfo")
                                                    .build()),
                                    CommonConstants.NULL);

                    String WTB_CHANNEL_ADVISOR_AUTHORIZATIONTOKEN =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode(
                                                            "wtb_channel_advisor_authorizationToken")
                                                    .build()),
                                    CommonConstants.NULL);

                    String WTB_CHANNEL_ADVISOR_TYPE =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_channel_advisor_type")
                                                    .build()),
                                    CommonConstants.NULL);

                    String WTB_CHANNEL_ADVISOR_TAG =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_channel_advisor_tag")
                                                    .build()),
                                    CommonConstants.NULL);

                    String WTB_CHANNEL_ADVISOR_TRACK =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_channel_advisor_track")
                                                    .build()),
                                    CommonConstants.NULL);

                    Map<String, Object> confMap = new HashMap<>();
                    confMap.put("wtb_channel_advisor", WTB_CHANNEL_ADVISOR);
                    confMap.put("wtb_channel_advisor_modelinfo", WTB_CHANNEL_ADVISOR_MODELINFO);
                    confMap.put(
                            "wtb_channel_advisor_authorizationtoken",
                            WTB_CHANNEL_ADVISOR_AUTHORIZATIONTOKEN);
                    confMap.put("wtb_channel_advisor_type", WTB_CHANNEL_ADVISOR_TYPE);
                    confMap.put("wtb_channel_advisor_tag", WTB_CHANNEL_ADVISOR_TAG);
                    confMap.put("wtb_channel_advisor_track", WTB_CHANNEL_ADVISOR_TRACK);

                    StringBuffer onlineAreaUrl =
                            new StringBuffer()
                                    .append(
                                            getChannelAdvisorUrl(
                                                    wtbInfo, confMap, "wtb_channel_advisor"));

                    if ("ZA".equals(localeCode)) {
                        String ZA_BUYONLINE_LGCOM_CATEGORY =
                                StringUtils.defaultIfBlank(
                                        cachedDataUtil.getComSystemConfigurationCode(
                                                CachedCodeRequestVO.builder()
                                                        .keyCode("za_buyonline_lgcom_category")
                                                        .build()),
                                        "");

                        String[] buyOnlineUseCategory = ZA_BUYONLINE_LGCOM_CATEGORY.split(",");
                        wtbInfo.setZaLgcomUseFlag("N");
                        for (int i = 0; i < buyOnlineUseCategory.length; i++) {
                            if (buyOnlineUseCategory[i].equals(wtbInfo.getWtbCateId())) {
                                wtbInfo.setZaLgcomUseFlag("Y");
                            }
                        }

                        List<SystemAdminRetailerGetResponseVO> systemAdminRetailerList =
                                systemAdminWhereToBuyClient.getRetailerList(
                                        SystemAdminRetailerGetRequestVO.builder()
                                                .localeCode(wtbInfo.getLocaleCode())
                                                .modelId(wtbInfo.getModelId())
                                                .bizType(wtbInfo.getBizType())
                                                .build());

                        List<WtbRetailerResponseVO> wtbRetailerList =
                                new ArrayList<WtbRetailerResponseVO>();
                        for (SystemAdminRetailerGetResponseVO systemAdminRetailer :
                                systemAdminRetailerList) {
                            wtbRetailerList.add(systemAdminRetailer.toWtbRetailerVO());
                        }

                        wtbInfo.setBuyOnlineList(wtbRetailerList);
                    }

                    wtbInfo.setOnlineAreaUrl(onlineAreaUrl.toString());
                }
                case "static" -> {
                    // [BuyOnlineType] Static
                    if ("BE_FR".equals(localeCode)) {
                        String WTB_RETAILER_CATEGORY =
                                StringUtils.defaultIfBlank(
                                        cachedDataUtil.getComSystemConfigurationCode(
                                                CachedCodeRequestVO.builder()
                                                        .keyCode("wtb_retailer_category")
                                                        .build()),
                                        "");

                        List<String> list = new ArrayList<String>();
                        Collections.addAll(list, WTB_RETAILER_CATEGORY.split(","));
                        String retailerFlag = "N";
                        if (list.indexOf(StringUtils.defaultIfBlank(wtbInfo.getWtbCateId(), "null"))
                                > -1) {
                            retailerFlag = "Y";
                        }
                        wtbInfo.setRetailerFlag(retailerFlag);
                    }
                }
                case "intellib" -> {
                    // [BuyOnlineType] Intellibrand
                    String WTB_INTELLIBRAND =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_intellibrand")
                                                    .build()),
                                    CommonConstants.NO_FLAG);

                    String WTB_INTELLIBRAND_FINDONLINE =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_intellibrand_findonline")
                                                    .build()),
                                    "");

                    String WTB_INTELLIBRAND_AUTHORIZATIONTOKEN =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_intellibrand_authorizationToken")
                                                    .build()),
                                    CommonConstants.NO_FLAG);

                    String WTB_INTELLIBRAND_TYPE =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_intellibrand_type")
                                                    .build()),
                                    CommonConstants.NO_FLAG);

                    String WTB_INTELLIBRAND_TAG =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_intellibrand_tag")
                                                    .build()),
                                    CommonConstants.NO_FLAG);

                    Map<String, Object> confMap = new HashMap<>();
                    confMap.put("wtb_intellibrand", WTB_INTELLIBRAND);
                    confMap.put("wtb_intellibrand_findonline", WTB_INTELLIBRAND_FINDONLINE);
                    confMap.put(
                            "wtb_intellibrand_authorizationtoken",
                            WTB_INTELLIBRAND_AUTHORIZATIONTOKEN);
                    confMap.put("wtb_intellibrand_type", WTB_INTELLIBRAND_TYPE);
                    confMap.put("wtb_intellibrand_tag", WTB_INTELLIBRAND_TAG);

                    if ("BR".equals(localeCode)) {
                        // modelId는 input의 sku값으로 조회된 modelid로 수정함
                        wtbInfo.setOnlineAreaUrl(WTB_INTELLIBRAND_FINDONLINE);
                    } else {
                        wtbInfo.setOnlineAreaUrl(
                                getChannelAdvisorUrl(wtbInfo, confMap, "wtb_intellibrand"));
                    }
                }
                case "lgwtb_lv" -> {
                    // [BuyOnlineType] lgwtb.lv
                    String WTB_LGWTB_LV =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_lgwtb_lv")
                                                    .build()),
                                    "");
                    wtbInfo.setOnlineAreaUrl(
                            WTB_LGWTB_LV.replace("{modelName}", wtbInfo.getModelName()));
                }
                case "solotodo" -> {
                    // [BuyOnlineType] SoloTodo
                    String WTB_SOLOTODO =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_solotodo")
                                                    .build()),
                                    "");
                    wtbInfo.setOnlineAreaUrl(
                            WTB_SOLOTODO.replace("{modelName}", wtbInfo.getModelName()));
                }
                case "etailing" -> {
                    // [BuyOnlineType] Etailing
                    String WTB_ETAILING =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_etailing")
                                                    .build()),
                                    "");
                    String etailingUrl =
                            WTB_ETAILING
                                    .replace("{modelId}", modelId)
                                    .replace("{modelName}", wtbInfo.getModelName())
                                    .replace("{salesModelCode}", wtbInfo.getSalesModelCode())
                                    .replace("{salesSuffixCode}", wtbInfo.getSalesSuffixCode())
                                    .replace("{categoryName}", wtbInfo.getWtbCateName());
                    wtbInfo.setOnlineAreaUrl(etailingUrl);
                }
                case "hatch_ru" -> {
                    // [BuyOnlineType] Hatch (RU)
                    wtbInfo.setModelUrlPath(wtbInfo.getModelUrlPath());
                }
                case "lgcom_cn" -> {
                    // [BuyOnlineType] LG.com (CN)
                    List<Map<String, Object>> cityList = new ArrayList<>();
                    List<SystemAdminCnBrandshopCityGetResponseVO> systemAdminCnBrandshopCityList =
                            systemAdminWhereToBuyClient.getCnBrandshopCityList(
                                    SystemAdminCnBrandshopCityGetRequestVO.builder()
                                            .modelId(wtbInfo.getModelId())
                                            .build());

                    for (SystemAdminCnBrandshopCityGetResponseVO systemAdminCnBrandshopCity :
                            systemAdminCnBrandshopCityList) {
                        Map<String, Object> tempMap = new HashMap<>();
                        tempMap.put("code", systemAdminCnBrandshopCity.getCode());
                        tempMap.put("value", systemAdminCnBrandshopCity.getValue());
                        cityList.add(tempMap);
                    }

                    Map<String, Object> tmpItem = new HashMap<String, Object>();
                    tmpItem.put("code", "all");
                    tmpItem.put("value", "全部");
                    cityList.add(0, tmpItem);
                    wtbInfo.setBrandshopCityList(cityList);

                    String WTB_CN_SITE_NAME =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_cn_site_name")
                                                    .build()),
                                    "京东,天猫,苏宁");
                    String[] siteName = WTB_CN_SITE_NAME.split(",");

                    List<WtbRetailerCnResponseVO> cnRetailerList = new ArrayList<>();
                    List<SystemAdminRetailerCnGetResponseVO> systemAdminRetailerCnList =
                            systemAdminWhereToBuyClient.getRetailerCnList(
                                    SystemAdminRetailerCnGetRequestVO.builder()
                                            .localeCode(localeCode)
                                            .modelId(wtbInfo.getModelId())
                                            .siteName(siteName)
                                            .build());

                    for (SystemAdminRetailerCnGetResponseVO systemAdminRetailerCn :
                            systemAdminRetailerCnList) {
                        cnRetailerList.add(systemAdminRetailerCn.toWtbRetailerCnVO());
                    }

                    wtbInfo.setBuyOnlineCnList(cnRetailerList);
                }
                case "lgcom" -> {
                    // [BuyOnlineType] LG.com
                    List<SystemAdminRetailerGetResponseVO> systemAdminRetailerList =
                            systemAdminWhereToBuyClient.getRetailerList(
                                    SystemAdminRetailerGetRequestVO.builder()
                                            .localeCode(wtbInfo.getLocaleCode())
                                            .modelId(wtbInfo.getModelId())
                                            .bizType(wtbInfo.getBizType())
                                            .build());

                    List<WtbRetailerResponseVO> wtbRetailerList =
                            new ArrayList<WtbRetailerResponseVO>();
                    for (SystemAdminRetailerGetResponseVO systemAdminRetailer :
                            systemAdminRetailerList) {
                        wtbRetailerList.add(systemAdminRetailer.toWtbRetailerVO());
                    }

                    wtbInfo.setBuyOnlineList(wtbRetailerList);
                }
                case "price_spider" -> {
                    // [BuyOnlineType] price_spider (CA_EN,CA_FR)
                    String WTB_PRICE_SPIDER_URL =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_price_spider_url")
                                                    .build()),
                                    "");
                    wtbInfo.setOnlineAreaUrl(WTB_PRICE_SPIDER_URL);
                }
                case "lgtrdata" -> {
                    // [BuyOnlineType] lgtrdata
                    String WTB_LGTRDATA_URL =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_lgtrdata_url")
                                                    .build()),
                                    "");

                    if (wtbInfo.getModelEanCode() == null || "".equals(wtbInfo.getModelEanCode())) {
                        wtbInfo.setOnlineAreaUrl(WTB_LGTRDATA_URL.replace("{eanCode}", ""));
                    } else {
                        wtbInfo.setOnlineAreaUrl(
                                WTB_LGTRDATA_URL
                                        .replace("{eanCode}", wtbInfo.getModelEanCode())
                                        .replace("{modelNo}", wtbInfo.getModelName()));
                    }
                }
                case "commerce" -> {
                    // Commerce Connector(NL)
                    String WTB_COMMERCE_CONNECTOR =
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("wtb_commerce_connector")
                                                    .build()),
                                    "");

                    wtbInfo.setOnlineAreaUrl(WTB_COMMERCE_CONNECTOR);
                }
                default -> {
                    // [BuyOnlineType] None
                }
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }
    }

    /**
     * Online, Offline Store 에 제품 존재 여부 (Y/N)
     *
     * @param wtbInfo - result setting
     */
    public void getOnOffStoreYn(WtbInitResponseVO wtbInfo) {
        String localeCode = wtbInfo.getLocaleCode();
        String onStoreYn = "";
        String offStoreYn = "";

        String onlineType = wtbInfo.getWtbSetting().getWtbUseFlag();
        // ZA는 특정카테고리의 속한 모델인 경우 lgcom 데이터 사용 그외엔 channel_ad 3rd party 사용
        if ("channel_ad".equals(onlineType)) {
            // 3rd party이기 때문에 default Y
            onStoreYn = "Y";
            if ("ZA".equals(localeCode)) {
                String ZA_BUYONLINE_LGCOM_CATEGORY =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("za_buyonline_lgcom_category")
                                                .build()),
                                "");
                if (!"".equals(ZA_BUYONLINE_LGCOM_CATEGORY)
                        && ZA_BUYONLINE_LGCOM_CATEGORY.contains(wtbInfo.getWtbCateId())) {
                    List<SystemAdminRetailerGetResponseVO> systemAdminRetailerList =
                            systemAdminWhereToBuyClient.getRetailerList(
                                    SystemAdminRetailerGetRequestVO.builder()
                                            .localeCode(wtbInfo.getLocaleCode())
                                            .modelId(wtbInfo.getModelId())
                                            .bizType(wtbInfo.getBizType())
                                            .build());

                    // lg.com을 사용하는 category에 속해있으며,online Store정보가 없는 경우에만 'N'
                    if (systemAdminRetailerList == null || systemAdminRetailerList.size() <= 0) {
                        onStoreYn = "N";
                    }
                }
            }
        } else if ("lgcom".equals(onlineType)) {
            List<SystemAdminRetailerGetResponseVO> systemAdminRetailerList =
                    systemAdminWhereToBuyClient.getRetailerList(
                            SystemAdminRetailerGetRequestVO.builder()
                                    .localeCode(wtbInfo.getLocaleCode())
                                    .modelId(wtbInfo.getModelId())
                                    .bizType(wtbInfo.getBizType())
                                    .build());

            if (systemAdminRetailerList.size() > 0) {
                onStoreYn = "Y";
            } else {
                onStoreYn = "N";
            }
        } else if ("lgcom_cn".equals(onlineType)) {
            String WTB_CN_SITE_NAME =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("wtb_cn_site_name")
                                            .build()),
                            "京东,天猫,苏宁");
            String[] siteName = WTB_CN_SITE_NAME.split(",");

            List<SystemAdminRetailerCnGetResponseVO> systemAdminRetailerCnList =
                    systemAdminWhereToBuyClient.getRetailerCnList(
                            SystemAdminRetailerCnGetRequestVO.builder()
                                    .localeCode(localeCode)
                                    .modelId(wtbInfo.getModelId())
                                    .siteName(siteName)
                                    .build());

            if (systemAdminRetailerCnList.size() > 0) {
                onStoreYn = "Y";
            } else {
                onStoreYn = "N";
            }
        } else if ("none".equals(onlineType) || "".equals(onlineType) || onlineType == null) {
            onStoreYn = "N";
        } else {
            onStoreYn = "Y";
        }

        String offlineType = wtbInfo.getWtbSetting().getWtbUseFlag();

        if ("none".equals(offlineType) || "".equals(offlineType) || offlineType == null) {
            offStoreYn = "N";
        } else {
            List<SystemPdsWtbOfflineRetailerGetResponseVO> systemPdsOfflineRetailerList =
                    systemPdsWhereToBuyClient.getOfflineRetailerList(
                            SystemPdsWtbOfflineRetailerGetRequestVO.builder()
                                    .siteCode(localeCode)
                                    .pdpId(wtbInfo.getModelId())
                                    .bizTypeCode(wtbInfo.getBizType())
                                    .build());

            if (systemPdsOfflineRetailerList.size() > 0) {
                offStoreYn = "Y";
            } else {
                offStoreYn = "N";
            }
        }

        if ("N".equals(onStoreYn) && "N".equals(offStoreYn)) {
            wtbInfo.setStoreYn("N");
        } else {
            wtbInfo.setStoreYn("Y");
        }
    }

    public List<WtbComboResponseVO> getComboInfo(Map<String, Object> param) {
        List<WtbComboResponseVO> comboList = new ArrayList<WtbComboResponseVO>();
        List<Map<String, Object>> countryList =
                (List<Map<String, Object>>) param.get("countryList"); // getComboInfo Country 수정
        Map<String, Object> tmpItem = new HashMap<String, Object>();
        String selectType = param.get("selectBoxType").toString();

        try {
            switch (selectType) {
                case "Country", "Country&Search" -> {
                    /* AE, AE_AR(Phase 5) / LEVANT_AR, LEVANT_EN(Phase 5) */
                    for (int i = 0; i < countryList.size(); i++) {
                        WtbComboResponseVO countryData = new WtbComboResponseVO();
                        countryData.setCode(countryList.get(i).get("countryCode").toString());
                        countryData.setValue(countryList.get(i).get("countryName").toString());
                        comboList.add(countryData);
                    }
                }
                case "City", "Province", "State", "TR_Province" -> {
                    /* EG_AR, EG_EN(Phase 5) / CN(Phase 4) / IN(Phase 2) */
                    List<SystemPdsWtbComboGetResponseVO> systemPdsWtbCombo =
                            systemPdsWhereToBuyClient.getWtbCombo(
                                    SystemPdsWtbComboGetRequestVO.builder()
                                            .siteCode(param.get("siteCode").toString())
                                            .selectBoxType(selectType)
                                            .build());

                    for (SystemPdsWtbComboGetResponseVO wtbCombo : systemPdsWtbCombo) {
                        comboList.add(wtbCombo.toWtbComboVO());
                    }
                }
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }
        return comboList;
    }

    /**
     * Channel Advisor URL 조회
     *
     * @param wtbInfo - 기본정보
     * @param confMap - 공통코드 조회 Map
     * @param keyPrefix - URL 등록된 conf_code
     * @return String - 조합한 iframe URL
     */
    public String getChannelAdvisorUrl(
            WtbInitResponseVO wtbInfo, Map<String, Object> confMap, String keyPrefix) {
        String result = "";

        List<String> paramList = new ArrayList<String>();
        try {
            for (String key : confMap.keySet()) {
                if (key.indexOf(keyPrefix) > -1
                        && !key.equals(keyPrefix)
                        && !key.equals(keyPrefix + "_modelInfo")) {
                    paramList.add(key);
                }
            }

            /** iframe URL 조회 */
            /*
             * exam) https://plrss-data.where-to-buy.co/feeds/plrss/v1/{modelInfo}
             * 대부분의 경우 URL 뒷부분에 modelName을 설정.
             * 간혹 modelId를 설정하는 국가 있음.(AU, BG, HR, HU, RS, UK)
             * */
            String modelInfo =
                    confMap.get(keyPrefix + "_modelInfo") == null
                            ? wtbInfo.getModelName()
                            : confMap.get(keyPrefix + "_modelInfo").toString();
            String iframeUrl = confMap.get(keyPrefix).toString().replace("{modelInfo}", modelInfo);

            if (iframeUrl.length() > 0) {
                StringBuffer sbUrl = new StringBuffer();
                sbUrl.append(iframeUrl);
                sbUrl.append("?");
                for (int i = 0; i < paramList.size(); i++) {
                    String key = paramList.get(i).replace(keyPrefix + "_", "");
                    String value = (String) confMap.get(paramList.get(i));
                    sbUrl.append(key);
                    sbUrl.append("=");
                    sbUrl.append(value);
                    if (i != paramList.size() - 1) {
                        sbUrl.append("&");
                    }
                }
                result = sbUrl.toString();
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }
        return result;
    }

    public Map<String, Object> getWtbTrComboOption(@Valid WtbTrComboRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String TYPE = requestVO.getType();
        final String SELECTED_STATE = requestVO.getSelectedState();

        Map<String, Object> wtbTrCombo = new HashMap<>();
        try {
            List<SystemPdsWtbStateGetResponseVO> SystemPdsWtbStateList = new ArrayList<>();
            if (TYPE.equals("state")) {
                SystemPdsWtbStateList =
                        systemPdsWhereToBuyClient.getWtbTrStateList(
                                SystemPdsWtbStateGetRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .selectedState(SELECTED_STATE)
                                        .build());
            }

            SystemPdsWtbStateGetResponseVO tmpItem = new SystemPdsWtbStateGetResponseVO();
            tmpItem.setCode("all");
            tmpItem.setValue("Tümü");
            SystemPdsWtbStateList.add(0, tmpItem);

            for (SystemPdsWtbStateGetResponseVO systemPdsWtbState : SystemPdsWtbStateList) {
                wtbTrCombo.put(systemPdsWtbState.getCode(), systemPdsWtbState.getValue());
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }

        return wtbTrCombo;
    }

    public WtbCnDistributorResponseVO getWtbCnDistributorList(WtbCnDistributorRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SUPER_CATEGORY = requestVO.getSuperCategory();
        final String CATEGORY = requestVO.getCategory();

        WtbCnDistributorResponseVO wtbDistributor = new WtbCnDistributorResponseVO();

        List<WtbCnDistributorsInfoResponseVO> wtbDistributorList =
                new ArrayList<WtbCnDistributorsInfoResponseVO>();

        try {
            int page = Integer.parseInt(requestVO.getPage());
            int pageCount = Integer.parseInt(requestVO.getPageCount());
            int startNo = util.getStartNo(page, pageCount);

            boolean brand =
                    Boolean.parseBoolean(StringUtils.defaultIfBlank(requestVO.getBrand(), "false"));
            if (brand && "1".equals(requestVO.getSort())) {
                List<SystemPdsKmProductResponseVO> SystemPdsKmProductList =
                        systemPdsSupportClient.getKmProductCodeList(
                                SystemPdsKmProductRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .superCategoryCode(SUPER_CATEGORY)
                                        .categoryCode(CATEGORY)
                                        .searchType("WKC")
                                        .build());

                if (SystemPdsKmProductList == null) {
                    SystemPdsKmProductList =
                            systemPdsSupportClient.getKmProductCodeList(
                                    SystemPdsKmProductRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .superCategoryCode(SUPER_CATEGORY)
                                            .categoryCode(CATEGORY)
                                            .searchType("WKCC")
                                            .build());
                }

                List<String> productCds = new ArrayList<>();
                if (SystemPdsKmProductList != null) {
                    for (SystemPdsKmProductResponseVO SystemPdsKmProduct : SystemPdsKmProductList) {
                        productCds.add(SystemPdsKmProduct.getKmProductCode());
                    }
                } else {
                    if (!"all".equals(SUPER_CATEGORY)) {
                        productCds.add("ZZZ");
                    }
                }
                // TODO SOHYUN : SVD_ASC_M 테이블 생성되면 만들것
                // wtbData = whereToBuyMapper.retrieveASCListAll(input);
            } else {
                List<SystemPdsWtbCnDistributorGetResponseVO> SystemPdsWtbCnDistributorList =
                        systemPdsWhereToBuyClient.getWtbCnDistributorList(
                                SystemPdsWtbCnDistributorGetRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .startNo(startNo)
                                        .pageCount(pageCount)
                                        .superCategory(SUPER_CATEGORY)
                                        .category(CATEGORY)
                                        .region(requestVO.getRegion())
                                        .city(requestVO.getCity())
                                        .brand(brand)
                                        .build());

                for (SystemPdsWtbCnDistributorGetResponseVO SystemPdsWtbCnDistributor :
                        SystemPdsWtbCnDistributorList) {
                    wtbDistributorList.add(SystemPdsWtbCnDistributor.toWtbCnDistributorVO());
                }
            }

            wtbDistributor.setDistributors(wtbDistributorList);

            int totalCnt = 0;
            // 게시물 총 개수 설정
            if (ObjectUtils.isNotEmpty(wtbDistributorList)) {
                totalCnt = wtbDistributorList.get(0).getTotal();
            }
            // paging 설정
            wtbDistributor.setPageInfo(
                    PagingUtil.getPagingInfoForAEM(
                            PageInfoAEMRequestVO.builder()
                                    .page(page)
                                    .pageCount(pageCount)
                                    .total(totalCnt)
                                    .build(),
                            PageInfoResultVO.builder()
                                    .pageNumber(page)
                                    .pageSize(pageCount)
                                    .totalCount((long) totalCnt)
                                    .build()));
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return wtbDistributor;
    }

    public Map<String, Object> getWtbCnComboOption(@Valid WtbCnComboRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String TYPE = requestVO.getType();
        final String REGION =
                StringUtils.defaultIfBlank(requestVO.getRegion(), CommonConstants.NULL);
        final String SUPER_CATEGORY =
                StringUtils.defaultIfBlank(requestVO.getSuperCategory(), CommonConstants.NULL);

        if (TYPE.equals("state") && StringUtils.isEmpty(REGION)) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        if (TYPE.equals("category") && StringUtils.isEmpty(SUPER_CATEGORY)) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        Map<String, Object> wtbCnCombo = new HashMap<>();
        try {
            if (TYPE.equals("state")) {
                // TODO SOHYUN : SVD_ASC_M 테이블 생성되면 만들것
                // list = whereToBuyMapper.retrieveWtbStateList(input);
            } else {
                String CATEGORY_STANDARD_FLAG =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("category_standard_flag")
                                                .build()),
                                CommonConstants.NO_FLAG);

                List<SystemPdsWtbCategoryListGetResponseVO> SystemPdsWtbComboList =
                        systemPdsWhereToBuyClient.getCategoryList(
                                SystemPdsWtbCategoryListGetRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .bizTypeCode("B2C")
                                        .type(TYPE)
                                        .superCategory(SUPER_CATEGORY)
                                        .categoryLvNo(
                                                CommonConstants.YES_FLAG.equals(
                                                                CATEGORY_STANDARD_FLAG)
                                                        ? 3
                                                        : 2)
                                        .build());

                SystemPdsWtbCategoryListGetResponseVO tmpItem =
                        new SystemPdsWtbCategoryListGetResponseVO();
                tmpItem.setCode("all");
                tmpItem.setValue("Tümü");
                SystemPdsWtbComboList.add(0, tmpItem);

                for (SystemPdsWtbCategoryListGetResponseVO systemPdsWtbCombo :
                        SystemPdsWtbComboList) {
                    wtbCnCombo.put(systemPdsWtbCombo.getCode(), systemPdsWtbCombo.getValue());
                }
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }

        return wtbCnCombo;
    }

    public Map<String, Object> getWtbOnlineDistributorList(
            @Valid WtbOnlineDistributorRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String PDP_ID = requestVO.getModelId();

        WtbInitResponseVO wtbInfo = new WtbInitResponseVO();
        JsonObject result = new JsonObject();
        Map<String, Object> returnMap = new HashMap<>();
        Map<String, Object> errorInput = new HashMap<String, Object>();
        errorInput.put("modelId", PDP_ID);

        try {
            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.ofComSysConfig("category_standard_flag")),
                            CommonConstants.NO_FLAG);

            String localeCode =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil
                                    .getComLocaleCode(
                                            CachedCodeRequestVO.builder()
                                                    .siteCode(SITE_CODE)
                                                    .build())
                                    .getLocaleCode(),
                            "");

            SystemPdsPdpResponseVO systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .pdpId(PDP_ID)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .standardFlag(CATEGORY_STANDARD_FLAG)
                                    .build());

            WtbInitRequestVO wtbInforequestVO = new WtbInitRequestVO();
            wtbInforequestVO.setSku(systemPdspdpResponseVO.getLgcomSkuId());
            wtbInfo = getWtbInit(wtbInforequestVO);

            String FINDONLINE_ACCESS_GET_URL =
                    StringUtils.defaultIfEmpty(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.ofComSysConfig(
                                                    "findonline_access_get_url")),
                                    "")
                            .replace("{modelName}", wtbInfo.getModelName());

            String FINDONLINE_ACCESS_TOKEN =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.ofComSysConfig("findonline_access_token")),
                            "");

            String FINDONLINE_CONNECTION_TIMEOUT =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.ofComSysConfig(
                                            "findonline_connection_timeout")),
                            "10000");

            String DEFAULT_WTB_COMPONENTID =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.ofComSysConfig("default_wtb_componentid")),
                            "PD0016");

            String COMPONENT_ID =
                    StringUtils.defaultIfEmpty(requestVO.getComponentId(), DEFAULT_WTB_COMPONENTID);
            int defaultTimeout = Integer.parseInt(FINDONLINE_CONNECTION_TIMEOUT);

            String COMPONENT_FIND_ONLINE_IN_STOCK =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            "component-findOnlineInStock",
                                            CommonConstants.SHOP_CODE_D2C)),
                            "Em Estoque");

            String COMPONENT_BUY_NOW =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            "component-buyNow", CommonConstants.SHOP_CODE_D2C)),
                            "comprar agora");

            String COMPONENT_FIND_ONLINE_VISIT_WEBSITE =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            "component-findOnlineVisitWebsite",
                                            CommonConstants.SHOP_CODE_D2C)),
                            "Indisponível no momento");

            String COMPONENT_FIND_ONLINE_ACCESS =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            "component-findOnlineAccess",
                                            CommonConstants.SHOP_CODE_D2C)),
                            "Visite o Site");

            Map<String, Object> messageMap = new HashMap<String, Object>();
            messageMap.put("availableTrueText", COMPONENT_FIND_ONLINE_IN_STOCK);
            messageMap.put("availableTrueBtnName", COMPONENT_BUY_NOW);
            messageMap.put("availableFalseText", COMPONENT_FIND_ONLINE_VISIT_WEBSITE);
            messageMap.put("availableFalseBtnName", COMPONENT_FIND_ONLINE_ACCESS);

            CloseableHttpClient client = HttpClientBuilder.create().build();
            HttpGet getRequest = new HttpGet(FINDONLINE_ACCESS_GET_URL);

            /** Header Settings */
            getRequest.addHeader("Authorization", FINDONLINE_ACCESS_TOKEN);

            /** defaultTimeout Settings */
            RequestConfig config =
                    RequestConfig.custom()
                            .setConnectTimeout(defaultTimeout)
                            .setConnectionRequestTimeout(defaultTimeout)
                            .build();
            getRequest.setConfig(config);
            CloseableHttpResponse response = client.execute(getRequest);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200 || statusCode == 201) {
                ResponseHandler<String> handler = new BasicResponseHandler();
                String body = handler.handleResponse(response);

                if ("".equals(body) || body == null) {
                    errorInput.put("componentId", COMPONENT_ID);
                    errorInput.put("localeCode", SITE_CODE);
                    errorInput.put("errorReport", result);
                } else {
                    result = JsonParser.parseString(body).getAsJsonObject();

                    returnMap =
                            new Gson()
                                    .fromJson(
                                            body,
                                            new TypeToken<Map<String, Object>>() {}.getType());

                    returnMap.put("availableMessge", messageMap);
                }
            } else {
                String json = EntityUtils.toString(response.getEntity(), "UTF-8");
                result = JsonParser.parseString(json).getAsJsonObject();

                errorInput.put(
                        "componentId", StringUtils.defaultIfEmpty(requestVO.getComponentId(), ""));
                errorInput.put("localeCode", SITE_CODE);
                errorInput.put("errorReport", Objects.toString(result.get("message"), json));
            }

            client.close();
            response.close();

        } catch (Exception e) {
            errorInput.put(
                    "componentId", StringUtils.defaultIfEmpty(requestVO.getComponentId(), ""));
            errorInput.put("localeCode", SITE_CODE);
            errorInput.put("errorReport", e);

            throw new D2xBusinessException(e);
        } finally {
            // HttpCall의 상태코드가 200,201이 아니거나 Exception이 발생하였을 때 메일send
            if (wtbInfo.getModelName() != null && wtbInfo.getModelUrlPath() != null) {
                if (ObjectUtils.isNotEmpty(errorInput.get("errorReport"))) {
                    sendEmail(wtbInfo, errorInput);
                }
            } else {
                returnMap.put("message", "No where to buy Data");
                returnMap.put("inputModelId", PDP_ID);
                return returnMap;
            }
        }

        return returnMap;
    }

    public void sendEmail(WtbInitResponseVO wtbInfo, Map<String, Object> input) {
        String localeCode = (String) input.get("localeCode");

        /** ##. 공통코드 조회 */
        String FINDONLINE_ERROR_RECIEVER_LIST =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig(
                                        "findonline_error_reciever_list")),
                        "");

        String FINDONLINE_MAIL_SUBJECT =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("findonline_mail_subject")),
                        "");

        // mail전송을 위한 변수 선언
        String emailAddress = FINDONLINE_ERROR_RECIEVER_LIST;
        String errorModelId = "";
        if (wtbInfo.getModelName() != null) {
            errorModelId = wtbInfo.getModelName();
        } else {
            errorModelId = StringUtils.defaultIfEmpty(input.get("modelId").toString(), "");
        }
        String emailSubject = FINDONLINE_MAIL_SUBJECT.replace("{modelName}", errorModelId);

        // MKT_MESSAGE_M 조회(이메일 컬럼에 들어갈 다국어 처리를 위함) + default이메일 조회
        String FINDONLINE_MAIL_CONTENT_FIRST =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "findonline_mail_content_first",
                                        CommonConstants.SHOP_CODE_D2C)),
                        "O produto não possui lojas cadastradas!");

        String FINDONLINE_MAIL_CONTENT_SECONDE =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "findonline_mail_content_seconde",
                                        CommonConstants.SHOP_CODE_D2C)),
                        "Descrição: ");

        String FINDONLINE_MAIL_CONTENT_THIRD =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "findonline_mail_content_third",
                                        CommonConstants.SHOP_CODE_D2C)),
                        "Intellibrand retornou uma lista de revendedores vazia para o produto: ");

        String FINDONLINE_MAIL_CONTENT_FORTH =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "FINDONLINE_MAIL_CONTENT_FORTH",
                                        CommonConstants.SHOP_CODE_D2C)),
                        "URL do produto: ");

        String FINDONLINE_MAIL_CONTENT_FIFTH =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "findonline_mail_content_fifth",
                                        CommonConstants.SHOP_CODE_D2C)),
                        "ID do Erro: ");

        String mailTitle =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "findonline_mail_subject", CommonConstants.SHOP_CODE_D2C)),
                        "LG.com Online WTB API Integration: Empty List");

        LocalDate date = LocalDate.now();
        int year = date.getYear();
        String defaultCopyright =
                "Copyright © 2009-{now} LG Electronics. All Rights Reserved"
                        .replace("{now}", Integer.toString(year));
        String copyright =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "mylg-send-mail-copyright",
                                        CommonConstants.SHOP_CODE_D2C,
                                        localeCode)),
                        defaultCopyright);

        String productCode = wtbInfo.getModelName();
        String productUrl = wtbInfo.getModelUrlPath();
        String componentId = StringUtils.defaultIfEmpty(input.get("componentId").toString(), "()");
        String errorReport = StringUtils.defaultIfEmpty(input.get("errorReport").toString(), "");

        // emailValues Setting
        Map<String, String> emailValues = new HashMap<>();
        emailValues.put("frontUrl", serverUrl);
        emailValues.put("localeCode", localeCode);
        emailValues.put("subject", emailSubject);
        emailValues.put("contentFirst", FINDONLINE_MAIL_CONTENT_FIRST);
        emailValues.put("contentSecond", FINDONLINE_MAIL_CONTENT_SECONDE);
        emailValues.put("contentThird", FINDONLINE_MAIL_CONTENT_THIRD);
        emailValues.put("contentForth", FINDONLINE_MAIL_CONTENT_FORTH);
        emailValues.put("contentFifth", FINDONLINE_MAIL_CONTENT_FIFTH);
        emailValues.put("productCode", productCode);
        emailValues.put("productUrl", productUrl);
        emailValues.put("componentId", componentId);
        emailValues.put("errorReport", errorReport);
        emailValues.put("copyright", copyright);

        if (emailAddress != null && !"".equals(emailAddress)) {
            String[] recieverArray = emailAddress.split(",");

            for (String reciever : recieverArray) {
                emailService.send(
                        mailTitle,
                        reciever.trim(),
                        "email-template-intellibrand-findonline",
                        emailValues);
            }
        }
    }

    public WtbBrandShopResponseVO getWtbBrandShop() {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        WtbBrandShopResponseVO responseVO = new WtbBrandShopResponseVO();
        Map<String, Object> input = new HashMap<>();
        input.put("brand", true);
        input.put("siteCode", SITE_CODE);

        try {
            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("category_standard_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String WTB_SELECTBOX_TYPE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("wtb_selectbox_type")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String WTB_SCROLL_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("wtb_scroll_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String WTB_TITLE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("wtb_title_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String BRANDSHOP_IMPROVING_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("BRANDSHOP_IMPROVING_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String MULTI_BRANDSHOP_IMPROVING_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("MULTI_BRANDSHOP_IMPROVING_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            responseVO.setWtbScrollFlag(WTB_SCROLL_FLAG);
            responseVO.setWtbTitleFlag(WTB_TITLE_FLAG);
            responseVO.setBrandShopImprovingFlag(BRANDSHOP_IMPROVING_FLAG);
            responseVO.setMultiBrandShopImprovingFlag(MULTI_BRANDSHOP_IMPROVING_FLAG);

            SystemPdsWtbSettingResponseVO SystemPdsWtbSetting =
                    systemPdsWhereToBuyClient.getWtbSettingInfo(
                            SystemPdsWtbSettingRequestVO.builder().siteCode(SITE_CODE).build());
            responseVO.setWtbSetting(SystemPdsWtbSetting.toWtbSettingVO());

            if (SystemPdsWtbSetting != null) {
                String WTB_BRANDSHOP_COUNTRY_USE =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("wtb_brandshop_country_use")
                                                .build()),
                                CommonConstants.NO_FLAG);

                /** ##.City SelectBox 목록 조회 */
                if ("Y".equals(SystemPdsWtbSetting.getCountryComboFlag())
                        || "Y".equals(WTB_BRANDSHOP_COUNTRY_USE)) {
                    input.put("selectBoxType", WTB_SELECTBOX_TYPE);
                    responseVO.setSelectBoxType(WTB_SELECTBOX_TYPE);
                    responseVO.setSelectBoxList(getComboInfo(input));
                }

                if ("CN".equals(SITE_CODE)) {
                    List<Map<String, Object>> superCateList = new ArrayList<Map<String, Object>>();
                    List<SystemPdsWtbCategoryListGetResponseVO> systemWtbSuperCateList =
                            systemPdsWhereToBuyClient.getCategoryList(
                                    SystemPdsWtbCategoryListGetRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .type("supercategory")
                                            .categoryLvNo(
                                                    CommonConstants.YES_FLAG.equals(
                                                                    CATEGORY_STANDARD_FLAG)
                                                            ? 2
                                                            : 1)
                                            .build());

                    for (SystemPdsWtbCategoryListGetResponseVO systemWtbSuperCategory :
                            systemWtbSuperCateList) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("code", systemWtbSuperCategory.getCode());
                        map.put("value", systemWtbSuperCategory.getValue());
                        superCateList.add(map);
                    }

                    Map<String, Object> tmpItem = new HashMap<String, Object>();
                    String strAll = "all";
                    tmpItem.put("code", strAll);
                    tmpItem.put("value", strAll);
                    superCateList.add(0, tmpItem);

                    responseVO.setSuperCategoryList(superCateList);
                }

                if ("TR".equals(SITE_CODE)) {
                    List<Map<String, Object>> cateList = new ArrayList<Map<String, Object>>();
                    List<SystemPdsWtbCategoryListGetResponseVO> systemWtbCateList =
                            systemPdsWhereToBuyClient.getCategoryList(
                                    SystemPdsWtbCategoryListGetRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .categoryLvNo(
                                                    CommonConstants.YES_FLAG.equals(
                                                                    CATEGORY_STANDARD_FLAG)
                                                            ? 2
                                                            : 1)
                                            .build());

                    for (SystemPdsWtbCategoryListGetResponseVO systemWtbCategory :
                            systemWtbCateList) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("code", systemWtbCategory.getCode());
                        map.put("value", systemWtbCategory.getValue());
                        cateList.add(map);
                    }
                    responseVO.setCategoryList(cateList);
                }
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }

        return responseVO;
    }

    public WtbStoreCategoryResponseVO getStoreCategoryList() {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        WtbStoreCategoryResponseVO responseVO = new WtbStoreCategoryResponseVO();

        try {
            List<SystemPdsWtbStoreCategoryResponseVO> SystemPdsWtbStoreCategoryList =
                    systemPdsWhereToBuyClient.getWtbStoreCategoryList(
                            SystemPdsWtbStoreCategoryListRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .build());

            List<Map<String, Object>> storeCategoryList = new ArrayList<Map<String, Object>>();
            for (SystemPdsWtbStoreCategoryResponseVO systemPdsWtbStoreCategory :
                    SystemPdsWtbStoreCategoryList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("gubunVal", systemPdsWtbStoreCategory.getGubunVal());
                map.put("categoryId", systemPdsWtbStoreCategory.getCategoryId());
                map.put("categoryName", systemPdsWtbStoreCategory.getCategoryName());

                storeCategoryList.add(map);
            }
            responseVO.setStoreCategoryList(storeCategoryList);

            List<SystemPdsWtbStoreFilterResponseVO> SystemPdsWtbStoreFilterList =
                    systemPdsWhereToBuyClient.getWtbStoreFilterList();

            List<Map<String, Object>> storeFilterList = new ArrayList<Map<String, Object>>();
            for (SystemPdsWtbStoreFilterResponseVO systemPdsWtbStoreFilter :
                    SystemPdsWtbStoreFilterList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("filterText", systemPdsWtbStoreFilter.getFilterText());
                map.put("gubunVal", systemPdsWtbStoreFilter.getGubunVal());

                storeFilterList.add(map);
            }
            responseVO.setStoreFilterList(storeFilterList);

        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }

        return responseVO;
    }
}
