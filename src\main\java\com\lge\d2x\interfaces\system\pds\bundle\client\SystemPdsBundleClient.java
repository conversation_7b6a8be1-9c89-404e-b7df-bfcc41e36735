package com.lge.d2x.interfaces.system.pds.bundle.client;

import com.lge.d2x.interfaces.system.pds.bundle.model.SystemPdsBundleElInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.bundle.model.SystemPdsBundleElInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.bundle.model.SystemPdsBundleListRequestVO;
import com.lge.d2x.interfaces.system.pds.bundle.model.SystemPdsBundleListResponseVO;
import com.lge.d2x.interfaces.system.pds.bundle.url.SystemPdsBundleUrlConstants;
import com.lge.d2xfrm.client.configuration.D2xFeignClientConfiguration;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(
        name = "SystemPdsBundleClient",
        url = "${client.url.system.pds}",
        configuration = D2xFeignClientConfiguration.class)
public interface SystemPdsBundleClient {
    @GetMapping(value = SystemPdsBundleUrlConstants.GET_BUNDLE_V1_BUNDLE_LIST)
    List<SystemPdsBundleListResponseVO> getBundleList(
            @SpringQueryMap SystemPdsBundleListRequestVO requestVO);

    @GetMapping(value = SystemPdsBundleUrlConstants.GET_BUNDLE_V1_BUNDLE_EL_INFO)
    List<SystemPdsBundleElInfoResponseVO> getBundleElInfo(
            @SpringQueryMap SystemPdsBundleElInfoRequestVO requestVO);
}
