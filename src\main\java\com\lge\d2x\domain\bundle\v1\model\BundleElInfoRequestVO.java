package com.lge.d2x.domain.bundle.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BundleElInfoRequestVO {
    @NotBlank(message = "Missing required parameters")
    @Schema(description = "사이트 코드", example = "UK")
    private String siteCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "PDP ID", example = "**********")
    private String pdpId;
}
