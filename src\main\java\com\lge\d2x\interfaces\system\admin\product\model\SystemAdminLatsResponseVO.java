package com.lge.d2x.interfaces.system.admin.product.model;

import com.lge.d2x.domain.product.v1.model.ProductSummaryLatsResponseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SystemAdminLatsResponseVO {
    @Schema(description = "제품명")
    private String productNm;

    @Schema(description = "LATS URL")
    private String latsUrlPath;

    public ProductSummaryLatsResponseVO toVO() {
        return ProductSummaryLatsResponseVO.builder()
                .productNm(this.productNm)
                .latsUrlPath(this.latsUrlPath)
                .build();
    }
}
