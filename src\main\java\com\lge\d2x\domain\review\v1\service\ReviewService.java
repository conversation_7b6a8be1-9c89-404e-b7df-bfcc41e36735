package com.lge.d2x.domain.review.v1.service;

import com.lge.d2x.core.constants.CommonConstants;
import com.lge.d2x.core.constants.enums.ReviewStatusEnum;
import com.lge.d2x.core.util.Util;
import com.lge.d2x.domain.category.v1.model.ReviewCategoryInfoListResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewCategoryRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewCategoryResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewCommentAllRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewCommentAllResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewCommentCreateRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewCommentCreateResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewCommentDeleteRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewCommentDeleteResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewCommentResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewCreateRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewCreateResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewDeleteRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewDeleteResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewExistRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewExistResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewHelpfulUpdateRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewHelpfulUpdateResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewInfoResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewMainRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewMainResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewNicknameRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewNicknameResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewNicknameUpdateRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewNicknameUpdateResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewOverallRatingResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewUpdateRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewUpdateResponseVO;
import com.lge.d2x.domain.review.v1.model.ReviewWriteRequestVO;
import com.lge.d2x.domain.review.v1.model.ReviewWriteResponseVO;
import com.lge.d2x.domain.review.v1.model.SendEmailRequestVO;
import com.lge.d2x.interfaces.internal.account.client.InternalAccountAccountClient;
import com.lge.d2x.interfaces.internal.account.model.InternalAccountAccountPrivacyPolicyAgreementCreateRequestVO;
import com.lge.d2x.interfaces.system.admin.account.client.SystemAdminAccountClient;
import com.lge.d2x.interfaces.system.admin.account.model.SystemAdminAccountNicknameGetRequestVO;
import com.lge.d2x.interfaces.system.admin.account.model.SystemAdminAccountNicknameGetResponseVO;
import com.lge.d2x.interfaces.system.admin.account.model.SystemAdminAccountNicknameUpdateRequestVO;
import com.lge.d2x.interfaces.system.admin.review.client.SystemAdminReviewClient;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminCommentCreateRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminCommentDeleteRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewCommentUserCheckRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewCreateRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewDeleteRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewDuplicateGetRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewHelpfulUpdateRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewManagerRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewManagerResponseVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewNicknameDuplicateGetRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewRatingCreateRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewUpdateRequestVO;
import com.lge.d2x.interfaces.system.admin.review.model.SystemAdminReviewUserCheckRequestVO;
import com.lge.d2x.interfaces.system.pds.category.client.SystemPdsCategoryClient;
import com.lge.d2x.interfaces.system.pds.category.model.SystemPdsReviewCategoryRequestVO;
import com.lge.d2x.interfaces.system.pds.category.model.SystemPdsReviewCategoryResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.client.SystemPdsPdpInfoClient;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpCategoryRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpCategoryResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpSiblingRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpSiblingResponseVO;
import com.lge.d2x.interfaces.system.pds.product.client.SystemPdsProductClient;
import com.lge.d2x.interfaces.system.pds.product.model.SystemPdsSiblingFlagRequestVO;
import com.lge.d2x.interfaces.system.pds.review.client.SystemPdsReviewClient;
import com.lge.d2x.interfaces.system.pds.review.model.SystemPdsBVUploadFlagRequestVO;
import com.lge.d2x.interfaces.system.pds.review.model.SystemPdsCommentRequestVO;
import com.lge.d2x.interfaces.system.pds.review.model.SystemPdsCommentResponseVO;
import com.lge.d2x.interfaces.system.pds.review.model.SystemPdsOverallRatingRequestVO;
import com.lge.d2x.interfaces.system.pds.review.model.SystemPdsOverallRatingResponseVO;
import com.lge.d2x.interfaces.system.pds.review.model.SystemPdsReviewInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.review.model.SystemPdsReviewInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.review.model.SystemPdsWritableFlagRequestVO;
import com.lge.d2xfrm.configuration.context.TokenContextHolder;
import com.lge.d2xfrm.constants.CommonCodes;
import com.lge.d2xfrm.constants.enums.JobSeperateCodeEnum;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.CachedCodeRequestVO;
import com.lge.d2xfrm.model.common.CachedComLocaleCodeVO;
import com.lge.d2xfrm.model.common.CachedCommonCodeRequestVO;
import com.lge.d2xfrm.model.common.CachedPdpIdRequestVO;
import com.lge.d2xfrm.model.common.CachedPdpIdVO;
import com.lge.d2xfrm.model.paging.PageInfoAEMRequestVO;
import com.lge.d2xfrm.model.paging.PageInfoResultVO;
import com.lge.d2xfrm.model.sso.SessionUserVO;
import com.lge.d2xfrm.service.email.EmailService;
import com.lge.d2xfrm.util.common.CachedDataUtil;
import com.lge.d2xfrm.util.common.RequestHeaderUtil;
import com.lge.d2xfrm.util.page.PagingUtil;
import com.lge.d2xfrm.util.security.ShaUtil;
import com.lge.d2xfrm.util.sso.SessionUserUtil;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReviewService {
    private final InternalAccountAccountClient internalAccountAccountClient;
    private final SystemAdminReviewClient systemAdminReviewClient;
    private final SystemAdminAccountClient systemAdminAccountClient;
    private final SystemPdsCategoryClient systemPdsCategoryClient;
    private final SystemPdsProductClient systemPdsProductClient;
    private final SystemPdsPdpInfoClient systemPdsPdpInfoClient;
    private final SystemPdsReviewClient systemPdsReviewClient;
    private final CachedDataUtil cachedDataUtil;
    private final EmailService emailService;
    private final Util util;

    @Value("${application.globalPlatform.serverInfo.url}")
    private String serverUrl;

    // base date dataFormat
    public static final String BASE_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public ReviewCategoryResponseVO getReviewCategoryList(ReviewCategoryRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String BU_CODE = requestVO.getBuCode();
        String CATEGORY_CODE = StringUtils.defaultIfBlank(requestVO.getCategoryCode(), "");

        ReviewCategoryResponseVO reviewCategoryInfo = new ReviewCategoryResponseVO();

        try {
            String useCategoryYnCode = "review_use_category_yn";
            String useSkipEvaluationCode = "review_skip_evaluation_yn";
            if ("HE".equals(BU_CODE)) {
                useCategoryYnCode = "HE_review_use_category_yn";
                useSkipEvaluationCode = "HE_review_skip_evaluation_yn";
            }

            String USE_CATEGORY_CODE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode(useCategoryYnCode)
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.NO_FLAG);

            String USE_SKIP_EVALUATION_CODE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode(useSkipEvaluationCode)
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.NO_FLAG);

            List<SystemPdsReviewCategoryResponseVO> systemReviewCategoryList =
                    new ArrayList<SystemPdsReviewCategoryResponseVO>();
            List<ReviewCategoryInfoListResponseVO> reviewCategoryList =
                    new ArrayList<ReviewCategoryInfoListResponseVO>();

            if (CommonConstants.YES_FLAG.equals(USE_CATEGORY_CODE) && "".equals(CATEGORY_CODE)) {
                systemReviewCategoryList =
                        systemPdsCategoryClient.getReviewCategoryList(
                                SystemPdsReviewCategoryRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .buCode(BU_CODE)
                                        .build());
            } else {
                String searchCode = "";

                if ("Y".equals(USE_CATEGORY_CODE)) {
                    searchCode = "REVIEW_PAGE_CNT_FLT_Y";

                    systemReviewCategoryList =
                            systemPdsCategoryClient.getReviewCategoryList(
                                    SystemPdsReviewCategoryRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .buCode(BU_CODE)
                                            .build());
                } else {
                    searchCode = "REVIEW_PAGE_CNT_FLT_N";
                }
            }

            if (systemReviewCategoryList.size() > 0) {
                for (SystemPdsReviewCategoryResponseVO reviewCategory : systemReviewCategoryList) {
                    reviewCategoryList.add(reviewCategory.toReviewCategoryVO());
                }
            }

            String BUSINESS_REVIEW_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("BUSINESS_REVIEW_USE_FLAG")
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_TYPE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_TYPE")
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.NO_FLAG);

            if ("B2B".equals(StringUtils.defaultIfBlank(requestVO.getBizTypeCode(), "B2C"))
                    && "N".equals(BUSINESS_REVIEW_USE_FLAG)) {
                REVIEW_TYPE = "N";
            }

            reviewCategoryInfo.setReviewType(REVIEW_TYPE);
            reviewCategoryInfo.setCategoryInfoList(reviewCategoryList);
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return reviewCategoryInfo;
    }

    public ReviewResponseVO getReviewList(ReviewRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        ReviewResponseVO review = new ReviewResponseVO();

        try {
            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("category_standard_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_SIBLING_MODEL_TOTAL_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_SIBLING_MODEL_TOTAL_FLAG")
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_CONFIRM_SELECTIVE_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_SELECTIVE_USE_FLAG")
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.NO_FLAG);

            String LOGIN_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("LOGIN_USE_FLAG")
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.YES_FLAG);

            CachedComLocaleCodeVO localeInfo =
                    cachedDataUtil.getComLocaleCode(
                            CachedCodeRequestVO.builder().siteCode(SITE_CODE).build());

            String dateFormat =
                    StringUtils.defaultIfEmpty(localeInfo.getDdFormatCnts(), BASE_DATE_FORMAT);
            String timeZone = StringUtils.defaultIfEmpty(localeInfo.getTimezoneNm(), "");

            String token = RequestHeaderUtil.getAccessCode();
            TokenContextHolder.setToken(token);
            boolean isMembers = SessionUserUtil.isLogin();
            if (isMembers) {
                String customerNo = SessionUserUtil.getUserInfo().getUserId();
            }

            int page = Integer.parseInt(requestVO.getPage());
            int pageCount = Integer.parseInt(requestVO.getPageCount());
            int startNo = util.getStartNo(page, pageCount);

            String siblingFlag =
                    StringUtils.defaultIfBlank(
                            systemPdsProductClient.getSiblingFlag(
                                    SystemPdsSiblingFlagRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .pdpId(PDP_ID)
                                            .build()),
                            CommonConstants.NO_FLAG);

            ArrayList<String> siblingPdpIds = new ArrayList<>();
            if (CommonConstants.YES_FLAG.equals(siblingFlag)
                    && CommonConstants.YES_FLAG.equals(REVIEW_SIBLING_MODEL_TOTAL_FLAG)) {
                List<SystemPdsPdpSiblingResponseVO> sysPdsPdpSiblingResp =
                        systemPdsPdpInfoClient.getPdpSiblingDataList(
                                SystemPdsPdpSiblingRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(PDP_ID)
                                        .bizTypeCode(BIZ_TYPE_CODE)
                                        .standardFlag(CATEGORY_STANDARD_FLAG)
                                        .build());
                for (SystemPdsPdpSiblingResponseVO vo : sysPdsPdpSiblingResp) {
                    siblingPdpIds.add(vo.getPdpId());
                }
            }

            SystemPdsReviewInfoRequestVO reviewInfoVo = new SystemPdsReviewInfoRequestVO();
            reviewInfoVo.setSiteCode(SITE_CODE);
            reviewInfoVo.setPdpId(PDP_ID);
            reviewInfoVo.setBizTypeCode(BIZ_TYPE_CODE);
            reviewInfoVo.setStartNo(startNo);
            reviewInfoVo.setPageCount(pageCount);
            reviewInfoVo.setHasSibling(siblingFlag);
            reviewInfoVo.setSiblingModelTotal(REVIEW_SIBLING_MODEL_TOTAL_FLAG);
            reviewInfoVo.setSortBy(
                    StringUtils.defaultIfEmpty(requestVO.getSortBy(), CommonConstants.NULL));
            reviewInfoVo.setReviewId(
                    ObjectUtils.isEmpty(requestVO.getReviewId()) ? null : requestVO.getReviewId());
            reviewInfoVo.setSiblingPdpIds(siblingPdpIds);
            reviewInfoVo.setMostHelpful(requestVO.getMostHelpful());

            List<SystemPdsReviewInfoResponseVO> sysPdsReviewInfoResp =
                    systemPdsReviewClient.getReviewInfo(reviewInfoVo);

            String defaultNickName =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            "default-nickname", CommonConstants.SHOP_CODE_D2C)),
                            "");

            List<ReviewInfoResponseVO> reviewInfoList = new ArrayList<ReviewInfoResponseVO>();
            sysPdsReviewInfoResp.forEach(
                    (SystemPdsReviewInfoResponseVO reviewsVO) -> {
                        // 로케일 시간으로 변경
                        if (StringUtils.isNotEmpty(reviewsVO.getCreationDate())) {
                            String createDate =
                                    util.getTimeZoneDateTime(
                                                    reviewsVO.getCreationDate().replace(".0", ""),
                                                    BASE_DATE_FORMAT,
                                                    dateFormat,
                                                    timeZone)
                                            .toString();
                            reviewsVO.setCreationDate(createDate);
                        }

                        // 닉네임을 마스킹한다.
                        if (StringUtils.isNotEmpty(reviewsVO.getAuthorNicknameNm())
                                && !"".equals(defaultNickName)) {
                            reviewsVO.setAuthorNicknameNm(defaultNickName);
                        } else {
                            reviewsVO.setAuthorNicknameNm(
                                    util.getNickNameMasking(reviewsVO.getAuthorNicknameNm()));
                        }

                        reviewInfoList.add(reviewsVO.toReviewInfoVO());
                    });
            review.setReviewInfo(reviewInfoList);

            int totalCnt = 0;
            // 게시물 총 개수 설정
            if (!ObjectUtils.isEmpty(reviewInfoList)) {
                totalCnt = reviewInfoList.get(0).getTotal();
            }
            // paging 설정
            review.setPageInfo(
                    PagingUtil.getPagingInfoForAEM(
                            PageInfoAEMRequestVO.builder()
                                    .page(page)
                                    .pageCount(pageCount)
                                    .total(totalCnt)
                                    .build(),
                            PageInfoResultVO.builder()
                                    .pageNumber(page)
                                    .pageSize(pageCount)
                                    .totalCount((long) totalCnt)
                                    .build()));

            review.setReviewConfirmSelectiveUseFlag(REVIEW_CONFIRM_SELECTIVE_USE_FLAG);
            review.setLoginUseFlag(LOGIN_USE_FLAG);
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return review;
    }

    public ReviewCommentAllResponseVO getReviewCommentsAll(ReviewCommentAllRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        ReviewCommentAllResponseVO reviewCommentsAll = new ReviewCommentAllResponseVO();
        List<ReviewInfoResponseVO> reviewInfoList = new ArrayList<ReviewInfoResponseVO>();

        try {
            SystemPdsReviewInfoRequestVO reviewInfoVo = new SystemPdsReviewInfoRequestVO();

            String REVIEW_SIBLING_MODEL_TOTAL_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_SIBLING_MODEL_TOTAL_FLAG")
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_BV_DISCLAIMER_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("review_bv_disclaimer_flag")
                                            .siteCode(SITE_CODE)
                                            .build()),
                            CommonConstants.NO_FLAG);

            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("category_standard_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            CachedComLocaleCodeVO localeInfo =
                    cachedDataUtil.getComLocaleCode(
                            CachedCodeRequestVO.builder().siteCode(SITE_CODE).build());

            String dateFormat =
                    StringUtils.defaultIfEmpty(localeInfo.getDdFormatCnts(), BASE_DATE_FORMAT);
            String timeZone = StringUtils.defaultIfEmpty(localeInfo.getTimezoneNm(), "");

            String token = RequestHeaderUtil.getAccessCode();
            TokenContextHolder.setToken(token);
            boolean isMembers = SessionUserUtil.isLogin();
            String customerNo = "";
            if (isMembers) {
                customerNo = SessionUserUtil.getUserInfo().getUserId();
                reviewInfoVo.setMemberFlag(CommonConstants.YES_FLAG);
                reviewInfoVo.setCustomerNo(customerNo);
            }

            String siblingFlag =
                    StringUtils.defaultIfBlank(
                            systemPdsProductClient.getSiblingFlag(
                                    SystemPdsSiblingFlagRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .pdpId(PDP_ID)
                                            .build()),
                            CommonConstants.NO_FLAG);

            ArrayList<String> siblingPdpIds = new ArrayList<>();
            if (CommonConstants.YES_FLAG.equals(siblingFlag)
                    && CommonConstants.YES_FLAG.equals(REVIEW_SIBLING_MODEL_TOTAL_FLAG)) {
                List<SystemPdsPdpSiblingResponseVO> sysPdsPdpSiblingResp =
                        systemPdsPdpInfoClient.getPdpSiblingDataList(
                                SystemPdsPdpSiblingRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(PDP_ID)
                                        .bizTypeCode(BIZ_TYPE_CODE)
                                        .standardFlag(CATEGORY_STANDARD_FLAG)
                                        .build());
                for (SystemPdsPdpSiblingResponseVO vo : sysPdsPdpSiblingResp) {
                    siblingPdpIds.add(vo.getPdpId());
                }
            }

            int page = Integer.parseInt(requestVO.getPage());
            int pageCount = Integer.parseInt(requestVO.getPageCount());
            int startNo = util.getStartNo(page, pageCount);

            reviewInfoVo.setSiteCode(SITE_CODE);
            reviewInfoVo.setPdpId(PDP_ID);
            reviewInfoVo.setBizTypeCode(BIZ_TYPE_CODE);
            reviewInfoVo.setStartNo(startNo);
            reviewInfoVo.setPageCount(pageCount);
            reviewInfoVo.setHasSibling(siblingFlag);
            reviewInfoVo.setSiblingModelTotal(REVIEW_SIBLING_MODEL_TOTAL_FLAG);
            reviewInfoVo.setSortBy(
                    StringUtils.defaultIfEmpty(requestVO.getSortBy(), CommonConstants.NULL));
            reviewInfoVo.setMostHelpful(
                    StringUtils.defaultIfEmpty(requestVO.getMostHelpful(), CommonConstants.NULL));
            reviewInfoVo.setSiblingPdpIds(siblingPdpIds);

            String defaultNickName =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            "default-nickname", CommonConstants.SHOP_CODE_D2C)),
                            "");

            List<SystemPdsReviewInfoResponseVO> sysPdsReviewInfoResp =
                    systemPdsReviewClient.getReviewAll(reviewInfoVo);

            sysPdsReviewInfoResp.forEach(
                    (SystemPdsReviewInfoResponseVO reviewsVO) -> {
                        // 로케일 시간으로 변경
                        if (StringUtils.isNotEmpty(reviewsVO.getCreationDate())) {
                            String createDate =
                                    util.getTimeZoneDateTime(
                                                    reviewsVO.getCreationDate().replace(".0", ""),
                                                    BASE_DATE_FORMAT,
                                                    dateFormat,
                                                    timeZone)
                                            .toString();
                            reviewsVO.setCreationDate(createDate);
                        }

                        // 닉네임을 마스킹한다.
                        if (StringUtils.isNotEmpty(reviewsVO.getAuthorNicknameNm())
                                && !"".equals(defaultNickName)) {
                            reviewsVO.setAuthorNicknameNm(defaultNickName);
                        } else {
                            reviewsVO.setAuthorNicknameNm(
                                    util.getNickNameMasking(reviewsVO.getAuthorNicknameNm()));
                        }
                        reviewInfoList.add(reviewsVO.toReviewInfoVO());
                    });

            // 리뷰에 해당하는 코멘트를 가져온다.
            reviewInfoList.forEach(
                    (ReviewInfoResponseVO reviewsVO) -> {
                        Map<String, Object> commentMap = new HashMap<>();
                        commentMap.put("reviewId", reviewsVO.getReviewId());
                        commentMap.put("siteCode", SITE_CODE);
                        List<ReviewCommentResponseVO> reviewCommentList =
                                getReviewComment(commentMap);
                        reviewsVO.setCommentList(reviewCommentList);
                    });

            // review_status 중 'H', 'D', 'I' 와 use_flag의 "N" 에서 코멘트가 없는 리뷰는 포함되지 않는다.
            final List<ReviewInfoResponseVO> reviewFilterList =
                    reviewInfoList.stream()
                            .filter(
                                    (ReviewInfoResponseVO reviewsVO) ->
                                            !(("H".equals(reviewsVO.getReviewStatus())
                                                            || "D"
                                                                    .equals(
                                                                            reviewsVO
                                                                                    .getReviewStatus())
                                                            || "I"
                                                                    .equals(
                                                                            reviewsVO
                                                                                    .getReviewStatus())
                                                            || "N".equals(reviewsVO.getUseFlag()))
                                                    && CollectionUtils.isEmpty(
                                                            reviewsVO.getCommentList())))
                            .collect(Collectors.toList());

            reviewCommentsAll.setReviewList(reviewFilterList);

            int totalCnt = 0;
            // 게시물 총 개수 설정
            if (!CollectionUtils.isEmpty(reviewFilterList)) {
                totalCnt = reviewInfoList.get(0).getTotal();
            }
            // paging 설정
            reviewCommentsAll.setPageInfo(
                    PagingUtil.getPagingInfoForAEM(
                            PageInfoAEMRequestVO.builder()
                                    .page(page)
                                    .pageCount(pageCount)
                                    .total(totalCnt)
                                    .build(),
                            PageInfoResultVO.builder()
                                    .pageNumber(page)
                                    .pageSize(pageCount)
                                    .totalCount((long) totalCnt)
                                    .build()));

            String bvUploadFlag = "N";
            String bvDisclaimerMsg = "";
            if (CommonConstants.YES_FLAG.equals(REVIEW_BV_DISCLAIMER_FLAG)) {
                bvUploadFlag =
                        StringUtils.defaultIfEmpty(
                                systemPdsReviewClient.getBVUploadFlag(
                                        SystemPdsBVUploadFlagRequestVO.builder()
                                                .siteCode(SITE_CODE)
                                                .pdpId(PDP_ID)
                                                .bizTypeCode(BIZ_TYPE_CODE)
                                                .build()),
                                CommonConstants.NO_FLAG);

                if (CommonConstants.YES_FLAG.equals(bvUploadFlag)) {
                    bvDisclaimerMsg = "Features of products may vary by country.";
                }
            }

            reviewCommentsAll.setBvUploadFlag(bvUploadFlag);
            reviewCommentsAll.setBvDisclaimerMsg(bvDisclaimerMsg);
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return reviewCommentsAll;
    }

    public List<ReviewCommentResponseVO> getReviewComment(Map<String, Object> input) {
        List<ReviewCommentResponseVO> reviewCommentList = new ArrayList<ReviewCommentResponseVO>();

        try {
            String SITE_CODE = input.get("siteCode").toString();

            CachedComLocaleCodeVO localeInfo =
                    cachedDataUtil.getComLocaleCode(
                            CachedCodeRequestVO.builder().siteCode(SITE_CODE).build());

            String dateFormat =
                    StringUtils.defaultIfEmpty(localeInfo.getDdFormatCnts(), BASE_DATE_FORMAT);
            String timeZone = StringUtils.defaultIfEmpty(localeInfo.getTimezoneNm(), "");
            boolean isMembers = SessionUserUtil.isLogin();
            String customerNo = "";
            if (isMembers) {
                customerNo = SessionUserUtil.getUserInfo().getUserId();
            }

            // 해당 리뷰의 코멘트 목록을 가져온다.
            List<SystemPdsCommentResponseVO> systemPdsCommentList =
                    systemPdsReviewClient.getComments(
                            SystemPdsCommentRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .reviewId(Integer.parseInt(input.get("reviewId").toString()))
                                    .memberFlag(
                                            isMembers
                                                    ? CommonConstants.YES_FLAG
                                                    : CommonConstants.NO_FLAG)
                                    .customerNo(customerNo)
                                    .build());

            String defaultNickName =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            "default-nickname", CommonConstants.SHOP_CODE_D2C)),
                            "");

            systemPdsCommentList.forEach(
                    (SystemPdsCommentResponseVO commentsVO) -> {
                        // 로케일 시간으로 변경
                        if (StringUtils.isNotEmpty(commentsVO.getCreationDate())) {
                            String createDate =
                                    util.getTimeZoneDateTime(
                                                    commentsVO.getCreationDate().replace(".0", ""),
                                                    BASE_DATE_FORMAT,
                                                    dateFormat,
                                                    timeZone)
                                            .toString();
                            commentsVO.setCreationDate(createDate);
                        }

                        // 닉네임을 마스킹한다.
                        if (StringUtils.isNotEmpty(commentsVO.getAuthorNicknameNm())
                                && !"".equals(defaultNickName)) {
                            commentsVO.setAuthorNicknameNm(defaultNickName);
                        } else {
                            commentsVO.setAuthorNicknameNm(
                                    util.getNickNameMasking(commentsVO.getAuthorNicknameNm()));
                        }

                        reviewCommentList.add(commentsVO.toReviewCommentVO());
                    });
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return reviewCommentList;
    }

    public ReviewMainResponseVO getReviewMain(ReviewMainRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        ReviewMainResponseVO reviewMain = new ReviewMainResponseVO();

        try {
            String REVIEW_SIBLING_MODEL_TOTAL_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_SIBLING_MODEL_TOTAL_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_SORT_BY_INIT =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_SORT_BY_INIT")
                                            .build()),
                            "");

            String BUSINESS_REVIEW_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("BUSINESS_REVIEW_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_TYPE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder().keyCode("REVIEW_TYPE").build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_CONFIRM_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_CONFIRM_SELECTIVE_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_SELECTIVE_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_AGREEMENT_TYPE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_AGREEMENT_TYPE")
                                            .build()),
                            "");

            String LOGIN_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("LOGIN_USE_FLAG")
                                            .build()),
                            CommonConstants.YES_FLAG);

            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("category_standard_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String token = RequestHeaderUtil.getAccessCode();
            TokenContextHolder.setToken(token);
            boolean isMembers = SessionUserUtil.isLogin();
            if (isMembers) {
                SessionUserVO userInfo = SessionUserUtil.getUserInfo();
                Map<String, Object> userInfoMap = util.setUserInfoByParam(userInfo, null);
            }

            String siblingFlag =
                    StringUtils.defaultIfBlank(
                            systemPdsProductClient.getSiblingFlag(
                                    SystemPdsSiblingFlagRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .pdpId(PDP_ID)
                                            .build()),
                            CommonConstants.NO_FLAG);

            ArrayList<String> siblingPdpIds = new ArrayList<>();
            if (CommonConstants.YES_FLAG.equals(siblingFlag)
                    && CommonConstants.YES_FLAG.equals(REVIEW_SIBLING_MODEL_TOTAL_FLAG)) {
                List<SystemPdsPdpSiblingResponseVO> sysPdsPdpSiblingResp =
                        systemPdsPdpInfoClient.getPdpSiblingDataList(
                                SystemPdsPdpSiblingRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(PDP_ID)
                                        .bizTypeCode(BIZ_TYPE_CODE)
                                        .standardFlag(CATEGORY_STANDARD_FLAG)
                                        .build());
                for (SystemPdsPdpSiblingResponseVO vo : sysPdsPdpSiblingResp) {
                    siblingPdpIds.add(vo.getPdpId());
                }
            }

            // 별점 정보 조회
            SystemPdsOverallRatingResponseVO systemPdsOverallRating =
                    systemPdsReviewClient.getOverallRating(
                            SystemPdsOverallRatingRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .pdpId(PDP_ID)
                                    .siblingModelTotal(REVIEW_SIBLING_MODEL_TOTAL_FLAG)
                                    .hasSibling(siblingFlag)
                                    .siblingPdpIds(siblingPdpIds)
                                    .build());
            ReviewOverallRatingResponseVO overallRatings = new ReviewOverallRatingResponseVO();

            if (systemPdsOverallRating != null) {
                overallRatings = systemPdsOverallRating.toReviewOverallVO();

                if ("B2B".equals(BIZ_TYPE_CODE)
                        && !CommonConstants.YES_FLAG.equals(BUSINESS_REVIEW_USE_FLAG)) {
                    REVIEW_TYPE = "N";
                }

                overallRatings.setReviewType(REVIEW_TYPE);
                overallRatings.setLocaleCode(SITE_CODE);
                overallRatings.setBizType(BIZ_TYPE_CODE);
                overallRatings.setReviewConfirmUseFlag(REVIEW_CONFIRM_USE_FLAG);
                overallRatings.setReviewConfirmSelectiveUseFlag(REVIEW_CONFIRM_SELECTIVE_USE_FLAG);
                overallRatings.setAgreementType(REVIEW_AGREEMENT_TYPE);
                overallRatings.setLoginUseFlag(LOGIN_USE_FLAG);
            }

            reviewMain.setOverallRatingsVO(overallRatings);

            // 리뷰와 리뷰 코멘트 조회
            ReviewCommentAllResponseVO reviewsMap =
                    getReviewCommentsAll(
                            ReviewCommentAllRequestVO.builder()
                                    .sku(SKU)
                                    .sortBy(REVIEW_SORT_BY_INIT)
                                    .page(requestVO.getPage())
                                    .pageCount(requestVO.getPageCount())
                                    .build());
            reviewMain.setReviewList(reviewsMap.getReviewList());

            // 좋아요 순 정렬
            ReviewResponseVO mostHelpfulSortMap =
                    getReviewList(
                            ReviewRequestVO.builder()
                                    .sku(SKU)
                                    .sortBy("helpful")
                                    .page(requestVO.getPage())
                                    .pageCount(requestVO.getPageCount())
                                    .build());

            List<ReviewInfoResponseVO> mostHelpfulList = mostHelpfulSortMap.getReviewInfo();
            if (!CollectionUtils.isEmpty(mostHelpfulList)) {
                int count = 0;
                count = reviewsMap.getReviewList().get(0).getTotal();
                if (count > 1) {
                    reviewMain.setMostHelpfulList(mostHelpfulList.get(0));
                } else {
                    reviewMain.setMostHelpfulList(null);
                }
            } else {
                reviewMain.setMostHelpfulList(null);
            }

            // 별점 4개 이상
            ReviewResponseVO favorableMap =
                    getReviewList(
                            ReviewRequestVO.builder()
                                    .sku(SKU)
                                    .mostHelpful("favorable")
                                    .page(requestVO.getPage())
                                    .pageCount(requestVO.getPageCount())
                                    .build());

            // 별점 3개 이하
            ReviewResponseVO criticalMap =
                    getReviewList(
                            ReviewRequestVO.builder()
                                    .sku(SKU)
                                    .mostHelpful("critical")
                                    .page(requestVO.getPage())
                                    .pageCount(requestVO.getPageCount())
                                    .build());

            if (constraintsMostHelpful(favorableMap, criticalMap)) {
                reviewMain.setFavorableList(null);
                reviewMain.setCriticalList(null);
            } else {
                // 어느 한 쪽이 0건 이거나 다 건 일 경우가 있으며 get(0)은 NullPointerException을 유발한다.
                List<ReviewInfoResponseVO> favorableList = favorableMap.getReviewInfo();
                List<ReviewInfoResponseVO> criticalList = criticalMap.getReviewInfo();

                if (!CollectionUtils.isEmpty(favorableList)) {
                    reviewMain.setFavorableList(favorableList.get(0));
                } else {
                    reviewMain.setFavorableList(null);
                }

                if (!CollectionUtils.isEmpty(criticalList)) {
                    reviewMain.setCriticalList(criticalList.get(0));
                } else {
                    reviewMain.setCriticalList(null);
                }
            }

            List<Map<String, Object>> sortList = new ArrayList<>();
            Map<String, String> REVIEW_SORT_TYPE_MAP =
                    cachedDataUtil.getComCommonCodeValueMap(
                            CachedCommonCodeRequestVO.builder()
                                    .commonCode("REVIEW_SORT_TYPE")
                                    .jobSeparateCodeEnum(JobSeperateCodeEnum.D2C)
                                    .build());

            Set<String> keys = REVIEW_SORT_TYPE_MAP.keySet();
            for (String key : keys) {
                String value =
                        StringUtils.defaultIfEmpty(
                                cachedDataUtil.getComMessageCode(
                                        CachedCodeRequestVO.ofDspTypeComMessage(
                                                REVIEW_SORT_TYPE_MAP.get(key),
                                                CommonConstants.SHOP_CODE_D2C)),
                                REVIEW_SORT_TYPE_MAP.get(key));
                Map<String, Object> reviewSortTypeMap = new HashMap<>();
                reviewSortTypeMap.put("commonCodeId", key);
                reviewSortTypeMap.put("messageContent", value);
                sortList.add(reviewSortTypeMap);
            }
            reviewMain.setSortList(sortList);

            // paging 설정
            int page = Integer.parseInt(requestVO.getPage());
            int pageCount = Integer.parseInt(requestVO.getPageCount());
            int totalCnt = 0;
            // 게시물 총 개수 설정
            if (!ObjectUtils.isEmpty(reviewsMap.getReviewList())) {
                totalCnt = reviewsMap.getReviewList().get(0).getTotal();
            }
            reviewMain.setPageInfo(
                    PagingUtil.getPagingInfoForAEM(
                            PageInfoAEMRequestVO.builder()
                                    .page(page)
                                    .pageCount(pageCount)
                                    .total(totalCnt)
                                    .build(),
                            PageInfoResultVO.builder()
                                    .pageNumber(page)
                                    .pageSize(pageCount)
                                    .totalCount((long) totalCnt)
                                    .build()));

            reviewMain.setBvUploadFlag(reviewsMap.getBvUploadFlag());
            reviewMain.setBvDisclaimerMsg(reviewsMap.getBvDisclaimerMsg());
            reviewMain.setSortByinit(REVIEW_SORT_BY_INIT);
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return reviewMain;
    }

    private boolean constraintsMostHelpful(ReviewResponseVO favorable, ReviewResponseVO critical) {
        int fCnt, cCnt;

        fCnt =
                CollectionUtils.isEmpty(favorable.getReviewInfo())
                        ? 0
                        : favorable.getReviewInfo().get(0).getTotal();
        cCnt =
                CollectionUtils.isEmpty(critical.getReviewInfo())
                        ? 0
                        : critical.getReviewInfo().get(0).getTotal();

        if (fCnt == 0 && cCnt == 0) {
            return true;
        }

        if ((fCnt == 0 && cCnt == 1) || (fCnt == 1 && cCnt == 0)) {
            return true;
        }

        return false;
    }

    public ReviewWriteResponseVO getWriteReviewLimit(ReviewWriteRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();

        ReviewWriteResponseVO checkWriteAReview = new ReviewWriteResponseVO();

        try {
            if (isMembers
                    || (StringUtils.isNotEmpty(requestVO.getSessionId())
                            && StringUtils.isNotEmpty(requestVO.getNickname()))) {
                String customerNo = null;
                String sessionId = null;
                String nickname = null;

                if (isMembers) {
                    SessionUserVO userInfo = SessionUserUtil.getUserInfo();
                    Map<String, Object> userInfoMap = util.setUserInfoByParam(userInfo, null);
                    customerNo = userInfoMap.get("customerNo").toString();
                    checkWriteAReview.setUserInfoMap(userInfoMap);
                    checkWriteAReview.setNickNameFlag(
                            userInfoMap.get("nickNameUseFlag").toString());
                } else {
                    sessionId = requestVO.getSessionId();
                    nickname = requestVO.getNickname();
                }

                int isWritable =
                        systemPdsReviewClient.getWritableFlag(
                                SystemPdsWritableFlagRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .bizTypeCode(BIZ_TYPE_CODE)
                                        .pdpId(PDP_ID)
                                        .memberFlag(
                                                isMembers
                                                        ? CommonConstants.YES_FLAG
                                                        : CommonConstants.NO_FLAG)
                                        .customerNo(customerNo)
                                        .sessionId(sessionId)
                                        .nickname(nickname)
                                        .build());

                checkWriteAReview.setLoginCheck(isMembers);
                checkWriteAReview.setIsWritable(isWritable);
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return checkWriteAReview;
    }

    public ReviewCreateResponseVO createReview(ReviewCreateRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final String NICKNAME = requestVO.getNickname();
        final String PASSWORD = requestVO.getPassword();
        final String EMAIL = requestVO.getEmail();
        final String SESSION_ID = requestVO.getSessionId();
        final String POLICY_AGREE = requestVO.getPolicyAgree();
        final String EMAIL_AGREE = requestVO.getEmailAgree();
        final int RATING = requestVO.getRating();

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();

        String isNicDup = "nonDuplicate";
        int reviewId = 0;
        String nickname = NICKNAME;
        String email = EMAIL;
        String password = PASSWORD;
        String customerNo = "";

        if (!isMembers
                && (StringUtils.isEmpty(requestVO.getEmail())
                        || StringUtils.isEmpty(requestVO.getPassword())
                        || StringUtils.isEmpty(requestVO.getNickname())
                        || StringUtils.isEmpty(requestVO.getSessionId()))) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        try {
            String REVIEW_CONFIRM_USE_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_CONFIRM_SELECTIVE_USE_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_SELECTIVE_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_AGREEMENT_TYPE =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_AGREEMENT_TYPE")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String reviewStatus = ReviewStatusEnum.APPROVAL.value();
            if (CommonConstants.YES_FLAG.equals(REVIEW_CONFIRM_USE_FLAG)) {
                if (CommonConstants.YES_FLAG.equals(REVIEW_CONFIRM_SELECTIVE_USE_FLAG)) {
                    if (RATING <= 3) {
                        reviewStatus = ReviewStatusEnum.INACTIVE.value();
                    }
                } else {
                    reviewStatus = ReviewStatusEnum.INACTIVE.value();
                }
            }

            boolean isNotDuplicatedNickname = false;
            if (isMembers) {
                SessionUserVO userInfo = SessionUserUtil.getUserInfo();
                String userId = userInfo.getUserId();
                String userEmailAddr = userInfo.getEmailAddr();
                customerNo = userId;

                boolean isMemberNicknameDuplicate =
                        systemAdminReviewClient.getNicknameDuplicate(
                                SystemAdminReviewNicknameDuplicateGetRequestVO.builder()
                                        .memberFlag(
                                                isMembers
                                                        ? CommonConstants.YES_FLAG
                                                        : CommonConstants.NO_FLAG)
                                        .bizType(BIZ_TYPE_CODE)
                                        .localeCode(SITE_CODE)
                                        .modelId(PDP_ID)
                                        .customerNo(customerNo)
                                        .build());

                if (!isMemberNicknameDuplicate) {
                    isNotDuplicatedNickname = true;
                    String userName;
                    String firstName = StringUtils.defaultIfEmpty(userInfo.getFirstName(), "");
                    String lastName = StringUtils.defaultIfEmpty(userInfo.getLastName(), "");

                    if (!ObjectUtils.isEmpty(firstName) && !ObjectUtils.isEmpty(lastName)) {
                        userName = String.format("%s%s", firstName, lastName);
                    } else {
                        userName = userEmailAddr.substring(0, userEmailAddr.indexOf("@"));
                    }

                    String NICK_NAME_USE_FLAG =
                            StringUtils.defaultIfEmpty(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .siteCode(SITE_CODE)
                                                    .keyCode("nick_name_use_flag")
                                                    .build()),
                                    CommonConstants.NO_FLAG);

                    if (CommonConstants.YES_FLAG.equals(NICK_NAME_USE_FLAG) && NICKNAME.isBlank()) {
                        SystemAdminAccountNicknameGetResponseVO nicknameResponseVO =
                                systemAdminAccountClient.getNickname(
                                        SystemAdminAccountNicknameGetRequestVO.builder()
                                                .localeCode(SITE_CODE)
                                                .userId(userId)
                                                .customerNo(customerNo)
                                                .build());
                        nickname = nicknameResponseVO.getNickname();
                    }

                    if (CommonConstants.NO_FLAG.equals(NICK_NAME_USE_FLAG)) {
                        nickname = userName;
                    }
                    email = userEmailAddr;
                    password = "";
                } else {
                    isNicDup = "duplicate";
                }
            } else {
                boolean isNonMemberNicknameDuplicate =
                        systemAdminReviewClient.getNicknameDuplicate(
                                SystemAdminReviewNicknameDuplicateGetRequestVO.builder()
                                        .memberFlag(
                                                isMembers
                                                        ? CommonConstants.YES_FLAG
                                                        : CommonConstants.NO_FLAG)
                                        .bizType(BIZ_TYPE_CODE)
                                        .localeCode(SITE_CODE)
                                        .modelId(PDP_ID)
                                        .nickname(NICKNAME)
                                        .sessionId(SESSION_ID)
                                        .build());

                if (!isNonMemberNicknameDuplicate) {
                    isNotDuplicatedNickname = true;
                } else {
                    isNicDup = "duplicate";
                }

                if (ObjectUtils.isEmpty(password)) {
                    return ReviewCreateResponseVO.builder()
                            .reviewSave(CommonCodes.JSON_RESPONSE_ERROR_STATUS + ": " + "password")
                            .build();
                }

                if (ObjectUtils.isEmpty(email)) {
                    return ReviewCreateResponseVO.builder()
                            .reviewSave(CommonCodes.JSON_RESPONSE_ERROR_STATUS + ": " + "email")
                            .build();
                }

                password = ShaUtil.getSha512Code(PASSWORD);
                customerNo = SESSION_ID;
            }

            if (isNotDuplicatedNickname) {
                CachedComLocaleCodeVO localeInfo =
                        cachedDataUtil.getComLocaleCode(
                                CachedCodeRequestVO.builder().siteCode(SITE_CODE).build());

                String countryCode = localeInfo.getCountryCode();

                reviewId =
                        systemAdminReviewClient.createReview(
                                SystemAdminReviewCreateRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .countryCode(countryCode)
                                        .bizType(BIZ_TYPE_CODE)
                                        .modelId(PDP_ID)
                                        .rating(RATING)
                                        .title(requestVO.getTitle())
                                        .review(requestVO.getReview())
                                        .recommendFlag(requestVO.getRecommendFlag())
                                        .reviewStatus(reviewStatus)
                                        .memberFlag(
                                                isMembers
                                                        ? CommonConstants.YES_FLAG
                                                        : CommonConstants.NO_FLAG)
                                        .customerNo(customerNo)
                                        .nickname(nickname)
                                        .email(email)
                                        .password(password)
                                        .reviewConfirmUseFlag(REVIEW_CONFIRM_USE_FLAG)
                                        .reviewConfirmSelectiveUseFlag(
                                                REVIEW_CONFIRM_SELECTIVE_USE_FLAG)
                                        .policyAgree(POLICY_AGREE)
                                        .emailAgree(EMAIL_AGREE)
                                        .reviewAgreementType(REVIEW_AGREEMENT_TYPE)
                                        .build());

                if (!isMembers && "M".equals(REVIEW_AGREEMENT_TYPE)) {
                    String agreementFlag =
                            "on".equals(POLICY_AGREE)
                                    ? CommonConstants.YES_FLAG
                                    : CommonConstants.NO_FLAG;
                    String agreementFlag2 =
                            "on".equals(EMAIL_AGREE)
                                    ? CommonConstants.YES_FLAG
                                    : CommonConstants.NO_FLAG;

                    internalAccountAccountClient.createPrivacyPolicyAgreement(
                            InternalAccountAccountPrivacyPolicyAgreementCreateRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .pageName("Review")
                                    .countryCode(countryCode)
                                    .emailAddr(email)
                                    .agreementFlag(agreementFlag)
                                    .agreement2Flag(agreementFlag2)
                                    .reviewId(reviewId)
                                    .build());
                }

                if (reviewId > 0) {
                    if (CommonConstants.NO_FLAG.equals(REVIEW_CONFIRM_USE_FLAG)
                            && requestVO.getRating() < 3) {
                        String mktTestKey =
                                StringUtils.defaultIfEmpty(
                                        requestVO.getMktTestKey(), CommonConstants.NO_FLAG);

                        try {
                            sendEmailToAdmin(
                                    SendEmailRequestVO.builder()
                                            .modelId(PDP_ID)
                                            .localeCode(SITE_CODE)
                                            .rating(requestVO.getRating())
                                            .title(requestVO.getTitle())
                                            .review(requestVO.getReview())
                                            .mktTestKey(mktTestKey)
                                            .build());
                        } catch (Exception e) {
                            log.error("createReview email send error");
                        }
                    }
                }
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewCreateResponseVO.builder()
                .reviewSave(
                        reviewId > 0
                                ? CommonCodes.JSON_RESPONSE_SUCCESS_STATUS
                                : CommonCodes.JSON_RESPONSE_FAILURE_STATUS)
                .reviewId(String.valueOf(reviewId))
                .isNicDup(isNicDup)
                .build();
    }

    public ReviewUpdateResponseVO updateReview(ReviewUpdateRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final int REVIEW_ID = Integer.parseInt(requestVO.getReviewId());
        final String NICKNAME = StringUtils.defaultIfEmpty(requestVO.getNickname(), "");
        final String PASSWORD = StringUtils.defaultIfEmpty(requestVO.getPassword(), "");
        final int RATING = requestVO.getRating();

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();

        int updateCnt = -1;
        String permit = "";

        if (!isMembers
                && (StringUtils.isEmpty(requestVO.getPassword())
                        || StringUtils.isEmpty(requestVO.getNickname()))) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        String password = PASSWORD;
        String nickname = NICKNAME;
        try {
            String REVIEW_CONFIRM_USE_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_CONFIRM_SELECTIVE_USE_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_SELECTIVE_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String reviewStatus = ReviewStatusEnum.APPROVAL.value();
            if (CommonConstants.YES_FLAG.equals(REVIEW_CONFIRM_USE_FLAG)) {
                if (CommonConstants.YES_FLAG.equals(REVIEW_CONFIRM_SELECTIVE_USE_FLAG)) {
                    if (RATING <= 3) {
                        reviewStatus = ReviewStatusEnum.INACTIVE.value();
                    }
                } else {
                    reviewStatus = ReviewStatusEnum.INACTIVE.value();
                }
            }

            boolean isUpdate = false;
            String customerNo = "";
            if (isMembers) {
                SessionUserVO userInfo = SessionUserUtil.getUserInfo();
                String frmNickName = "";
                if (SITE_CODE.equals("JP")) {
                    frmNickName = StringUtils.defaultIfEmpty(requestVO.getFrmNickname(), "");
                }
                Map<String, Object> userInfoMap = util.setUserInfoByParam(userInfo, frmNickName);
                nickname = userInfoMap.get("nickname").toString();
                customerNo = userInfoMap.get("customerNo").toString();
                isUpdate = true;
            } else {
                if (ObjectUtils.isEmpty(password)) {
                    return ReviewUpdateResponseVO.builder()
                            .updateReview(
                                    CommonCodes.JSON_RESPONSE_ERROR_STATUS + ": " + "password")
                            .build();
                }

                password = ShaUtil.getSha512Code(PASSWORD);
                boolean userCheck =
                        systemAdminReviewClient.getReviewUserCheck(
                                SystemAdminReviewUserCheckRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .bizType(BIZ_TYPE_CODE)
                                        .modelId(PDP_ID)
                                        .reviewId(requestVO.getReviewId())
                                        .nickname(nickname)
                                        .password(password)
                                        .build());

                permit = userCheck ? "success" : "fail";
                isUpdate = userCheck ? true : false;
            }

            if (isUpdate) {
                updateCnt =
                        systemAdminReviewClient.updateReview(
                                SystemAdminReviewUpdateRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .bizType(BIZ_TYPE_CODE)
                                        .modelId(PDP_ID)
                                        .reviewId(REVIEW_ID)
                                        .rating(RATING)
                                        .title(requestVO.getTitle())
                                        .review(requestVO.getReview())
                                        .recommendFlag(requestVO.getRecommendFlag())
                                        .reviewStatus(reviewStatus)
                                        .memberFlag(
                                                isMembers
                                                        ? CommonConstants.YES_FLAG
                                                        : CommonConstants.NO_FLAG)
                                        .nickname(nickname)
                                        .password(password)
                                        .customerNo(customerNo)
                                        .build());
            }

            if (updateCnt > 0) {
                if (CommonConstants.NO_FLAG.equals(REVIEW_CONFIRM_USE_FLAG)
                        && requestVO.getRating() < 3) {
                    String mktTestKey =
                            StringUtils.defaultIfEmpty(
                                    requestVO.getMktTestKey(), CommonConstants.NO_FLAG);

                    try {
                        sendEmailToAdmin(
                                SendEmailRequestVO.builder()
                                        .modelId(PDP_ID)
                                        .localeCode(SITE_CODE)
                                        .rating(requestVO.getRating())
                                        .title(requestVO.getTitle())
                                        .review(requestVO.getReview())
                                        .mktTestKey(mktTestKey)
                                        .build());
                    } catch (Exception e) {
                        log.error("updateReview email send error");
                    }
                }
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewUpdateResponseVO.builder()
                .updateReview(
                        updateCnt > 0
                                ? CommonCodes.JSON_RESPONSE_SUCCESS_STATUS
                                : CommonCodes.JSON_RESPONSE_FAILURE_STATUS)
                .permit(permit)
                .build();
    }

    public ReviewDeleteResponseVO deleteReview(ReviewDeleteRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final int REVIEW_ID = Integer.parseInt(requestVO.getReviewId());
        final String NICKNAME = StringUtils.defaultIfEmpty(requestVO.getNickname(), "");
        final String PASSWORD = StringUtils.defaultIfEmpty(requestVO.getPassword(), "");

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();

        int deleteCnt = -1;
        String permit = "";

        if (!isMembers
                && (StringUtils.isEmpty(requestVO.getPassword())
                        || StringUtils.isEmpty(requestVO.getNickname()))) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        String password = PASSWORD;
        String nickname = NICKNAME;

        try {
            String REVIEW_AGREEMENT_TYPE =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_AGREEMENT_TYPE")
                                            .build()),
                            "");

            boolean isDelete = false;
            String customerNo = "";
            if (isMembers) {
                SessionUserVO userInfo = SessionUserUtil.getUserInfo();
                Map<String, Object> userInfoMap = util.setUserInfoByParam(userInfo, null);
                nickname = userInfoMap.get("nickname").toString();
                customerNo = userInfoMap.get("customerNo").toString();
                isDelete = true;
            } else {
                if (StringUtils.isEmpty(PASSWORD)) {
                    return ReviewDeleteResponseVO.builder()
                            .deleteReview(
                                    CommonCodes.JSON_RESPONSE_ERROR_STATUS + ": " + "password")
                            .build();
                }

                password = ShaUtil.getSha512Code(PASSWORD);
                boolean userCheck =
                        systemAdminReviewClient.getReviewUserCheck(
                                SystemAdminReviewUserCheckRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .bizType(BIZ_TYPE_CODE)
                                        .modelId(PDP_ID)
                                        .reviewId(requestVO.getReviewId())
                                        .nickname(NICKNAME)
                                        .password(password)
                                        .build());

                permit = userCheck ? "success" : "fail";
                isDelete = userCheck ? true : false;
            }

            if (isDelete) {
                deleteCnt =
                        systemAdminReviewClient.deleteReview(
                                SystemAdminReviewDeleteRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .bizType(BIZ_TYPE_CODE)
                                        .modelId(PDP_ID)
                                        .reviewId(REVIEW_ID)
                                        .memberFlag(
                                                isMembers
                                                        ? CommonConstants.YES_FLAG
                                                        : CommonConstants.NO_FLAG)
                                        .nickname(nickname)
                                        .password(password)
                                        .customerNo(customerNo)
                                        .build());
            }

            if (deleteCnt > 0) {
                if (REVIEW_AGREEMENT_TYPE.equals("M")) {
                    if (!isMembers & REVIEW_ID > 0) {
                        CachedComLocaleCodeVO localeInfo =
                                cachedDataUtil.getComLocaleCode(
                                        CachedCodeRequestVO.builder().siteCode(SITE_CODE).build());

                        String countryCode = localeInfo.getCountryCode();

                        internalAccountAccountClient.updatePrivacyPolicyAgreement(
                                InternalAccountAccountPrivacyPolicyAgreementCreateRequestVO
                                        .builder()
                                        .localeCode(SITE_CODE)
                                        .countryCode(countryCode)
                                        .reviewId(REVIEW_ID)
                                        .build());
                    }
                }

                systemAdminReviewClient.saveReviewRating(
                        SystemAdminReviewRatingCreateRequestVO.builder()
                                .nickname(nickname)
                                .localeCode(SITE_CODE)
                                .modelId(PDP_ID)
                                .bizType(BIZ_TYPE_CODE)
                                .build());
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewDeleteResponseVO.builder()
                .deleteReview(
                        deleteCnt > 0
                                ? CommonCodes.JSON_RESPONSE_SUCCESS_STATUS
                                : CommonCodes.JSON_RESPONSE_FAILURE_STATUS)
                .permit(permit)
                .build();
    }

    public ReviewCommentCreateResponseVO createReviewComment(
            ReviewCommentCreateRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final String NICKNAME = StringUtils.defaultIfEmpty(requestVO.getNickname(), "");
        final String PASSWORD = StringUtils.defaultIfEmpty(requestVO.getPassword(), "");
        final String EMAIL = StringUtils.defaultIfEmpty(requestVO.getEmail(), "");

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();

        int insertCnt = -1;
        String nickname = NICKNAME;
        String email = EMAIL;
        String password = PASSWORD;
        String customerNo = "";

        if (!isMembers
                && (StringUtils.isEmpty(requestVO.getEmail())
                        || StringUtils.isEmpty(requestVO.getPassword())
                        || StringUtils.isEmpty(requestVO.getNickname()))) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        try {
            String REVIEW_CONFIRM_USE_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_CONFIRM_SELECTIVE_USE_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("REVIEW_COMFIRM_SELECTIVE_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String commentStatus = ReviewStatusEnum.APPROVAL.value();
            if (CommonConstants.YES_FLAG.equals(REVIEW_CONFIRM_USE_FLAG)) {
                if (CommonConstants.NO_FLAG.equals(REVIEW_CONFIRM_SELECTIVE_USE_FLAG)) {
                    commentStatus = ReviewStatusEnum.INACTIVE.value();
                }
            }

            if (isMembers) {
                SessionUserVO userInfo = SessionUserUtil.getUserInfo();
                String userId = userInfo.getUserId();
                String userEmailAddr = userInfo.getEmailAddr();
                customerNo = userId;

                String userName;
                String firstName = StringUtils.defaultIfEmpty(userInfo.getFirstName(), "");
                String lastName = StringUtils.defaultIfEmpty(userInfo.getLastName(), "");

                if (!ObjectUtils.isEmpty(firstName) && !ObjectUtils.isEmpty(lastName)) {
                    userName = String.format("%s%s", firstName, lastName);
                } else {
                    userName = userEmailAddr.substring(0, userEmailAddr.indexOf("@"));
                }

                String NICK_NAME_USE_FLAG =
                        StringUtils.defaultIfEmpty(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .siteCode(SITE_CODE)
                                                .keyCode("nick_name_use_flag")
                                                .build()),
                                CommonConstants.NO_FLAG);

                if (CommonConstants.YES_FLAG.equals(NICK_NAME_USE_FLAG) && NICKNAME.isBlank()) {
                    SystemAdminAccountNicknameGetResponseVO nicknameResponseVO =
                            systemAdminAccountClient.getNickname(
                                    SystemAdminAccountNicknameGetRequestVO.builder()
                                            .localeCode(SITE_CODE)
                                            .userId(userId)
                                            .customerNo(customerNo)
                                            .build());
                    nickname = nicknameResponseVO.getNickname();
                }

                if (CommonConstants.NO_FLAG.equals(NICK_NAME_USE_FLAG)) {
                    nickname = userName;
                }
                email = userEmailAddr;
                password = "";
            } else {
                if (ObjectUtils.isEmpty(password)) {
                    return ReviewCommentCreateResponseVO.builder()
                            .commentSave(CommonCodes.JSON_RESPONSE_ERROR_STATUS + ": " + "password")
                            .build();
                }

                if (ObjectUtils.isEmpty(email)) {
                    return ReviewCommentCreateResponseVO.builder()
                            .commentSave(CommonCodes.JSON_RESPONSE_ERROR_STATUS + ": " + "email")
                            .build();
                }

                password = ShaUtil.getSha512Code(PASSWORD);
            }

            insertCnt =
                    systemAdminReviewClient.createReviewComment(
                            SystemAdminCommentCreateRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .bizType(BIZ_TYPE_CODE)
                                    .modelId(PDP_ID)
                                    .reviewId(requestVO.getReviewId())
                                    .commentText(requestVO.getCommentText())
                                    .memberFlag(
                                            isMembers
                                                    ? CommonConstants.YES_FLAG
                                                    : CommonConstants.NO_FLAG)
                                    .customerNo(customerNo)
                                    .nickname(nickname)
                                    .email(email)
                                    .password(password)
                                    .commentStatus(commentStatus)
                                    .build());

        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewCommentCreateResponseVO.builder()
                .commentSave(
                        insertCnt > 0
                                ? CommonCodes.JSON_RESPONSE_SUCCESS_STATUS
                                : CommonCodes.JSON_RESPONSE_FAILURE_STATUS)
                .build();
    }

    public ReviewCommentDeleteResponseVO deleteReviewComment(
            ReviewCommentDeleteRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final String NICKNAME = StringUtils.defaultIfEmpty(requestVO.getNickname(), "");
        final String PASSWORD = StringUtils.defaultIfEmpty(requestVO.getPassword(), "");

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();

        int deleteCnt = -1;
        String permit = "";

        if (!isMembers
                && (StringUtils.isEmpty(requestVO.getPassword())
                        || StringUtils.isEmpty(requestVO.getNickname()))) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        String password = PASSWORD;
        String customerNo = "";

        try {
            boolean isDelete = false;
            if (isMembers) {
                SessionUserVO userInfo = SessionUserUtil.getUserInfo();
                Map<String, Object> userInfoMap = util.setUserInfoByParam(userInfo, null);
                customerNo = userInfoMap.get("customerNo").toString();
                isDelete = true;
            } else {
                if (ObjectUtils.isEmpty(password)) {
                    return ReviewCommentDeleteResponseVO.builder()
                            .deleteReviewComment(
                                    CommonCodes.JSON_RESPONSE_ERROR_STATUS + ": " + "password")
                            .build();
                }

                password = ShaUtil.getSha512Code(PASSWORD);
                boolean userCheck =
                        systemAdminReviewClient.getReviewCommentUserCheck(
                                SystemAdminReviewCommentUserCheckRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .bizType(BIZ_TYPE_CODE)
                                        .commentId(requestVO.getCommentId())
                                        .reviewId(requestVO.getReviewId())
                                        .nickname(NICKNAME)
                                        .password(password)
                                        .build());

                permit = userCheck ? "success" : "fail";
                isDelete = userCheck ? true : false;
            }
            if (isDelete) {
                deleteCnt =
                        systemAdminReviewClient.deleteReviewComment(
                                SystemAdminCommentDeleteRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .bizType(BIZ_TYPE_CODE)
                                        .commentId(requestVO.getCommentId())
                                        .memberFlag(
                                                isMembers
                                                        ? CommonConstants.YES_FLAG
                                                        : CommonConstants.NO_FLAG)
                                        .customerNo(customerNo)
                                        .nickname(NICKNAME)
                                        .password(password)
                                        .build());
            }

        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewCommentDeleteResponseVO.builder()
                .permit(permit)
                .deleteReviewComment(
                        deleteCnt > 0
                                ? CommonCodes.JSON_RESPONSE_SUCCESS_STATUS
                                : CommonCodes.JSON_RESPONSE_FAILURE_STATUS)
                .build();
    }

    public ReviewHelpfulUpdateResponseVO updateReviewHelpful(
            ReviewHelpfulUpdateRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final int REVIEW_ID = Integer.parseInt(requestVO.getReviewId());
        final String REVIEW_ON = StringUtils.defaultIfEmpty(requestVO.getReviewOn(), "false");

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        int helpful = 0;

        try {
            helpful =
                    systemAdminReviewClient.updateReviewHelpful(
                            SystemAdminReviewHelpfulUpdateRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .reviewId(REVIEW_ID)
                                    .reviewOn(REVIEW_ON)
                                    .build());

        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewHelpfulUpdateResponseVO.builder().helpful(helpful).reviewId(REVIEW_ID).build();
    }

    public ReviewNicknameResponseVO existReviewNickname(ReviewNicknameRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final String NICKNAME = requestVO.getNickname();
        final String SESSION_ID = requestVO.getSessionId();

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();
        if (isMembers) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }

        boolean isMemberNicknameDuplicate = true;
        try {
            isMemberNicknameDuplicate =
                    systemAdminReviewClient.getNicknameDuplicate(
                            SystemAdminReviewNicknameDuplicateGetRequestVO.builder()
                                    .memberFlag(CommonConstants.NO_FLAG)
                                    .bizType(BIZ_TYPE_CODE)
                                    .localeCode(SITE_CODE)
                                    .modelId(PDP_ID)
                                    .nickname(NICKNAME)
                                    .sessionId(SESSION_ID)
                                    .build());
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewNicknameResponseVO.builder()
                .isNicDup(isMemberNicknameDuplicate ? "duplicate" : "nonDuplicate")
                .build();
    }

    public ReviewExistResponseVO existReview(ReviewExistRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        final String REVIEW_ID = requestVO.getReviewId();
        final String NICKNAME = StringUtils.defaultIfEmpty(requestVO.getNickname(), "");
        final String PASSWORD = StringUtils.defaultIfEmpty(requestVO.getPassword(), "");

        List<CachedPdpIdVO> pdpList =
                cachedDataUtil.getPdpIdList(
                        CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
        if (ObjectUtils.isEmpty(pdpList)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        String PDP_ID = pdpList.get(0).getPdpId();
        String BIZ_TYPE_CODE = SKU.substring(SKU.length() - 1).equals("C") ? "B2C" : "B2B";

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();
        if (!isMembers
                && (StringUtils.isEmpty(requestVO.getPassword())
                        || StringUtils.isEmpty(requestVO.getNickname()))) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        String permit = "";
        String password = PASSWORD;
        try {
            if (isMembers) {
                String customerNo = SessionUserUtil.getUserInfo().getUserId();

                boolean isReviewDuplicate =
                        systemAdminReviewClient.getReviewDuplicate(
                                SystemAdminReviewDuplicateGetRequestVO.builder()
                                        .memberFlag(
                                                isMembers
                                                        ? CommonConstants.YES_FLAG
                                                        : CommonConstants.NO_FLAG)
                                        .bizType(BIZ_TYPE_CODE)
                                        .localeCode(SITE_CODE)
                                        .modelId(PDP_ID)
                                        .reviewId(REVIEW_ID)
                                        .customerNo(customerNo)
                                        .build());

                permit = isReviewDuplicate ? "success" : "fail";
            } else {
                password = ShaUtil.getSha512Code(PASSWORD);

                boolean userCheck =
                        systemAdminReviewClient.getReviewUserCheck(
                                SystemAdminReviewUserCheckRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .bizType(BIZ_TYPE_CODE)
                                        .modelId(PDP_ID)
                                        .reviewId(REVIEW_ID)
                                        .nickname(NICKNAME)
                                        .password(password)
                                        .build());

                permit = userCheck ? "success" : "fail";
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewExistResponseVO.builder().permit(permit).build();
    }

    public void sendEmailToAdmin(SendEmailRequestVO requestVO) {
        final String localeCode = requestVO.getLocaleCode();
        final String modelId = requestVO.getModelId();
        String javaLocaleCode =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil
                                .getComLocaleCode(
                                        CachedCodeRequestVO.builder().siteCode(localeCode).build())
                                .getLocaleCode(),
                        "");

        try {
            // 모델 ID로 CategoryId 정보 조회
            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("category_standard_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            SystemPdsPdpCategoryResponseVO systemPdsPdpCategoryRespVO =
                    systemPdsPdpInfoClient.getPdpCategoryInfo(
                            SystemPdsPdpCategoryRequestVO.builder()
                                    .pdpId(modelId)
                                    .standardFlag(CATEGORY_STANDARD_FLAG)
                                    .shopCode("D2C")
                                    .build());

            SystemPdsPdpResponseVO systemPdsPdpResponseVO =
                    systemPdsPdpInfoClient.getProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .pdpId(modelId)
                                    .siteCode(localeCode)
                                    .localeCode(javaLocaleCode)
                                    .standardFlag(CATEGORY_STANDARD_FLAG)
                                    .build());

            if (systemPdsPdpResponseVO == null) {
                systemPdsPdpResponseVO.setProductNm("none");
                systemPdsPdpResponseVO.setSmlImgUrl("empty");
            }

            List<SystemAdminReviewManagerResponseVO> reviewManagerList =
                    systemAdminReviewClient.getReviewManager(
                            SystemAdminReviewManagerRequestVO.builder()
                                    .localeCode(localeCode)
                                    .superCategoryId(
                                            StringUtils.defaultIfEmpty(
                                                    systemPdsPdpCategoryRespVO.getLv2CategoryCode(),
                                                    ""))
                                    .categoryId(
                                            StringUtils.defaultIfEmpty(
                                                    systemPdsPdpCategoryRespVO.getLv3CategoryCode(),
                                                    ""))
                                    .build());

            if (CommonConstants.YES_FLAG.equals(requestVO.getMktTestKey())) {
                reviewManagerList.clear();
                String MKT_TEST_EMAIL_LIST =
                        StringUtils.defaultIfEmpty(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("mkt_test_email_list")
                                                .build()),
                                "");

                if (!"".equals(MKT_TEST_EMAIL_LIST)) {
                    List<String> testMailList = new ArrayList<String>();
                    testMailList =
                            new ArrayList<String>(
                                    Arrays.asList(MKT_TEST_EMAIL_LIST.replace(" ", "").split(",")));
                    for (int i = 0; i < testMailList.size(); i++) {
                        SystemAdminReviewManagerResponseVO testMail =
                                new SystemAdminReviewManagerResponseVO();
                        testMail.setEmail(testMailList.get(i));
                        testMail.setName("mktTester");
                        reviewManagerList.add(testMail);
                    }
                }
            }

            String REVIEW_SUBJECT_TEXT =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("review_subject_text")
                                            .build()),
                            "");

            String SEND_EMAIL_TO_ADMIN_REVIEW_URL =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("send_email_to_admin_review_url")
                                            .build()),
                            "");

            LocalDate date = LocalDate.now();
            int year = date.getYear();
            String defaultCopyright =
                    "Copyright © 2009-{now} LG Electronics. All Rights Reserved"
                            .replace("{now}", Integer.toString(year));
            String copyright =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            "mylg-send-mail-copyright",
                                            CommonConstants.SHOP_CODE_D2C)),
                            defaultCopyright);

            if (reviewManagerList != null && reviewManagerList.size() > 0) {
                String toMailAddress = "";
                String subject = "";
                for (SystemAdminReviewManagerResponseVO reviewManager : reviewManagerList) {
                    toMailAddress = reviewManager.getEmail();
                    subject = REVIEW_SUBJECT_TEXT;

                    Map<String, String> emailValues = new HashMap<>();
                    emailValues.put("domain", serverUrl);
                    emailValues.put("localeCode", localeCode.toLowerCase());
                    emailValues.put("adminName", reviewManager.getName());

                    if (requestVO.getRating() == 1) {
                        emailValues.put("star1", "carmine");
                        emailValues.put("star2", "white");
                    } else if (requestVO.getRating() == 2) {
                        emailValues.put("star1", "carmine");
                        emailValues.put("star2", "carmine");
                    }

                    String reviewUrl =
                            serverUrl + systemPdsPdpResponseVO.getPdpUrl() + "#pdp-review";
                    String reviewAdminUrl = SEND_EMAIL_TO_ADMIN_REVIEW_URL;
                    if ("".equals(reviewAdminUrl)) {
                        reviewAdminUrl =
                                serverUrl + systemPdsPdpResponseVO.getPdpUrl() + "#pdp-review";
                    }
                    emailValues.put("title", requestVO.getTitle());
                    emailValues.put("review", requestVO.getReview());
                    emailValues.put("modelImageUrl", systemPdsPdpResponseVO.getSmlImgUrl());
                    emailValues.put("reviewUrl", reviewUrl);
                    emailValues.put("reviewAdminUrl", reviewAdminUrl);
                    emailValues.put("modelName", systemPdsPdpResponseVO.getProductNm());
                    emailValues.put("emailCopyright", copyright);

                    emailService.send(
                            subject,
                            toMailAddress,
                            "email-template-review-critical-submission",
                            emailValues);
                }
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }
    }

    public ReviewNicknameUpdateResponseVO updateReviewNickname(
            ReviewNicknameUpdateRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String NICKNAME = requestVO.getNickname();

        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        boolean isMembers = SessionUserUtil.isLogin();
        if (!isMembers) {
            return ReviewNicknameUpdateResponseVO.builder()
                    .updateNickName(CommonCodes.JSON_RESPONSE_ERROR_MESSAGE)
                    .build();
        }

        Map<String, Object> resultMap = new HashMap<>();

        try {
            if (isMembers) {
                String customerNo = SessionUserUtil.getUserInfo().getUserId();

                resultMap =
                        systemAdminAccountClient.updateNickname(
                                SystemAdminAccountNicknameUpdateRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .customerNo(customerNo)
                                        .nickname(NICKNAME)
                                        .build());
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return ReviewNicknameUpdateResponseVO.builder()
                .updateNickName(
                        CommonCodes.JSON_RESPONSE_SUCCESS_MESSAGE.equals(resultMap.get("message"))
                                ? CommonCodes.JSON_RESPONSE_SUCCESS_MESSAGE
                                : CommonCodes.JSON_RESPONSE_ERROR_MESSAGE)
                .build();
    }
}
