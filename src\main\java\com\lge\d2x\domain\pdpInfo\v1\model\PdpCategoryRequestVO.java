package com.lge.d2x.domain.pdpInfo.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PdpCategoryRequestVO {
    @NotBlank(message = "Missing required parameters")
    @Schema(description = "pdp id", example = "MD01234567")
    private String pdpId;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "표준화 여부", example = "Y")
    private String standardFlag;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "샵 코드", example = "de_students")
    private String shopCode;

    @Schema(description = "LGCOM sku id", example = "0CZZW1H001X.ACC.ESSP.BR.C")
    private String lgcomSkuId;

    @Schema(description = "사이트 코드", example = "UK")
    private String siteCode;

    @Schema(description = "검색유형", example = "all")
    private String searchType;
}
