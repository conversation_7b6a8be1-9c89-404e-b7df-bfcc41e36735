<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lge.d2x.domain.pdpInfo.v1.repository.pdsmgr.PdpInfoRepository">
    <select id="selectPdpBasicInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectPdpBasicInfo */
               A.PDP_ID AS pdpId
             , A.SITE_CODE AS siteCode
             , A.SKU_ID AS skuId
             , <PERSON><PERSON>LGCOM_SKU_ID AS lgcomSkuId
             , <PERSON><PERSON>BIZ_TYPE_CODE AS bizTypeCode
             , A.PDP_TYPE_CODE AS pdpTypeCode
             , CASE WHEN A.PDP_TYPE_CODE = 'A'
                    THEN 'ADP'
                    WHEN A.PDP_TYPE_CODE = 'B'
                    THEN 'BDP'
                    WHEN A.PDP_TYPE_CODE = 'O'
                    THEN 'ODP'
                    ELSE 'PDP'
               END pdpType
             , A.PRODUCT_NM AS productNm
             , A.USER_FRNDY_PRODUCT_NM AS userFrndyProductNm
        <choose>
            <when test='siteCode == "SA" || siteCode == "SA_EN" || siteCode == "CL"'>
             , ROUND(IFNULL(A.MSRP_SALES_PRICE, 0.00), 2) AS msrpSalesPrice
            </when>
            <otherwise>
             , IFNULL(A.MSRP_SALES_PRICE, 0.00) AS msrpSalesPrice
            </otherwise>
        </choose>
             , A.CATEGORY_CODE AS categoryCode
             , A.EXCL_PRODUCT_SETT_CODE AS exclProductSettCode
             , A.INCH_VAL AS inchVal
             , A.ELABEL_GRD_CODE AS elabelGrdCode
             , A.ELABEL_CLS_CODE AS elabelClsCode
             , A.SECOND_ELABEL_GRD_CODE AS secondElabelGrdCode
             , A.SECOND_ELABEL_CLS_CODE AS secondElabelClsCode
             , A.PRODUCT_RELES_DD AS productRelesDd
             , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productYear
             , A.WTB_OFFLINE_USE_FLAG AS wtbOfflineUseFlag
             , A.WTB_ONLINE_USE_FLAG AS wtbOnlineUseFlag
             , A.WTB_DIRECT_USE_FLAG AS wtbDirectUseFlag
             , A.WTB_USE_FLAG AS wtbUseFlag
             , A.WTB_EXTL_LINK_USE_FLAG AS wtbExtlLinkUseFlag
             , A.WTB_EXTL_LINK_NM AS wtbExtlLinkNm
             , A.WTB_EXTL_LINK_URL AS wtbExtlLinkUrl
             , A.WTB_EXTL_LINK_SELF_SCREEN_FLAG AS wtbExtlLinkSelfScreenFlag
             , A.WTB_LV4_CATEGORY_CODE_LIST_CNTS AS wtbSubId
             , A.ITB_USE_FLAG AS itbUseFlag
             , A.THINQ_PRODUCT_FLAG AS thinqProductFlag
             , A.SIGNT_PRODUCT_FLAG AS signtProductFlag
             , A.OBJET_ACSRY_FLAG AS objetAcsryFlag
             , A.OBJET_PRODUCT_FLAG AS objetProductFlag
             , A.WTOWER_PRODUCT_FLAG AS wtowerProductFlag
             , B.SHOP_CODE AS shopCode
             , B.SML_IMG_URL AS smlImgUrl
             , B.MDM_IMG_URL AS mdmImgUrl
             , B.BIG_IMG_URL AS bigImgUrl
             , B.IMG_ALT_TEXT_CNTS AS imgAltTextCnts
             , B.BUNDLE_IMG_URL AS bundleImgUrl
             , B.BUNDLE_MOBL_IMG_URL AS bundleMoblImgUrl
             , B.BUNDLE_IMG_ALT_TEXT_CNTS AS bundleImgAltTextCnts
             , B.BUNDLE_DESC AS bundleDesc
             , B.PRODUCT_STATE_CODE AS productStateCode
             , B.PDP_URL AS pdpUrl
             , B.DEFAULT_PRODUCT_TAG_CODE AS defaultProductTagCode
             , B.PRODUCT_TAG_CODE1 AS productTagCode1
             , B.PRODUCT_TAG_EXP_BEGIN_DATE1 AS productTagExpBeginDate1
             , B.PRODUCT_TAG_EXP_END_DATE1 AS productTagExpEndDate1
             , B.PRODUCT_TAG_USE_FLAG1 AS productTagUseFlag1
             , B.PRODUCT_TAG_CODE2 AS productTagCode2
             , B.PRODUCT_TAG_EXP_BEGIN_DATE2 AS productTagExpBeginDate2
             , B.PRODUCT_TAG_EXP_END_DATE2 AS productTagExpEndDate2
             , B.PRODUCT_TAG_USE_FLAG2 AS productTagUseFlag2
        <if test="timezone != null and timezone != ''">
             , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                    THEN NULL
                    WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1
                    THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG1 = 'Y'
                               THEN B.PRODUCT_TAG_CODE1
                               ELSE NULL
                          END)
                    ELSE NULL
               END AS realProductTag1
             , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                    THEN NULL
                    WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1
                    THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG1 = 'Y'
                               THEN TO_CHAR(B.PRODUCT_TAG_EXP_END_DATE1, 'YYYYMMDD')
                               ELSE NULL
                          END)
                    ELSE NULL
               END AS realProductTag1EndDate
             , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                    THEN NULL
                    WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2
                    THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG2 = 'Y'
                               THEN B.PRODUCT_TAG_CODE2
                               ELSE NULL
                          END)
                    ELSE NULL
               END AS realProductTag2
             , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                    THEN NULL
                    WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2
                    THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG2 = 'Y'
                               THEN TO_CHAR(B.PRODUCT_TAG_EXP_END_DATE2, 'YYYYMMDD')
                               ELSE NULL
                          END)
                    ELSE NULL
               END AS realProductTag2EndDate
        </if>
             , B.PRODUCT_THEME_TYPE_CODE AS productThemeTypeCode
             , B.AEM_PUBL_FLAG AS aemPublFlag
             , B.AEM_PUBL_DATE AS aemPublDate
             , E.SEO_PAGE_TITLE AS seoPageTitle
             , E.SEO_DESC AS seoDesc
             , IF(F.SALES_STATE_CODE = 'SALABLE', 'Y', 'N') AS obsSellFlag
          FROM DSP_PDP_M A
         INNER JOIN DSP_PDP_D B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.PDP_ID = A.PDP_ID
           AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
        <if test="productStateCode != null and productStateCode != ''">
           AND B.PRODUCT_STATE_CODE = #{productStateCode}
        </if>
           AND B.SHOP_CODE = 'D2C'
           AND B.USE_FLAG = 'Y'
           AND B.AEM_PUBL_FLAG = 'Y'
         INNER JOIN DSP_PDP_CATEGORY_R C
            ON C.PDP_ID = A.PDP_ID
           AND C.SITE_CODE = A.SITE_CODE
           AND C.USE_FLAG = 'Y'
           AND C.DEFAULT_MAP_FLAG = 'Y'
         INNER JOIN DSP_DISPLAY_CATEGORY_M D
            ON D.SITE_CODE = C.SITE_CODE
           AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
           AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
           AND D.USE_FLAG = 'Y'
          LEFT OUTER JOIN DSP_PRODUCT_SEO_INFO_M E
            ON E.SITE_CODE = B.SITE_CODE
           AND E.PDP_URL = B.PDP_URL
          LEFT OUTER JOIN ECM_ATTRIBUTE_M F
            ON F.STORE_CODE = A.SITE_CODE
           AND F.SKU_ID = SUBSTR(A.LGCOM_SKU_ID, 1, LENGTH(A.LGCOM_SKU_ID) - 2)
         WHERE A.USE_FLAG = 'Y'
        <if test="pdpId != null and pdpId != ''">
           AND A.PDP_ID = #{pdpId}
        </if>
        <if test="lgcomSkuId != null and lgcomSkuId != ''">
           AND A.LGCOM_SKU_ID = #{lgcomSkuId}
        </if>
    </select>

    <select id="selectPdpBasicInfoNotStandard" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectPdpBasicInfo */
               A.LGCOM_SKU_ID AS lgcomSkuId
             , A.SKU_ID AS skuId
             , A.PDP_ID AS pdpId
             , A.BIZ_TYPE_CODE AS bizTypeCode
             , A.INCH_VAL AS inchVal
             , A.SITE_CODE AS siteCode
             , A.PRODUCT_NM AS productNm
             , B.PRODUCT_STATE_CODE AS productStateCode
        <choose>
            <when test='siteCode == "SA" || siteCode == "SA_EN" || siteCode == "CL"'>
             , ROUND(IFNULL(A.MSRP_SALES_PRICE, 0.00), 2) AS msrpSalesPrice
            </when>
            <otherwise>
             , IFNULL(A.MSRP_SALES_PRICE, 0.00) AS msrpSalesPrice
            </otherwise>
        </choose>
             , A.PDP_TYPE_CODE AS pdpTypeCode
             , A.RETAILER_PRICING_FLAG AS retailerPricingFlag
             , A.WTB_USE_FLAG AS wtbUseFlag
             , A.WTB_EXTL_LINK_USE_FLAG AS wtbExtlLinkUseFlag
             , A.WTB_EXTL_LINK_NM AS wtbExtlLinkNm
             , A.WTB_EXTL_LINK_URL AS wtbExtlLinkUrl
             , IFNULL(A.WTB_EXTL_LINK_SELF_SCREEN_FLAG, 'N') AS wtbExtlLinkSelfScreenFlag
             , A.WTB_LV4_CATEGORY_CODE_LIST_CNTS AS wtbSubId
             , A.ELABEL_GRD_CODE AS elabelGrdCode
             , T.EL_DOC_TYPE_CODE AS elDocTypeCode
             , T.PIS_DOC_TYPE_CODE AS pisDocTypeCode
             , T.EL_TYPE_CODE AS elTypeCode
             , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productYear
             , B.IMG_ALT_TEXT_CNTS AS imgAltTextCnts
             , B.BIG_IMG_URL AS bigImgUrl
             , B.MDM_IMG_URL AS mdmImgUrl
             , B.SML_IMG_URL AS smlImgUrl
             , IFNULL(B.PRODUCT360_IMG_PATH, '') AS product360ImgPath
             , IFNULL(B.PRODUCT360_TITLE, '') AS product360Title
             , IFNULL(B.PRODUCT_VR_IMG_PATH, '') AS productVrImgPath
             , IFNULL(B.PRODUCT_VR_TITLE, '') AS productVrTitle
             , IFNULL(B.PRODUCT_AR_IMG_PATH, '') AS productArImgPath
             , IFNULL(B.PRODUCT_AR_TITLE, '') AS productArTitle
             , IFNULL(B.PRODUCT360_DIM3_IMG_PATH, '') AS product360Dim3ImgPath
             , IFNULL(B.PRODUCT360_DIM3_TITLE, '') AS product360Dim3Title
             , IFNULL(B.PRODUCT_AR_MOBL_IMG_PATH, '') AS productArMoblImgPath
             , IFNULL(B.PRODUCT_AR_MOBL_TITLE, '') AS productArMoblTitle
             , CASE WHEN A.PDP_TYPE_CODE = 'A'
                    THEN 'ADP'
                    WHEN A.PDP_TYPE_CODE = 'B'
                    THEN 'BDP'
                    WHEN A.PDP_TYPE_CODE = 'O'
                    THEN 'ODP'
                    ELSE 'PDP'
               END pdpType
        <choose>
            <when test="siteCode == 'IN'">
             , IFNULL(A.INQUIRY_FLAG, 'N') AS inquiryFlag
            </when>
            <otherwise>
             , 'N' AS inquiryFlag
            </otherwise>
        </choose>
             , A.USER_FRNDY_PRODUCT_NM AS userFrndyProductNm
             , IFNULL(G.SEO_PAGE_TITLE, '') AS seoPageTitle
             , IFNULL(G.SEO_DESC, '') AS seoDesc
             , A.SIGNT_PRODUCT_FLAG AS signtProductFlag
             , A.THINQ_PRODUCT_FLAG AS thinqProductFlag
             , A.FIND_DEALER_USE_FLAG AS findDealerUseFlag
             , A.OBJET_PRODUCT_FLAG AS objetProductFlag
             , A.OBJET_ACSRY_FLAG AS objetAcsryFlag
             , IF(IFNULL(I.STOCK_STATE_CODE, '') != '', 'Y', 'N') AS obsSellFlag
             , IF(I.STOCK_STATE_CODE = 'IN_STOCK', 'Y', 'N') AS obsInventoryFlag
             , B.PRODUCT_THEME_TYPE_CODE AS productThemeTypeCode
             , A.WTOWER_PRODUCT_FLAG AS wtowerProductFlag
             , A.ELABEL_CLS_CODE AS elabelClsCode
             , T.SECOND_EL_DOC_TYPE_CODE AS secondElDocTypeCode
             , T.SECOND_PIS_DOC_TYPE_CODE AS secondPisDocTypeCode
             , T.PIS_DOC_OLD_FLAG AS pisDocOldFlag
             , T.SECOND_PF_CODE AS secondPfCode
             , T.SECOND_EL_TYPE_CODE AS secondElTypeCode
             , A.SECOND_ELABEL_GRD_CODE AS secondElabelGrdCode
             , A.SECOND_ELABEL_CLS_CODE AS secondElabelClsCode
             , A.EXCL_PRODUCT_SETT_CODE AS exclProductSettCode
        <choose>
            <when test='siteCode == "TW"'>
             , (SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE CATEGORY_CODE = F.LV1_CATEGORY_CODE AND BIZ_TYPE_CODE = A.BIZ_TYPE_CODE AND CATEGORY_LV_NO = 1) AS classificationFlagLv1
             , (SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE CATEGORY_CODE = F.LV2_CATEGORY_CODE AND BIZ_TYPE_CODE = A.BIZ_TYPE_CODE AND HIGH_CATEGORY_CODE = F.LV1_CATEGORY_CODE AND CATEGORY_LV_NO = 2) AS classificationFlagLv2
             , (SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE CATEGORY_CODE = F.LV3_CATEGORY_CODE AND BIZ_TYPE_CODE = A.BIZ_TYPE_CODE AND HIGH_CATEGORY_CODE = F.LV2_CATEGORY_CODE AND CATEGORY_LV_NO = 3) AS classificationFlagLv3
             , (SELECT CATEGORY_NM FROM PDM_CATEGORY_COUNTRY_M WHERE CATEGORY_CODE = F.LV4_CATEGORY_CODE AND BIZ_TYPE_CODE = A.BIZ_TYPE_CODE AND HIGH_CATEGORY_CODE = F.LV3_CATEGORY_CODE AND CATEGORY_LV_NO = 4) AS classificationFlagLv4
            </when>
            <otherwise>
             , (SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE CATEGORY_CODE = F.LV1_CATEGORY_CODE AND BIZ_TYPE_CODE = A.BIZ_TYPE_CODE AND CATEGORY_LV_NO = 1) AS classificationFlagLv1
             , (SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE CATEGORY_CODE = F.LV2_CATEGORY_CODE AND BIZ_TYPE_CODE = A.BIZ_TYPE_CODE AND HIGH_CATEGORY_CODE = F.LV1_CATEGORY_CODE AND CATEGORY_LV_NO = 2) AS classificationFlagLv2
             , (SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE CATEGORY_CODE = F.LV3_CATEGORY_CODE AND BIZ_TYPE_CODE = A.BIZ_TYPE_CODE AND HIGH_CATEGORY_CODE = F.LV2_CATEGORY_CODE AND CATEGORY_LV_NO = 3) AS classificationFlagLv3
             , (SELECT CATEGORY_NM FROM PDM_CATEGORY_M WHERE CATEGORY_CODE = F.LV4_CATEGORY_CODE AND BIZ_TYPE_CODE = A.BIZ_TYPE_CODE AND HIGH_CATEGORY_CODE = F.LV3_CATEGORY_CODE AND CATEGORY_LV_NO = 4) AS classificationFlagLv4
            </otherwise>
        </choose>
             , A.WTB_OFFLINE_USE_FLAG AS wtbOfflineUseFlag
             , A.WTB_ONLINE_USE_FLAG AS wtbOnlineUseFlag
             , A.WTB_DIRECT_USE_FLAG AS wtbDirectUseFlag
             , CASE WHEN U.BUNDLE_PDP_ID IS NOT NULL 
                    THEN 'Y'
                    ELSE 'N'
               END AS bundleModelFlag
             , A.ENERGY_CERT_CODE AS energyCertCode 
             , A.CATEGORY_CODE AS categoryCode
             , A.PRODUCT_RELES_DD AS productRelesDd
             , A.ITB_USE_FLAG AS itbUseFlag
             , B.SHOP_CODE AS shopCode
             , B.BUNDLE_IMG_URL AS bundleImgUrl
             , B.BUNDLE_MOBL_IMG_URL AS bundleMoblImgUrl
             , B.BUNDLE_IMG_ALT_TEXT_CNTS AS bundleImgAltTextCnts
             , B.BUNDLE_DESC AS bundleDesc
             , B.PDP_URL AS pdpUrl
             , B.DEFAULT_PRODUCT_TAG_CODE AS defaultProductTagCode
             , B.PRODUCT_TAG_CODE1 AS productTagCode1
             , B.PRODUCT_TAG_EXP_BEGIN_DATE1 AS productTagExpBeginDate1
             , B.PRODUCT_TAG_EXP_END_DATE1 AS productTagExpEndDate1
             , B.PRODUCT_TAG_USE_FLAG1 AS productTagUseFlag1
             , B.PRODUCT_TAG_CODE2 AS productTagCode2
             , B.PRODUCT_TAG_EXP_BEGIN_DATE2 AS productTagExpBeginDate2
             , B.PRODUCT_TAG_EXP_END_DATE2 AS productTagExpEndDate2
             , B.PRODUCT_TAG_USE_FLAG2 AS productTagUseFlag2
        <if test="timezone != null and timezone != ''">
             , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                    THEN NULL
                    WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1
                    THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG1 = 'Y'
                               THEN B.PRODUCT_TAG_CODE1
                               ELSE NULL
                          END)
                    ELSE NULL
               END AS realProductTag1
             , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                    THEN NULL
                    WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1
                    THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG1 = 'Y'
                               THEN TO_CHAR(B.PRODUCT_TAG_EXP_END_DATE1, 'YYYYMMDD')
                               ELSE NULL
                          END)
                    ELSE NULL
               END AS realProductTag1EndDate
             , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                    THEN NULL
                    WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2
                    THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG2 = 'Y'
                               THEN B.PRODUCT_TAG_CODE2
                               ELSE NULL
                          END)
                    ELSE NULL
               END AS realProductTag2
             , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                    THEN NULL
                    WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2
                    THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG2 = 'Y'
                               THEN TO_CHAR(B.PRODUCT_TAG_EXP_END_DATE2, 'YYYYMMDD')
                               ELSE NULL
                          END)
                    ELSE NULL
               END AS realProductTag2EndDate
        </if>
             , B.PRODUCT_TAG_USE_TYPE_CODE1 AS productTagUserTypeCode1
             , B.PRODUCT_TAG_USE_TYPE_CODE2 AS productTagUserTypeCode2
             , B.AEM_PUBL_FLAG AS aemPublFlag
             , B.AEM_PUBL_DATE AS aemPublDate
          FROM DSP_PDP_M A
         INNER JOIN DSP_PDP_D B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.PDP_ID = A.PDP_ID
           AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
        <if test="productStateCode != null and productStateCode != ''">
           AND B.PRODUCT_STATE_CODE = #{productStateCode}
        </if>
           AND B.SHOP_CODE = 'D2C'
           AND B.USE_FLAG = 'Y'
           AND B.AEM_PUBL_FLAG = 'Y'
         INNER JOIN DSP_OLD_PDP_CATEGORY_R C
            ON C.PDP_ID = A.PDP_ID
           AND C.SITE_CODE = A.SITE_CODE
           AND C.USE_FLAG = 'Y'
           AND C.DEFAULT_MAP_FLAG = 'Y'
         INNER JOIN DSP_DISPLAY_CATEGORY_M D
            ON D.SITE_CODE = C.SITE_CODE
           AND D.CATEGORY_CODE = C.LV2_CATEGORY_ID
           AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
           AND D.USE_FLAG = 'Y'
         INNER JOIN PDM_PRODUCT_M O
            ON O.SKU_ID = A.SKU_ID
         INNER JOIN PDM_PRODUCT_D E
            ON E.SKU_ID = A.SKU_ID
           AND E.LOCALE_CODE = #{localeCode}
           AND E.USE_FLAG = 'Y'
        <choose>
            <when test='siteCode == "TW"'>
         INNER JOIN PDM_PRODUCT_CATEGORY_COUNTRY_R F
            </when>
            <otherwise>
         INNER JOIN PDM_PRODUCT_CATEGORY_R F
            </otherwise>
        </choose>
            ON F.SKU_ID = A.SKU_ID
           AND F.LOCALE_CODE = E.LOCALE_CODE
           AND F.USE_FLAG = 'Y'
          LEFT OUTER JOIN DSP_PRODUCT_SEO_INFO_M G
            ON G.SITE_CODE = B.SITE_CODE
           AND G.PDP_URL = B.PDP_URL
          LEFT OUTER JOIN ECM_ATTRIBUTE_M H
            ON H.STORE_CODE = A.SITE_CODE
           AND H.SKU_ID = SUBSTR(A.LGCOM_SKU_ID, 1, LENGTH(A.LGCOM_SKU_ID) - 2)
          LEFT OUTER JOIN ECM_STOCK_D I
            ON I.STORE_CODE = H.STORE_CODE
           AND I.SKU_ID = H.SKU_ID
          LEFT OUTER JOIN PDM_PRODUCT_EL_PIS_R T
            ON A.SITE_CODE = T.SITE_CODE
           AND O.LV1_PRODUCT_CODE = T.LV1_EL_PRODUCT_CODE
           AND O.LV2_PRODUCT_CODE = T.LV2_EL_PRODUCT_CODE
          LEFT OUTER JOIN (SELECT *
                             FROM DSP_PRODUCT_BUNDLE_D
                            WHERE USE_FLAG ='Y' 
                              AND SITE_CODE = #{siteCode}
                            GROUP BY PDP_ID, SITE_CODE) U
            ON A.PDP_ID = U.PDP_ID 
           AND A.SITE_CODE = U.SITE_CODE
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
        <if test="pdpId != null and pdpId != ''">
           AND A.PDP_ID = #{pdpId}
        </if>
        <if test="lgcomSkuId != null and lgcomSkuId != ''">
           AND A.LGCOM_SKU_ID = #{lgcomSkuId}
        </if>
    </select>

    <select id="selectPdpCategoryInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectPdpCategoryInfo */
             A.PDP_ID AS pdpId
           , A.LV1_CATEGORY_CODE AS lv1CategoryCode
           , B.SITE_CATEGORY_NM AS lv1SiteCategoryNm
           , B.CATEGORY_NM AS lv1CategoryNm
           , A.LV2_CATEGORY_CODE AS lv2CategoryCode
           , D.SITE_CATEGORY_NM AS lv2SiteCategoryNm
           , IFNULL(NULLIF(SUBSTRING_INDEX(E.CATEGORY_PAGE_URL, '/', -1), ''), D.CATEGORY_NM) AS lv2CategoryNm
           , A.LV3_CATEGORY_CODE AS    lv3CategoryCode
           , F.CATEGORY_NM AS lv3CategoryNm
           , F.SITE_CATEGORY_NM AS lv3SiteCategoryNm
           , A.LV4_CATEGORY_CODE AS    lv4CategoryCode
           , H.CATEGORY_NM AS lv4CategoryNm
           , H.SITE_CATEGORY_NM AS lv4SiteCategoryNm
           , A.DEFAULT_MAP_FLAG    AS    defaultMapFlag
           , A.LGPICK_FLAG    AS    lgpickFlag
           , A.BIZ_TYPE_CODE    AS    bizTypeCode
           , A.SITE_CODE    AS    siteCode
           , A.PDP_CATEGORY_REL_ID    AS    pdpCategoryRelId
        FROM DSP_PDP_CATEGORY_R A
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M B
          ON B.SITE_CODE = A.SITE_CODE
         AND B.CATEGORY_CODE = A.LV1_CATEGORY_CODE
        <if test='shopCode == "D2C"'>
         AND B.USE_FLAG = 'Y'
        </if>
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D C
          ON C.SITE_CODE = B.SITE_CODE
         AND C.CATEGORY_CODE = B.CATEGORY_CODE
         AND C.SHOP_CODE = #{shopCode}
         AND C.USE_FLAG = 'Y'
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M D
          ON D.SITE_CODE = A.SITE_CODE
         AND D.CATEGORY_CODE = A.LV2_CATEGORY_CODE
        <if test='shopCode == "D2C"'>
         AND D.USE_FLAG = 'Y'
        </if>
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D E
          ON E.SITE_CODE = D.SITE_CODE
         AND E.CATEGORY_CODE = D.CATEGORY_CODE
         AND E.SHOP_CODE = #{shopCode}
         AND E.USE_FLAG = 'Y'
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M F
          ON F.SITE_CODE = A.SITE_CODE
         AND F.CATEGORY_CODE = A.LV3_CATEGORY_CODE
        <if test='shopCode == "D2C"'>
         AND F.USE_FLAG = 'Y'
        </if>
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D G
          ON G.SITE_CODE = F.SITE_CODE
         AND G.CATEGORY_CODE = F.CATEGORY_CODE
         AND G.SHOP_CODE = #{shopCode}
         AND G.USE_FLAG = 'Y'
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M H
          ON H.SITE_CODE = A.SITE_CODE
         AND H.CATEGORY_CODE = A.LV4_CATEGORY_CODE
        <if test='shopCode == "D2C"'>
         AND H.USE_FLAG = 'Y'
        </if>
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D I
          ON I.SITE_CODE = H.SITE_CODE
         AND I.CATEGORY_CODE = H.CATEGORY_CODE
         AND I.SHOP_CODE = #{shopCode}
         AND I.USE_FLAG = 'Y'
       WHERE A.USE_FLAG = 'Y'
       <if test="searchType != 'all'">
         AND A.DEFAULT_MAP_FLAG = 'Y'
       </if>
         AND A.PDP_ID = #{pdpId}
       <if test="siteCode != null and siteCode != ''">
         AND A.SITE_CODE = #{siteCode}
       </if>
       LIMIT 1
    </select>

    <select id="selectOldPdpCategoryInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectPdpCategoryInfo */
             A.PDP_ID AS pdpId
           , A.LV1_CATEGORY_ID AS lv1CategoryCode
           , IFNULL(NULLIF(SUBSTRING_INDEX(C.CATEGORY_PAGE_URL, '/', -1), ''), B.CATEGORY_NM) AS lv1CategoryNm
           , B.SITE_CATEGORY_NM AS lv1SiteCategoryNm
           , A.LV2_CATEGORY_ID AS lv2CategoryCode
           , D.CATEGORY_NM AS lv2CategoryNm
           , D.SITE_CATEGORY_NM AS lv2SiteCategoryNm
           , A.LV3_CATEGORY_ID AS lv3CategoryCode
           , F.CATEGORY_NM AS lv3CategoryNm
           , F.SITE_CATEGORY_NM AS lv3SiteCategoryNm
           , A.DEFAULT_MAP_FLAG AS defaultMapFlag
           , A.LGPICK_FLAG AS lgpickFlag
           , A.BIZ_TYPE_CODE AS bizTypeCode
           , A.SITE_CODE AS siteCode
           , A.PDP_CATEGORY_REL_ID AS pdpCategoryRelId
        FROM DSP_OLD_PDP_CATEGORY_R A
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M B
          ON B.SITE_CODE = A.SITE_CODE
         AND B.CATEGORY_CODE = A.LV1_CATEGORY_ID
        <if test='shopCode == "D2C"'>
         AND B.USE_FLAG = 'Y'
        </if>
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D C
          ON C.SITE_CODE = B.SITE_CODE
         AND C.CATEGORY_CODE = B.CATEGORY_CODE
         AND C.SHOP_CODE = #{shopCode}
         AND C.USE_FLAG = 'Y'
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M D
          ON D.SITE_CODE = A.SITE_CODE
         AND D.CATEGORY_CODE = A.LV2_CATEGORY_ID
        <if test='shopCode == "D2C"'>
         AND D.USE_FLAG = 'Y'
        </if>
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D E
          ON E.SITE_CODE = D.SITE_CODE
         AND E.CATEGORY_CODE = D.CATEGORY_CODE
         AND E.SHOP_CODE = #{shopCode}
         AND E.USE_FLAG = 'Y'
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_M F
          ON F.SITE_CODE = A.SITE_CODE
         AND F.CATEGORY_CODE = A.LV3_CATEGORY_ID
        <if test='shopCode == "D2C"'>
         AND F.USE_FLAG = 'Y'
        </if>
        LEFT OUTER JOIN DSP_DISPLAY_CATEGORY_D G
          ON G.SITE_CODE = F.SITE_CODE
         AND G.CATEGORY_CODE = F.CATEGORY_CODE
         AND G.SHOP_CODE = #{shopCode}
         AND G.USE_FLAG = 'Y'
       WHERE A.USE_FLAG = 'Y'
       <if test="searchType != 'all'">
         AND A.DEFAULT_MAP_FLAG = 'Y'
       </if>
         AND A.PDP_ID = #{pdpId}
       <if test="siteCode != null and siteCode != ''">
         AND A.SITE_CODE = #{siteCode}
       </if>
       <if test="searchType != 'all'">
         LIMIT 1
       </if>
    </select>

    <select id="selectPdpEnergyLabelInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpEnergyLabelInfoRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpEnergyLabelInfoResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectPdpEnergyLabelInfo */
               A.ELABEL_TYPE_CODE
             , A.ELABEL_GRD_CODE
             , A.ELABEL_IMG_PATH
             , B.CODE_VAL_NM AS ENERGY_LABEL_NAME
          FROM DSP_ENERGYLABEL_TYPE_M A
         INNER JOIN ( SELECT CODE_VAL_NM
                           , COMMON_CODE_VAL
                        FROM COM_COMMON_CODE_D
                       WHERE COMMON_CODE = 'Energy_Label_Code'
                         AND USE_FLAG = 'Y' ) B
            ON A.ELABEL_GRD_CODE = B.COMMON_CODE_VAL
         WHERE A.USE_FLAG = 'Y' 
           AND A.SITE_CODE = #{siteCode}
           AND A.ELABEL_TYPE_CODE = #{elabelClsCode}
           AND A.ELABEL_GRD_CODE = #{elabelGrdCode}
    </select>

    <select id="selectKeyfeaturesList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.KeyfeaturesRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.KeyfeaturesResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectKeyfeaturesList */
               A.KEYFEATURE_DESC
             , A.DSP_SEQ
             , A.PDP_ID
             , REPLACE(REPLACE(A.KEYFEATURE_DESC, '<![CDATA[<li>]]>', ''), '<![CDATA[</li>]]>', '') AS SHORT_DESC
          FROM DSP_PRODUCT_KEYFEATURE_D A
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
     <choose>
        <when test="pdpIdListFlag != null and pdpIdListFlag == &quot;Y&quot;">
          AND A.DSP_SEQ <![CDATA[<=]]> 3
           <foreach item="item" collection="pdpIdList" open="AND PDP_ID IN (" close=")" separator=",">
                #{item}
            </foreach>
        </when>
        <otherwise>
           AND A.PDP_ID = #{pdpId}
        </otherwise>
     </choose>
         ORDER BY A.DSP_SEQ ASC
    </select>

    <select id="selectPdpSiblingDataList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectPdpSiblingDataList */
               C.LGCOM_SKU_ID
             , C.SKU_ID
             , C.PDP_ID
             , C.PRODUCT_NM
             , C.BIZ_TYPE_CODE
             , CASE WHEN C.SITE_CODE = 'SA' OR C.SITE_CODE = 'SA_EN' OR C.SITE_CODE = 'CL'
                    THEN ROUND(IFNULL(C.MSRP_SALES_PRICE, 0.00), 2)
                    ELSE IFNULL(C.MSRP_SALES_PRICE, 0.00)
                END MSRP
             , C.USER_FRNDY_PRODUCT_NM
             , D.PRODUCT_STATE_CODE
             , A.SITE_CODE
             , B.DEFAULT_SIBLING_MODEL_FLAG
             , F.PDP_TITLE
             , B.SIBLING_CODE
             , B.SIBLING_GRP_CODE
             , IFNULL(A.SIBLING_LOCAL_VAL, A.SIBLING_CODE) AS SIBLING_VALUE
             , B.SIBLING_TYPE_CODE
             , D.PDP_URL
             , B.PRICE_USE_FLAG
             , B.SIBLING_GRP_NM
             , B.SIBLING_SBJ_TYPE_CODE
             , DENSE_RANK() OVER(ORDER BY F.EXP_SEQ ASC) AS DISPLAY_ORDER_NO
             , DENSE_RANK() OVER(PARTITION BY A.SITE_CODE 
                                     ORDER BY CASE WHEN A.SIBLING_TYPE_CODE = 'SIZE' THEN 1 
                                                   WHEN A.SIBLING_TYPE_CODE = 'COLOR' THEN 2
                                                   WHEN A.SIBLING_TYPE_CODE = 'VOLTAGE' THEN 3
                                                   WHEN A.SIBLING_TYPE_CODE = 'BTU' THEN 4
                                                   WHEN A.SIBLING_TYPE_CODE = 'SSD' THEN 5
                                                   WHEN A.SIBLING_TYPE_CODE = 'CPU' THEN 6
                                                   WHEN A.SIBLING_TYPE_CODE = 'MEMORY' THEN 7
                                                   WHEN A.SIBLING_TYPE_CODE = 'SERIES' THEN 8
                                                   ELSE 9
                                              END) AS PRIORITY_ORDER_NO
            , CASE WHEN F.EXP_SEQ = lag(F.EXP_SEQ) OVER(ORDER BY F.EXP_SEQ ASC
                                                               , B.SIBLING_GRP_CODE ASC
                                                               , B.SORT_SEQ ASC
                                                               , B.SIBLING_CODE asc)
                                    AND A.SIBLING_TYPE_CODE = lag(A.SIBLING_TYPE_CODE) OVER(ORDER BY F.EXP_SEQ ASC
                                                                                                   , B.SIBLING_GRP_CODE ASC
                                                                                                   , B.SORT_SEQ ASC
                                                                                                   , B.SIBLING_CODE asc)
                   THEN '2'
                   ELSE '1'
               END AS DISPLAY_1DEPTH
          FROM DSP_SIBLING_M A
         INNER JOIN DSP_SIBLING_D B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
           AND B.SIBLING_CODE = A.SIBLING_CODE
           AND B.SIBLING_TYPE_CODE = A.SIBLING_TYPE_CODE
           AND B.USE_FLAG = 'Y'
           AND B.SIBLING_GRP_USE_FLAG = 'Y'
           AND B.SIBLING_GRP_CODE IN (SELECT G.SIBLING_GRP_CODE
                                        FROM DSP_SIBLING_D G
                                       WHERE G.USE_FLAG = 'Y'
                                         AND G.SIBLING_GRP_USE_FLAG = 'Y'
                                         AND G.BIZ_TYPE_CODE = #{bizTypeCode}
                                         AND G.SITE_CODE = #{siteCode}
                                         AND G.PDP_ID = #{pdpId})
         INNER JOIN DSP_PDP_M C
            ON C.SITE_CODE = B.SITE_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.PDP_ID = B.PDP_ID
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.SITE_CODE = B.SITE_CODE
           AND D.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND D.PDP_ID = B.PDP_ID
           AND D.SHOP_CODE = 'D2C'
           AND D.USE_FLAG = 'Y'
           AND D.AEM_PUBL_FLAG = 'Y'
    <choose>
      <when test='standardFlag == "Y"'>
         INNER JOIN DSP_PDP_CATEGORY_R E
      </when>
      <otherwise>
         INNER JOIN DSP_OLD_PDP_CATEGORY_R E
      </otherwise>
    </choose>
            ON E.SITE_CODE = B.SITE_CODE
           AND E.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND E.DEFAULT_MAP_FLAG = 'Y'
           AND E.PDP_ID = B.PDP_ID
           AND E.USE_FLAG = 'Y'
         INNER JOIN DSP_SIBLING_EXP_SEQ_D F
            ON F.SITE_CODE = B.SITE_CODE
           AND F.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND F.SIBLING_TYPE_CODE = B.SIBLING_TYPE_CODE
    <choose>
      <when test='standardFlag == "Y"'>
           AND F.CATEGORY_CODE = E.LV3_CATEGORY_CODE
      </when>
      <otherwise>
           AND F.CATEGORY_CODE = E.LV2_CATEGORY_ID
      </otherwise>
    </choose>
           AND F.USE_FLAG = 'Y'
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         ORDER BY F.EXP_SEQ ASC
                , B.SIBLING_GRP_CODE ASC
                , B.SORT_SEQ ASC
                , B.SIBLING_CODE ASC
    </select>

    <select id="selectPdpUserReviewRatingData" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectPdpUserReviewRatingData */
               A.PDP_ID
             , A.SITE_CODE
             , IFNULL(A.PARTICIPANT_CNT, 0) AS P_COUNT
             , ROUND(IFNULL(A.STRARRATING_VAL, 0) * 20, 0) AS RATING_PERCENT
             , ROUND(IFNULL(A.STRARRATING_VAL, 0), 0) AS S_RATING
             , ROUND(IFNULL(A.STRARRATING_VAL, 0), 1) AS S_RATING2
          FROM DSP_USER_REVIEW_RATING_D A
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
           AND A.PDP_ID = #{pdpId}
         LIMIT 1
    </select>

    <select id="selectPdpUserReviewSiblingRatingData" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectPdpUserReviewSiblingRatingData */
               COUNT(A.REVIEW_AVG_SCOR) AS P_COUNT
               , ROUND(IFNULL(ROUND(SUM(A.REVIEW_AVG_SCOR) / COUNT(A.REVIEW_AVG_SCOR), 1), 0) * 20, 0) AS RATING_PERCENT
             , ROUND(SUM(A.REVIEW_AVG_SCOR) / COUNT(A.REVIEW_AVG_SCOR), 0) AS S_RATING
             , ROUND(SUM(A.REVIEW_AVG_SCOR) / COUNT(A.REVIEW_AVG_SCOR), 1) AS S_RATING2
          FROM DSP_REVIEW_M A
          LEFT JOIN
            (SELECT
                    DISTINCT B.REVIEW_ID
                  , 'Y' AS COMMENT_USE
                  , B.SITE_CODE
               FROM DSP_REVIEW_COMMENT_D B
              WHERE B.SITE_CODE = #{siteCode}
                AND B.COMMENT_STATE_CODE = 'A') C
            ON C.REVIEW_ID = A.REVIEW_ID
           AND C.SITE_CODE = A.SITE_CODE
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
           AND A.PDP_ID IN (
                SELECT D.PDP_ID
                  FROM DSP_SIBLING_D D
                  WHERE D.SITE_CODE = #{siteCode}
                    AND D.USE_FLAG = 'Y'
                    AND D.SIBLING_GRP_USE_FLAG = 'Y'
                    AND D.SIBLING_GRP_CODE IN (SELECT E.SIBLING_GRP_CODE
                                                   FROM DSP_SIBLING_D E
                                                  WHERE E.USE_FLAG = 'Y'
                                                    AND E.SIBLING_GRP_USE_FLAG = 'Y'
                                                    AND E.SITE_CODE =  #{siteCode}
                                                    AND E.PDP_ID = #{pdpId}
                                                 )
                )
           AND (A.REVIEW_STATE_CODE = 'A' OR C.COMMENT_USE = 'Y')
         LIMIT 1
    </select>

    <select id="selectAccessoryProductList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.AccessoryProductRequestVO" resultType="java.util.HashMap">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectAccessoryProductList */
               A.CMPAT_POSS_PRODUCT_NM AS model
          FROM DSP_ACCESSORY_PRODUCT_D A
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
           AND A.PDP_ID = #{pdpId}
        <if test="bizTypeCode != null and bizTypeCode != ''">
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
        </if>
        <if test="functionName == 'retrieveAutomaticCompatibleSearch'">
           AND UPPER(A.CMPAT_POSS_PRODUCT_NM) LIKE CONCAT('%', UPPER(#{searchModelNm}), '%')
        </if>
        <if test="functionName == 'retrieveCompatibleSearch'">
           AND UPPER(A.CMPAT_POSS_PRODUCT_NM) = UPPER(#{searchModelNm})
        </if>
         ORDER BY A.CMPAT_POSS_PRODUCT_NM ASC
    </select>

    <select id="selectProductAccessoryList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductAccessoryRequestVO" resultType="java.util.HashMap">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductAccessoryList */
               B.PRODUCT_NM AS model
        <if test='compatibleProducts3TypeUseFlag == "Y"'>
             , B.LGCOM_SKU_ID AS sku
        </if>
            FROM (
                 SELECT JSON_UNQUOTE(JSON_EXTRACT(
                        JSON_ARRAY(MAP_ACSRY_PDP_ID1, MAP_ACSRY_PDP_ID2, MAP_ACSRY_PDP_ID3, MAP_ACSRY_PDP_ID4, MAP_ACSRY_PDP_ID5, MAP_ACSRY_PDP_ID6, MAP_ACSRY_PDP_ID7, MAP_ACSRY_PDP_ID8, MAP_ACSRY_PDP_ID9, MAP_ACSRY_PDP_ID10),
                        CONCAT('$[', idx, ']')
                        )) AS PDP_ID
                      , A.SITE_CODE
                   FROM DSP_PRODUCT_ACCESSORY_D A
                  INNER JOIN (SELECT 0 AS idx UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4
                                UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9
                                ) AS idxs
                    WHERE A.SITE_CODE = #{siteCode}
                      AND A.PDP_ID = #{pdpId}
                      AND A.USE_FLAG = 'Y'
                ) AS A
         INNER JOIN DSP_PDP_M B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_CATEGORY_R C
            ON C.SITE_CODE = B.SITE_CODE
           AND C.PDP_ID = B.PDP_ID
           AND C.BIZ_TYPE_CODE  = B.BIZ_TYPE_CODE
           AND C.USE_FLAG = 'Y'
           AND C.DEFAULT_MAP_FLAG = 'Y'
         WHERE A.PDP_ID IS NOT NULL
           AND A.PDP_ID <![CDATA[<>]]> ''
    </select>

    <select id="selectProductAccessoryListNotStandard" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductAccessoryRequestVO" resultType="java.util.HashMap">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductAccessoryList */
               B.PRODUCT_NM AS model
        <if test='compatibleProducts3TypeUseFlag == "Y"'>
             , B.LGCOM_SKU_ID AS sku
        </if>
            FROM (
                 SELECT JSON_UNQUOTE(JSON_EXTRACT(
                        JSON_ARRAY(MAP_ACSRY_PDP_ID1, MAP_ACSRY_PDP_ID2, MAP_ACSRY_PDP_ID3, MAP_ACSRY_PDP_ID4, MAP_ACSRY_PDP_ID5, MAP_ACSRY_PDP_ID6, MAP_ACSRY_PDP_ID7, MAP_ACSRY_PDP_ID8, MAP_ACSRY_PDP_ID9, MAP_ACSRY_PDP_ID10),
                        CONCAT('$[', idx, ']')
                        )) AS PDP_ID
                      , A.SITE_CODE
                   FROM DSP_PRODUCT_ACCESSORY_D A
                  INNER JOIN (SELECT 0 AS idx UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4
                                UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9
                                ) AS idxs
                   WHERE A.SITE_CODE = #{siteCode}
                     AND A.PDP_ID = #{pdpId}
                     AND A.USE_FLAG = 'Y'
                ) AS A
         INNER JOIN DSP_PDP_M B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.PDP_ID = A.PDP_ID
           AND B.USE_FLAG = 'Y'
         INNER JOIN DSP_OLD_PDP_CATEGORY_R C
            ON C.SITE_CODE = B.SITE_CODE
           AND C.PDP_ID = B.PDP_ID
           AND C.BIZ_TYPE_CODE  = B.BIZ_TYPE_CODE
           AND C.USE_FLAG = 'Y'
           AND C.DEFAULT_MAP_FLAG = 'Y'
         WHERE A.PDP_ID IS NOT NULL
           AND A.PDP_ID <![CDATA[<>]]> ''
    </select>

    <select id="selectProductIconList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductIconListRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ProductIconListResponseVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductIconList */
               A.LGCOM_SKU_ID
               , B.PDP_ID
             , B.SCREEN_EXP_SEQ
             , B.PLP_FLAG
             , B.ICON_DIRECT_ADD_FLAG
               , IFNULL(B.PDP_LABEL_USE_FLAG, 'N') AS PDP_LABEL_USE_FLAG
             , C.ICON_ID
             , C.ICON_NM
             , C.ICON_DESC
             , C.IMG_PATH
             , C.IMG_ACCESS_URL
             , C.IMG_ALT_TEXT_CNTS
             , UPPER(C.ICON_TYPE_CODE) AS ICON_TYPE_CODE
             , C.LINK_URL
             , C.LINK_MODE_CODE
             , C.BOLDFT_USE_FLAG
             , C.ITALICFT_USE_FLAG
               , C.USE_BEGIN_DD
               , C.USE_END_DD
          FROM DSP_PDP_M A
         INNER JOIN DSP_ICON_PRODUCT_R B
            ON B.PDP_ID = A.PDP_ID
           AND B.SITE_CODE = A.SITE_CODE
          LEFT OUTER JOIN DSP_ICON_M C
            ON C.ICON_ID = B.ICON_ID
           AND C.SITE_CODE = A.SITE_CODE
           AND C.USE_FLAG = 'Y'
         <if test="repairabilityIndexFlag != null and repairabilityIndexFlag != ''">
             AND C.ICON_TYPE_CODE = 'Repairability Index'
         </if>
           WHERE B.USE_FLAG = 'Y'
         <if test="repairabilityIndexFlag == null or repairabilityIndexFlag == ''">
             AND B.PLP_FLAG = 'Y'
         </if>
             AND B.BIZ_TYPE_CODE = #{bizTypeCode}
             AND B.SITE_CODE = #{siteCode}
         <if test="pdpIdList != null">
            <foreach item="item" collection="pdpIdList" open="AND B.PDP_ID IN (" close=")" separator=",">
                #{item}
            </foreach>
          </if>
             AND ((STR_TO_DATE(#{today}, 'YYYYMMDD') BETWEEN C.USE_BEGIN_DD AND C.USE_END_DD) OR (C.USE_BEGIN_DD IS NULL OR C.USE_END_DD IS NULL))
           ORDER BY B.PDP_ID, B.SCREEN_EXP_SEQ
    </select>

    <select id="selectProductPanel" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductPanelEntityVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ProductPanelEntityVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductPanel */
               A.PDP_ID
             , A.SITE_CODE
             , A.PANEL_FILE_NM
             , A.PANEL_FILE_PATH
             , A.PANEL_FILE_EXETENSION_NM
          FROM DSP_PRODUCT_PANEL_D A
         WHERE A.PDP_ID = #{pdpId}
           AND A.SITE_CODE = #{siteCode}
           AND A.PANEL_FILE_USE_FLAG = 'Y'
    </select>

    <select id="selectObjetProduct" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetProductEntityVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetProductEntityVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectObjetProduct */
               B.LGCOM_SKU_ID
             , A.SITE_CODE
             , A.PANEL_TYPE_CODE
             , A.OBJET_PRODUCT_IMG_FULL_PATH
             , A.OBJET_PRODUCT_IMG_PATH
             , A.DESKTOP_BG_IMG_FULL_PATH
             , A.DESKTOP_BG_IMG_PATH
             , A.MOBL_BG_IMG_FULL_PATH
             , A.MOBL_BG_IMG_PATH
             , A.BG_IMG_USE_FLAG
             , A.DESKTOP_BANNER_IMG_FULL_PATH
             , A.DESKTOP_BANNER_IMG_PATH
             , A.MOBL_BANNER_IMG_FULL_PATH
             , A.MOBL_BANNER_IMG_PATH
             , A.DEFAULT_MATERIAL_ID
          FROM DSP_OBJET_PRODUCT_M A
         INNER JOIN DSP_PDP_M B
            ON A.OBJET_PDP_ID = B.PDP_ID
           AND A.SITE_CODE = B.SITE_CODE
         WHERE A.OBJET_PDP_ID = #{pdpId}
           AND A.USE_FLAG = 'Y'
    </select>

    <select id="selectObjetMaterialList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialEntityVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialEntityVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectObjetMaterialList */
               D.MATERIAL_ID
             , D.SITE_CODE
             , D.BIZ_TYPE_CODE
             , D.MATERIAL_NM
             , D.SITE_MATERIAL_NM
             , D.MATERIAL_DESC
             , D.MATERIAL_DESKTOP_IMG_NM
             , D.MATERIAL_MOBL_IMG_NM
             , D.MATERIAL_DESKTOP_IMG_PATH
             , D.MATERIAL_MOBL_IMG_PATH
             , D.MATERIAL_ALT_CNTS
             , A.PANEL_TYPE_CODE
          FROM DSP_OBJET_PRODUCT_M A
         INNER JOIN DSP_OBJET_PRODUCT_PANEL_R B
            ON B.OBJET_PDP_ID = A.OBJET_PDP_ID
           AND B.USE_FLAG = 'Y'
         INNER JOIN DSP_OBJET_PANEL_PRODUCT_D C
            ON C.PANEL_PDP_ID = B.PANEL_PDP_ID
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_OBJET_MATERIAL_M D
            ON D.MATERIAL_ID = C.MATERIAL_ID
           AND D.USE_FLAG = 'Y'
         INNER JOIN DSP_OBJET_PRODUCT_MATERIAL_R E
            ON E.OBJET_PDP_ID = A.OBJET_PDP_ID
           AND E.MATERIAL_ID = D.MATERIAL_ID
           AND E.USE_FLAG = 'Y'
         WHERE A.OBJET_PDP_ID = #{pdpId}
         ORDER BY E.SORT_SEQ ASC
    </select>

    <select id="selectObjetMaterialDetailList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialDetailEntityVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialDetailEntityVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectObjetMaterialDetailList */
               E.LGCOM_SKU_ID AS OBJET_PANEL_SKU
             , G.LGCOM_SKU_ID AS OBJET_SKU
             , C.PANEL_TYPE_CODE
             , C.SUB_PANEL_TYPE_CODE
             , C.PANEL_TYPE_IMG_PATH
             , B.MATERIAL_ID
             , B.COLOR_ID
             , A.COLOR_NM
             , A.COUNTRY_COLOR_NM
             , A.COLOR_IMG_PATH
             , H.USE_FLAG
          FROM DSP_OBJET_COLOR_M A
         INNER JOIN DSP_OBJET_PANEL_PRODUCT_D B
            ON B.MATERIAL_ID = A.MATERIAL_ID
           AND B.COLOR_ID = A.COLOR_ID
           AND B.USE_FLAG = 'Y'
         INNER JOIN DSP_OBJET_PRODUCT_PANEL_R C
            ON C.OBJET_PDP_ID = #{pdpId}
           AND C.PANEL_PDP_ID = B.PANEL_PDP_ID
           AND C.USE_FLAG = 'Y'
           AND C.SALES_FLAG = 'Y'
         INNER JOIN DSP_PDP_M E
            ON E.PDP_ID = B.PANEL_PDP_ID
         INNER JOIN DSP_PDP_D F
            ON F.PDP_ID = E.PDP_ID
           AND F.PRODUCT_STATE_CODE = 'ACTIVE'
           AND F.AEM_PUBL_FLAG = 'Y'
           AND F.SHOP_CODE = 'D2C'
         INNER JOIN DSP_PDP_M G
            ON G.PDP_ID = C.OBJET_PDP_ID
         INNER JOIN DSP_PDP_D H
            ON H.PDP_ID = G.PDP_ID
           AND H.PRODUCT_STATE_CODE = 'ACTIVE'
           AND H.AEM_PUBL_FLAG = 'Y'
           AND H.SHOP_CODE = 'D2C'
         ORDER BY A.MATERIAL_ID
                , C.SUB_PANEL_TYPE_CODE
                , C.DSP_SEQ
    </select>

    <select id="selectObjetPanelTypeList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetPanelTypeEntityVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetPanelTypeEntityVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectObjetPanelTypeList */
               A.SITE_CODE
             , A.PANEL_TYPE_CODE
             , A.PANEL_TYPE_NM
             , A.PANEL_TYPE_DESC
             , A.PANEL_TYPE_IMG_PATH
             , A.HIGH_LV_PANEL_TYPE_CODE
             , A.PANEL_TYPE_POSIT_NO
             , A.SORT_SEQ
             , A.USE_FLAG
             , A.CREATION_USER_ID
             , A.CREATION_DATE
             , A.LAST_UPDATE_USER_ID
             , A.LAST_UPDATE_DATE
             , C.COLOR_ID
          FROM DSP_OBJET_PANEL_TYPE_M A
         INNER JOIN DSP_OBJET_PRODUCT_M B
            ON B.PANEL_TYPE_CODE = A.HIGH_LV_PANEL_TYPE_CODE
           AND B.OBJET_PDP_ID = #{pdpId}
         INNER JOIN DSP_OBJET_PRODUCT_COLOR_R C
            ON C.OBJET_PDP_ID = B.OBJET_PDP_ID
           AND C.USE_FLAG = 'Y'
           AND C.MATERIAL_ID = B.DEFAULT_MATERIAL_ID
           AND C.PANEL_TYPE_CODE = A.PANEL_TYPE_CODE
         WHERE A.USE_FLAG = 'Y'
         ORDER BY A.SORT_SEQ ASC
    </select>

    <select id="selectProductBundleList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleResponseVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductBundleList */
               A.PDP_ID
             , A.LGCOM_SKU_ID
             , A.SKU_ID
             , A.PRODUCT_NM
             , A.PDP_TYPE_CODE
             , A.USER_FRNDY_PRODUCT_NM
             , IFNULL(A.MSRP_SALES_PRICE, 0.00) AS MSRP
             , A.EXCL_PRODUCT_SETT_CODE
             , A.ELABEL_GRD_CODE
             , A.ELABEL_CLS_CODE
             , A.WTOWER_PRODUCT_FLAG
             , A.SECOND_ELABEL_GRD_CODE
             , A.SECOND_ELABEL_CLS_CODE
             , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productYear
          <choose>
            <when test='standardFlag == "Y"'>
             , C.LV2_CATEGORY_CODE AS superCategoryId
            </when>
            <otherwise>
             , C.LV1_CATEGORY_ID AS superCategoryId
            </otherwise>
          </choose>
             , D.SITE_CATEGORY_NM
             , E.IMG_ALT_TEXT_CNTS
             , E.MDM_IMG_URL
             , E.PDP_URL
             , E.PRODUCT_STATE_CODE
             , H.SIBLING_GRP_CODE
             , H.SIBLING_GRP_NM
             , H.SIBLING_SBJ_TYPE_CODE
             , IFNULL(H.DEFAULT_SIBLING_MODEL_FLAG, 'N') AS DEFAULT_SIBLING_MODEL_FLAG
             , PRS.PROMOTION_TAG_VAL AS PROMOTION_TEXT
          FROM DSP_PDP_M A
         INNER JOIN DSP_PRODUCT_BUNDLE_D B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.USE_FLAG = 'Y'
        <choose>
          <when test='pdpTypeCode == "B" or pdpTypeCode == "O"'>
           AND B.BUNDLE_PDP_ID = A.PDP_ID
           AND B.PDP_ID = #{pdpId}
          </when>
          <otherwise>
           AND B.PDP_ID = A.PDP_ID
           AND B.BUNDLE_PDP_ID = #{pdpId}
          </otherwise>
        </choose>
        <choose>
          <when test='standardFlag == "Y"'>
         INNER JOIN DSP_PDP_CATEGORY_R C
          </when>
          <otherwise>
         INNER JOIN DSP_OLD_PDP_CATEGORY_R C
          </otherwise>
        </choose>
            ON C.PDP_ID = A.PDP_ID
           AND C.SITE_CODE = A.SITE_CODE
           AND C.USE_FLAG = 'Y'
           AND C.DEFAULT_MAP_FLAG = 'Y'
         INNER JOIN DSP_DISPLAY_CATEGORY_M D
        <choose>
          <when test='standardFlag == "Y"'>
            ON D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
          </when>
          <otherwise>
            ON D.CATEGORY_CODE = C.LV2_CATEGORY_ID
          </otherwise>
        </choose>
           AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
           AND D.SITE_CODE = C.SITE_CODE
           AND D.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D E
            ON E.PDP_ID = A.PDP_ID
           AND E.SITE_CODE = A.SITE_CODE
           AND E.USE_FLAG = 'Y'
           AND E.AEM_PUBL_FLAG = 'Y'
           AND E.SHOP_CODE = 'D2C'
           AND E.PRODUCT_STATE_CODE <![CDATA[<>]]> 'SUSPENDED'
          LEFT OUTER JOIN (SELECT
                                  F.PDP_ID
                                , F.SIBLING_GRP_CODE
                                , F.SIBLING_GRP_NM
                                , F.DEFAULT_SIBLING_MODEL_FLAG
                                , F.SIBLING_SBJ_TYPE_CODE
                                , G.CATEGORY_CODE
                             FROM DSP_SIBLING_D F
                            INNER JOIN DSP_SIBLING_EXP_SEQ_D G
                               ON G.SIBLING_TYPE_CODE = F.SIBLING_TYPE_CODE
                              AND G.USE_FLAG = 'Y'
                              AND G.BIZ_TYPE_CODE = F.BIZ_TYPE_CODE
                              AND G.SITE_CODE = #{siteCode}
                              AND G.PLP_DEFAULT_SIBLING_FLAG = 'Y'
                            WHERE F.USE_FLAG = 'Y'
                              AND F.SIBLING_GRP_USE_FLAG = 'Y'
                              AND F.SITE_CODE = #{siteCode}
                              AND F.BIZ_TYPE_CODE = #{bizTypeCode}) H
            ON H.PDP_ID = A.PDP_ID
         <choose>
           <when test='standardFlag == "Y"'>
           AND H.CATEGORY_CODE = C.LV3_CATEGORY_CODE
           </when>
           <otherwise>
           AND H.CATEGORY_CODE = C.LV2_CATEGORY_ID
           </otherwise>
         </choose>
         LEFT JOIN (SELECT PRSSUB.*
                           FROM 
                                (SELECT PMM.PDP_ID
                                       ,MPM.PROMOTION_TAG_VAL
                                   FROM PRM_PROMOTION_PRODUCT_R PMM
                                  INNER JOIN PRM_PROMOTION_M MPM
                                     ON PMM.PROMOTION_ID = MPM.PROMOTION_ID
                                    AND PMM.SITE_CODE = MPM.SITE_CODE
                                  WHERE MPM.SITE_CODE = #{siteCode}
                                    AND MPM.USE_FLAG = 'Y'
                                    AND DATE_FORMAT(#{today},'%Y%m%d') BETWEEN MPM.PROMOTION_EXP_BEGIN_DATE AND MPM.PROMOTION_EXP_END_DATE
                                    AND PMM.USE_FLAG = 'Y'
                                    AND IFNULL(MPM.PROMOTION_NONEXP_FLAG,'N') <![CDATA[<>]]> 'Y'
                                  ORDER BY PMM.PROMOTION_PAGE_PRODUCT_SEQ IS NULL ASC, PMM.PROMOTION_PAGE_PRODUCT_SEQ ASC
                                  ) PRSSUB
                                   GROUP BY PRSSUB.PDP_ID
                            ) PRS
                   ON A.PDP_ID = PRS.PDP_ID
         WHERE A.SITE_CODE = #{siteCode}
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
           AND A.USE_FLAG = 'Y'
         ORDER BY B.DSP_SEQ ASC
    </select>

    <select id="selectProductSpecBundleSimpleList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductSpecBundleSimpleListRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ProductSpecBundleSimpleListResponseVO">
        SELECT D.LGCOM_SKU_ID
             , D.SKU_ID AS PIM_SKU_ID
             , B.LGCOM_SKU_ID  AS BUNDLE_SKU_ID
             , B.SKU_ID AS BUNDLE_PIM_SKU_ID
             , A.PDP_ID
             , A.BUNDLE_PDP_ID
             , B.PRODUCT_NM AS BUNDLE_MODEL_NAME
             , C.BIG_IMG_URL
             , C.MDM_IMG_URL
             , C.SML_IMG_URL
             , C.IMG_ALT_TEXT_CNTS
             , B.USER_FRNDY_PRODUCT_NM
             , IFNULL(H.PARTICIPANT_CNT, 0) AS P_COUNT
             , ROUND(IFNULL(H.STRARRATING_VAL, 0) * 20, 0) AS RATING_PERCENT
             , ROUND(IFNULL(H.STRARRATING_VAL, 0), 0) AS S_RATING
             , ROUND(IFNULL(H.STRARRATING_VAL, 0), 1) AS S_RATING2
             , C.PDP_URL
             , IF(G.STOCK_STATE_CODE = 'IN_STOCK', 'Y' , 'N') AS OBS_SELL_FLAG
             , B.WTB_USE_FLAG
             , B.WTB_EXTL_LINK_USE_FLAG
             , B.WTB_EXTL_LINK_NM
             , B.WTB_EXTL_LINK_URL
             , B.WTB_EXTL_LINK_SELF_SCREEN_FLAG
             , C.PRODUCT_STATE_CODE
             , F.SITE_CATEGORY_NM
          FROM DSP_PRODUCT_BUNDLE_D A
         INNER JOIN DSP_PDP_M B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.PDP_ID = A.BUNDLE_PDP_ID
        <if test="bizTypeCode != null and bizTypeCode != ''">
           AND B.BIZ_TYPE_CODE = 'B2C'
        </if>
           AND B.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D C
            ON C.SITE_CODE = A.SITE_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.PDP_ID = A.BUNDLE_PDP_ID
           AND C.PRODUCT_STATE_CODE <![CDATA[<>]]> 'SUSPENDED'
           AND C.AEM_PUBL_FLAG = 'Y'
           AND C.SHOP_CODE = 'D2C'
         INNER JOIN DSP_PDP_M D
            ON D.SITE_CODE = A.SITE_CODE
           AND D.PDP_ID = A.PDP_ID
           AND D.USE_FLAG = 'Y'
        <choose>
          <when test='standardFlag == "Y"'>
         INNER JOIN DSP_PDP_CATEGORY_R E
          </when>
          <otherwise>
         INNER JOIN DSP_OLD_PDP_CATEGORY_R E
          </otherwise>
        </choose>
            ON E.PDP_ID = A.BUNDLE_PDP_ID
           AND E.SITE_CODE = A.SITE_CODE
           AND E.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND E.DEFAULT_MAP_FLAG = 'Y'
           AND E.USE_FLAG = 'Y'
         INNER JOIN DSP_DISPLAY_CATEGORY_M F
        <choose>
          <when test='standardFlag == "Y"'>
            ON F.CATEGORY_CODE = E.LV3_CATEGORY_CODE
          </when>
          <otherwise>
            ON F.CATEGORY_CODE = E.LV2_CATEGORY_ID
          </otherwise>
        </choose>
           AND F.SITE_CODE = B.SITE_CODE
           AND F.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND F.USE_FLAG = 'Y'
         INNER JOIN ECM_STOCK_D G
            ON B.LGCOM_SKU_ID = CONCAT(SUBSTRING_INDEX(G.SKU_ID , '.', LENGTH(G.SKU_ID) - LENGTH(REPLACE(G.SKU_ID, '.', ''))), '.', G.STORE_CODE, '.C') 
           AND B.SITE_CODE = G.STORE_CODE
        <choose>
          <when test="reviewType != null and reviewType != '' and reviewType == 'LGCOM' ">
          LEFT OUTER JOIN DSP_USER_REVIEW_RATING_D H
          </when>
          <otherwise>
          LEFT OUTER JOIN PDSMGR_PUBL.DSP_USER_REVIEW_RATING_D H
          </otherwise>
        </choose>
            ON H.USE_FLAG = 'Y'
           AND H.SITE_CODE = A.SITE_CODE
           AND H.PDP_ID = A.BUNDLE_PDP_ID
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
           AND A.PDP_ID = #{pdpId}
         ORDER BY A.DSP_SEQ ASC
    </select>

    <select id="selectDefaultSiblingModelInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectDefaultSiblingModelInfo */
               A.SIBLING_CODE
              ,A.SIBLING_TYPE_CODE
              ,B.SIBLING_GRP_CODE
              ,B.DEFAULT_SIBLING_MODEL_FLAG
              ,C.PDP_ID
              ,C.SKU_ID
              ,C.SITE_CODE
              ,C.BIZ_TYPE_CODE
              ,C.PRODUCT_NM
              ,C.PDP_TYPE_CODE
              ,D.PRODUCT_STATE_CODE
              ,F.PDP_TITLE
              ,B.SIBLING_SBJ_TYPE_CODE
          FROM DSP_SIBLING_M A
         INNER JOIN DSP_SIBLING_D B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
           AND B.SIBLING_TYPE_CODE = A.SIBLING_TYPE_CODE
           AND B.SIBLING_CODE = A.SIBLING_CODE
           AND B.SIBLING_GRP_USE_FLAG = 'Y'
           AND B.DEFAULT_SIBLING_MODEL_FLAG = 'Y'
           AND B.USE_FLAG = 'Y'
           AND B.SIBLING_GRP_CODE IN (SELECT H.SIBLING_GRP_CODE
                                        FROM DSP_SIBLING_M G
                                       INNER JOIN DSP_SIBLING_D H
                                          ON H.SITE_CODE = G.SITE_CODE
                                         AND H.BIZ_TYPE_CODE = G.BIZ_TYPE_CODE
                                         AND H.SIBLING_TYPE_CODE = G.SIBLING_TYPE_CODE
                                         AND H.SIBLING_CODE = G.SIBLING_CODE
                                         AND H.SIBLING_GRP_USE_FLAG = 'Y'
                                         AND H.USE_FLAG = 'Y'
                                         AND H.PDP_ID = #{pdpId}
                                       WHERE G.USE_FLAG = 'Y'
                                         AND G.SITE_CODE = #{siteCode}
                                         AND G.BIZ_TYPE_CODE = #{bizTypeCode})
         INNER JOIN DSP_PDP_M C
            ON C.PDP_ID = B.PDP_ID
           AND C.SITE_CODE = A.SITE_CODE
           AND C.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
           AND C.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D D
            ON D.PDP_ID = C.PDP_ID
           AND D.SITE_CODE = C.SITE_CODE
           AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
           AND D.SHOP_CODE = 'D2C'
           AND D.AEM_PUBL_FLAG = 'Y'
           AND D.USE_FLAG = 'Y'
        <if test="productStatusCode != null and productStatusCode != ''">
           AND D.PRODUCT_STATE_CODE = #{productStatusCode}
        </if>
        <choose>
           <when test='standardFlag == "Y"'>
          LEFT OUTER JOIN DSP_PDP_CATEGORY_R E
           </when>
           <otherwise>
          LEFT OUTER JOIN DSP_OLD_PDP_CATEGORY_R E
           </otherwise>
         </choose>
            ON E.PDP_ID = B.PDP_ID
           AND E.SITE_CODE = B.SITE_CODE
           AND E.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND E.DEFAULT_MAP_FLAG = 'Y'
           AND E.USE_FLAG = 'Y'
          LEFT OUTER JOIN DSP_SIBLING_EXP_SEQ_D F
        <choose>
           <when test='standardFlag == "Y"'>
            ON F.CATEGORY_CODE = E.LV3_CATEGORY_CODE
           </when>
           <otherwise>
            ON F.CATEGORY_CODE = E.LV2_CATEGORY_ID
           </otherwise>
         </choose>
            AND F.SITE_CODE = B.SITE_CODE
            AND F.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
            AND F.SIBLING_TYPE_CODE = B.SIBLING_TYPE_CODE
            AND F.USE_FLAG = 'Y'
          WHERE A.USE_FLAG = 'Y'
            AND A.SITE_CODE = #{siteCode}
            AND A.BIZ_TYPE_CODE = #{bizTypeCode}
          ORDER BY F.EXP_SEQ, B.SIBLING_GRP_CODE, B.SORT_SEQ, A.SIBLING_CODE
          LIMIT 1
    </select>

    <select id="selectProductVideoInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductVideoRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ProductVideoResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductVideoInfo */
               PDP_ID
             , SITE_CODE
             , DSP_SEQ
             , VIDEO_TYPE_CODE
             , VIDEO_TYPE_NM
             , VIDEO_ID
             , VIDEO_CNTS
             , VIDEO_THUMBNAIL_IMG_PATH
             , VIDEO_EMBEDED_URL
             , VIDEO_PREVIEW_URL
          FROM DSP_PRODUCT_VIDEO_R
         WHERE USE_FLAG = 'Y'
           AND SITE_CODE = #{siteCode}
           AND PDP_ID = #{pdpId}
         ORDER BY DSP_SEQ ASC
         LIMIT 2
    </select>

    <select id="selectMtsPdpBasicInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectMtsPdpBasicInfo */
             A.PDP_ID AS pdpId
           , A.SITE_CODE AS siteCode
           , A.SKU_ID AS skuId
           , A.LGCOM_SKU_ID AS lgcomSkuId
           , A.BIZ_TYPE_CODE AS bizTypeCode
             , A.PDP_TYPE_CODE AS pdpTypeCode
             , CASE WHEN A.PDP_TYPE_CODE = 'A'
                    THEN 'ADP'
                    WHEN A.PDP_TYPE_CODE = 'B'
                    THEN 'BDP'
                    WHEN A.PDP_TYPE_CODE = 'O'
                    THEN 'ODP'
                    ELSE 'PDP'
               END pdpType
           , A.PRODUCT_NM AS productNm
           , A.USER_FRNDY_PRODUCT_NM AS userFrndyProductNm
        <choose>
           <when test='siteCode == "SA" || siteCode == "SA_EN" || siteCode == "CL"'>
           , ROUND(IFNULL(A.MSRP_SALES_PRICE, 0.00), 2) AS msrpSalesPrice
           </when>
           <otherwise>
           , IFNULL(A.MSRP_SALES_PRICE, 0.00) AS msrpSalesPrice
           </otherwise>
        </choose>
           , A.CATEGORY_CODE AS categoryCode
           , A.EXCL_PRODUCT_SETT_CODE AS exclProductSettCode
           , A.INCH_VAL AS inchVal
           , A.ELABEL_GRD_CODE AS elabelGrdCode
           , A.ELABEL_CLS_CODE AS elabelClsCode
           , A.SECOND_ELABEL_GRD_CODE AS secondElabelGrdCode
           , A.SECOND_ELABEL_CLS_CODE AS secondElabelClsCode
           , A.PRODUCT_RELES_DD AS productRelesDd
           , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productYear
           , A.WTB_OFFLINE_USE_FLAG AS wtbOfflineUseFlag
           , A.WTB_ONLINE_USE_FLAG AS wtbOnlineUseFlag
           , A.WTB_DIRECT_USE_FLAG AS wtbDirectUseFlag
           , A.WTB_USE_FLAG AS wtbUseFlag
           , A.WTB_EXTL_LINK_USE_FLAG AS wtbExtlLinkUseFlag
           , A.WTB_EXTL_LINK_NM AS wtbExtlLinkNm
           , A.WTB_EXTL_LINK_URL AS wtbExtlLinkUrl
           , A.WTB_EXTL_LINK_SELF_SCREEN_FLAG AS wtbExtlLinkSelfScreenFlag
           , A.ITB_USE_FLAG AS itbUseFlag
           , A.THINQ_PRODUCT_FLAG AS thinqProductFlag
           , A.SIGNT_PRODUCT_FLAG AS signtProductFlag
           , A.OBJET_ACSRY_FLAG AS objetAcsryFlag
           , A.OBJET_PRODUCT_FLAG AS objetProductFlag
           , A.WTOWER_PRODUCT_FLAG AS wtowerProductFlag
           , B.SHOP_CODE AS shopCode
           , B.SML_IMG_URL AS smlImgUrl
           , B.MDM_IMG_URL AS mdmImgUrl
           , B.BIG_IMG_URL AS bigImgUrl
           , B.IMG_ALT_TEXT_CNTS AS imgAltTextCnts
           , B.BUNDLE_IMG_URL AS bundleImgUrl
           , B.BUNDLE_MOBL_IMG_URL AS bundleMoblImgUrl
           , B.BUNDLE_IMG_ALT_TEXT_CNTS AS bundleImgAltTextCnts
           , B.BUNDLE_DESC AS bundleDesc
           , B.PRODUCT_STATE_CODE AS productStateCode
           , B.PDP_URL AS pdpUrl
           , B.DEFAULT_PRODUCT_TAG_CODE AS defaultProductTagCode
           , B.PRODUCT_TAG_CODE1 AS productTagCode1
           , B.PRODUCT_TAG_EXP_BEGIN_DATE1 AS productTagExpBeginDate1
           , B.PRODUCT_TAG_EXP_END_DATE1 AS productTagExpEndDate1
           , B.PRODUCT_TAG_USE_FLAG1 AS productTagUseFlag1
           , B.PRODUCT_TAG_CODE2 AS productTagCode2
           , B.PRODUCT_TAG_EXP_BEGIN_DATE2 AS productTagExpBeginDate2
           , B.PRODUCT_TAG_EXP_END_DATE2 AS productTagExpEndDate2
           , B.PRODUCT_TAG_USE_FLAG2 AS productTagUseFlag2
           <if test="timezone != null and timezone != ''">
           , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                  THEN NULL
                  WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1
                  THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG1 = 'Y'
                             THEN B.PRODUCT_TAG_CODE1
                             ELSE NULL
                         END)
                  ELSE NULL
              END AS realProductTag1
           , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                  THEN NULL
                  WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1
                  THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG1 = 'Y'
                             THEN TO_CHAR(B.PRODUCT_TAG_EXP_END_DATE1, 'YYYYMMDD')
                             ELSE NULL
                         END)
                  ELSE NULL
              END AS realProductTag1EndDate
           , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                  THEN NULL
                  WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2
                  THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG2 = 'Y'
                             THEN B.PRODUCT_TAG_CODE2
                             ELSE NULL
                         END)
                  ELSE NULL
              END AS realProductTag2
           , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                  THEN NULL
                  WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2
                  THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG2 = 'Y'
                             THEN TO_CHAR(B.PRODUCT_TAG_EXP_END_DATE2, 'YYYYMMDD')
                             ELSE NULL
                         END)
                  ELSE NULL
              END AS realProductTag2EndDate
              </if>
           , B.PRODUCT_THEME_TYPE_CODE AS productThemeTypeCode
           , B.AEM_PUBL_FLAG AS aemPublFlag
           , B.AEM_PUBL_DATE AS aemPublDate
           , E.SEO_PAGE_TITLE AS seoPageTitle
           , E.SEO_DESC AS seoDesc
        FROM DSP_PDP_M A
       INNER JOIN DSP_PDP_D B
          ON B.SITE_CODE = A.SITE_CODE
         AND B.PDP_ID = A.PDP_ID
         AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
       <if test="productStateCode != null and productStateCode != ''">
         AND B.PRODUCT_STATE_CODE = #{productStateCode}
       </if>
         AND B.SHOP_CODE = #{shopCode}
         AND B.USE_FLAG = 'Y'
         AND B.AEM_PUBL_FLAG = 'Y'
       INNER JOIN DSP_PDP_CATEGORY_R C
          ON C.PDP_ID = A.PDP_ID
         AND C.SITE_CODE = A.SITE_CODE
         AND C.USE_FLAG = 'Y'
         AND C.DEFAULT_MAP_FLAG = 'Y'
       INNER JOIN DSP_DISPLAY_CATEGORY_M D
          ON D.SITE_CODE = C.SITE_CODE
         AND D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
         AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
         AND D.USE_FLAG = 'Y'
       INNER JOIN DSP_DISPLAY_CATEGORY_D F
          ON F.SITE_CODE = D.SITE_CODE
         AND F.CATEGORY_CODE = D.CATEGORY_CODE
         AND F.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
         AND F.USE_FLAG = 'Y'
         AND F.SHOP_CODE = #{shopCode}
        LEFT OUTER JOIN DSP_PRODUCT_SEO_INFO_M E
          ON E.SITE_CODE = B.SITE_CODE
         AND E.PDP_URL = B.PDP_URL
       <if test="pdpId != null and pdpId != ''">
       WHERE A.PDP_ID = #{pdpId}
       </if>
       <if test="lgcomSkuId != null and lgcomSkuId != ''">
       WHERE A.LGCOM_SKU_ID = #{lgcomSkuId}
       </if>
       <if test="customerGroup != null and customerGroup !=''">
         AND (NOT EXISTS (SELECT 1
                               FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
                              WHERE SHOP_CODE = #{shopCode}
                                AND SITE_CODE = #{siteCode}
                                AND PDP_ID = A.PDP_ID
                                AND USE_FLAG = 'Y')
               OR EXISTS (SELECT 1
                               FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
                              WHERE SHOP_CODE = #{shopCode}
                                AND CUST_GRP_CODE = #{customerGroup}
                                AND SITE_CODE = #{siteCode}
                                AND PDP_ID = A.PDP_ID
                                AND USE_FLAG = 'Y'))
        </if>
    </select>

    <select id="selectMtsPdpBasicInfoNotStandard" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpResponseVO">
      SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectMtsPdpBasicInfo */
             A.PDP_ID AS pdpId
           , A.SITE_CODE AS siteCode
           , A.SKU_ID AS skuId
           , A.LGCOM_SKU_ID AS lgcomSkuId
           , A.BIZ_TYPE_CODE AS bizTypeCode
             , A.PDP_TYPE_CODE AS pdpTypeCode
             , CASE WHEN A.PDP_TYPE_CODE = 'A'
                    THEN 'ADP'
                    WHEN A.PDP_TYPE_CODE = 'B'
                    THEN 'BDP'
                    WHEN A.PDP_TYPE_CODE = 'O'
                    THEN 'ODP'
                    ELSE 'PDP'
               END pdpType
           , A.PRODUCT_NM AS productNm
           , A.USER_FRNDY_PRODUCT_NM AS userFrndyProductNm
        <choose>
           <when test='siteCode == "SA" || siteCode == "SA_EN" || siteCode == "CL"'>
           , ROUND(IFNULL(A.MSRP_SALES_PRICE, 0.00), 2) AS msrpSalesPrice
           </when>
           <otherwise>
           , IFNULL(A.MSRP_SALES_PRICE, 0.00) AS msrpSalesPrice
           </otherwise>
        </choose>
           , A.CATEGORY_CODE AS categoryCode
           , A.EXCL_PRODUCT_SETT_CODE AS exclProductSettCode
           , A.INCH_VAL AS inchVal
           , A.ELABEL_GRD_CODE AS elabelGrdCode
           , A.ELABEL_CLS_CODE AS elabelClsCode
           , A.SECOND_ELABEL_GRD_CODE AS secondElabelGrdCode
           , A.SECOND_ELABEL_CLS_CODE AS secondElabelClsCode
           , A.PRODUCT_RELES_DD AS productRelesDd
           , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productYear
           , A.WTB_OFFLINE_USE_FLAG AS wtbOfflineUseFlag
           , A.WTB_ONLINE_USE_FLAG AS wtbOnlineUseFlag
           , A.WTB_DIRECT_USE_FLAG AS wtbDirectUseFlag
           , A.WTB_USE_FLAG AS wtbUseFlag
           , A.WTB_EXTL_LINK_USE_FLAG AS wtbExtlLinkUseFlag
           , A.WTB_EXTL_LINK_NM AS wtbExtlLinkNm
           , A.WTB_EXTL_LINK_URL AS wtbExtlLinkUrl
           , A.WTB_EXTL_LINK_SELF_SCREEN_FLAG AS wtbExtlLinkSelfScreenFlag
           , A.ITB_USE_FLAG AS itbUseFlag
           , A.THINQ_PRODUCT_FLAG AS thinqProductFlag
           , A.SIGNT_PRODUCT_FLAG AS signtProductFlag
           , A.OBJET_ACSRY_FLAG AS objetAcsryFlag
           , A.OBJET_PRODUCT_FLAG AS objetProductFlag
           , A.WTOWER_PRODUCT_FLAG AS wtowerProductFlag
           , B.SHOP_CODE AS shopCode
           , B.SML_IMG_URL AS smlImgUrl
           , B.MDM_IMG_URL AS mdmImgUrl
           , B.BIG_IMG_URL AS bigImgUrl
           , B.IMG_ALT_TEXT_CNTS AS imgAltTextCnts
           , B.BUNDLE_IMG_URL AS bundleImgUrl
           , B.BUNDLE_MOBL_IMG_URL AS bundleMoblImgUrl
           , B.BUNDLE_IMG_ALT_TEXT_CNTS AS bundleImgAltTextCnts
           , B.BUNDLE_DESC AS bundleDesc
           , B.PRODUCT_STATE_CODE AS productStateCode
           , B.PDP_URL AS pdpUrl
           , B.DEFAULT_PRODUCT_TAG_CODE AS defaultProductTagCode
           , B.PRODUCT_TAG_CODE1 AS productTagCode1
           , B.PRODUCT_TAG_EXP_BEGIN_DATE1 AS productTagExpBeginDate1
           , B.PRODUCT_TAG_EXP_END_DATE1 AS productTagExpEndDate1
           , B.PRODUCT_TAG_USE_FLAG1 AS productTagUseFlag1
           , B.PRODUCT_TAG_CODE2 AS productTagCode2
           , B.PRODUCT_TAG_EXP_BEGIN_DATE2 AS productTagExpBeginDate2
           , B.PRODUCT_TAG_EXP_END_DATE2 AS productTagExpEndDate2
           , B.PRODUCT_TAG_USE_FLAG2 AS productTagUseFlag2
           <if test="timezone != null and timezone != ''">
           , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                  THEN NULL
                  WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1
                  THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG1 = 'Y'
                             THEN B.PRODUCT_TAG_CODE1
                             ELSE NULL
                         END)
                  ELSE NULL
              END AS realProductTag1
           , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                  THEN NULL
                  WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE1 AND B.PRODUCT_TAG_EXP_END_DATE1
                  THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG1 = 'Y'
                             THEN TO_CHAR(B.PRODUCT_TAG_EXP_END_DATE1, 'YYYYMMDD')
                             ELSE NULL
                         END)
                  ELSE NULL
              END AS realProductTag1EndDate
           , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                  THEN NULL
                  WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2
                  THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG2 = 'Y'
                             THEN B.PRODUCT_TAG_CODE2
                             ELSE NULL
                         END)
                  ELSE NULL
              END AS realProductTag2
           , CASE WHEN B.PRODUCT_STATE_CODE = 'DISCONTINUED'
                  THEN NULL
                  WHEN CONVERT_TZ(NOW(),'Asia/Seoul', #{timezone}) BETWEEN B.PRODUCT_TAG_EXP_BEGIN_DATE2 AND B.PRODUCT_TAG_EXP_END_DATE2
                  THEN (CASE WHEN B.PRODUCT_TAG_USE_FLAG2 = 'Y'
                             THEN TO_CHAR(B.PRODUCT_TAG_EXP_END_DATE2, 'YYYYMMDD')
                             ELSE NULL
                         END)
                  ELSE NULL
              END AS realProductTag2EndDate
              </if>
           , B.PRODUCT_THEME_TYPE_CODE AS productThemeTypeCode
           , B.AEM_PUBL_FLAG AS aemPublFlag
           , B.AEM_PUBL_DATE AS aemPublDate
           , E.SEO_PAGE_TITLE AS seoPageTitle
           , E.SEO_DESC AS seoDesc
        FROM DSP_PDP_M A
       INNER JOIN DSP_PDP_D B
          ON B.SITE_CODE = A.SITE_CODE
         AND B.PDP_ID = A.PDP_ID
         AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
       <if test="productStateCode != null and productStateCode != ''">
         AND B.PRODUCT_STATE_CODE = #{productStateCode}
       </if>
         AND B.SHOP_CODE = #{shopCode}
         AND B.USE_FLAG = 'Y'
         AND B.AEM_PUBL_FLAG = 'Y'
       INNER JOIN DSP_OLD_PDP_CATEGORY_R C
          ON C.PDP_ID = A.PDP_ID
         AND C.SITE_CODE = A.SITE_CODE
         AND C.USE_FLAG = 'Y'
         AND C.DEFAULT_MAP_FLAG = 'Y'
       INNER JOIN DSP_DISPLAY_CATEGORY_M D
          ON D.SITE_CODE = C.SITE_CODE
         AND D.CATEGORY_CODE = C.LV2_CATEGORY_ID
         AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
         AND D.USE_FLAG = 'Y'
       INNER JOIN DSP_DISPLAY_CATEGORY_D F
          ON F.SITE_CODE = D.SITE_CODE
         AND F.CATEGORY_CODE = D.CATEGORY_CODE
         AND F.BIZ_TYPE_CODE = D.BIZ_TYPE_CODE
         AND F.USE_FLAG = 'Y'
         AND F.SHOP_CODE = #{shopCode}
        LEFT OUTER JOIN DSP_PRODUCT_SEO_INFO_M E
          ON E.SITE_CODE = B.SITE_CODE
         AND E.PDP_URL = B.PDP_URL
       <if test="pdpId != null and pdpId != ''">
       WHERE A.PDP_ID = #{pdpId}
       </if>
       <if test="lgcomSkuId != null and lgcomSkuId != ''">
       WHERE A.LGCOM_SKU_ID = #{lgcomSkuId}
       </if>
       <if test="customerGroup != null and customerGroup !=''">
         AND (NOT EXISTS (SELECT 1
                               FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
                              WHERE SHOP_CODE = #{shopCode}
                                AND SITE_CODE = #{siteCode}
                                AND PDP_ID = A.PDP_ID
                                AND USE_FLAG = 'Y')
               OR EXISTS (SELECT 1
                               FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
                              WHERE SHOP_CODE = #{shopCode}
                                AND CUST_GRP_CODE = #{customerGroup}
                                AND SITE_CODE = #{siteCode}
                                AND PDP_ID = A.PDP_ID
                                AND USE_FLAG = 'Y'))
        </if>
    </select>

    <select id="selectMtsPdpSiblingDataList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectMtsPdpSiblingData */
               C.LGCOM_SKU_ID
             , C.SKU_ID
             , C.PDP_ID
             , C.PRODUCT_NM
             , C.BIZ_TYPE_CODE
             , CASE WHEN C.SITE_CODE = 'SA' OR C.SITE_CODE = 'SA_EN' OR C.SITE_CODE = 'CL'
                    THEN ROUND(IFNULL(C.MSRP_SALES_PRICE, 0.00), 2)
                    ELSE IFNULL(C.MSRP_SALES_PRICE, 0.00)
                END MSRP
             , C.USER_FRNDY_PRODUCT_NM
             , D.PRODUCT_STATE_CODE
             , A.SITE_CODE
             , B.DEFAULT_SIBLING_MODEL_FLAG
             , F.PDP_TITLE
             , B.SIBLING_CODE
             , B.SIBLING_GRP_CODE
             , IFNULL(A.SIBLING_LOCAL_VAL, A.SIBLING_CODE) AS SIBLING_VALUE
             , B.SIBLING_TYPE_CODE
             , D.PDP_URL
             , B.PRICE_USE_FLAG
             , B.SIBLING_GRP_NM
             , B.SIBLING_SBJ_TYPE_CODE
          FROM DSP_SIBLING_M A
         INNER JOIN DSP_SIBLING_D B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.BIZ_TYPE_CODE = A.BIZ_TYPE_CODE
           AND B.SIBLING_CODE = A.SIBLING_CODE
           AND B.SIBLING_TYPE_CODE = A.SIBLING_TYPE_CODE
           AND B.USE_FLAG = 'Y'
           AND B.SIBLING_GRP_USE_FLAG = 'Y'
           AND B.SIBLING_GRP_CODE IN (SELECT G.SIBLING_GRP_CODE
                                        FROM DSP_SIBLING_D G
                                       WHERE G.USE_FLAG = 'Y'
                                         AND G.SIBLING_GRP_USE_FLAG = 'Y'
                                         AND G.BIZ_TYPE_CODE = #{bizTypeCode}
                                         AND G.SITE_CODE = #{siteCode}
                                         AND G.PDP_ID = #{pdpId})
         INNER JOIN DSP_PDP_M C
            ON C.SITE_CODE = B.SITE_CODE
           AND C.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND C.PDP_ID = B.PDP_ID
         INNER JOIN DSP_PDP_D D
            ON D.SITE_CODE = B.SITE_CODE
           AND D.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND D.PDP_ID = B.PDP_ID
           AND D.SHOP_CODE = #{shopCode}
           AND D.USE_FLAG = 'Y'
        <if test="customerGroup != null and customerGroup !=''">
         AND (NOT EXISTS (SELECT 1
                               FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
                              WHERE SHOP_CODE = #{shopCode}
                                AND SITE_CODE = #{siteCode}
                                AND PDP_ID = C.PDP_ID
                                AND USE_FLAG = 'Y')
               OR EXISTS (SELECT 1
                               FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
                              WHERE SHOP_CODE = #{shopCode}
                                AND CUST_GRP_CODE = #{customerGroup}
                                AND SITE_CODE = #{siteCode}
                                AND PDP_ID = C.PDP_ID
                                AND USE_FLAG = 'Y'))
        </if>           
    <choose>
      <when test='standardFlag == "Y"'>
         INNER JOIN DSP_PDP_CATEGORY_R E
      </when>
      <otherwise>
         INNER JOIN DSP_OLD_PDP_CATEGORY_R E
      </otherwise>
    </choose>
            ON E.SITE_CODE = B.SITE_CODE
           AND E.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND E.DEFAULT_MAP_FLAG = 'Y'
           AND E.PDP_ID = B.PDP_ID
           AND E.USE_FLAG = 'Y'
         INNER JOIN DSP_SIBLING_EXP_SEQ_D F
            ON F.SITE_CODE = B.SITE_CODE
           AND F.BIZ_TYPE_CODE = B.BIZ_TYPE_CODE
           AND F.SIBLING_TYPE_CODE = B.SIBLING_TYPE_CODE
    <choose>
      <when test='standardFlag == "Y"'>
           AND F.CATEGORY_CODE = E.LV3_CATEGORY_CODE
      </when>
      <otherwise>
           AND F.CATEGORY_CODE = E.LV2_CATEGORY_ID
      </otherwise>
    </choose>
           AND F.USE_FLAG = 'Y'
         WHERE A.USE_FLAG = 'Y'
           AND A.SITE_CODE = #{siteCode}
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         ORDER BY F.EXP_SEQ ASC
                , B.SIBLING_GRP_CODE ASC
                , B.SORT_SEQ ASC
                , B.SIBLING_CODE ASC
    </select>

    <select id="selectMtsObjetMaterialDetailList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialDetailEntityVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialDetailEntityVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectMtsObjetMaterialDetailList */
               E.LGCOM_SKU_ID AS OBJET_PANEL_SKU
             , G.LGCOM_SKU_ID AS OBJET_SKU
             , C.PANEL_TYPE_CODE
             , C.SUB_PANEL_TYPE_CODE
             , C.PANEL_TYPE_IMG_PATH
             , B.MATERIAL_ID
             , B.COLOR_ID
             , A.COLOR_NM
             , A.COUNTRY_COLOR_NM
             , A.COLOR_IMG_PATH
             , H.USE_FLAG
          FROM DSP_OBJET_COLOR_M A
         INNER JOIN DSP_OBJET_PANEL_PRODUCT_D B
            ON B.MATERIAL_ID = A.MATERIAL_ID
           AND B.COLOR_ID = A.COLOR_ID
           AND B.USE_FLAG = 'Y'
         INNER JOIN DSP_OBJET_PRODUCT_PANEL_R C
            ON C.OBJET_PDP_ID = #{pdpId}
           AND C.PANEL_PDP_ID = B.PANEL_PDP_ID
           AND C.USE_FLAG = 'Y'
           AND C.SALES_FLAG = 'Y'
         INNER JOIN DSP_PDP_M E
            ON E.PDP_ID = B.PANEL_PDP_ID
         INNER JOIN DSP_PDP_D F
            ON F.PDP_ID = E.PDP_ID
           AND F.PRODUCT_STATE_CODE = 'ACTIVE'
           AND F.SHOP_CODE = #{shopCode}
         INNER JOIN DSP_PDP_M G
            ON G.PDP_ID = C.OBJET_PDP_ID
         INNER JOIN DSP_PDP_D H
            ON H.PDP_ID = G.PDP_ID
           AND H.PRODUCT_STATE_CODE = 'ACTIVE'
           AND H.SHOP_CODE = #{shopCode}
         ORDER BY A.MATERIAL_ID
                , C.SUB_PANEL_TYPE_CODE
                , C.DSP_SEQ
    </select>

    <select id="selectMtsProductBundleList" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleResponseVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectMtsProductBundleList */
               A.PDP_ID
             , A.LGCOM_SKU_ID
             , A.SKU_ID
             , A.PRODUCT_NM
             , A.PDP_TYPE_CODE
             , A.USER_FRNDY_PRODUCT_NM
             , IFNULL(A.MSRP_SALES_PRICE, 0.00) AS MSRP
             , A.EXCL_PRODUCT_SETT_CODE
             , A.ELABEL_GRD_CODE
             , A.ELABEL_CLS_CODE
             , A.WTOWER_PRODUCT_FLAG
             , A.SECOND_ELABEL_GRD_CODE
             , A.SECOND_ELABEL_CLS_CODE
             , IFNULL(TO_CHAR(A.PRODUCT_RELES_DD, 'YYYY'), '') AS productYear
          <choose>
            <when test='standardFlag == "Y"'>
             , C.LV2_CATEGORY_CODE AS superCategoryId
            </when>
            <otherwise>
             , C.LV1_CATEGORY_ID AS superCategoryId
            </otherwise>
          </choose>
             , D.SITE_CATEGORY_NM
             , E.IMG_ALT_TEXT_CNTS
             , E.MDM_IMG_URL
             , E.PDP_URL
             , E.PRODUCT_STATE_CODE
             , H.SIBLING_GRP_CODE
             , H.SIBLING_GRP_NM
             , H.SIBLING_SBJ_TYPE_CODE
             , IFNULL(H.DEFAULT_SIBLING_MODEL_FLAG, 'N') AS DEFAULT_SIBLING_MODEL_FLAG
             , PRS.PROMOTION_TAG_VAL AS PROMOTION_TEXT
          FROM DSP_PDP_M A
         INNER JOIN DSP_PRODUCT_BUNDLE_D B
            ON B.SITE_CODE = A.SITE_CODE
           AND B.USE_FLAG = 'Y'
        <choose>
          <when test='pdpTypeCode == "B" or pdpTypeCode == "O"'>
           AND B.BUNDLE_PDP_ID = A.PDP_ID
           AND B.PDP_ID = #{pdpId}
          </when>
          <otherwise>
           AND B.PDP_ID = A.PDP_ID
           AND B.BUNDLE_PDP_ID = #{pdpId}
          </otherwise>
        </choose>
        <choose>
          <when test='standardFlag == "Y"'>
         INNER JOIN DSP_PDP_CATEGORY_R C
          </when>
          <otherwise>
         INNER JOIN DSP_OLD_PDP_CATEGORY_R C
          </otherwise>
        </choose>
            ON C.PDP_ID = A.PDP_ID
           AND C.SITE_CODE = A.SITE_CODE
           AND C.USE_FLAG = 'Y'
           AND C.DEFAULT_MAP_FLAG = 'Y'
         INNER JOIN DSP_DISPLAY_CATEGORY_M D
        <choose>
          <when test='standardFlag == "Y"'>
            ON D.CATEGORY_CODE = C.LV3_CATEGORY_CODE
          </when>
          <otherwise>
            ON D.CATEGORY_CODE = C.LV2_CATEGORY_ID
          </otherwise>
        </choose>
           AND D.BIZ_TYPE_CODE = C.BIZ_TYPE_CODE
           AND D.SITE_CODE = C.SITE_CODE
           AND D.USE_FLAG = 'Y'
         INNER JOIN DSP_PDP_D E
            ON E.PDP_ID = A.PDP_ID
           AND E.SITE_CODE = A.SITE_CODE
           AND E.USE_FLAG = 'Y'
           AND E.AEM_PUBL_FLAG = 'Y'
           AND E.SHOP_CODE = #{shopCode}
           <if test="customerGroup != null and customerGroup !=''">
               AND (NOT EXISTS (SELECT 1
                               FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
                              WHERE SHOP_CODE = #{shopCode}
                                AND SITE_CODE = #{siteCode}
                                AND PDP_ID = A.PDP_ID
                                AND USE_FLAG = 'Y')
               OR EXISTS (SELECT 1
                               FROM DSP_MTS_PRODUCT_CUSTOMER_GROUP_R
                              WHERE SHOP_CODE = #{shopCode}
                                AND CUST_GRP_CODE = #{customerGroup}
                                AND SITE_CODE = #{siteCode}
                                AND PDP_ID = A.PDP_ID
                                AND USE_FLAG = 'Y'))
           </if>
           AND E.PRODUCT_STATE_CODE <![CDATA[<>]]> 'SUSPENDED'
          LEFT OUTER JOIN (SELECT
                                  F.PDP_ID
                                , F.SIBLING_GRP_CODE
                                , F.SIBLING_GRP_NM
                                , F.DEFAULT_SIBLING_MODEL_FLAG
                                , F.SIBLING_SBJ_TYPE_CODE
                                , G.CATEGORY_CODE
                             FROM DSP_SIBLING_D F
                            INNER JOIN DSP_SIBLING_EXP_SEQ_D G
                               ON G.SIBLING_TYPE_CODE = F.SIBLING_TYPE_CODE
                              AND G.USE_FLAG = 'Y'
                              AND G.BIZ_TYPE_CODE = F.BIZ_TYPE_CODE
                              AND G.SITE_CODE = #{siteCode}
                              AND G.PLP_DEFAULT_SIBLING_FLAG = 'Y'
                            WHERE F.USE_FLAG = 'Y'
                              AND F.SIBLING_GRP_USE_FLAG = 'Y'
                              AND F.SITE_CODE = #{siteCode}
                              AND F.BIZ_TYPE_CODE = #{bizTypeCode}) H
            ON H.PDP_ID = A.PDP_ID
         <choose>
           <when test='standardFlag == "Y"'>
           AND H.CATEGORY_CODE = C.LV3_CATEGORY_CODE
           </when>
           <otherwise>
           AND H.CATEGORY_CODE = C.LV2_CATEGORY_ID
           </otherwise>
         </choose>
         LEFT JOIN (SELECT PRSSUB.*
                           FROM 
                                (SELECT PMM.PDP_ID
                                       ,MPM.PROMOTION_TAG_VAL
                                   FROM PRM_PROMOTION_PRODUCT_R PMM
                                  INNER JOIN PRM_PROMOTION_M MPM
                                     ON PMM.PROMOTION_ID = MPM.PROMOTION_ID
                                    AND PMM.SITE_CODE = MPM.SITE_CODE
                                  WHERE MPM.SITE_CODE = #{siteCode}
                                    AND MPM.USE_FLAG = 'Y'
                                    AND DATE_FORMAT(#{today},'%Y%m%d') BETWEEN MPM.PROMOTION_EXP_BEGIN_DATE AND MPM.PROMOTION_EXP_END_DATE
                                    AND PMM.USE_FLAG = 'Y'
                                    AND IFNULL(MPM.PROMOTION_NONEXP_FLAG,'N') <![CDATA[<>]]> 'Y'
                                  ORDER BY PMM.PROMOTION_PAGE_PRODUCT_SEQ IS NULL ASC, PMM.PROMOTION_PAGE_PRODUCT_SEQ ASC
                                  ) PRSSUB
                                   GROUP BY PRSSUB.PDP_ID
                            ) PRS
                   ON A.PDP_ID = PRS.PDP_ID
         WHERE A.SITE_CODE = #{siteCode}
           AND A.BIZ_TYPE_CODE = #{bizTypeCode}
         ORDER BY B.DSP_SEQ ASC
    </select>

    <select id="selectProductResources" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductResourceRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ProductResourceResponseVO">
        SELECT  /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductResources */
               AA.resource_type_code
             , AA.file_nm
             , AA.file_type_code
             , AA.file_path
             , AA.file_url
             , AA.category_lv_no
             , AA.last_update_date
        FROM ((SELECT A.resource_type_code
                     , A.file_nm
                     , A.file_type_code
                     , A.file_path
                     , A.file_url
                     , '99' AS category_lv_no
                     , B.last_update_date
                FROM dsp_biz_resource_m A
                INNER JOIN dsp_res_model_r B
                        ON A.file_no = B.file_no
                       AND A.site_code = B.site_code
                       AND A.use_flag = B.use_flag
                       AND B.pdp_id = #{pdpId}
                WHERE A.site_code = #{siteCode}
                AND A.publ_state_code IS NOT NULL
                AND A.resource_type_code  != 'manual')
                UNION ALL
                (SELECT C.resource_type_code
                     , C.file_nm
                     , C.file_type_code
                     , C.file_path
                     , C.file_url
                     , D.category_lv_no
                     , D.last_update_date
                FROM dsp_biz_resource_m C
                INNER JOIN dsp_res_category_r D
                        ON C.file_no = D.file_no
                       AND C.site_code = D.site_code
                       AND C.use_flag = D.use_flag
                       <if test='categoryIds != null and categoryIds.size != 0'>
                          <foreach collection='categoryIds' item='categoryId' open='AND D.OCATEGORY_ID IN (' separator=',' close=')'>
                          #{categoryId}
                          </foreach>
                      </if>
                WHERE C.site_code = #{siteCode}
                AND C.publ_state_code IS NOT NULL
                AND C.resource_type_code  != 'manual')) AA
        INNER JOIN com_common_code_d F
           ON F.common_code = 'FILE_RESOURCE_TYPE001'
         AND F.USE_FLAG = 'Y'
         AND AA.resource_type_code = F.common_code_val
         ORDER BY AA.RESOURCE_TYPE_CODE
                 ,AA.file_type_code
                 ,AA.file_nm
                 ,AA.category_lv_no DESC
                 ,AA.LAST_UPDATE_DATE DESC
    </select>

    <select id="selectProductGsriFileInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductGsriFileInfoRequestVO"
        resultType="com.lge.d2x.domain.pdpInfo.v1.model.GsriFileInfoResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductGsriFileInfo */
               A.GSRI_DOC_ID
             , A.FILE_NM
             , A.GSRI_DOC_ID
             , A.LOCAL_CODE
             , A.LOCAL_NM
             , A.COUNTRY_CODE
             , A.SITE_CODE
             , A.DOC_TYPE_CODE
             , A.DOC_TYPE_NM
             , A.LV1_PRODUCT_CODE
             , A.PRODUCT_NM
             , A.MODEL_NM
             , A.FILE_NM
             , A.ORIGINAL_FILE_NM
             , A.FILE_SIZE
             , A.GSRI_USER_NO
             , A.GSRI_USER_NM
             , A.DEPT_NM
             , A.ISSU_DD
             , A.ISSUE_MM
             , A.DEL_FLAG
             , A.SALES_MODEL_CODE
             , A.ATTR5
             , A.ATTR6
          FROM SVD_GSRI_DOC_M A
         WHERE SALES_MODEL_CODE = #{salesModelCode}
           AND A.DOC_TYPE_CODE = #{docTypeCode}
           AND A.DEL_FLAG = 'N'
           AND A.GSRI_DOC_ID NOT IN (#{fEnergyLabelDocId}, #{productFichelDocId}, #{energyLabelDocId})
         ORDER BY A.ATTR5 DESC, A.GSRI_DOC_ID DESC
    </select>

    <select id="selectCompatibleProductsFor3Type" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.AccessoryProductRequestVO" resultType="java.util.HashMap">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectCompatibleProductsFor3Type */
                C.CMPAT_POSS_PRODUCT_NM AS model
              , C.SKU_ID AS sku
          FROM (
                SELECT 
                        A.CMPAT_POSS_PRODUCT_NM
                      , B.SKU_ID
                      , ROW_NUMBER() OVER (PARTITION BY A.CMPAT_POSS_PRODUCT_NM ORDER BY SKU_ID) AS RNUM
                      , A.DSP_SEQ
                  FROM DSP_ACCESSORY_PRODUCT_D A
                  JOIN (
                        SELECT * 
                          FROM DSP_PDP_M
                         WHERE SITE_CODE = 'DE' 
                           AND BIZ_TYPE_CODE = 'B2C' 
                           AND LGCOM_SKU_ID IS NOT NULL 
                           AND LGCOM_SKU_ID != ''
                        ) AS B
                    ON B.PRODUCT_NM = A.CMPAT_POSS_PRODUCT_NM
                 WHERE A.USE_FLAG = 'Y'
                   AND A.SITE_CODE = #{siteCode}
                   AND A.PDP_ID = #{pdpId}
                <if test="bizTypeCode != null and bizTypeCode != ''">
                    AND A.BIZ_TYPE_CODE = #{bizTypeCode}
                </if>
                ) AS C
         WHERE C.RNUM = 1
         ORDER BY C.DSP_SEQ ASC
    </select>

    <select id="selectProductEpsInfo" parameterType="com.lge.d2x.domain.pdpInfo.v1.model.ProductEpsRequestVO" resultType="com.lge.d2x.domain.pdpInfo.v1.model.ProductEpsResponseVO">
        SELECT /* system.pdsmgr.oln.com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService.selectProductEpsInfo */
               EPS_USE_FLAG
             , EPS_USB_PD_NM
             , EPS_MAX_VOLTAGE
             , EPS_MIN_VOLTAGE
          FROM DSP_EPS_PICTOGRAM_PRODUCT_R
         WHERE USE_FLAG = 'Y'
           AND PDP_ID = #{pdpId}
         LIMIT 1
    </select>
</mapper>