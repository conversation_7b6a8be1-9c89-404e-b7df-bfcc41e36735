package com.lge.d2x.domain.spec.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductStandardSpecRequestVO {
    @NotBlank(message = "Missing required parameters")
    @Schema(description = "SKU아이디", example = "38WR85QC-W.AUS.ENCI.C")
    private String skuId;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "로케일코드", example = "fr_CA")
    private String localeCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "PDP아이디", example = "MD07567157")
    private String pdpId;
}
