package com.lge.d2x.domain.product.v1.restcontroller;

import com.lge.d2x.domain.product.v1.model.AccessoryProductListRequestVO;
import com.lge.d2x.domain.product.v1.model.AccessoryProductListResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductBundleRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductBundleResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductEanCodeRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductEanCodeResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummaryLatsResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummaryRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductSummaryResourceResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummaryResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummarySeoSchemaResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummarySpecResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSupportInfoRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductSupportInfoResponseVO;
import com.lge.d2x.domain.product.v1.service.PdpInfoService;
import com.lge.d2xfrm.model.common.D2xCommonResponseVO;
import com.lge.d2xfrm.util.common.D2xResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pdpInfo/v1")
@RequiredArgsConstructor
@Tag(name = "PdpInfoRestController", description = "Pdp Info API")
public class PdpInfoRestController {
    private final PdpInfoService pdpInfoService;

    // @ApiCaching(name = "productSummary", ttlMinutesExpiredAt = 5)
    @Operation(summary = "제품 summary", description = "제품 summary 정보를 조회한다")
    @PostMapping(path = "/product-summary", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<ProductSummaryResponseVO>> productSummary(
            @Valid @RequestBody ProductSummaryRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(pdpInfoService.getProductSummary(requestVO));
    }

    // @ApiCaching(name = "productSummarySpec", ttlMinutesExpiredAt = 5)
    @Operation(summary = "제품 summary - spec", description = "제품 summary 정보 중 spec 정보를 조회한다")
    @PostMapping(path = "/product-summary-spec", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<List<ProductSummarySpecResponseVO>>>
            productSummarySpec(@Valid @RequestBody ProductSummaryRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(
                pdpInfoService.getProductSummarySpec(requestVO));
    }

    // @ApiCaching(name = "productSummaryResource", ttlMinutesExpiredAt = 5)
    @Operation(summary = "제품 summary - resource", description = "제품 summary 정보 중 resource 정보를 조회한다")
    @PostMapping(path = "/product-summary-resource", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<ProductSummaryResourceResponseVO>>
            productSummaryResource(@Valid @RequestBody ProductSummaryRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(
                pdpInfoService.getProductSummaryResource(requestVO));
    }

    @Operation(
            summary = "제품 summary - seo schema spec",
            description = "제품 summary 정보 중 seo schema spec 정보를 조회한다")
    @PostMapping(path = "/product-seo-schema", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<ProductSummarySeoSchemaResponseVO>>
            productSummarySeoSchema(@Valid @RequestBody ProductSummaryRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(
                pdpInfoService.getProductSummarySeoSchema(requestVO));
    }

    @Operation(summary = "제품 summary - LATS", description = "제품 summary 정보 중 LATS 정보를 조회한다")
    @PostMapping(path = "/product-lats", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<ProductSummaryLatsResponseVO>> productSummaryLats(
            @Valid @RequestBody ProductSummaryRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(
                pdpInfoService.getProductSummaryLats(requestVO));
    }

    @Operation(summary = "제품 Ean Code 정보", description = "제품 정보 중 Ean Code 정보를 조회한다")
    @PostMapping(path = "/product-ean-code", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<ProductEanCodeResponseVO>> productEanCode(
            @Valid @RequestBody ProductEanCodeRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(pdpInfoService.getProductEanCode(requestVO));
    }

    @Operation(summary = "악세서리 호환 제품 목록", description = "악세서리 호환 제품 목록을 조회한다")
    @PostMapping(path = "/accessory-product-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<List<AccessoryProductListResponseVO>>>
            accessoryProductList(@Valid @RequestBody AccessoryProductListRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(
                pdpInfoService.getAccessoryProductList(requestVO));
    }

    @Operation(summary = "제품 Support Tab 정보 조회", description = "제품 Support Tab 정보를 조회한다")
    @PostMapping(path = "/product-support", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<ProductSupportInfoResponseVO>> getProductSupport(
            @Valid @RequestBody ProductSupportInfoRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(pdpInfoService.getProductSupport(requestVO));
    }

    @Operation(summary = "제품 번들 리스트 정보 조회", description = "제품 번들 리스트 정보를 조회한다")
    @PostMapping(path = "/product-bundle-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<D2xCommonResponseVO<List<ProductBundleResponseVO>>> getProductBundleList(
            @Valid @RequestBody ProductBundleRequestVO requestVO) {
        return D2xResponseUtil.createSuccessResponse(
                pdpInfoService.getProductBundleList(requestVO));
    }
}
