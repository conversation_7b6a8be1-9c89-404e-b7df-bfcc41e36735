package com.lge.d2x.domain.bundle.v1.repository.pdsmgr;

import com.lge.d2x.domain.bundle.v1.model.BundleElInfoRequestVO;
import com.lge.d2x.domain.bundle.v1.model.BundleElInfoResponseVO;
import com.lge.d2x.domain.bundle.v1.model.BundleListRequestVO;
import com.lge.d2x.domain.bundle.v1.model.BundleListResponseVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface BundleRepository {

    List<BundleListResponseVO> selectBundle(BundleListRequestVO requestVO);

    List<BundleListResponseVO> selectBundleNotStandard(BundleListRequestVO requestVO);

    List<BundleElInfoResponseVO> selectBundleElInfo(BundleElInfoRequestVO requestVO);
}
