package com.lge.d2x.domain.productList.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductListPdpInfoRequestVO {
    @NotEmpty(message = "Missing required parameters")
    @Schema(description = "Pdp Id 리스트", example = "[\"**********\", \"**********\"]")
    private List<String> pdpIdList;

    @Schema(description = "리스트 타입", example = "NEW")
    private String listType;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "표준화 여부", example = "Y")
    private String standardFlag;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "사이트 코드", example = "UK")
    private String siteCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "국가 코드", example = "en_GB")
    private String localeCode;

    @NotBlank(message = "Missing required parameters")
    @Schema(
            description = "기본 이미지 url",
            example = "/lg5-common-gp/images/common/product-default-list-350.jpg")
    private String dummyImageUrl;

    @NotBlank(message = "Missing required parameters")
    @Schema(description = "타임존이 적용 된 오늘 날짜", example = "20240708")
    private String today;

    @Schema(description = "샵 코드", example = "de_students")
    private String shopCode;

    @Schema(description = "Customer Group", example = "B2B2C")
    private String customerGroup;

    @Schema(description = "skuList 여부", example = "Y")
    private String skuListFlag;

    @Schema(description = "비즈니스 유형 코드", example = "B2C")
    private String bizTypeCode;

    @Schema(description = "Sibling Group 여부", example = "Y")
    private String siblingGroupFlag;

    @Schema(description = "Promotion 제품 여부", example = "Y")
    private String promoProductFlag;

    @Schema(description = "Promotion 아이디", example = "PM00019002")
    private String promotionId;

    @Schema(description = "HIDDEN Model Search 여부", example = "Y")
    private String hiddenModelSearchFlag;

    @Schema(description = "Order List 여부", example = "Y")
    private String orderListFlag;

    @Schema(description = "페이지 타입", example = "PLP")
    private String pageType;

    @Schema(description = "리뷰 타입", example = "LGCOM")
    private String reviewType;
}
