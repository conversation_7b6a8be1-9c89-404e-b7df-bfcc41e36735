package com.lge.d2x.domain.manualSoftware.v1.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class McSupportDownResponseVO {
    private String siteCode;
    private String custModelCode;
    private String osCode;
    private String downloadTypeCode;
    private String urlAddr;
    private String versionNo;
    private String versionOrder;
    private String useFlag;
}
