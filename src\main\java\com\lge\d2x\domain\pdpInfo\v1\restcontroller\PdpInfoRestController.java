package com.lge.d2x.domain.pdpInfo.v1.restcontroller;

import com.lge.d2x.domain.pdpInfo.v1.model.AccessoryProductRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.GsriFileInfoResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.KeyfeaturesRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.KeyfeaturesResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialDetailEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetMaterialEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetPanelTypeEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ObjetProductEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpCategoryResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpEnergyLabelInfoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpEnergyLabelInfoResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpSiblingResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.PdpUserReviewRatingResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductAccessoryRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductBundleResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductEpsRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductEpsResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductGsriFileInfoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductIconListRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductIconListResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductPanelEntityVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductResourceRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductResourceResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductSpecBundleSimpleListRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductSpecBundleSimpleListResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductVideoRequestVO;
import com.lge.d2x.domain.pdpInfo.v1.model.ProductVideoResponseVO;
import com.lge.d2x.domain.pdpInfo.v1.service.PdpInfoService;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.D2xCommonResponseVO;
import com.lge.d2xfrm.util.common.D2xResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/pdpInfo/v1")
@Tag(name = "PdpInfoRestController", description = "PDP 정보를 조회하는 컨트롤러이다")
public class PdpInfoRestController {
    private final PdpInfoService pdpService;

    // @ApiCaching(name = "pdpBasicInfo", ttlMinutesExpiredAt = 1)
    @GetMapping("/pdp-basic-info")
    @Operation(summary = "pdp 기본 정보 조회", description = "pdp 기본 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PdpResponseVO>> pdpBasicInfo(
            @Valid PdpRequestVO req) {

        if (ObjectUtils.isEmpty(req.getPdpId()) && ObjectUtils.isEmpty(req.getLgcomSkuId())) {
            throw new D2xBusinessException("Missing required parameters");
        }

        return D2xResponseUtil.createSuccessResponse(pdpService.selectPdpBasicInfo(req));
    }

    @GetMapping("/pdp-category-info")
    @Operation(summary = "pdp 카테고리 정보 조회", description = "pdp 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PdpCategoryResponseVO>> pdpCategoryInfo(
            @Valid PdpCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectPdpCategoryInfo(req));
    }

    @GetMapping("/pdp-all-category-info")
    @Operation(summary = "pdp 전체 카테고리 정보 조회", description = "pdp 전체 카테고리 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PdpCategoryResponseVO>>> pdpAllCategoryInfo(
            @Valid PdpCategoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectPdpAllCategoryInfo(req));
    }

    @GetMapping("/pdp-energy-label-info")
    @Operation(summary = "PDP 에너지 라벨 정보", description = "PDP 에너지 라벨 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PdpEnergyLabelInfoResponseVO>> pdpEnergyLabelInfo(
            @Valid PdpEnergyLabelInfoRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectPdpEnergyLabelInfo(req));
    }

    @GetMapping("/pdp-key-features-list")
    @Operation(summary = "PDP 키피쳐 리스트", description = "PDP 키피쳐 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<KeyfeaturesResponseVO>>> keyFeaturesList(
            @Valid KeyfeaturesRequestVO req) {

        if ((!ObjectUtils.isEmpty(req.getPdpIdListFlag()) && "Y".equals(req.getPdpIdListFlag()))) {
            if (ObjectUtils.isEmpty(req.getPdpIdList())) {
                throw new D2xBusinessException("Missing required parameters");
            }
        } else {
            if (ObjectUtils.isEmpty(req.getPdpId())) {
                throw new D2xBusinessException("Missing required parameters");
            }
        }

        return D2xResponseUtil.createSuccessResponse(pdpService.selectKeyfeaturesList(req));
    }

    @GetMapping("/pdp-sibling-data-list")
    @Operation(summary = "PDP 시블링 데이터 리스트", description = "PDP 시블링 데이터 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PdpSiblingResponseVO>>> pdpSiblingData(
            @Valid PdpSiblingRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectPdpSiblingDataList(req));
    }

    @GetMapping("/pdp-user-review-rating-info")
    @Operation(summary = "PDP 유저 리뷰 별점 정보", description = "PDP 유저 리뷰 별점 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PdpUserReviewRatingResponseVO>>
            pdpUserReviewRatingInfo(@Valid PdpUserReviewRatingRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectPdpUserReviewRatingData(req));
    }

    @GetMapping("/pdp-user-review-sibling-rating-info")
    @Operation(summary = "PDP 유저 리뷰 별점 정보(시블링 제품)", description = "PDP 유저 리뷰 별점 정보(시블링 제품)를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PdpUserReviewRatingResponseVO>>
            pdpUserReviewSiblingRatingInfo(@Valid PdpUserReviewRatingRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                pdpService.selectPdpUserReviewSiblingRatingData(req));
    }

    @GetMapping("/accessory-product-list")
    @Operation(
            summary = "ACCESSORY 모델에 해당하는 본품 SKU 리스트",
            description = "ACCESSORY 모델에 해당하는 본품 SKU 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<Map<String, Object>>>> accessoryProductList(
            @Valid AccessoryProductRequestVO req) {

        if ((!ObjectUtils.isEmpty(req.getFunctionName())
                && ObjectUtils.isEmpty(req.getSearchModelNm()))) {
            throw new D2xBusinessException("Missing required parameters");
        }

        return D2xResponseUtil.createSuccessResponse(pdpService.selectAccessoryProductList(req));
    }

    @GetMapping("/product-accessory")
    @Operation(summary = "본품에 사용되는 액세서리", description = "본품에 사용되는 액세서리 모델를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<Map<String, Object>>>> productAccessory(
            @Valid ProductAccessoryRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectProductAccessoryList(req));
    }

    @GetMapping("/product-icon-list")
    @Operation(summary = "제품 아이콘 리스트", description = "제품 아이콘 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductIconListResponseVO>>> productIconList(
            @Valid ProductIconListRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectProductIconList(req));
    }

    @GetMapping("/product-panel")
    @Operation(summary = "제품 패널", description = "제품 패널 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<ProductPanelEntityVO>> productPanel(
            @Valid ProductPanelEntityVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectProductPanel(req));
    }

    @GetMapping("/objet-product")
    @Operation(summary = "오브제 제품", description = "오브제 제품 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<ObjetProductEntityVO>> objetProduct(
            @Valid ObjetProductEntityVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectObjetProduct(req));
    }

    @GetMapping("/objet-material-list")
    @Operation(summary = "오브제 자재 리스트", description = "오브제 자재 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ObjetMaterialEntityVO>>> objetMaterialList(
            @Valid ObjetMaterialEntityVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectObjetMaterialList(req));
    }

    @GetMapping("/objet-material-detail-list")
    @Operation(summary = "오브제 자재 상세 리스트", description = "오브제 자재 상세 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ObjetMaterialDetailEntityVO>>>
            objetMaterialDetailList(@Valid ObjetMaterialDetailEntityVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectObjetMaterialDetailList(req));
    }

    @GetMapping("/objet-panel-type-list")
    @Operation(summary = "오브제 패널 타입 리스트", description = "오브제 패널 타입 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ObjetPanelTypeEntityVO>>> objetPanelTypeList(
            @Valid ObjetPanelTypeEntityVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectObjetPanelTypeList(req));
    }

    @GetMapping("/product-bundle-list")
    @Operation(summary = "제품 번들 리스트", description = "제품 번들 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductBundleResponseVO>>> productBundleList(
            @Valid ProductBundleRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectProductBundleList(req));
    }

    @GetMapping("/product-spec-bundle-simple-list")
    @Operation(summary = "제품 스펙 번들 리스트", description = "제품 스펙 번들 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductSpecBundleSimpleListResponseVO>>>
            productSpecBundleSimpleList(@Valid ProductSpecBundleSimpleListRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(
                pdpService.selectProductSpecBundleSimpleList(req));
    }

    @GetMapping("/product-video-info")
    @Operation(summary = "제품 비디오 정보", description = "제품 비디오 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductVideoResponseVO>>> productVideoInfo(
            @Valid ProductVideoRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectProductVideoInfo(req));
    }

    @GetMapping("/default-sibling-product-info")
    @Operation(
            summary = "맨 처음 노출되는 Sibling의 default 모델",
            description = "맨 처음 노출되는 Sibling의 default 모델 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PdpSiblingResponseVO>> defaultSiblingModelInfo(
            @Valid PdpSiblingRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectDefaultSiblingModelInfo(req));
    }

    // @ApiCaching(name = "mtsPdpBasicInfo", ttlMinutesExpiredAt = 60)
    @GetMapping("/mts-pdp-basic-info")
    @Operation(summary = "멀티샵 pdp 기본 정보 조회", description = "멀티샵 pdp 기본 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<PdpResponseVO>> mtsPdpBasicInfo(
            @Valid PdpRequestVO req) {

        if ((ObjectUtils.isEmpty(req.getPdpId()) && ObjectUtils.isEmpty(req.getLgcomSkuId()))
                || ObjectUtils.isEmpty(req.getShopCode())) {
            throw new D2xBusinessException("Missing required parameters");
        }

        return D2xResponseUtil.createSuccessResponse(pdpService.selectMtsPdpBasicInfo(req));
    }

    @GetMapping("/mts-pdp-sibling-data-list")
    @Operation(summary = "멀티샵 PDP 시블링 데이터 리스트", description = "멀티샵 PDP 시블링 데이터 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<PdpSiblingResponseVO>>> mtsPdpSiblingData(
            @Valid PdpSiblingRequestVO req) {

        if (ObjectUtils.isEmpty(req.getShopCode())) {
            throw new D2xBusinessException("Missing required parameters");
        }

        return D2xResponseUtil.createSuccessResponse(pdpService.selectMtsPdpSiblingDataList(req));
    }

    @GetMapping("/mts-objet-material-detail-list")
    @Operation(summary = "멀티샵 오브제 자재 상세 리스트", description = "멀티샵 오브제 자재 상세 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ObjetMaterialDetailEntityVO>>>
            mtsObjetMaterialDetailList(@Valid ObjetMaterialDetailEntityVO req) {

        if (ObjectUtils.isEmpty(req.getShopCode())) {
            throw new D2xBusinessException("Missing required parameters");
        }

        return D2xResponseUtil.createSuccessResponse(
                pdpService.selectMtsObjetMaterialDetailList(req));
    }

    @GetMapping("/mts-product-bundle-list")
    @Operation(summary = "멀티샵 제품 번들 리스트", description = "멀티샵 제품 번들 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductBundleResponseVO>>> mtsProductBundleList(
            @Valid ProductBundleRequestVO req) {

        if (ObjectUtils.isEmpty(req.getShopCode())) {
            throw new D2xBusinessException("Missing required parameters");
        }

        return D2xResponseUtil.createSuccessResponse(pdpService.selectMtsProductBundleList(req));
    }

    @GetMapping("/product-resources")
    @Operation(summary = "제품 리소스 조회", description = "제품 리소스를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<ProductResourceResponseVO>>> productResources(
            @Valid ProductResourceRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectProductResources(req));
    }

    @GetMapping("/product-gsri-file-info")
    @Operation(summary = "GSRI문서기본 조회", description = "GSRI문서기본 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<GsriFileInfoResponseVO>> getProductGsriFileInfo(
            ProductGsriFileInfoRequestVO requestVO) throws D2xBusinessException {
        return D2xResponseUtil.createSuccessResponse(
                pdpService.selectProductGsriFileInfo(requestVO));
    }

    @GetMapping("/accessory-product-list-for-3type")
    @Operation(summary = "3Type 악세서리 제품 리스트", description = "3Type 악세서리 제품 리스트를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<Map<String, Object>>>>
            getCompatibleProductsFor3Type(AccessoryProductRequestVO requestVO)
                    throws D2xBusinessException {
        return D2xResponseUtil.createSuccessResponse(
                pdpService.selectCompatibleProductsFor3Type(requestVO));
    }

    @GetMapping("/product-eps-info")
    @Operation(summary = "제품 EPS 정보 조회", description = "제품 EPS 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<ProductEpsResponseVO>> productEpsInfo(
            @Valid ProductEpsRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(pdpService.selectProductEpsInfo(req));
    }
}
