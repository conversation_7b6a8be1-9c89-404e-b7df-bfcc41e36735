package com.lge.d2x.domain.troubleshoot.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class DetailSymptCwTypeResponseVO {
    @Schema(description = "sympDetailId")
    private String sympDetailId;

    @Schema(description = "sympDetailName")
    private String sympDetailName;

    @Schema(description = "symptomDepthSetup")
    private String symptomDepthSetup;
}
