package com.lge.d2x.domain.support.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SympTomInfoResponseVO {

    @Schema(description = "증상코드", example = "")
    private String symptomCode;

    @Schema(description = "증사명", example = "")
    private String symptomNm;

    @Schema(description = "부증상코드", example = "")
    private String subSymptomCode;

    @Schema(description = "부증사명", example = "")
    private String subSymptomNm;

    @Schema(description = "법인코드", example = "")
    private String corpCode;

    @Schema(description = "언어코드", example = "")
    private String langCode;

    @Schema(description = "MDMS 제품그룹코드", example = "")
    private String mdmsPrdgrpCode;

    @Schema(description = "translationStateCode", example = "")
    private String translationStateCode;

    @Schema(description = "lgeaiFlag", example = "")
    private String lgeaiFlag;

    @Schema(description = "consumerFlag", example = "")
    private String consumerFlag;

    @Schema(description = "전시여부", example = "")
    private String dspFlag;

    @Schema(description = "relExpressionTextCnts", example = "")
    private String relExpressionTextCnts;

    @Schema(description = "articleFlag", example = "")
    private String articleFlag;

    @Schema(description = "symptomTypeCode", example = "")
    private String symptomTypeCode;

    @Schema(description = "증상코드", example = "")
    private String code;

    @Schema(description = "증사명", example = "")
    private String name;
}
