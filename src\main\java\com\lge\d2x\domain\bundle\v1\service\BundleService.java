package com.lge.d2x.domain.bundle.v1.service;

import com.lge.d2x.domain.bundle.v1.model.BundleElInfoRequestVO;
import com.lge.d2x.domain.bundle.v1.model.BundleElInfoResponseVO;
import com.lge.d2x.domain.bundle.v1.model.BundleListRequestVO;
import com.lge.d2x.domain.bundle.v1.model.BundleListResponseVO;
import com.lge.d2x.domain.bundle.v1.repository.pdsmgr.BundleRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class BundleService {

    private final BundleRepository bundleRepository;

    /**
     * @Description
     */
    @Transactional(readOnly = true)
    public List<BundleListResponseVO> selectBundle(BundleListRequestVO requestVO) {

        List<BundleListResponseVO> result;
        if ("Y".equals(requestVO.getStandardFlag())) {
            result = bundleRepository.selectBundle(requestVO);
        } else {
            result = bundleRepository.selectBundleNotStandard(requestVO);
        }
        return result;
    }

    @Transactional(readOnly = true)
    public List<BundleElInfoResponseVO> selectBundleElInfo(BundleElInfoRequestVO requestVO) {

        return bundleRepository.selectBundleElInfo(requestVO);
    }
}
