package com.lge.d2x.domain.bundle.v1.restcontroller;

import com.lge.d2x.domain.bundle.v1.model.BundleElInfoRequestVO;
import com.lge.d2x.domain.bundle.v1.model.BundleElInfoResponseVO;
import com.lge.d2x.domain.bundle.v1.model.BundleListRequestVO;
import com.lge.d2x.domain.bundle.v1.model.BundleListResponseVO;
import com.lge.d2x.domain.bundle.v1.service.BundleService;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.D2xCommonResponseVO;
import com.lge.d2xfrm.util.common.D2xResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import software.amazon.awssdk.utils.StringUtils;

@RestController
@RequiredArgsConstructor
@RequestMapping("/bundle/v1")
@Tag(name = "BundleRestController", description = "번들 정보를 조회하는 컨트롤러이다")
public class BundleRestController {
    private final BundleService bundleService;

    @GetMapping("/bundle-list")
    @Operation(summary = "번들 리스트", description = "번들 리스트 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<BundleListResponseVO>>> bundleList(
            @Valid BundleListRequestVO req) {

        if (ObjectUtils.isNotEmpty(req.getBundleListFlag())
                && "Y".equals(req.getBundleListFlag())) {
            if (StringUtils.isBlank(req.getProductStateCode())) {
                throw new D2xBusinessException("Missing required parameters:productStateCode");
            }

            if (StringUtils.isBlank(req.getViewAll())) {
                if (ObjectUtils.isEmpty(req.getStartNo())) {
                    throw new D2xBusinessException("Missing required parameters:startNo");
                }

                if (ObjectUtils.isEmpty(req.getPageCnt())) {
                    throw new D2xBusinessException("Missing required parameters:pageCnt");
                }
            }
        }

        return D2xResponseUtil.createSuccessResponse(bundleService.selectBundle(req));
    }

    @GetMapping("/bundle-el-info")
    @Operation(summary = "번들 에너지라벨 정보", description = "번들 에너지라벨 정보를 조회한다.")
    public ResponseEntity<D2xCommonResponseVO<List<BundleElInfoResponseVO>>> bundleElInfoList(
            @Valid BundleElInfoRequestVO req) {

        return D2xResponseUtil.createSuccessResponse(bundleService.selectBundleElInfo(req));
    }
}
