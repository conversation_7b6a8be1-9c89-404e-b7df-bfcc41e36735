package com.lge.d2x.domain.product.v1.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lge.d2x.core.constants.CommonConstants;
import com.lge.d2x.domain.common.v1.model.ProductSupportRequestVO;
import com.lge.d2x.domain.common.v1.model.SnsShareInfoRequestVO;
import com.lge.d2x.domain.common.v1.service.CommonService;
import com.lge.d2x.domain.product.v1.model.AccessoryProductListRequestVO;
import com.lge.d2x.domain.product.v1.model.AccessoryProductListResponseVO;
import com.lge.d2x.domain.product.v1.model.KeyfeaturesVO;
import com.lge.d2x.domain.product.v1.model.ObjetMaterialDetailEntityVO;
import com.lge.d2x.domain.product.v1.model.ObjetMaterialEntityVO;
import com.lge.d2x.domain.product.v1.model.ObjetPanelTypeEntityVO;
import com.lge.d2x.domain.product.v1.model.PdfDownloadInfoResponseVO;
import com.lge.d2x.domain.product.v1.model.PdpSiblingItemVO;
import com.lge.d2x.domain.product.v1.model.PdpSiblingModelResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductBundleRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductBundleResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductEanCodeRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductEanCodeResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductIconListResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummaryLatsResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummaryRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductSummaryResourceResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummaryResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummarySeoSchemaResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSummarySpecResponseVO;
import com.lge.d2x.domain.product.v1.model.ProductSupportInfoRequestVO;
import com.lge.d2x.domain.product.v1.model.ProductSupportInfoResponseVO;
import com.lge.d2x.domain.product.v1.model.SpecItemVO;
import com.lge.d2x.interfaces.system.admin.product.client.SystemAdminProductClient;
import com.lge.d2x.interfaces.system.admin.product.client.SystemAdminPromotionClient;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminCatalogueListRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminCatalogueListResponseVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminDowntimeInfoVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminLatsRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminLatsResponseVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminProductPromotionMsgRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminProductPromotionMsgResponseVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminSeoKeySpecRequestVO;
import com.lge.d2x.interfaces.system.admin.product.model.SystemAdminSeoKeySpecResponseVO;
import com.lge.d2x.interfaces.system.admin.support.client.SystemAdminSupportClient;
import com.lge.d2x.interfaces.system.admin.support.model.SystemAdminProductGsriCntRequestVO;
import com.lge.d2x.interfaces.system.admin.wishlist.client.SystemAdminWishlistClient;
import com.lge.d2x.interfaces.system.admin.wishlist.model.SystemAdminWishlistCountRequestVO;
import com.lge.d2x.interfaces.system.admin.wishlist.model.SystemAdminWishlistCountResponseVO;
import com.lge.d2x.interfaces.system.pds.bundle.client.SystemPdsBundleClient;
import com.lge.d2x.interfaces.system.pds.bundle.model.SystemPdsBundleElInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.bundle.model.SystemPdsBundleElInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.category.client.SystemPdsCategoryClient;
import com.lge.d2x.interfaces.system.pds.category.model.SystemPdsCategoryRequestVO;
import com.lge.d2x.interfaces.system.pds.category.model.SystemPdsCategoryResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.client.SystemPdsPdpInfoClient;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsAccessoryProductRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsKeyfeaturesRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsKeyfeaturesResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsObjetMaterialDetailEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsObjetMaterialEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsObjetPanelTypeEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsObjetProductEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpCategoryRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpCategoryResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpEnergyLabelInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpSiblingRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpSiblingResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpUserReviewRatingRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsPdpUserReviewRatingResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductAccessoryRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductBundleRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductBundleResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductIconListResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductPanelEntityVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductResourceRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductResourceResponseVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductSpecBundleSimpleListRequestVO;
import com.lge.d2x.interfaces.system.pds.pdpinfo.model.SystemPdsProductSpecBundleSimpleListResponseVO;
import com.lge.d2x.interfaces.system.pds.pdsCommon.client.SystemPdsCommonClient;
import com.lge.d2x.interfaces.system.pds.pdsCommon.model.SystemPdsConfigsRequestVO;
import com.lge.d2x.interfaces.system.pds.pdsCommon.model.SystemPdsConfigsResponseVO;
import com.lge.d2x.interfaces.system.pds.product.client.SystemPdsProductClient;
import com.lge.d2x.interfaces.system.pds.product.model.SystemPdsProductRequestVO;
import com.lge.d2x.interfaces.system.pds.product.model.SystemPdsProductResponseVO;
import com.lge.d2x.interfaces.system.pds.productList.client.SystemPdsProductListClient;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsPdpIdListBySkuRequestVO;
import com.lge.d2x.interfaces.system.pds.productList.model.SystemPdsProductListSelfSiblingDefaultRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.client.SystemPdsSpecClient;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsKeySpecRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsKeySpecResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductLgcomSpecRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductLgcomSpecResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductSpecDmsRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductSpecDmsResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductSpecSmrRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductSpecSmrResponseVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductStandardSpecRequestVO;
import com.lge.d2x.interfaces.system.pds.spec.model.SystemPdsProductStandardSpecResponseVO;
import com.lge.d2x.interfaces.system.pds.support.client.SystemPdsSupportClient;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsBundleGsriFileInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsPdfGsriSpecInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsPdfGsriSpecInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductGsriFileInfoResponseVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductManualSoftwareInfoRequestVO;
import com.lge.d2x.interfaces.system.pds.support.model.SystemPdsProductManualSoftwareInfoResponseVO;
import com.lge.d2xfrm.configuration.context.TokenContextHolder;
import com.lge.d2xfrm.constants.CommonCodes;
import com.lge.d2xfrm.constants.enums.JobSeperateCodeEnum;
import com.lge.d2xfrm.exception.D2xBusinessException;
import com.lge.d2xfrm.model.common.CachedCodeRequestVO;
import com.lge.d2xfrm.model.common.CachedCommonCodeRequestVO;
import com.lge.d2xfrm.model.common.CachedPdpIdRequestVO;
import com.lge.d2xfrm.model.common.CachedPdpIdVO;
import com.lge.d2xfrm.model.sso.SessionUserVO;
import com.lge.d2xfrm.util.common.CachedDataUtil;
import com.lge.d2xfrm.util.common.DateUtil;
import com.lge.d2xfrm.util.common.RequestHeaderUtil;
import com.lge.d2xfrm.util.sso.SessionUserUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PdpInfoService {
    private final SystemPdsPdpInfoClient systemPdsPdpInfoClient;
    private final SystemPdsProductClient systemPdsProductClient;
    private final SystemPdsProductListClient systemPdsProductListClient;
    private final SystemPdsCategoryClient systemPdsCategoryClient;
    private final SystemPdsSpecClient systemPdsSpecClient;
    private final SystemPdsCommonClient systemPdsCommonClient;
    private final SystemPdsSupportClient systemPdsSupportClient;
    private final SystemPdsBundleClient systemPdsBundleClient;
    private final SystemAdminWishlistClient systemAdminWishlistClient;
    private final SystemAdminProductClient systemAdminProductClient;
    private final SystemAdminPromotionClient systemAdminPromotionClient;
    private final SystemAdminSupportClient systemAdminSupportClient;
    private final CachedDataUtil cachedDataUtil;
    private final CommonService commonService;

    // Model Type (G-general, B-bundle, A-accessory)
    public static final String ACCESSORY_PRODUCT = "A";
    public static final String BUNDLE_PRODUCT = "B";
    public static final String OBS_BUNDLE_PRODUCT = "O";
    public static final String GENERAL_PRODUCT = "G";
    public static final String EXTERNAL_WTB_PREFIX = "EXTERNAL_WTB_";

    // PDF Download Code
    public static final String E_GUIDE_GSRI = "FTC EnergyGuide Label";
    public static final String E_GUIDE_LINK = "Energy Guide";
    public static final String LINK_TYPE = "link";
    public static final String GSRI_TYPE = "gsri";
    public static final String FILE_TYPE_DU02 = "DU02";
    public static final String FILE_TYPE_DU06 = "DU06";

    public static final String ENERGY_EFFICIENCY_PREFIX =
            "https://schema.org/EUEnergyEfficiencyCategory";

    public ProductSummaryResponseVO getProductSummary(ProductSummaryRequestVO requestVO) {

        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String SKU = requestVO.getSku();
        String GROUP = StringUtils.defaultIfBlank(requestVO.getGroup(), "none");
        String SHOP_CODE = CommonConstants.SHOP_CODE_D2C;
        String CUSTOMER_GROUP = "";

        boolean mtsFlag = false;
        if (StringUtils.isNotBlank(requestVO.getMultishopCode())
                && !CommonConstants.SHOP_CODE_D2C.equals(requestVO.getMultishopCode())) {
            mtsFlag = true;
            SHOP_CODE = requestVO.getMultishopCode();
            CUSTOMER_GROUP = StringUtils.defaultIfEmpty(requestVO.getCustomerGroup(), "");
        }

        if (StringUtils.isEmpty(SITE_CODE)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }

        String localeCode =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil
                                .getComLocaleCode(
                                        CachedCodeRequestVO.builder().siteCode(SITE_CODE).build())
                                .getLocaleCode(),
                        "");

        /** 카테고리 표준화 국가 여부 조회 */
        String standardFlag = commonService.getCategoryStandardFlag();

        /** 타임존 조회 */
        String TIMEZONE =
                cachedDataUtil
                        .getComLocaleCode(CachedCodeRequestVO.builder().siteCode(SITE_CODE).build())
                        .getTimezoneNm();

        /** 제품 기본 정보 조회 */
        SystemPdsPdpResponseVO systemPdspdpResponseVO = new SystemPdsPdpResponseVO();

        if (mtsFlag) {
            systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getMtsProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .lgcomSkuId(SKU)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .timezone(TIMEZONE)
                                    .standardFlag(standardFlag)
                                    .shopCode(SHOP_CODE)
                                    .customerGroup(CUSTOMER_GROUP)
                                    .build());
        } else {
            systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .lgcomSkuId(SKU)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .timezone(TIMEZONE)
                                    .standardFlag(standardFlag)
                                    .build());
        }

        if (ObjectUtils.isEmpty(systemPdspdpResponseVO)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        ProductSummaryResponseVO productSmrRespVO = systemPdspdpResponseVO.toPdpSummaryVO();

        String pdpId = productSmrRespVO.getModelId();
        String bizType = productSmrRespVO.getBizType();
        String modelStatusCode = productSmrRespVO.getModelStatusCode();
        String pimSkuId = systemPdspdpResponseVO.getSkuId();
        String modelTypeM = productSmrRespVO.getModelTypeM();

        productSmrRespVO.setModelType(getModelType(modelTypeM));

        /** PRODUCT 정보 조회 (PIM) */
        SystemPdsProductResponseVO systemPdsProductRespVO = getProductInfo(pimSkuId);

        String salesModelCode = systemPdsProductRespVO.getSalesModelCode();
        String salesSuffixCode = systemPdsProductRespVO.getSalesModelSuffixCode();
        String pimUserFrndyProductNm = systemPdsProductRespVO.getNewMktProductNm();

        String userFrndyProductNm =
                StringUtils.isNotBlank(pimUserFrndyProductNm)
                        ? pimUserFrndyProductNm
                        : systemPdspdpResponseVO.getUserFrndyProductNm();

        if (StringUtils.isNotBlank(userFrndyProductNm)) {
            productSmrRespVO.setUserFriendlyName(userFrndyProductNm.replaceAll("\\\"", "&#39;"));
        }

        productSmrRespVO.setAffiliateCode(systemPdsProductRespVO.getAffiliateCode());
        productSmrRespVO.setSalesModelCode(salesModelCode);
        productSmrRespVO.setSalesSuffixCode(salesSuffixCode);
        productSmrRespVO.setClassificationFlagLv1(systemPdsProductRespVO.getLv1CategoryCodeNm());
        productSmrRespVO.setClassificationFlagLv2(systemPdsProductRespVO.getLv2CategoryCodeNm());
        productSmrRespVO.setClassificationFlagLv3(systemPdsProductRespVO.getLv3CategoryCodeNm());
        productSmrRespVO.setClassificationFlagLv4(systemPdsProductRespVO.getLv4CategoryCodeNm());
        productSmrRespVO.setModelEanCode(systemPdsProductRespVO.getModelEanCode());
        productSmrRespVO.setPdrUseFlag(systemPdsProductRespVO.getPimSpecUseFlag());
        productSmrRespVO.setProductLevelCode(systemPdsProductRespVO.getLv1ProductCode());

        /** 접속한 멤버 ID 조회 */
        String token = RequestHeaderUtil.getAccessCode();
        TokenContextHolder.setToken(token);
        if (SessionUserUtil.isLogin()) {
            SessionUserVO userInfo = SessionUserUtil.getUserInfo();
            productSmrRespVO.setUseId(Objects.toString(userInfo.getUserId()));
        }

        productSmrRespVO.setLoginGroup(GROUP);

        /** configuration/preference 코드 정보 세팅 */
        setCodeInfo(productSmrRespVO, bizType);

        productSmrRespVO.setMsrp(
                cachedDataUtil.getMsrp(SITE_CODE, systemPdspdpResponseVO.getMsrpSalesPrice()));

        String rsUseFlag = CommonConstants.NO_FLAG;
        if (SITE_CODE.equals("RS")
                && productSmrRespVO.getPisDocOldFlag().equalsIgnoreCase(CommonConstants.NO_FLAG)) {
            rsUseFlag = CommonConstants.YES_FLAG;
        }
        productSmrRespVO.setRsUseFlag(rsUseFlag);

        String defaultProductTag = "";
        if (CommonConstants.PRODUCT_STATE_DISCONTINUED.equals(
                productSmrRespVO.getModelStatusCode())) {
            defaultProductTag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComMessageCode(
                                    CachedCodeRequestVO.ofDspTypeComMessage(
                                            productSmrRespVO.getModelStatusCode(),
                                            CommonConstants.SHOP_CODE_D2C,
                                            localeCode)),
                            "Discontinued");
        }
        productSmrRespVO.setDefaultProductTag(defaultProductTag);

        String defaultDummyImg =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("dummy_image_url")),
                        "");

        productSmrRespVO.setLargeImageAddr(
                StringUtils.defaultIfBlank(systemPdspdpResponseVO.getBigImgUrl(), defaultDummyImg));
        productSmrRespVO.setMediumImageAddr(
                StringUtils.defaultIfBlank(systemPdspdpResponseVO.getMdmImgUrl(), defaultDummyImg));
        productSmrRespVO.setSmallImageAddr(
                StringUtils.defaultIfBlank(systemPdspdpResponseVO.getSmlImgUrl(), defaultDummyImg));

        String eLabelFlag =
                CommonConstants.YES_FLAG.equals(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("Energy_Label_Flag")
                                                .build()))
                        ? CommonConstants.YES_FLAG
                        : CommonConstants.NO_FLAG;
        productSmrRespVO.setEnergyLabelFlag(eLabelFlag);

        /** 제품 GSRI(환경규제 관련 모델) 정보 세팅 */
        setGsriEnergyLabelVO(productSmrRespVO, SHOP_CODE);

        /** 제품 카테고리 정보 조회 */
        SystemPdsPdpCategoryResponseVO systemPdsPdpCategoryRespVO =
                systemPdsPdpInfoClient.getPdpCategoryInfo(
                        SystemPdsPdpCategoryRequestVO.builder()
                                .pdpId(pdpId)
                                .standardFlag(standardFlag)
                                .shopCode(SHOP_CODE)
                                .build());

        if (ObjectUtils.isEmpty(systemPdsPdpCategoryRespVO)) {
            systemPdsPdpCategoryRespVO = new SystemPdsPdpCategoryResponseVO();
            systemPdsPdpCategoryRespVO = systemPdsPdpCategoryRespVO.createEmptyVO();
        }

        if (CommonConstants.YES_FLAG.equals(standardFlag)) {
            productSmrRespVO.setSuperCategoryId(systemPdsPdpCategoryRespVO.getLv2CategoryCode());
            productSmrRespVO.setSuperCategoryName(
                    systemPdsPdpCategoryRespVO.getLv2SiteCategoryNm());
            productSmrRespVO.setSuperCategoryEngName(systemPdsPdpCategoryRespVO.getLv2CategoryNm());
            productSmrRespVO.setCategoryId(systemPdsPdpCategoryRespVO.getLv3CategoryCode());
            productSmrRespVO.setCategoryName(systemPdsPdpCategoryRespVO.getLv3SiteCategoryNm());
            productSmrRespVO.setSubCategoryId(systemPdsPdpCategoryRespVO.getLv4CategoryCode());
            productSmrRespVO.setSubCategoryName(systemPdsPdpCategoryRespVO.getLv4SiteCategoryNm());
        } else {
            productSmrRespVO.setSuperCategoryId(systemPdsPdpCategoryRespVO.getLv1CategoryCode());
            productSmrRespVO.setSuperCategoryName(
                    systemPdsPdpCategoryRespVO.getLv1SiteCategoryNm());
            productSmrRespVO.setSuperCategoryEngName(systemPdsPdpCategoryRespVO.getLv1CategoryNm());
            productSmrRespVO.setCategoryId(systemPdsPdpCategoryRespVO.getLv2CategoryCode());
            productSmrRespVO.setCategoryName(systemPdsPdpCategoryRespVO.getLv2SiteCategoryNm());
            productSmrRespVO.setSubCategoryId(systemPdsPdpCategoryRespVO.getLv3CategoryCode());
            productSmrRespVO.setSubCategoryName(systemPdsPdpCategoryRespVO.getLv3SiteCategoryNm());
        }

        String superCategoryId = productSmrRespVO.getSuperCategoryId();
        String categoryId = productSmrRespVO.getCategoryId();
        String subCategoryId = productSmrRespVO.getSubCategoryId();

        /** PDP Inquiry To Buy Tab */
        String pdpITBTabUseCategoryIds =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.builder()
                                .keyCode("inquiry_to_buy_pdp_tab_use_category")
                                .build());

        String pdpITBTabLinkUrl =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.builder()
                                .keyCode("inquiry_to_buy_pdp_tab_link_url")
                                .build());

        boolean pdpItbTabUseFlag =
                !"".equals(subCategoryId) ? pdpITBTabUseCategoryIds.contains(subCategoryId) : false;

        if (pdpItbTabUseFlag) {
            productSmrRespVO.setPdpInquiryToBuyTabUseFlag(CommonConstants.YES_FLAG);
            productSmrRespVO.setPdpInquiryToBuyTabLinkUrl(pdpITBTabLinkUrl);
        } else {
            productSmrRespVO.setPdpInquiryToBuyTabUseFlag(CommonConstants.NO_FLAG);
            productSmrRespVO.setPdpInquiryToBuyTabLinkUrl("");
        }

        /** Find The Dealer Hide Flag */
        String pdpFTDHideCategoryIds =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.builder()
                                .keyCode("find_the_dealer_hide_category_id")
                                .build());

        String findTheDealerHideFlag = CommonConstants.NO_FLAG;
        if (StringUtils.isNotBlank(pdpFTDHideCategoryIds) && !"".equals(superCategoryId)) {
            pdpFTDHideCategoryIds = pdpFTDHideCategoryIds.trim();
            findTheDealerHideFlag =
                    pdpFTDHideCategoryIds.contains(superCategoryId)
                            ? CommonConstants.YES_FLAG
                            : CommonConstants.NO_FLAG;
        }
        productSmrRespVO.setFindTheDealerHideFlag(findTheDealerHideFlag);

        /** 제품 키피쳐(불렛 피쳐) 정보 조회 */
        List<SystemPdsKeyfeaturesResponseVO> systemPdsKeyFeatureList =
                systemPdsPdpInfoClient.getPdpKeyFeatureList(
                        SystemPdsKeyfeaturesRequestVO.builder()
                                .siteCode(SITE_CODE)
                                .pdpId(pdpId)
                                .build());

        if (ObjectUtils.isNotEmpty(systemPdsKeyFeatureList)) {
            List<KeyfeaturesVO> keyFeatureList = new ArrayList<KeyfeaturesVO>();
            for (SystemPdsKeyfeaturesResponseVO systemPdsKeyFeature : systemPdsKeyFeatureList) {
                keyFeatureList.add(systemPdsKeyFeature.toPdsKeyFeaturesVO());
            }
            productSmrRespVO.setBulletFeatures(keyFeatureList);
        }

        /** wtb flag 세팅 */
        setWtbFlag(productSmrRespVO);

        /** 제품 sibling 정보 조회 */
        productSmrRespVO.setSiblings(
                getSiblings(productSmrRespVO, standardFlag, mtsFlag, SHOP_CODE, CUSTOMER_GROUP));

        /** 제품 Rating 조회 */
        String siblingModelTotal =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("REVIEW_SIBLING_MODEL_TOTAL_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG);

        SystemPdsPdpUserReviewRatingResponseVO systemPdsUserReviewRatingRespVO =
                new SystemPdsPdpUserReviewRatingResponseVO();
        if (CommonConstants.YES_FLAG.equals(siblingModelTotal)
                && ObjectUtils.isNotEmpty(productSmrRespVO.getSiblings())) {
            systemPdsUserReviewRatingRespVO =
                    systemPdsPdpInfoClient.getUserReviewSiblingRatings(
                            SystemPdsPdpUserReviewRatingRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .pdpId(pdpId)
                                    .build());
        } else {
            systemPdsUserReviewRatingRespVO =
                    systemPdsPdpInfoClient.getUserReviewRatings(
                            SystemPdsPdpUserReviewRatingRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .pdpId(pdpId)
                                    .build());
        }

        int srating = 0;
        BigDecimal srating2 = BigDecimal.ZERO;
        int ratingPercent = 0;
        if (ObjectUtils.isNotEmpty(systemPdsUserReviewRatingRespVO)) {
            srating = systemPdsUserReviewRatingRespVO.getSRating().intValue();
            srating2 = systemPdsUserReviewRatingRespVO.getSRating2();
            ratingPercent = systemPdsUserReviewRatingRespVO.getRatingPercent().intValue();
            productSmrRespVO.setPCount(systemPdsUserReviewRatingRespVO.getPCount());
        }
        productSmrRespVO.setSRating(srating);
        productSmrRespVO.setSRating2(srating2);
        productSmrRespVO.setRatingPercent(ratingPercent);

        /** Review Type */
        String b2bUseFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("BUSINESS_REVIEW_USE_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG);

        String reviewType =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("REVIEW_TYPE").build()),
                        CommonConstants.NO_FLAG);
        reviewType =
                !(CommonConstants.BIZ_TYPE_B2B.equals(bizType)
                                && CommonConstants.NO_FLAG.equals(b2bUseFlag))
                        ? reviewType
                        : CommonConstants.NO_FLAG;
        productSmrRespVO.setReviewType(reviewType);

        /** Compatible Products */
        String compatibleProducts3TypeUseFlag =
                mtsFlag
                        ? ""
                        : cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig(
                                        "compatibleProducts3TypeUseFlag"));
        boolean findMyModelFlag = false;
        List<Map<String, Object>> compatibleList = new ArrayList<>();
        if (ACCESSORY_PRODUCT.equals(productSmrRespVO.getModelTypeM())) {
            if (CommonConstants.YES_FLAG.equals(compatibleProducts3TypeUseFlag)) {
                compatibleList =
                        systemPdsPdpInfoClient.getAccProductsFor3Type(
                                SystemPdsAccessoryProductRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(pdpId)
                                        .bizTypeCode(bizType)
                                        .build());
            } else {
                compatibleList =
                        systemPdsPdpInfoClient.getAccessoryProductList(
                                SystemPdsAccessoryProductRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(pdpId)
                                        .bizTypeCode(bizType)
                                        .build());
            }
        } else if (GENERAL_PRODUCT.equals(productSmrRespVO.getModelTypeM())) {
            String showCompatibleProducts =
                    cachedDataUtil.getComSystemConfigurationCode(
                            CachedCodeRequestVO.ofComSysConfig("showCompatibleProducts"));

            if (CommonConstants.YES_FLAG.equals(showCompatibleProducts)) {
                compatibleList =
                        systemPdsPdpInfoClient.getProductAccessoryList(
                                SystemPdsProductAccessoryRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(pdpId)
                                        .standardFlag(standardFlag)
                                        .compatibleProducts3TypeUseFlag(
                                                compatibleProducts3TypeUseFlag)
                                        .build());

                findMyModelFlag = true;
            }
        }
        productSmrRespVO.setCompatibleModels(compatibleList);
        productSmrRespVO.setCompatibleCount(compatibleList.size());

        List<Map<String, Object>> superCateList = new ArrayList<Map<String, Object>>();
        if (findMyModelFlag) {
            List<SystemPdsCategoryResponseVO> systemPdsFindMyMdCategoryList =
                    systemPdsCategoryClient.getFindMyModelSuperCategoryList(
                            SystemPdsCategoryRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .bizTypeCode(bizType)
                                    .build());

            if (ObjectUtils.isNotEmpty(systemPdsFindMyMdCategoryList)) {
                for (SystemPdsCategoryResponseVO sysPdsCategoryRespVO :
                        systemPdsFindMyMdCategoryList) {
                    Map<String, Object> superCategoryMap = new HashMap<String, Object>();
                    superCategoryMap.put("code", sysPdsCategoryRespVO.getCategoryCode());
                    superCategoryMap.put("value", sysPdsCategoryRespVO.getCategoryNm());
                    superCateList.add(superCategoryMap);
                }
            }
        }
        productSmrRespVO.setSuperCateList(superCateList);

        if (!mtsFlag) {
            String discontinuedPageSpBtn =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .keyCode("DISCONTINUED_PAGE_SUPPORT_BTN")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String productSupportFlag =
                    (CommonConstants.PRODUCT_STATE_DISCONTINUED.equals(modelStatusCode)
                                    && CommonConstants.YES_FLAG.equals(discontinuedPageSpBtn))
                            ? CommonConstants.YES_FLAG
                            : CommonConstants.NO_FLAG;

            productSmrRespVO.setProductSupportFlag(productSupportFlag);
            if (CommonConstants.YES_FLAG.equals(productSupportFlag)) {
                productSmrRespVO.setProductSupportUrl(
                        commonService.getSupportLinkUrl(
                                ProductSupportRequestVO.builder()
                                        .localeCode(SITE_CODE)
                                        .modelId(pdpId)
                                        .salesModelCode(salesModelCode)
                                        .salesSuffixCode(salesSuffixCode)
                                        .build()));
            }

            /** WishCnt */
            SystemAdminWishlistCountResponseVO sysAdmWishlistCntRespVO =
                    systemAdminWishlistClient.getCustomerWishlistCnt(
                            SystemAdminWishlistCountRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .modelId(pdpId)
                                    .build());
            String wishCnt = "0";
            if (ObjectUtils.isNotEmpty(sysAdmWishlistCntRespVO)) {
                wishCnt = String.valueOf(sysAdmWishlistCntRespVO.getWishTotalCnt());
            }

            productSmrRespVO.setWishCnt(wishCnt);
            productSmrRespVO.setWishTotalCnt(wishCnt);
        }
        /** Warranty Msg Exception List */
        String warrantyExceptionModels =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.ofComSysConfig("pdp_warranty_message_exception_model"));

        List<String> warrantyMsgExcpList = new ArrayList<>();
        warrantyMsgExcpList.add("");
        if (StringUtils.isNotBlank(warrantyExceptionModels)) {
            String[] splitWarrantyExceptionModels = warrantyExceptionModels.split(",");
            warrantyMsgExcpList = Arrays.asList(splitWarrantyExceptionModels);
        }
        productSmrRespVO.setWarrantyMsgExceptionList(warrantyMsgExcpList);

        /** Label Icon List */
        String pdpLabelUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("Pdp_Label_Use_Flag")
                                        .build()),
                        CommonConstants.NO_FLAG);

        String labelUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("LABEL_USE_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG);

        String repairabilityIndexFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("repairability_index_use_flag")
                                        .build()),
                        CommonConstants.NO_FLAG);

        boolean pdpLabelUseChk =
                CommonConstants.YES_FLAG.equals(labelUseFlag)
                        && CommonConstants.YES_FLAG.equals(pdpLabelUseFlag);

        boolean labelIconOnlyChk =
                CommonConstants.YES_FLAG.equals(labelUseFlag)
                        && CommonConstants.YES_FLAG.equals(repairabilityIndexFlag);

        LocalDateTime todayTime = LocalDateTime.now();
        String pattern = "yyyyMMdd HH:mm:ss";
        String today = "";
        try {
            todayTime = DateUtil.getCurrentTime(TIMEZONE, pattern);
            today = todayTime.toLocalDate().toString().replaceAll("-", "");
        } catch (Exception e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            today = todayTime.atOffset(ZoneOffset.of("+09:00")).format(formatter);
        }

        List<ProductIconListResponseVO> productIconList =
                new ArrayList<ProductIconListResponseVO>();
        List<ProductIconListResponseVO> labelRepairMapList =
                new ArrayList<ProductIconListResponseVO>();
        if (pdpLabelUseChk && labelIconOnlyChk) {
            List<SystemPdsProductIconListResponseVO> sysPdsProductIconListVO =
                    systemPdsPdpInfoClient.getProductIconList(
                            SystemPdsProductIconListRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .bizTypeCode(bizType)
                                    .today(today)
                                    .pdpIdList(Arrays.asList(pdpId))
                                    .build());

            String pdpLabelUse = CommonConstants.NO_FLAG;

            if (ObjectUtils.isNotEmpty(sysPdsProductIconListVO)) {
                for (SystemPdsProductIconListResponseVO sysPdsPdpIcon : sysPdsProductIconListVO) {
                    ProductIconListResponseVO pdpIconRespVO = sysPdsPdpIcon.toVO();

                    if (CommonConstants.YES_FLAG.equals(pdpIconRespVO.getPdpUse())
                            && !"REPAIRABILITY INDEX".equals(pdpIconRespVO.getShortDesc())) {
                        pdpLabelUse = CommonConstants.YES_FLAG;
                    }

                    if (pdpLabelUseChk
                            && "REPAIRABILITY INDEX".equals(pdpIconRespVO.getShortDesc())
                            && CommonConstants.YES_FLAG.equals(repairabilityIndexFlag)) {
                        if (labelRepairMapList.size() == 0) {
                            labelRepairMapList.add(pdpIconRespVO);
                        }
                    }

                    if (labelIconOnlyChk
                            && "REPAIRABILITY INDEX".equals(pdpIconRespVO.getShortDesc())) {
                        if (labelRepairMapList.size() == 0) {
                            labelRepairMapList.add(pdpIconRespVO);
                        }
                    }
                    productIconList.add(pdpIconRespVO);
                }
                if (pdpLabelUseChk) {
                    productSmrRespVO.setPdpLabelUse(pdpLabelUse);
                }
            }
            productSmrRespVO.setLabelUseFlag(labelUseFlag);
            productSmrRespVO.setRepairabilityIndexFlag(repairabilityIndexFlag);
        }
        productSmrRespVO.setLabelIconList(productIconList);
        productSmrRespVO.setLabelRepairMap(labelRepairMapList);

        /** Catalogue */
        String catalogueUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("CATALOGUE_USE_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG);

        if (CommonConstants.YES_FLAG.equals(catalogueUseFlag)) {
            SystemAdminCatalogueListResponseVO systemAdminProductCatalogue =
                    systemAdminProductClient.getProductCatalogue(
                            SystemAdminCatalogueListRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .modelId(pdpId)
                                    .build());

            if (ObjectUtils.isNotEmpty(systemAdminProductCatalogue)) {
                productSmrRespVO.setCatalogueFileName(
                        systemAdminProductCatalogue.getCatalogueFileName());
                productSmrRespVO.setCatalogueFilePathText(
                        systemAdminProductCatalogue.getCatalogueFilePathText());
            } else {
                catalogueUseFlag = CommonConstants.NO_FLAG;
            }
        }

        productSmrRespVO.setCatalogueUseFlag(catalogueUseFlag);

        /** MWO Panel */
        String mwoPanelFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("mwo_panel_flag")
                                        .build()),
                        CommonConstants.NO_FLAG);

        String categoryList =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.builder()
                                .siteCode(SITE_CODE)
                                .keyCode("panel_categoryId_list")
                                .build());

        if (CommonConstants.YES_FLAG.equals(mwoPanelFlag)) {
            if (categoryList.contains(categoryId)) {
                SystemPdsProductPanelEntityVO sysPdsProductpanel =
                        systemPdsPdpInfoClient.getProductPanel(
                                SystemPdsProductPanelEntityVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(pdpId)
                                        .build());
                if (ObjectUtils.isNotEmpty(sysPdsProductpanel)) {
                    productSmrRespVO.setMwoPanelFileName(sysPdsProductpanel.getPanelFileNm());
                    productSmrRespVO.setMwoPanelFilePathText(sysPdsProductpanel.getPanelFilePath());
                } else {
                    mwoPanelFlag = CommonConstants.NO_FLAG;
                }
            } else {
                mwoPanelFlag = CommonConstants.NO_FLAG;
            }
        }

        productSmrRespVO.setMwoPanelUseFlag(mwoPanelFlag);

        /** Objet */
        if (CommonConstants.YES_FLAG.equals(productSmrRespVO.getObjetProductFlag())) {
            SystemPdsObjetProductEntityVO systemPdsObjetProduct =
                    systemPdsPdpInfoClient.getObjetProduct(
                            SystemPdsObjetProductEntityVO.builder().pdpId(pdpId).build());

            if (ObjectUtils.isNotEmpty(systemPdsObjetProduct)) {
                productSmrRespVO.setObjetModel(systemPdsObjetProduct.toVO());
            }

            List<SystemPdsObjetMaterialEntityVO> systemPdsObjetMaterialList =
                    systemPdsPdpInfoClient.getObjetMaterialList(
                            SystemPdsObjetMaterialEntityVO.builder().pdpId(pdpId).build());

            if (ObjectUtils.isNotEmpty(systemPdsObjetMaterialList)) {
                List<ObjetMaterialEntityVO> objetMaterialList =
                        new ArrayList<ObjetMaterialEntityVO>();
                for (SystemPdsObjetMaterialEntityVO sysPdsObjetMaterial :
                        systemPdsObjetMaterialList) {
                    objetMaterialList.add(sysPdsObjetMaterial.toVO());
                }
                productSmrRespVO.setObjetMaterialList(
                        ObjectUtils.isNotEmpty(objetMaterialList) ? objetMaterialList : null);
            }

            List<SystemPdsObjetMaterialDetailEntityVO> systemPdsObjetMaterialDetailList =
                    systemPdsPdpInfoClient.getObjetMaterialDetailList(
                            SystemPdsObjetMaterialDetailEntityVO.builder().pdpId(pdpId).build());

            if (ObjectUtils.isNotEmpty(systemPdsObjetMaterialDetailList)) {
                List<ObjetMaterialDetailEntityVO> objetMaterialDetailList =
                        new ArrayList<ObjetMaterialDetailEntityVO>();
                for (SystemPdsObjetMaterialDetailEntityVO sysPdsObjetMaterialDetail :
                        systemPdsObjetMaterialDetailList) {
                    objetMaterialDetailList.add(sysPdsObjetMaterialDetail.toVO());
                }
                productSmrRespVO.setObjetMaterialDetailList(
                        ObjectUtils.isNotEmpty(objetMaterialDetailList)
                                ? objetMaterialDetailList
                                : null);
            }

            List<SystemPdsObjetPanelTypeEntityVO> systemPdsObjetPanelTypeList =
                    systemPdsPdpInfoClient.getObjetPanelTypeList(
                            SystemPdsObjetPanelTypeEntityVO.builder().pdpId(pdpId).build());

            if (ObjectUtils.isNotEmpty(systemPdsObjetPanelTypeList)) {
                List<ObjetPanelTypeEntityVO> objetPanelTypeList =
                        new ArrayList<ObjetPanelTypeEntityVO>();
                for (SystemPdsObjetPanelTypeEntityVO systemPdsObjetPanelType :
                        systemPdsObjetPanelTypeList) {
                    objetPanelTypeList.add(systemPdsObjetPanelType.toVO());
                }
                productSmrRespVO.setObjetPanelList(
                        ObjectUtils.isNotEmpty(objetPanelTypeList) ? objetPanelTypeList : null);
            }
        }

        /** Capacity Link */
        String capacityLinkUseCategory =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("capacity_link_use_category")
                                        .build()),
                        "");

        String capacityLinkUseFlag = CommonConstants.NO_FLAG;
        if (StringUtils.isNotBlank(capacityLinkUseCategory)
                && capacityLinkUseCategory.contains(categoryId)) {
            capacityLinkUseFlag = CommonConstants.YES_FLAG;
        }
        productSmrRespVO.setCapacityLinkUseFlag(capacityLinkUseFlag);

        /** Tax Info */
        String taxInfoProductLvl1Code =
                cachedDataUtil.getComSystemPreferenceCode(
                        CachedCodeRequestVO.builder()
                                .siteCode(SITE_CODE)
                                .keyCode("tax_info_product_level_code")
                                .build());

        String taxInformationUseFlag = CommonConstants.NO_FLAG;
        if (StringUtils.isNotBlank(taxInfoProductLvl1Code)) {
            List<String> taxInfoProductLv1CodeList =
                    Arrays.asList(taxInfoProductLvl1Code.split(","));
            String productLevelCode = systemPdsProductRespVO.getLv1ProductCode();
            for (String code : taxInfoProductLv1CodeList) {
                if (code.equals(productLevelCode)) {
                    taxInformationUseFlag = CommonConstants.YES_FLAG;
                }
            }
        }
        productSmrRespVO.setTaxInformationUseFlag(taxInformationUseFlag);

        /** Expert Chat */
        String expertChatYn = "N";
        String expertChatLocale =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.builder()
                                .siteCode("GLOBAL")
                                .keyCode("expert_chat_locale")
                                .build());

        List<String> expertChatLocaleList =
                expertChatLocale != null
                        ? Arrays.asList(expertChatLocale.split(","))
                        : Collections.emptyList();

        if (expertChatLocaleList.contains(SITE_CODE)) {
            String expertChatCategory =
                    cachedDataUtil.getComSystemConfigurationCode(
                            CachedCodeRequestVO.builder().keyCode("expert_chat_category").build());

            List<String> expertChatCategoryList =
                    expertChatCategory != null
                            ? Arrays.asList(expertChatCategory.split(","))
                            : Collections.emptyList();

            List<SystemPdsPdpCategoryResponseVO> systemPdsPdpAllCategoryRespVO =
                    systemPdsPdpInfoClient.getPdpAllCategoryInfo(
                            SystemPdsPdpCategoryRequestVO.builder()
                                    .pdpId(pdpId)
                                    .standardFlag(standardFlag)
                                    .shopCode(SHOP_CODE)
                                    .searchType("all")
                                    .build());

            boolean hasMatchingCategory =
                    systemPdsPdpAllCategoryRespVO.stream()
                            .map(
                                    vo -> {
                                        String subCategory = null;
                                        String category = null;
                                        if (CommonConstants.YES_FLAG.equals(standardFlag)) {
                                            subCategory = vo.getLv4CategoryCode();
                                            category = vo.getLv3CategoryCode();
                                        } else {
                                            subCategory = vo.getLv3CategoryCode();
                                            category = vo.getLv2CategoryCode();
                                        }
                                        String selectedId =
                                                subCategory != null ? subCategory : category;
                                        return selectedId;
                                    })
                            .anyMatch(expertChatCategoryList::contains);

            if (hasMatchingCategory) {
                expertChatYn = "Y";
            } else {
                expertChatYn = "N";
            }
        }
        productSmrRespVO.setExpertChatYn(expertChatYn);

        /* productEnquiryUrl  */
        if ("IN".equals(SITE_CODE) && "Y".equals(productSmrRespVO.getProductEnquiryFlag())) {
            StringBuffer productEnquiryUrl = new StringBuffer();
            productEnquiryUrl.append(
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("pdp_product_enquiry_url")
                                            .build()),
                            ""));
            productEnquiryUrl.append("?c=");
            productEnquiryUrl.append(productSmrRespVO.getCategoryName());
            productEnquiryUrl.append("&m=");
            productEnquiryUrl.append(productSmrRespVO.getModelDisplayName());
            productSmrRespVO.setProductEnquiryUrl(productEnquiryUrl.toString());
        }

        productSmrRespVO.setCompatibleProducts3TypeUseFlag(compatibleProducts3TypeUseFlag);
        List<Map<String, Object>> countryDefaultMapList = new ArrayList<>();
        productSmrRespVO.setCountryList(countryDefaultMapList);
        productSmrRespVO.setDeliveryFlag("N");
        productSmrRespVO.setDeliveryMsg(null);
        productSmrRespVO.setEcommerceTarget("self");
        productSmrRespVO.setFirstLabelCheckFlag("");
        productSmrRespVO.setInstallationFlag("N");
        productSmrRespVO.setInstallationMsg(null);
        productSmrRespVO.setRepairModelAreaYn(null);
        productSmrRespVO.setShippingFlag("N");
        productSmrRespVO.setShippingMsg(null);
        productSmrRespVO.setSpecMsgFlag("N");

        /** Model Promotion Message */
        setModelPromotionMsg(productSmrRespVO, today);

        /** SNS Share Info */
        productSmrRespVO.setShareInfo(
                commonService.shareInfo(
                        SnsShareInfoRequestVO.builder()
                                .localeCode(SITE_CODE)
                                .bizType(bizType)
                                .build()));

        return productSmrRespVO;
    }

    public List<ProductSummarySpecResponseVO> getProductSummarySpec(
            ProductSummaryRequestVO requestVO) {

        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String SKU = requestVO.getSku();
        String SHOP_CODE = CommonConstants.SHOP_CODE_D2C;
        String CUSTOMER_GROUP = "";

        boolean mtsFlag = false;
        if (StringUtils.isNotBlank(requestVO.getMultishopCode())
                && !CommonConstants.SHOP_CODE_D2C.equals(requestVO.getMultishopCode())) {
            mtsFlag = true;
            SHOP_CODE = requestVO.getMultishopCode();
            CUSTOMER_GROUP = requestVO.getCustomerGroup();
        }

        if (StringUtils.isEmpty(SITE_CODE)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }

        String localeCode =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil
                                .getComLocaleCode(
                                        CachedCodeRequestVO.builder().siteCode(SITE_CODE).build())
                                .getLocaleCode(),
                        "");

        /** 카테고리 표준화 국가 여부 조회 */
        String standardFlag = commonService.getCategoryStandardFlag();

        /** 타임존 조회 */
        String TIMEZONE =
                cachedDataUtil
                        .getComLocaleCode(CachedCodeRequestVO.builder().siteCode(SITE_CODE).build())
                        .getTimezoneNm();

        /** 제품 기본 정보 조회 */
        SystemPdsPdpResponseVO systemPdspdpResponseVO = new SystemPdsPdpResponseVO();
        if (mtsFlag) {
            systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getMtsProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .lgcomSkuId(SKU)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .timezone(TIMEZONE)
                                    .standardFlag(standardFlag)
                                    .shopCode(SHOP_CODE)
                                    .customerGroup(CUSTOMER_GROUP)
                                    .build());
        } else {
            systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .lgcomSkuId(SKU)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .timezone(TIMEZONE)
                                    .standardFlag(standardFlag)
                                    .build());
        }

        if (ObjectUtils.isEmpty(systemPdspdpResponseVO)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        ProductSummaryResponseVO productSmrRespVO = systemPdspdpResponseVO.toPdpSummaryVO();

        String pdpId = productSmrRespVO.getModelId();
        String bizType = productSmrRespVO.getBizType();
        String modelStatusCode = productSmrRespVO.getModelStatusCode();

        setWtbFlag(productSmrRespVO);

        List<SystemPdsProductSpecBundleSimpleListResponseVO> sysPdsProductSpecBundleSimpleList =
                new ArrayList<SystemPdsProductSpecBundleSimpleListResponseVO>();
        if (BUNDLE_PRODUCT.equals(productSmrRespVO.getModelTypeM())
                || OBS_BUNDLE_PRODUCT.equals(productSmrRespVO.getModelTypeM())) {
            String BUSINESS_REVIEW_USE_FLAG =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("BUSINESS_REVIEW_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String REVIEW_TYPE =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder().keyCode("REVIEW_TYPE").build()),
                            CommonConstants.NO_FLAG);

            if (productSmrRespVO.getBizType().equals("B2B")
                    && BUSINESS_REVIEW_USE_FLAG.equals("N")) {
                REVIEW_TYPE = "N";
            }

            sysPdsProductSpecBundleSimpleList =
                    systemPdsPdpInfoClient.getProductSpecBundleSimpleList(
                            SystemPdsProductSpecBundleSimpleListRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .pdpId(pdpId)
                                    .standardFlag(standardFlag)
                                    .reviewType(REVIEW_TYPE)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductSpecBundleSimpleList)) {
                for (SystemPdsProductSpecBundleSimpleListResponseVO sysPdsProductSpecBundle :
                        sysPdsProductSpecBundleSimpleList) {

                    String pimSku = sysPdsProductSpecBundle.getBundlePimSkuId();

                    /** PRODUCT 정보 조회 (PIM) */
                    SystemPdsProductResponseVO systemPdsProductRespVO = getProductInfo(pimSku);

                    String pimUserFrndyProductNm = systemPdsProductRespVO.getNewMktProductNm();

                    String userFrndyProductNm =
                            StringUtils.isNotBlank(pimUserFrndyProductNm)
                                    ? pimUserFrndyProductNm
                                    : sysPdsProductSpecBundle.getUserFrndyProductNm();

                    if (StringUtils.isNotBlank(userFrndyProductNm)) {
                        sysPdsProductSpecBundle.setUserFrndyProductNm(
                                userFrndyProductNm.replaceAll("\\\"", "&#39;"));
                    }

                    sysPdsProductSpecBundle.setSalesModelCode(
                            systemPdsProductRespVO.getSalesModelCode());
                    sysPdsProductSpecBundle.setSalesSuffixCode(
                            systemPdsProductRespVO.getSalesModelSuffixCode());
                    sysPdsProductSpecBundle.setPdrUseFlag(
                            systemPdsProductRespVO.getPimSpecUseFlag());
                }
            }

        } else {
            /** 제품 카테고리 정보 조회 */
            SystemPdsPdpCategoryResponseVO systemPdsPdpCategoryRespVO =
                    systemPdsPdpInfoClient.getPdpCategoryInfo(
                            SystemPdsPdpCategoryRequestVO.builder()
                                    .pdpId(pdpId)
                                    .standardFlag(standardFlag)
                                    .shopCode(SHOP_CODE)
                                    .build());

            if (ObjectUtils.isEmpty(systemPdsPdpCategoryRespVO)) {
                systemPdsPdpCategoryRespVO = new SystemPdsPdpCategoryResponseVO();
                systemPdsPdpCategoryRespVO = systemPdsPdpCategoryRespVO.createEmptyVO();
            }

            if (CommonConstants.YES_FLAG.equals(standardFlag)) {
                productSmrRespVO.setCategoryName(systemPdsPdpCategoryRespVO.getLv3SiteCategoryNm());
            } else {
                productSmrRespVO.setCategoryName(systemPdsPdpCategoryRespVO.getLv2SiteCategoryNm());
            }

            String pimSkuId = systemPdspdpResponseVO.getSkuId();

            /** PRODUCT 정보 조회 (PIM) */
            SystemPdsProductResponseVO systemPdsProductRespVO = getProductInfo(pimSkuId);

            String pimUserFrndyProductNm = systemPdsProductRespVO.getNewMktProductNm();

            String userFrndyProductNm =
                    StringUtils.isNotBlank(pimUserFrndyProductNm)
                            ? pimUserFrndyProductNm
                            : systemPdspdpResponseVO.getUserFrndyProductNm();

            SystemPdsProductSpecBundleSimpleListResponseVO
                    sysPdsProductSpecBundleSimpleListResponse =
                            new SystemPdsProductSpecBundleSimpleListResponseVO();
            sysPdsProductSpecBundleSimpleListResponse.setLgcomSkuId(SKU);
            sysPdsProductSpecBundleSimpleListResponse.setPimSkuId(productSmrRespVO.getPimSku());
            sysPdsProductSpecBundleSimpleListResponse.setPdpId(pdpId);
            sysPdsProductSpecBundleSimpleListResponse.setBundleModelName(
                    productSmrRespVO.getModelName());
            sysPdsProductSpecBundleSimpleListResponse.setBigImgUrl(
                    productSmrRespVO.getLargeImageAddr());
            sysPdsProductSpecBundleSimpleListResponse.setMdmImgUrl(
                    productSmrRespVO.getMediumImageAddr());
            sysPdsProductSpecBundleSimpleListResponse.setSmlImgUrl(
                    productSmrRespVO.getSmallImageAddr());
            sysPdsProductSpecBundleSimpleListResponse.setWtbUseFlag(
                    productSmrRespVO.getWtbUseFlag());
            sysPdsProductSpecBundleSimpleListResponse.setWtbExtlLinkUseFlag(
                    productSmrRespVO.getWtbExternalLinkUseFlag());
            sysPdsProductSpecBundleSimpleListResponse.setWtbExtlLinkNm(
                    productSmrRespVO.getWtbExternalLinkName());
            sysPdsProductSpecBundleSimpleListResponse.setWtbExtlLinkUrl(
                    productSmrRespVO.getWtbExternalLinkUrl());
            sysPdsProductSpecBundleSimpleListResponse.setWtbExtlLinkSelfScreenFlag(
                    productSmrRespVO.getWtbExternalLinkSelfFlag());
            sysPdsProductSpecBundleSimpleListResponse.setObsSellFlag(
                    productSmrRespVO.getObsSellFlag());
            sysPdsProductSpecBundleSimpleListResponse.setSalesModelCode(
                    systemPdsProductRespVO.getSalesModelCode());
            sysPdsProductSpecBundleSimpleListResponse.setSalesSuffixCode(
                    systemPdsProductRespVO.getSalesModelSuffixCode());
            sysPdsProductSpecBundleSimpleListResponse.setPdrUseFlag(
                    systemPdsProductRespVO.getPimSpecUseFlag());
            if (StringUtils.isNotBlank(userFrndyProductNm)) {
                sysPdsProductSpecBundleSimpleListResponse.setUserFrndyProductNm(
                        userFrndyProductNm.replaceAll("\\\"", "&#39;"));
            }
            sysPdsProductSpecBundleSimpleList.add(sysPdsProductSpecBundleSimpleListResponse);
        }

        String defaultDummyImg =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("dummy_image_url")),
                        "");

        String inquiryToBuyFlag =
                CommonConstants.BIZ_TYPE_B2B.equals(bizType)
                        ? CommonConstants.YES_FLAG
                        : CommonConstants.NO_FLAG;
        String buyNowFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("BUY_NOW_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG);
        String buyNowURL =
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.builder()
                                .siteCode(SITE_CODE)
                                .keyCode("default_buy_now_url")
                                .build());

        String discontinuedSupportFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("DISCONTINUED_PAGE_SUPPORT_BTN")
                                        .build()),
                        CommonConstants.NO_FLAG);

        List<ProductSummarySpecResponseVO> specs = new ArrayList<ProductSummarySpecResponseVO>();

        if (ObjectUtils.isNotEmpty(sysPdsProductSpecBundleSimpleList)) {
            String eGuideCategories =
                    cachedDataUtil.getComSystemConfigurationCode(
                            CachedCodeRequestVO.builder().keyCode("default_buy_now_url").build());
            for (SystemPdsProductSpecBundleSimpleListResponseVO bundleModel :
                    sysPdsProductSpecBundleSimpleList) {
                ProductSummarySpecResponseVO detail = new ProductSummarySpecResponseVO();
                if (!BUNDLE_PRODUCT.equals(productSmrRespVO.getModelTypeM())
                        && !OBS_BUNDLE_PRODUCT.equals(productSmrRespVO.getModelTypeM())) {
                    detail.setSku(bundleModel.getLgcomSkuId());
                    detail.setPimSku(bundleModel.getPimSkuId());
                    detail.setBundleComponentYn(CommonConstants.NO_FLAG);
                    detail.setBizType(bizType);
                    detail.setModelId(bundleModel.getPdpId());
                } else {
                    detail.setSku(bundleModel.getBundleSkuId());
                    detail.setPimSku(bundleModel.getBundlePimSkuId());
                    detail.setBundleComponentYn(CommonConstants.YES_FLAG);
                    detail.setBizType(bizType);
                    detail.setModelId(bundleModel.getBundlePdpId());
                }

                detail.setModelName(bundleModel.getBundleModelName());
                detail.setSalesModelCode(bundleModel.getSalesModelCode());
                detail.setUserFriendlyName(bundleModel.getUserFrndyProductNm());
                detail.setLargeImageAddr(
                        StringUtils.defaultIfBlank(bundleModel.getBigImgUrl(), defaultDummyImg));
                detail.setMediumImageAddr(
                        StringUtils.defaultIfBlank(bundleModel.getMdmImgUrl(), defaultDummyImg));
                detail.setSmallImageAddr(
                        StringUtils.defaultIfBlank(bundleModel.getSmlImgUrl(), defaultDummyImg));
                detail.setWtbUseFlag(bundleModel.getWtbUseFlag());
                detail.setWtbExternalLinkUseFlag(bundleModel.getWtbExtlLinkUseFlag());
                detail.setWtbExternalLinkName(bundleModel.getWtbExtlLinkNm());
                detail.setWtbExternalLinkUrl(bundleModel.getWtbExtlLinkUrl());
                detail.setWtbExternalLinkSelfFlag(bundleModel.getWtbExtlLinkSelfScreenFlag());
                detail.setPdrUseFlag(bundleModel.getPdrUseFlag());
                detail.setInquiryToBuyFlag(inquiryToBuyFlag);
                detail.setBuyNowFlag(buyNowFlag);
                detail.setBuyNowURL(buyNowURL);
                detail.setObsSellFlag(bundleModel.getObsSellFlag());

                boolean psDiscontinuedCondition =
                        CommonConstants.YES_FLAG.equals(discontinuedSupportFlag)
                                && CommonConstants.PRODUCT_STATE_DISCONTINUED.equals(
                                        bundleModel.getProductStateCode());

                String productSupportFlag =
                        psDiscontinuedCondition
                                ? CommonConstants.YES_FLAG
                                : CommonConstants.NO_FLAG;

                detail.setProductSupportFlag(productSupportFlag);
                if (CommonConstants.YES_FLAG.equals(productSupportFlag)) {
                    detail.setProductSupportUrl(
                            commonService.getSupportLinkUrl(
                                    ProductSupportRequestVO.builder()
                                            .localeCode(SITE_CODE)
                                            .modelId(pdpId)
                                            .salesModelCode(bundleModel.getSalesModelCode())
                                            .salesSuffixCode(bundleModel.getSalesSuffixCode())
                                            .build()));
                }

                SystemPdsProductSpecDmsResponseVO sysPdsProductSpecDmsResp =
                        systemPdsSpecClient.getProductSpecDms(
                                SystemPdsProductSpecDmsRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(pdpId)
                                        .build());

                Map<String, Object> dimensionMap = null;
                if (ObjectUtils.isNotEmpty(sysPdsProductSpecDmsResp)) {
                    dimensionMap = new HashMap<>();
                    dimensionMap.put("altText", sysPdsProductSpecDmsResp.getAltTextCnts());
                    dimensionMap.put("contents", sysPdsProductSpecDmsResp.getCommentCnts());
                    dimensionMap.put("imagePathName", sysPdsProductSpecDmsResp.getImgPathUrl());
                    dimensionMap.put(
                            "mobileImagePathName", sysPdsProductSpecDmsResp.getMoblImgPath());
                    dimensionMap.put("sku", detail.getSku());
                }
                detail.setDimensionMap(dimensionMap);

                List<SystemPdsProductSpecSmrResponseVO> sysPdsProductSpecSmrRespList =
                        systemPdsSpecClient.getProductSpecSmr(
                                SystemPdsProductSpecSmrRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(pdpId)
                                        .build());

                List<Map<String, Object>> specSmrList = new ArrayList<>();
                if (ObjectUtils.isNotEmpty(sysPdsProductSpecSmrRespList)) {
                    for (SystemPdsProductSpecSmrResponseVO sysPdsProductSpecSmr :
                            sysPdsProductSpecSmrRespList) {
                        Map<String, Object> specSmr = new HashMap<String, Object>();
                        specSmr.put("name", sysPdsProductSpecSmr.getSpecSmrNmM());
                        specSmr.put("value", sysPdsProductSpecSmr.getSpecSmrVal());
                        specSmrList.add(specSmr);
                    }
                }
                detail.setKeyFeatures(specSmrList);
                detail.setEnegyLabelLocalizationFlag(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("energy-localization-flag")
                                        .build()));

                SystemPdsPdpSiblingResponseVO sysPdsPdpSiblingResp =
                        systemPdsPdpInfoClient.getDefaultSiblingProductInfo(
                                SystemPdsPdpSiblingRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(pdpId)
                                        .bizTypeCode(bizType)
                                        .productStatusCode(modelStatusCode)
                                        .standardFlag(standardFlag)
                                        .build());

                String siblingPdpId = "";
                String siblingSalesModelCode = "";
                String siblingSalesSuffixCode = "";
                if (ObjectUtils.isNotEmpty(sysPdsPdpSiblingResp)) {
                    // PIM 제품 정보 조회 후 salesModelCode, salesSuffixCode 가져오기
                    SystemPdsProductResponseVO siblingProductInfo =
                            getProductInfo(sysPdsPdpSiblingResp.getSkuId());

                    siblingSalesModelCode = siblingProductInfo.getSalesModelCode();
                    siblingSalesSuffixCode = siblingProductInfo.getSalesModelSuffixCode();
                    siblingPdpId = sysPdsPdpSiblingResp.getPdpId();
                }

                String docDownloadType =
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("download_spec_sheet_type")
                                        .build());

                String cedocUseFlag =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("mkt_gsri_cedoc_flag")
                                                .siteCode(SITE_CODE)
                                                .build()),
                                CommonConstants.NO_FLAG);

                String eLabelFlag =
                        CommonConstants.YES_FLAG.equals(
                                        cachedDataUtil.getComSystemConfigurationCode(
                                                CachedCodeRequestVO.builder()
                                                        .keyCode("Energy_Label_Flag")
                                                        .build()))
                                ? CommonConstants.YES_FLAG
                                : CommonConstants.NO_FLAG;

                SystemPdsPdfGsriSpecInfoResponseVO sysPdsPdfDownloadResp =
                        systemPdsSupportClient.getPdfDownloadInfo(
                                SystemPdsPdfGsriSpecInfoRequestVO.builder()
                                        .pdpId(detail.getModelId())
                                        .siteCode(SITE_CODE)
                                        .docTypeNm(E_GUIDE_GSRI)
                                        .modelFileTypeCode(FILE_TYPE_DU06)
                                        .docDownloadType(docDownloadType)
                                        .cedocUseFlag(cedocUseFlag)
                                        .energyLabelFlag(eLabelFlag)
                                        .energyLabelCategory(
                                                productSmrRespVO.getEnergyLabelCategory())
                                        .washTowerFlag(
                                                StringUtils.isBlank(
                                                                productSmrRespVO.getWashTowerFlag())
                                                        ? ""
                                                        : productSmrRespVO.getWashTowerFlag())
                                        .build());

                PdfDownloadInfoResponseVO pdfDownloadInfo = null;

                if (ObjectUtils.isNotEmpty(sysPdsPdfDownloadResp)
                        && ObjectUtils.isNotEmpty(sysPdsPdfDownloadResp.getPdfDownloadInfo())) {
                    pdfDownloadInfo = sysPdsPdfDownloadResp.getPdfDownloadInfo();
                }

                if (ObjectUtils.isNotEmpty(pdfDownloadInfo)
                        && StringUtils.isNotBlank(pdfDownloadInfo.getDocId())) {
                    detail.setFileType(GSRI_TYPE);
                    detail.setDocFileName(pdfDownloadInfo.getFileNm());
                    detail.setDocId(pdfDownloadInfo.getDocId());
                    detail.setOriginalFileName(pdfDownloadInfo.getOriginalFileNm());
                } else {
                    SystemPdsPdpCategoryResponseVO sysPdsPdpCategoryResp =
                            systemPdsPdpInfoClient.getPdpCategoryInfo(
                                    SystemPdsPdpCategoryRequestVO.builder()
                                            .pdpId(
                                                    StringUtils.isBlank(siblingPdpId)
                                                            ? pdpId
                                                            : siblingPdpId)
                                            .standardFlag(standardFlag)
                                            .shopCode(SHOP_CODE)
                                            .build());

                    if (ObjectUtils.isNotEmpty(sysPdsPdpCategoryResp)
                            && ObjectUtils.isNotEmpty(pdfDownloadInfo)
                            && StringUtils.isNotBlank(pdfDownloadInfo.getLinkAddr())) {
                        String categoryId =
                                CommonConstants.YES_FLAG.equals(standardFlag)
                                        ? sysPdsPdpCategoryResp.getLv3CategoryCode()
                                        : sysPdsPdpCategoryResp.getLv2CategoryCode();

                        if (eGuideCategories.matches(categoryId)
                                && StringUtils.isNotBlank(pdfDownloadInfo.getLinkAddr())) {
                            detail.setFileType(LINK_TYPE);
                            detail.setLinkAddr(
                                    StringUtils.defaultIfBlank(pdfDownloadInfo.getLinkAddr(), ""));
                            detail.setLinkTargetName(
                                    StringUtils.defaultIfBlank(
                                            pdfDownloadInfo.getLinkTargetName(), ""));
                        }
                    }
                }
                detail.setSpecDownloadAddr(
                        StringUtils.defaultIfBlank(pdfDownloadInfo.getSpecDownloadAddr(), ""));

                /** Compliance Information(ECO) 조회 */
                String ecoUseFlag =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.builder()
                                                .keyCode("pdp_eco_use_flag")
                                                .siteCode(SITE_CODE)
                                                .build()),
                                CommonConstants.NO_FLAG);

                List<Map<String, Object>> gsriList = new ArrayList<Map<String, Object>>();
                if (CommonConstants.YES_FLAG.equals(ecoUseFlag)) {
                    detail.setEcoBtnUrl("/global/support/cedoc/cedoc");

                    int gsriCnt =
                            systemAdminSupportClient.getGsriCnt(
                                    SystemAdminProductGsriCntRequestVO.builder()
                                            .localeCode(SITE_CODE)
                                            .build());
                    if (gsriCnt > 0) {
                        SystemAdminDowntimeInfoVO sysAdminDowntimeInfo =
                                systemAdminProductClient.getDowntimeInfo(
                                        SystemAdminDowntimeInfoVO.builder()
                                                .downSystem(GSRI_TYPE)
                                                .build());

                        if (ObjectUtils.isNotEmpty(sysAdminDowntimeInfo)) {
                            detail.setDownTime(sysAdminDowntimeInfo.getDownTime());
                            detail.setOpentime(sysAdminDowntimeInfo.getOpenTime());
                        }

                        if (ObjectUtils.isNotEmpty(sysPdsPdfDownloadResp.getGsriList())) {
                            gsriList = sysPdsPdfDownloadResp.getGsriList();
                        }

                        detail.setGsri(gsriList);
                    }
                }

                detail.setTechSpecs(getTechSpecsOfModel(detail, SITE_CODE));

                List<Map<String, Object>> keySpec = new ArrayList<Map<String, Object>>();
                detail.setKeySpec(keySpec);
                if (CommonConstants.YES_FLAG.equals(detail.getPdrUseFlag())) {
                    detail.setKeySpec(getKeySpecsOfModel(detail, SITE_CODE));
                }

                /* productEnquiryUrl  */
                if ("IN".equals(SITE_CODE)
                        && "Y".equals(productSmrRespVO.getProductEnquiryFlag())) {
                    StringBuffer productEnquiryUrl = new StringBuffer();
                    productEnquiryUrl.append(
                            StringUtils.defaultIfBlank(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("pdp_product_enquiry_url")
                                                    .build()),
                                    ""));
                    productEnquiryUrl.append("?c=");
                    productEnquiryUrl.append(productSmrRespVO.getCategoryName());
                    productEnquiryUrl.append("&m=");
                    productEnquiryUrl.append(productSmrRespVO.getModelDisplayName());
                    detail.setProductEnquiryUrl(productEnquiryUrl.toString());
                }

                String rsUseFlag = CommonConstants.NO_FLAG;
                if (SITE_CODE.equals("RS")
                        && productSmrRespVO
                                .getPisDocOldFlag()
                                .equalsIgnoreCase(CommonConstants.NO_FLAG)) {
                    rsUseFlag = CommonConstants.YES_FLAG;
                }
                detail.setRsUseFlag(rsUseFlag);

                detail.setFindTheDealerFlag(systemPdspdpResponseVO.getFindDealerUseFlag());
                detail.setProductEnquiryFlag(systemPdspdpResponseVO.getInquiryFlag());
                detail.setRetailerPricingFlag(systemPdspdpResponseVO.getRetailerPricingFlag());

                specs.add(detail);
            }
        }

        return specs;
    }

    @SuppressWarnings("unchecked")
    public ProductSummaryResourceResponseVO getProductSummaryResource(
            ProductSummaryRequestVO requestVO) {

        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String SHOP_CODE = CommonConstants.SHOP_CODE_D2C;
        String SKU = requestVO.getSku();

        ProductSummaryResourceResponseVO result = new ProductSummaryResourceResponseVO();
        List<Map<String, Object>> businessResource = new ArrayList<Map<String, Object>>();
        List<String> resourceTypeList = new ArrayList<>();

        try {
            if (StringUtils.isNotBlank(requestVO.getMultishopCode())
                    && !CommonConstants.SHOP_CODE_D2C.equals(requestVO.getMultishopCode())) {
                SHOP_CODE = requestVO.getMultishopCode();
            }

            if (StringUtils.isEmpty(SITE_CODE)) {
                throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
            }

            List<CachedPdpIdVO> pdpList =
                    cachedDataUtil.getPdpIdList(
                            CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));

            if (ObjectUtils.isEmpty(pdpList)) {
                throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
            }

            String pdpId = pdpList.get(0).getPdpId();
            String pimSku = pdpList.get(0).getSkuId();

            /** 카테고리 표준화 국가 여부 조회 */
            String standardFlag = commonService.getCategoryStandardFlag();

            result.setPartnerPortalLink(
                    cachedDataUtil.getComSystemConfigurationCode(
                            CachedCodeRequestVO.builder()
                                    .keyCode("LG_B2B_PARTNER_PORTAL_LINK")
                                    .build()));

            result.setPartnerPortalLinkSolar(
                    cachedDataUtil.getComSystemConfigurationCode(
                            CachedCodeRequestVO.builder()
                                    .keyCode("LG_B2B_PARTNER_PORTAL_LINK_SOLAR")
                                    .build()));

            SystemPdsPdpCategoryResponseVO sysPdsCategoryResp =
                    systemPdsPdpInfoClient.getPdpCategoryInfo(
                            SystemPdsPdpCategoryRequestVO.builder()
                                    .pdpId(pdpId)
                                    .standardFlag(standardFlag)
                                    .shopCode(SHOP_CODE)
                                    .build());

            if (ObjectUtils.isEmpty(sysPdsCategoryResp)) {
                return result;
            }

            /** 모델의 카테고리 정보 세팅 */
            String superCategoryId = "";
            String categoryId = "";
            String subCategoryId = "";

            if (CommonConstants.YES_FLAG.equals(standardFlag)) {
                superCategoryId = sysPdsCategoryResp.getLv2CategoryCode();
                categoryId = sysPdsCategoryResp.getLv3CategoryCode();
                subCategoryId = sysPdsCategoryResp.getLv4CategoryCode();
            } else {
                superCategoryId = sysPdsCategoryResp.getLv1CategoryCode();
                categoryId = sysPdsCategoryResp.getLv2CategoryCode();
                subCategoryId = sysPdsCategoryResp.getLv3CategoryCode();
            }

            List<String> categoryIds = new ArrayList<String>();
            categoryIds.add(superCategoryId);
            categoryIds.add(categoryId);
            categoryIds.add(subCategoryId);

            result.setIdCategoryFlag(
                    superCategoryId.equals(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("information_display_category")
                                                    .build()))
                            ? CommonConstants.YES_FLAG
                            : CommonConstants.NO_FLAG);

            result.setItProductCategoryFlag(
                    "CT00000233".equals(superCategoryId)
                            ? CommonConstants.YES_FLAG
                            : CommonConstants.NO_FLAG);

            /** BusinessResource 조회 */
            List<SystemPdsProductResourceResponseVO> sysPdsProductResourcesList =
                    systemPdsPdpInfoClient.getProductResources(
                            SystemPdsProductResourceRequestVO.builder()
                                    .pdpId(pdpId)
                                    .categoryIds(categoryIds)
                                    .siteCode(SITE_CODE)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductResourcesList)) {
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> businessResourceMap = new HashMap<>();
                for (SystemPdsProductResourceResponseVO sysPdsProductResourceResp :
                        sysPdsProductResourcesList) {
                    businessResourceMap =
                            objectMapper.convertValue(sysPdsProductResourceResp, Map.class);
                    businessResource.add(businessResourceMap);
                }

                resourceTypeList =
                        sysPdsProductResourcesList.stream()
                                .map(SystemPdsProductResourceResponseVO::getResourceTypeCode)
                                .collect(Collectors.toList());
            }

            result.setResourceTypeList(resourceTypeList);
            result.setBusinessResource(businessResource);

            /** GLOBAL 국가 IT Product 카테고리만 Resource에 Manual, Software 추가 */
            if ("GLOBAL".equals(SITE_CODE)
                    && CommonConstants.YES_FLAG.equals(result.getItProductCategoryFlag())) {

                SystemPdsProductResponseVO systemPdsProductRespVO =
                        systemPdsProductClient.getProductInfo(
                                SystemPdsProductRequestVO.builder()
                                        .skuId(pimSku)
                                        .localeCode(
                                                cachedDataUtil
                                                        .getComLocaleCode(
                                                                CachedCodeRequestVO.builder()
                                                                        .siteCode(SITE_CODE)
                                                                        .build())
                                                        .getLocaleCode())
                                        .build());

                if (ObjectUtils.isEmpty(systemPdsProductRespVO)) {
                    throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
                }

                String dateFormatInfo =
                        StringUtils.defaultIfEmpty(
                                cachedDataUtil.getComSystemPreferenceCode(
                                        CachedCodeRequestVO.ofComSysPreference("DB_DATE_FORMAT")),
                                "%m/%d/%Y");

                String countryCode =
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("category_standard_flag"));

                String buyerCode =
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig(
                                        "mkt_global_itProduct_resource_buyerCode"));

                String manualCnt =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.ofComSysConfig(
                                                "mkt_global_itProduct_resource_manual_cnt")),
                                "1");

                String softwareCnt =
                        StringUtils.defaultIfBlank(
                                cachedDataUtil.getComSystemConfigurationCode(
                                        CachedCodeRequestVO.ofComSysConfig(
                                                "mkt_global_itProduct_resource_software_cnt")),
                                "2");

                String gscsDownUrl =
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("component-gscs-downUrl"));

                SystemPdsProductManualSoftwareInfoResponseVO sysPdsProductResp =
                        systemPdsSupportClient.getProductManualSoftwareInfo(
                                SystemPdsProductManualSoftwareInfoRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .salesModelCode(systemPdsProductRespVO.getSalesModelCode())
                                        .salesSuffixCode(
                                                systemPdsProductRespVO.getSalesModelSuffixCode())
                                        .pdpId(pdpId)
                                        .dateFormatInfo(dateFormatInfo)
                                        .countryCode(countryCode)
                                        .buyerCode(buyerCode)
                                        .manualCnt(manualCnt)
                                        .softwareCnt(softwareCnt)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsProductResp)) {
                    result.setManualList(sysPdsProductResp.getManualList());
                    result.setSoftwareList(sysPdsProductResp.getSoftwareList());
                }
                result.setGscsDownUrl(gscsDownUrl);
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return result;
    }

    public ProductSummarySeoSchemaResponseVO getProductSummarySeoSchema(
            ProductSummaryRequestVO requestVO) {

        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String SHOP_CODE = CommonConstants.SHOP_CODE_D2C;
        String PDP_ID = requestVO.getPdpId();

        ProductSummarySeoSchemaResponseVO seoSchemaSpecInfo =
                new ProductSummarySeoSchemaResponseVO();
        String enableSEOSchemaSize =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "enableSEOSchemaSize", SHOP_CODE, SITE_CODE)),
                        CommonConstants.NO_FLAG);

        String enableSEOSchemaEnergy =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        "enableSEOSchemaEnergy", SHOP_CODE, SITE_CODE)),
                        CommonConstants.NO_FLAG);

        seoSchemaSpecInfo.setEnableSEOSchemaSize(enableSEOSchemaSize);
        seoSchemaSpecInfo.setEnableSEOSchemaEnergy(enableSEOSchemaEnergy);

        if (CommonConstants.YES_FLAG.equals(enableSEOSchemaSize)) {
            // TODO : 추후에 PDS 테이블로 바꿀 것
            SystemAdminSeoKeySpecResponseVO systemAdminSeoKeySpecRespVO =
                    systemAdminProductClient.getSeoKeySpec(
                            SystemAdminSeoKeySpecRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .modelId(PDP_ID)
                                    .build());

            List<Map<String, Object>> seoKeySpecList = new ArrayList<>();
            if (systemAdminSeoKeySpecRespVO != null) {
                ProductSummarySpecResponseVO detail = new ProductSummarySpecResponseVO();
                String seoKeySpecName =
                        StringUtils.defaultIfBlank(systemAdminSeoKeySpecRespVO.getSpecCode(), "");
                detail.setSeoSpecId(seoKeySpecName);
                seoKeySpecList = getKeySpecsOfModel(detail, SITE_CODE);
            }

            Map<String, String> sizeKeySpecInfo = new HashMap<>();
            sizeKeySpecInfo.put("unitCode", "");
            sizeKeySpecInfo.put("value", "");
            seoSchemaSpecInfo.setSize(sizeKeySpecInfo);

            if (seoKeySpecList.size() > 0) {
                Map<String, Object> tempSizeKeySpecInfo =
                        (Map<String, Object>) seoKeySpecList.get(0);
                sizeKeySpecInfo.put("unitCode", systemAdminSeoKeySpecRespVO.getUnitCode());
                sizeKeySpecInfo.put(
                        "value",
                        ObjectUtils.isNotEmpty(tempSizeKeySpecInfo.get("specValue"))
                                ? tempSizeKeySpecInfo.get("specValue").toString()
                                : "");
                seoSchemaSpecInfo.setSize(sizeKeySpecInfo);
            }
        }

        if (CommonConstants.YES_FLAG.equals(enableSEOSchemaEnergy)) {
            // TODO : 추후에 PDS 테이블로 바꿀 것
            List<String> systemAdminSeoEnergyLabelMinMax =
                    systemAdminProductClient.getSeoEnergyMinMax(
                            SystemAdminSeoKeySpecRequestVO.builder()
                                    .localeCode(SITE_CODE)
                                    .modelId(PDP_ID)
                                    .build());

            String energyEfficiencyScaleMin = "";
            String energyEfficiencyScaleMax = "";
            for (String item : systemAdminSeoEnergyLabelMinMax) {
                if (item.contains("MINMAX")) {
                    energyEfficiencyScaleMin = item.replace("MINMAX", "");
                    energyEfficiencyScaleMax = energyEfficiencyScaleMin;
                } else if (item.contains("MIN")) {
                    energyEfficiencyScaleMin = item.replace("MIN", "");
                } else if (item.contains("MAX")) {
                    energyEfficiencyScaleMax = item.replace("MAX", "");
                }
            }

            Map<String, Object> energyMap = new HashMap<>();
            energyMap.put(
                    "hasEnergyEfficiencyCategory",
                    replaceEnergyLabelForSEO(
                            StringUtils.defaultIfBlank(requestVO.getEnergyLabelName(), "")));
            energyMap.put(
                    "energyEfficiencyScaleMin", replaceEnergyLabelForSEO(energyEfficiencyScaleMin));
            energyMap.put(
                    "energyEfficiencyScaleMax", replaceEnergyLabelForSEO(energyEfficiencyScaleMax));
            seoSchemaSpecInfo.setHasEnergyConsumptionDetails(energyMap);
        }

        return seoSchemaSpecInfo;
    }

    public String replaceEnergyLabelForSEO(String energyLabel) {
        String energyValue = "";
        if (energyLabel.contains("+")) {
            int cnt = energyLabel.length() - 1;
            energyValue =
                    ENERGY_EFFICIENCY_PREFIX
                            + String.valueOf(energyLabel.charAt(0))
                            + Integer.toString(cnt)
                            + "Plus";

        } else if (!energyLabel.isEmpty()) {
            energyValue = ENERGY_EFFICIENCY_PREFIX + energyLabel;
        }
        return energyValue;
    }

    public ProductSummaryLatsResponseVO getProductSummaryLats(ProductSummaryRequestVO requestVO) {

        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String MODEL_NAME = requestVO.getModelName();

        ProductSummaryLatsResponseVO LatsInfo = new ProductSummaryLatsResponseVO();

        // TODO : 추후에 PDS 테이블로 바꿀 것
        SystemAdminLatsResponseVO systemAdminLatsRespVO =
                systemAdminProductClient.getLatsInfo(
                        SystemAdminLatsRequestVO.builder()
                                .localeCode(SITE_CODE)
                                .modelName(MODEL_NAME)
                                .build());

        if (ObjectUtils.isNotEmpty(systemAdminLatsRespVO)) {
            LatsInfo = systemAdminLatsRespVO.toVO();
        }

        return LatsInfo;
    }

    private void setWtbFlag(ProductSummaryResponseVO productInfo) {

        String wtbUseFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("WTB_USE_FLAG")),
                        CommonConstants.YES_FLAG);

        if (CommonConstants.NO_FLAG.equals(wtbUseFlag)) {
            productInfo.setWtbUseFlag(CommonConstants.NO_FLAG);
        }

        String externalLinkUseFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference(
                                        "WTB_EXTERNAL_LINK_USE_FLAG")),
                        CommonConstants.NO_FLAG);

        if (CommonConstants.NO_FLAG.equals(externalLinkUseFlag)) {
            productInfo.setWtbExternalLinkName("");
            productInfo.setWtbExternalLinkUrl("");
            productInfo.setWtbExternalLinkUseFlag(CommonConstants.NO_FLAG);
            productInfo.setWtbExternalLinkSelfFlag(CommonConstants.NO_FLAG);
        } else {
            boolean wtbExternalConditon =
                    CommonConstants.YES_FLAG.equals(productInfo.getWtbExternalLinkUseFlag())
                            && StringUtils.isNotBlank(productInfo.getWtbExternalLinkName());

            if (wtbExternalConditon) {
                String defaultExternalLinkName =
                        productInfo.getWtbExternalLinkName().replaceAll("_", " ");
                StringBuffer externalBuffer = new StringBuffer();
                externalBuffer.append(EXTERNAL_WTB_PREFIX).append(defaultExternalLinkName);
                String msgExternalLinkName = externalBuffer.toString();

                String localeCode =
                        cachedDataUtil
                                .getComLocaleCode(CachedCodeRequestVO.ofComLocale())
                                .getLocaleCode();
                String wtbExtlLinkNm =
                        cachedDataUtil.getComMessageCode(
                                CachedCodeRequestVO.ofDspTypeComMessage(
                                        msgExternalLinkName,
                                        CommonConstants.SHOP_CODE_D2C,
                                        localeCode));
                productInfo.setWtbExternalLinkName(wtbExtlLinkNm);
            }
        }
    }

    private List<PdpSiblingItemVO> getSiblings(
            ProductSummaryResponseVO productInfo,
            String standardFlag,
            boolean mtsFlag,
            String shopCode,
            String customerGroup) {
        String SITE_CODE = RequestHeaderUtil.getSiteCode();

        List<PdpSiblingItemVO> siblings = new ArrayList<PdpSiblingItemVO>();

        try {
            List<PdpSiblingModelResponseVO> siblingModels =
                    new ArrayList<PdpSiblingModelResponseVO>();

            List<SystemPdsPdpSiblingResponseVO> systemPdsPdpSiblingDataList =
                    new ArrayList<SystemPdsPdpSiblingResponseVO>();

            if (mtsFlag) {
                systemPdsPdpSiblingDataList =
                        systemPdsPdpInfoClient.getMtsPdpSiblingDataList(
                                SystemPdsPdpSiblingRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(productInfo.getModelId())
                                        .bizTypeCode(productInfo.getBizType())
                                        .standardFlag(standardFlag)
                                        .shopCode(shopCode)
                                        .customerGroup(customerGroup)
                                        .build());
            } else {
                systemPdsPdpSiblingDataList =
                        systemPdsPdpInfoClient.getPdpSiblingDataList(
                                SystemPdsPdpSiblingRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(productInfo.getModelId())
                                        .bizTypeCode(productInfo.getBizType())
                                        .standardFlag(standardFlag)
                                        .build());
            }

            int pdpLimitedDisplayOrder = 0;
            int pdpLimitedPriorityOrder = 0;
            String pdpDefaultDisplayOrder = "";
            String pdpDefaultPriorityOrder = "";
            boolean pdpDefaultDisplayOrderFlag = false;

            for (int i = 0; i < systemPdsPdpSiblingDataList.size(); i++) {
                SystemPdsPdpSiblingResponseVO pItem = systemPdsPdpSiblingDataList.get(i);
                pItem.setMsrp(cachedDataUtil.getMsrp(SITE_CODE, pItem.getMsrp()));

                SystemPdsProductResponseVO systemPdsProductRespVO =
                        systemPdsProductClient.getProductInfo(
                                SystemPdsProductRequestVO.builder()
                                        .skuId(pItem.getSkuId())
                                        .localeCode(
                                                cachedDataUtil
                                                        .getComLocaleCode(
                                                                CachedCodeRequestVO.builder()
                                                                        .siteCode(SITE_CODE)
                                                                        .build())
                                                        .getLocaleCode())
                                        .build());

                String pimUserFrndyProductNm = systemPdsProductRespVO.getNewMktProductNm();

                String userFrndyProductNm =
                        StringUtils.isNotBlank(pimUserFrndyProductNm)
                                ? pimUserFrndyProductNm
                                : pItem.getUserFrndyProductNm();

                if (StringUtils.isNotBlank(userFrndyProductNm)) {
                    pItem.setUserFrndyProductNm(userFrndyProductNm.replaceAll("\\\"", "&#39;"));
                }

                PdpSiblingModelResponseVO siblingData = new PdpSiblingModelResponseVO();
                siblingData = pItem.toVO();
                siblingData.setSalesModelCode(systemPdsProductRespVO.getSalesModelCode());
                siblingModels.add(siblingData);

                if (i == 0) {
                    productInfo.setSiblingGroupCode(siblingData.getSiblingGroupCode());
                    productInfo.setSeriesName(pItem.getSiblingGrpNm());
                }

                if ("1".equals(pItem.getDisplayOrderNo())) { // displayOrderNo이 1로된 시블링이 보여진다.
                    if ("".equals(pdpDefaultDisplayOrder)
                            || pdpDefaultDisplayOrder.equals(pItem.getSiblingTypeCode())) {
                        pdpLimitedDisplayOrder++;
                        pdpDefaultDisplayOrder = pItem.getSiblingTypeCode();
                        pdpDefaultDisplayOrderFlag = true;
                    } else if (!"".equals(pdpDefaultDisplayOrder)
                            && !pdpDefaultDisplayOrder.equals(
                                    pItem.getSiblingTypeCode())) { // DisplayOrderNo이 1로된 시블링이
                        // 여러개인 경우
                        pdpDefaultDisplayOrderFlag = false;
                    }
                }
                if ("1".equals(pItem.getPriorityOrderNo())) { // query에서 설정된 우선순위 중
                    // PriorityOrderNo 1인 시블링이 보여진다.
                    pdpDefaultPriorityOrder = pItem.getSiblingTypeCode();
                    pdpLimitedPriorityOrder++;
                }

                if (i == systemPdsPdpSiblingDataList.size() - 1) { // last sibling item add
                    PdpSiblingItemVO siblingItem = new PdpSiblingItemVO();
                    siblingItem.setTarget(pItem.getSiblingSbjTypeCode());
                    siblingItem.setPdpTitle(pItem.getPdpTitle());
                    siblingItem.setSiblingType(pItem.getSiblingTypeCode());
                    siblingItem.setSku(pItem.getLgcomSkuId());
                    siblingItem.setModelId(pItem.getPdpId());
                    siblingItem.setSiblingCode(pItem.getSiblingCode());
                    siblingItem.setSiblingModels(siblingModels);
                    siblingItem.setPdpDefaultSiblingType(
                            pdpDefaultDisplayOrderFlag
                                    ? pdpDefaultDisplayOrder
                                    : pdpDefaultPriorityOrder);
                    siblingItem.setPdpLimitedNumber(
                            pdpDefaultDisplayOrderFlag
                                    ? pdpLimitedDisplayOrder
                                    : pdpLimitedPriorityOrder);
                    siblings.add(siblingItem);
                }
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return siblings;
    }

    public ProductEanCodeResponseVO getProductEanCode(ProductEanCodeRequestVO requestVO) {
        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String SKU = requestVO.getSku();
        if (StringUtils.isEmpty(SITE_CODE)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }

        ProductEanCodeResponseVO modelEanCode = new ProductEanCodeResponseVO();

        try {
            List<CachedPdpIdVO> pdpList =
                    cachedDataUtil.getPdpIdList(
                            CachedPdpIdRequestVO.ofPdpId(Arrays.asList(SKU), SITE_CODE));
            if (ObjectUtils.isEmpty(pdpList)) {
                throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
            }
            String PDP_ID = pdpList.get(0).getPdpId();

            String CATEGORY_STANDARD_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("category_standard_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String localeCode =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil
                                    .getComLocaleCode(
                                            CachedCodeRequestVO.builder()
                                                    .siteCode(SITE_CODE)
                                                    .build())
                                    .getLocaleCode(),
                            "");

            /** 제품 기본 정보 조회 */
            SystemPdsPdpResponseVO systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .pdpId(PDP_ID)
                                    .lgcomSkuId(SKU)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .standardFlag(CATEGORY_STANDARD_FLAG)
                                    .build());

            if (ObjectUtils.isNotEmpty(systemPdspdpResponseVO)) {
                String pimSkuId = systemPdspdpResponseVO.getSkuId();

                /** PRODUCT 정보 조회 (PIM) */
                SystemPdsProductResponseVO systemPdsProductRespVO =
                        systemPdsProductClient.getProductInfo(
                                SystemPdsProductRequestVO.builder()
                                        .skuId(pimSkuId)
                                        .localeCode(
                                                cachedDataUtil
                                                        .getComLocaleCode(
                                                                CachedCodeRequestVO.builder()
                                                                        .siteCode(SITE_CODE)
                                                                        .build())
                                                        .getLocaleCode())
                                        .build());

                if (ObjectUtils.isNotEmpty(systemPdsProductRespVO)) {
                    String BUSINESS_REVIEW_USE_FLAG =
                            StringUtils.defaultIfEmpty(
                                    cachedDataUtil.getComSystemPreferenceCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("BUSINESS_REVIEW_USE_FLAG")
                                                    .build()),
                                    CommonConstants.NO_FLAG);

                    String REVIEW_TYPE =
                            StringUtils.defaultIfEmpty(
                                    cachedDataUtil.getComSystemPreferenceCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("REVIEW_TYPE")
                                                    .build()),
                                    CommonConstants.NO_FLAG);

                    if (systemPdsProductRespVO.getBizTypeCode().equals("B2B")
                            && BUSINESS_REVIEW_USE_FLAG.equals("N")) {
                        REVIEW_TYPE = "N";
                    }

                    modelEanCode.setSkuId(SKU);
                    modelEanCode.setBizTypeCode(systemPdsProductRespVO.getBizTypeCode());
                    modelEanCode.setModelEanCode(systemPdsProductRespVO.getModelEanCode());
                    modelEanCode.setProductNm(systemPdsProductRespVO.getProductNm());
                    modelEanCode.setReviewType(REVIEW_TYPE);
                }
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return modelEanCode;
    }

    @SuppressWarnings("unchecked")
    public List<ProductBundleResponseVO> getProductBundleList(ProductBundleRequestVO requestVO) {
        List<ProductBundleResponseVO> bundleList = new ArrayList<>();
        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String SKU = requestVO.getSku();
        String SHOP_CODE = CommonConstants.SHOP_CODE_D2C;
        String CUSTOMER_GROUP = "";

        boolean mtsFlag = false;
        if (StringUtils.isNotBlank(requestVO.getMultishopCode())
                && !CommonConstants.SHOP_CODE_D2C.equals(requestVO.getMultishopCode())) {
            mtsFlag = true;
            SHOP_CODE = requestVO.getMultishopCode();
            CUSTOMER_GROUP = requestVO.getCustomerGroup();
        }

        if (StringUtils.isEmpty(SITE_CODE)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_FAILURE_MESSAGE);
        }

        String localeCode =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil
                                .getComLocaleCode(
                                        CachedCodeRequestVO.builder().siteCode(SITE_CODE).build())
                                .getLocaleCode(),
                        "");

        /** 카테고리 표준화 국가 여부 조회 */
        String standardFlag = commonService.getCategoryStandardFlag();

        /** 타임존 조회 */
        String TIMEZONE =
                cachedDataUtil
                        .getComLocaleCode(CachedCodeRequestVO.builder().siteCode(SITE_CODE).build())
                        .getTimezoneNm();

        LocalDateTime todayTime = LocalDateTime.now();
        String pattern = "yyyyMMdd HH:mm:ss";
        String today = "";
        try {
            todayTime = DateUtil.getCurrentTime(TIMEZONE, pattern);
            today = todayTime.toLocalDate().toString().replaceAll("-", "");
        } catch (Exception e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            today = todayTime.atOffset(ZoneOffset.of("+09:00")).format(formatter);
        }

        /** 제품 기본 정보 조회 */
        SystemPdsPdpResponseVO systemPdspdpResponseVO = new SystemPdsPdpResponseVO();

        if (mtsFlag) {
            systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getMtsProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .lgcomSkuId(SKU)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .timezone(TIMEZONE)
                                    .standardFlag(standardFlag)
                                    .shopCode(SHOP_CODE)
                                    .customerGroup(CUSTOMER_GROUP)
                                    .build());
        } else {
            systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .lgcomSkuId(SKU)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .timezone(TIMEZONE)
                                    .standardFlag(standardFlag)
                                    .build());
        }

        if (ObjectUtils.isEmpty(systemPdspdpResponseVO)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        ProductSummaryResponseVO productSmrRespVO = systemPdspdpResponseVO.toPdpSummaryVO();

        String originPdpId = productSmrRespVO.getModelId();
        String bizType = productSmrRespVO.getBizType();
        String modelTypeM = productSmrRespVO.getModelTypeM();

        if (!"G".equals(modelTypeM)
                && !"B".equals(modelTypeM)
                && !"O".equals(modelTypeM)) { // PJTOBS-4
            return bundleList;
        }

        /** Review Type */
        String b2bUseFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .keyCode("BUSINESS_REVIEW_USE_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG);

        String reviewType =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("REVIEW_TYPE").build()),
                        CommonConstants.NO_FLAG);
        reviewType =
                !(CommonConstants.BIZ_TYPE_B2B.equals(bizType)
                                && CommonConstants.NO_FLAG.equals(b2bUseFlag))
                        ? reviewType
                        : CommonConstants.NO_FLAG;

        String vipPriceFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("VIP_PRICE_USE_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG);

        String obsLoginFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("OBS_LOGIN_FLAG").build()),
                        CommonConstants.NO_FLAG);

        List<SystemPdsProductBundleResponseVO> sysPdsPdpBundleRespList = new ArrayList<>();

        if (mtsFlag) {
            sysPdsPdpBundleRespList =
                    systemPdsPdpInfoClient.getMtsProductBundleList(
                            SystemPdsProductBundleRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .pdpId(originPdpId)
                                    .standardFlag(standardFlag)
                                    .pdpTypeCode(modelTypeM)
                                    .shopCode(SHOP_CODE)
                                    .customerGroup(CUSTOMER_GROUP)
                                    .bizTypeCode(bizType)
                                    .today(today)
                                    .build());
        } else {
            sysPdsPdpBundleRespList =
                    systemPdsPdpInfoClient.getProductBundleList(
                            SystemPdsProductBundleRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .pdpId(originPdpId)
                                    .standardFlag(standardFlag)
                                    .pdpTypeCode(modelTypeM)
                                    .shopCode(SHOP_CODE)
                                    .bizTypeCode(bizType)
                                    .today(today)
                                    .build());
        }

        if (ObjectUtils.isEmpty(sysPdsPdpBundleRespList)) {
            return bundleList;
        }

        String defaultDummyImg =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.ofComSysConfig("dummy_image_url")),
                        "");

        for (SystemPdsProductBundleResponseVO sysPdsPdpBundleResp : sysPdsPdpBundleRespList) {
            ProductBundleResponseVO resp = sysPdsPdpBundleResp.toVO();

            String pdpId = resp.getModelId();

            /** PRODUCT 정보 조회 (PIM) */
            SystemPdsProductResponseVO systemPdsProductRespVO =
                    getProductInfo(sysPdsPdpBundleResp.getSkuId());

            if (ObjectUtils.isEmpty(systemPdsProductRespVO)) {
                return bundleList;
            }

            String salesModelCode = systemPdsProductRespVO.getSalesModelCode();
            String salesSuffixCode = systemPdsProductRespVO.getSalesModelSuffixCode();
            String pimUserFrndyProductNm = systemPdsProductRespVO.getNewMktProductNm();

            String userFrndyProductNm =
                    StringUtils.isNotBlank(pimUserFrndyProductNm)
                            ? pimUserFrndyProductNm
                            : sysPdsPdpBundleResp.getUserFrndyProductNm();

            if (StringUtils.isNotBlank(userFrndyProductNm)) {
                resp.setUserFriendlyName(userFrndyProductNm.replaceAll("\\\"", "&#39;"));
            }

            resp.setSalesModelCode(salesModelCode);
            resp.setSalesSuffixCode(salesSuffixCode);
            resp.setClassificationFlagLv1(systemPdsProductRespVO.getLv1CategoryCodeNm());
            resp.setClassificationFlagLv2(systemPdsProductRespVO.getLv2CategoryCodeNm());
            resp.setClassificationFlagLv3(systemPdsProductRespVO.getLv3CategoryCodeNm());
            resp.setClassificationFlagLv4(systemPdsProductRespVO.getLv4CategoryCodeNm());

            resp.setMsrp(cachedDataUtil.getMsrp(SITE_CODE, sysPdsPdpBundleResp.getMsrp()));

            if (StringUtils.isBlank(resp.getMediumImageAddr())) {
                resp.setMediumImageAddr(defaultDummyImg);
            }

            SystemPdsPdpUserReviewRatingResponseVO userReviewResp =
                    systemPdsPdpInfoClient.getUserReviewRatings(
                            SystemPdsPdpUserReviewRatingRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .pdpId(resp.getModelId())
                                    .build());

            int srating = 0;
            BigDecimal srating2 = BigDecimal.ZERO;
            int ratingPercent = 0;
            if (ObjectUtils.isNotEmpty(userReviewResp)) {
                srating = userReviewResp.getSRating().intValue();
                srating2 = userReviewResp.getSRating2();
                ratingPercent = userReviewResp.getRatingPercent().intValue();
                resp.setPCount(userReviewResp.getPCount());
            }

            resp.setSRating(srating);
            resp.setSRating2(srating2);
            resp.setRatingPercent(ratingPercent);

            resp.setVipPriceFlag(vipPriceFlag);
            resp.setObsLoginFlag(obsLoginFlag);

            String energyLabelCategory = sysPdsPdpBundleResp.getElabelClsCode();
            String secondELabelCategory = sysPdsPdpBundleResp.getSecondElabelClsCode();
            String secondELabel = sysPdsPdpBundleResp.getSecondElabelGrdCode();
            String washTowerFlag = sysPdsPdpBundleResp.getWtowerProductFlag();

            String rsUseFlag = CommonConstants.NO_FLAG;
            if ("RS".equals(SITE_CODE)
                    && (("EL_CAT_01".equals(energyLabelCategory))
                            || ("EL_CAT_02".equals(energyLabelCategory))
                            || ("EL_CAT_03".equals(energyLabelCategory))
                            || ("EL_CAT_05".equals(energyLabelCategory)))) {
                rsUseFlag = "Y";
            }

            /** 제품 GSRI(환경규제 관련 모델) 정보 조회 * */
            String docTypeCode = "";
            String energyLabelDocId = "";
            String fEnergyLabelDocId = "";
            String fEnergyLabelFileName = "";
            String fEnergyLabelOriginalName = "";
            String fEnergyLabelproductLeve1Code = "";
            String productFichelDocId = "";
            String productFicheFileName = "";
            String productFicheOriginalName = "";
            String productFicheproductLeve1Code = "";
            String energyLabelFileName = "";
            String energyLabelOriginalName = "";
            String energyLabelproductLeve1Code = "";

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                if ("TR".equals(SITE_CODE)) {
                    docTypeCode = "TL";
                } else {
                    docTypeCode = "EL";
                }
            } else if ("TR".equals(SITE_CODE)) {
                docTypeCode = "TE";
            } else {
                docTypeCode = "EN";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsProductGsriFileResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductGsriFileResp.getResultMap();

                energyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                energyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                energyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                energyLabelproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            resp.setEnergyLabelDocId(energyLabelDocId);
            resp.setEnergyLabelFileName(energyLabelFileName);
            resp.setEnergyLabelOriginalName(energyLabelOriginalName);
            resp.setEnergyLabelproductLeve1Code(energyLabelproductLeve1Code);

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                if ("TR".equals(SITE_CODE)) {
                    docTypeCode = "TF";
                } else {
                    docTypeCode = "PF";
                }
            } else if ("TR".equals(SITE_CODE) && !"EL_CAT_04".equals(energyLabelCategory)) {
                docTypeCode = "TI";
            } else if (CommonConstants.YES_FLAG.equals(rsUseFlag)) {
                docTypeCode = "SP";
            } else {
                docTypeCode = "PI";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsProductFicheResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductFicheResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductFicheResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductFicheResp.getResultMap();

                productFichelDocId = Objects.toString(resultMap.get("docId"), "");
                productFicheFileName = Objects.toString(resultMap.get("fileName"), "");
                productFicheOriginalName = Objects.toString(resultMap.get("originalName"), "");
                productFicheproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            resp.setProductFicheDocId(productFichelDocId);
            resp.setProductFicheFileName(productFicheFileName);
            resp.setProductFicheOriginalName(productFicheOriginalName);
            resp.setProductFicheproductLeve1Code(productFicheproductLeve1Code);

            if ("EL_CAT_06".equals(energyLabelCategory)
                    || "EL_CAT_07".equals(energyLabelCategory)
                    || "EL_CAT_08".equals(energyLabelCategory)
                    || "EL_CAT_09".equals(energyLabelCategory)
                    || "EL_CAT_10".equals(energyLabelCategory)
                    || "EL_CAT_11".equals(energyLabelCategory)) {
                docTypeCode = "FL";
            } else {
                docTypeCode = "FE";
            }

            SystemPdsProductGsriFileInfoResponseVO sysPdsFEnergyLabelResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp)
                    && ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsFEnergyLabelResp.getResultMap();

                fEnergyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                fEnergyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                fEnergyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                fEnergyLabelproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            resp.setFEnergyLabelDocId(fEnergyLabelDocId);
            resp.setFEnergyLabelFileName(fEnergyLabelFileName);
            resp.setFEnergyLabelOriginalName(fEnergyLabelOriginalName);
            resp.setFenergyLabelproductLeve1Code(fEnergyLabelproductLeve1Code);

            /** 제품 에너지 라벨 조회 */
            SystemPdsPdpEnergyLabelInfoResponseVO eLabelInfoResponseVO =
                    systemPdsPdpInfoClient.getEnergyLabelInfo(
                            SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                    .elabelClsCode(resp.getEnergyLabelCategory())
                                    .elabelGrdCode(resp.getEnergyLabel())
                                    .build());

            String eLabelImageAddr = "";
            String eLabelName = "";

            if (ObjectUtils.isNotEmpty(eLabelInfoResponseVO)) {
                eLabelImageAddr = Objects.toString(eLabelInfoResponseVO.getElabelImgPath(), "");
                eLabelName =
                        Objects.toString(
                                cachedDataUtil.getComCommonCodeValue(
                                        CachedCommonCodeRequestVO.ofCommonCodeValue(
                                                "Energy_Label_Code",
                                                eLabelInfoResponseVO.getElabelGrdCode(),
                                                JobSeperateCodeEnum.D2C)),
                                "");
            }

            resp.setEnergyLabelImageAddr(eLabelImageAddr);
            resp.setEnergyLabelName(eLabelName);

            String secondELabelImageAddr = "";
            String secondELabelName = "";

            if (CommonConstants.YES_FLAG.equals(washTowerFlag)) {
                if ("RS".equals(SITE_CODE)
                        && (("EL_CAT_01".equals(energyLabelCategory))
                                || ("EL_CAT_02".equals(energyLabelCategory))
                                || ("EL_CAT_03".equals(energyLabelCategory))
                                || ("EL_CAT_05".equals(energyLabelCategory)))) {
                    rsUseFlag = "Y";
                }

                if ("EL_CAT_06".equals(secondELabelCategory)
                        || "EL_CAT_07".equals(secondELabelCategory)
                        || "EL_CAT_08".equals(secondELabelCategory)
                        || "EL_CAT_09".equals(secondELabelCategory)
                        || "EL_CAT_10".equals(secondELabelCategory)
                        || "EL_CAT_11".equals(secondELabelCategory)) {
                    if ("TR".equals(SITE_CODE)) {
                        docTypeCode = "TL";
                    } else {
                        docTypeCode = "EL";
                    }
                } else if ("TR".equals(SITE_CODE)) {
                    docTypeCode = "TE";
                } else {
                    docTypeCode = "EN";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductGsriFileResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondProductGsriFileResp.getResultMap();

                    resp.setSecondEnergyLabelDocId(Objects.toString(resultMap.get("docId"), ""));
                    resp.setSecondEnergyLabelFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    resp.setSecondEnergyLabelOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    resp.setSecondEnergyLabelproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                if ("EL_CAT_06".equals(secondELabelCategory)
                        || "EL_CAT_07".equals(secondELabelCategory)
                        || "EL_CAT_08".equals(secondELabelCategory)
                        || "EL_CAT_09".equals(secondELabelCategory)
                        || "EL_CAT_10".equals(secondELabelCategory)
                        || "EL_CAT_11".equals(secondELabelCategory)) {
                    if ("TR".equals(SITE_CODE)) {
                        docTypeCode = "TF";
                    } else {
                        docTypeCode = "PF";
                    }
                } else if ("TR".equals(SITE_CODE) && !"EL_CAT_04".equals(secondELabelCategory)) {
                    docTypeCode = "TI";
                } else if (CommonConstants.YES_FLAG.equals(rsUseFlag)) {
                    docTypeCode = "SP";
                } else {
                    docTypeCode = "PI";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductFicheResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondProductFicheResp.getResultMap();
                    resp.setSecondProductFicheDocId(Objects.toString(resultMap.get("docId"), ""));
                    resp.setSecondProductFicheFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    resp.setSecondProductFicheOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    resp.setSecondProductFicheproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                if ("EL_CAT_06".equals(secondELabelCategory)
                        || "EL_CAT_07".equals(secondELabelCategory)
                        || "EL_CAT_08".equals(secondELabelCategory)
                        || "EL_CAT_09".equals(secondELabelCategory)
                        || "EL_CAT_10".equals(secondELabelCategory)
                        || "EL_CAT_11".equals(secondELabelCategory)) {
                    docTypeCode = "FL";
                } else {
                    docTypeCode = "FE";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondFEnergyLabelResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondFEnergyLabelResp.getResultMap();
                    resp.setSecondFEnergyLabelDocId(Objects.toString(resultMap.get("docId"), ""));
                    resp.setSecondFEnergyLabelFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    resp.setSecondFEnergyLabelOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    resp.setSecondFEnergyLabelproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                SystemPdsPdpEnergyLabelInfoResponseVO secondELabelInfoResponseVO =
                        systemPdsPdpInfoClient.getEnergyLabelInfo(
                                SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                        .elabelClsCode(secondELabelCategory)
                                        .elabelGrdCode(secondELabel)
                                        .build());

                if (ObjectUtils.isNotEmpty(secondELabelInfoResponseVO)) {
                    secondELabelImageAddr =
                            Objects.toString(secondELabelInfoResponseVO.getElabelImgPath(), "");
                    secondELabelName =
                            Objects.toString(
                                    cachedDataUtil.getComCommonCodeValue(
                                            CachedCommonCodeRequestVO.ofCommonCodeValue(
                                                    "Energy_Label_Code",
                                                    secondELabelInfoResponseVO.getElabelGrdCode(),
                                                    JobSeperateCodeEnum.D2C)),
                                    "");
                }
            }

            resp.setSecondEnergyLabelImageAddr(secondELabelImageAddr);
            resp.setSecondEnergyLabelName(secondELabelName);

            String eLabelFlag =
                    CommonConstants.YES_FLAG.equals(
                                    cachedDataUtil.getComSystemConfigurationCode(
                                            CachedCodeRequestVO.builder()
                                                    .keyCode("Energy_Label_Flag")
                                                    .build()))
                            ? CommonConstants.YES_FLAG
                            : CommonConstants.NO_FLAG;
            resp.setEnergyLabelFlag(eLabelFlag);

            String labelUseFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("LABEL_USE_FLAG")
                                            .build()),
                            CommonConstants.NO_FLAG);

            String repairabilityIndexFlag =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("repairability_index_use_flag")
                                            .build()),
                            CommonConstants.NO_FLAG);

            if (CommonConstants.YES_FLAG.equals(labelUseFlag)
                    && CommonConstants.YES_FLAG.equals(repairabilityIndexFlag)) {
                List<SystemPdsProductIconListResponseVO> sysPdsProductIconListVO =
                        systemPdsPdpInfoClient.getProductIconList(
                                SystemPdsProductIconListRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .bizTypeCode(bizType)
                                        .today(today)
                                        .pdpIdList(Arrays.asList(pdpId))
                                        .build());

                List<Map<String, Object>> labelRepairMapList = new ArrayList<>();
                if (ObjectUtils.isNotEmpty(sysPdsProductIconListVO)) {
                    ObjectMapper mapper = new ObjectMapper();
                    for (SystemPdsProductIconListResponseVO sysPdsPdpIcon :
                            sysPdsProductIconListVO) {
                        ProductIconListResponseVO pdpIconRespVO = sysPdsPdpIcon.toVO();
                        if ("REPAIRABILITY INDEX".equals(pdpIconRespVO.getShortDescType())) {
                            if (labelRepairMapList.size() == 0) {
                                Map<String, Object> labelRepairMap =
                                        mapper.convertValue(pdpIconRespVO, Map.class);
                                labelRepairMapList.add(labelRepairMap);
                            }
                        }
                    }
                }

                resp.setLabelRepairMap(labelRepairMapList);
                resp.setLabelUseFlag(labelUseFlag);
                resp.setRepairabilityIndexFlag(repairabilityIndexFlag);
            }

            /** modelUrlPath */
            if (null != resp.getSiblingGroupCode()) {
                if ("SELF".equals(resp.getTarget())
                        && CommonConstants.NO_FLAG.equals(resp.getDefaultSiblingModelFlag())) {
                    String modelUrlPath =
                            systemPdsProductListClient.getSelfSiblingDefaultUrl(
                                    SystemPdsProductListSelfSiblingDefaultRequestVO.builder()
                                            .siteCode(SITE_CODE)
                                            .bizTypeCode(bizType)
                                            .standardFlag(standardFlag)
                                            .pdpId(resp.getModelId())
                                            .superCategoryId(resp.getSuperCategoryId())
                                            .build());
                    resp.setModelUrlPath(modelUrlPath);
                }
            }

            resp.setParamModelType(modelTypeM);
            resp.setReviewType(reviewType);
            bundleList.add(resp);
        }

        return bundleList;
    }

    private void setGsriEnergyLabelVO(ProductSummaryResponseVO productSmrResp, String shopCode) {
        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        try {
            String pdpId = productSmrResp.getModelId();
            String bundleModelFlag = productSmrResp.getBundleModelFlag();
            String energyLabel = productSmrResp.getEnergyLabel();
            String energyLabelCategory = productSmrResp.getEnergyLabelCategory();
            String secondELabel = productSmrResp.getSecondEnergyLabel();
            String secondELabelCategory = productSmrResp.getSecondEnergyLabelCategory();
            String elType = productSmrResp.getElType();
            String secondElType = productSmrResp.getSecondElType();
            String washTowerFlag = productSmrResp.getWashTowerFlag();
            String bizType = productSmrResp.getBizType();
            String docTypeCode = energyLabelCategory;

            /** 제품 GSRI(환경규제 관련 모델) 정보 조회 * */
            String energyLabelDocId = "";
            String energyLabelFileName = "";
            String energyLabelOriginalName = "";
            String energyLabelproductLeve1Code = "";
            String productFichelDocId = "";
            String productFicheFileName = "";
            String productFicheOriginalName = "";
            String productFicheproductLeve1Code = "";
            String fEnergyLabelDocId = "";
            String fEnergyLabelFileName = "";
            String fEnergyLabelOriginalName = "";
            String fEnergyLabelproductLeve1Code = "";

            SystemPdsProductGsriFileInfoResponseVO sysPdsProductGsriFileResp =
                    new SystemPdsProductGsriFileInfoResponseVO();

            sysPdsProductGsriFileResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductGsriFileResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductGsriFileResp.getResultMap();

                energyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                energyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                energyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                energyLabelproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            productSmrResp.setEnergyLabelDocId(energyLabelDocId);
            productSmrResp.setEnergyLabelFileName(energyLabelFileName);
            productSmrResp.setEnergyLabelOriginalName(energyLabelOriginalName);
            productSmrResp.setEnergyLabelproductLeve1Code(energyLabelproductLeve1Code);

            SystemPdsProductGsriFileInfoResponseVO sysPdsProductFicheResp =
                    new SystemPdsProductGsriFileInfoResponseVO();

            sysPdsProductFicheResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(productSmrResp.getPisDocType())
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsProductFicheResp)
                    && ObjectUtils.isNotEmpty(sysPdsProductFicheResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsProductFicheResp.getResultMap();

                productFichelDocId = Objects.toString(resultMap.get("docId"), "");
                productFicheFileName = Objects.toString(resultMap.get("fileName"), "");
                productFicheOriginalName = Objects.toString(resultMap.get("originalName"), "");
                productFicheproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            productSmrResp.setProductFicheDocId(productFichelDocId);
            productSmrResp.setProductFicheFileName(productFicheFileName);
            productSmrResp.setProductFicheOriginalName(productFicheOriginalName);
            productSmrResp.setProductFicheproductLeve1Code(productFicheproductLeve1Code);

            SystemPdsProductGsriFileInfoResponseVO sysPdsFEnergyLabelResp =
                    new SystemPdsProductGsriFileInfoResponseVO();

            if ("EL".equals(energyLabelCategory)) {
                docTypeCode = "FL";
            } else {
                docTypeCode = "FE";
            }

            sysPdsFEnergyLabelResp =
                    systemPdsSupportClient.getProductGsriFileInfo(
                            SystemPdsProductGsriFileInfoRequestVO.builder()
                                    .docTypeCode(docTypeCode)
                                    .pdpId(pdpId)
                                    .fEnergyLabelDocId(fEnergyLabelDocId)
                                    .energyLabelDocId(energyLabelDocId)
                                    .productFichelDocId(productFichelDocId)
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp)
                    && ObjectUtils.isNotEmpty(sysPdsFEnergyLabelResp.getResultMap())) {
                Map<String, Object> resultMap = sysPdsFEnergyLabelResp.getResultMap();

                fEnergyLabelDocId = Objects.toString(resultMap.get("docId"), "");
                fEnergyLabelFileName = Objects.toString(resultMap.get("fileName"), "");
                fEnergyLabelOriginalName = Objects.toString(resultMap.get("originalName"), "");
                fEnergyLabelproductLeve1Code =
                        Objects.toString(resultMap.get("productLeve1Code"), "");
            }

            productSmrResp.setFEnergyLabelDocId(fEnergyLabelDocId);
            productSmrResp.setFEnergyLabelFileName(fEnergyLabelFileName);
            productSmrResp.setFEnergyLabelOriginalName(fEnergyLabelOriginalName);
            productSmrResp.setFenergyLabelproductLeve1Code(fEnergyLabelproductLeve1Code);

            /** 제품 에너지 라벨 조회 */
            SystemPdsPdpEnergyLabelInfoResponseVO eLabelInfoResponseVO =
                    systemPdsPdpInfoClient.getEnergyLabelInfo(
                            SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                    .siteCode(SITE_CODE)
                                    .bizTypeCode(bizType)
                                    .elabelClsCode(elType)
                                    .elabelGrdCode(energyLabel)
                                    .build());

            String eLabelImageAddr = "";
            String eLabelName = "";
            if (ObjectUtils.isNotEmpty(eLabelInfoResponseVO)) {
                eLabelImageAddr = Objects.toString(eLabelInfoResponseVO.getElabelImgPath(), "");
                eLabelName = Objects.toString(eLabelInfoResponseVO.getEnergyLabelName(), "");
            }
            productSmrResp.setEnergyLabelImageAddr(eLabelImageAddr);
            productSmrResp.setEnergyLabelName(eLabelName);

            if (CommonConstants.YES_FLAG.equals(washTowerFlag)) {
                docTypeCode = secondELabelCategory;

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductGsriFileResp =
                        new SystemPdsProductGsriFileInfoResponseVO();

                sysPdsSecondProductGsriFileResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondProductGsriFileResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondProductGsriFileResp.getResultMap();

                    productSmrResp.setSecondEnergyLabelDocId(
                            Objects.toString(resultMap.get("docId"), ""));
                    productSmrResp.setSecondEnergyLabelFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    productSmrResp.setSecondEnergyLabelOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    productSmrResp.setSecondEnergyLabelproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                docTypeCode = productSmrResp.getSecondPisDocType();

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondProductFicheResp =
                        new SystemPdsProductGsriFileInfoResponseVO();

                sysPdsSecondProductFicheResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondProductFicheResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondProductFicheResp.getResultMap();

                    productSmrResp.setSecondProductFicheDocId(
                            Objects.toString(resultMap.get("docId"), ""));
                    productSmrResp.setSecondProductFicheFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    productSmrResp.setSecondProductFicheOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    productSmrResp.setSecondProductFicheproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                if ("EL".equals(productSmrResp.getEnergyLabelCategory())) {
                    docTypeCode = "FL";
                } else {
                    docTypeCode = "FE";
                }

                SystemPdsProductGsriFileInfoResponseVO sysPdsSecondFEnergyLabelResp =
                        new SystemPdsProductGsriFileInfoResponseVO();

                sysPdsSecondFEnergyLabelResp =
                        systemPdsSupportClient.getProductGsriFileInfo(
                                SystemPdsProductGsriFileInfoRequestVO.builder()
                                        .docTypeCode(docTypeCode)
                                        .pdpId(pdpId)
                                        .fEnergyLabelDocId(fEnergyLabelDocId)
                                        .energyLabelDocId(energyLabelDocId)
                                        .productFichelDocId(productFichelDocId)
                                        .build());

                if (ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp)
                        && ObjectUtils.isNotEmpty(sysPdsSecondFEnergyLabelResp.getResultMap())) {
                    Map<String, Object> resultMap = sysPdsSecondFEnergyLabelResp.getResultMap();

                    productSmrResp.setSecondFEnergyLabelDocId(
                            Objects.toString(resultMap.get("docId"), ""));
                    productSmrResp.setSecondFEnergyLabelFileName(
                            Objects.toString(resultMap.get("fileName"), ""));
                    productSmrResp.setSecondFEnergyLabelOriginalName(
                            Objects.toString(resultMap.get("originalName"), ""));
                    productSmrResp.setSecondFEnergyLabelproductLeve1Code(
                            Objects.toString(resultMap.get("productLeve1Code"), ""));
                }

                /** 제품 에너지 라벨 조회 */
                SystemPdsPdpEnergyLabelInfoResponseVO secondELabelInfoResponseVO =
                        systemPdsPdpInfoClient.getEnergyLabelInfo(
                                SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .bizTypeCode(bizType)
                                        .elabelClsCode(secondElType)
                                        .elabelGrdCode(secondELabel)
                                        .build());

                String secondELabelImageAddr = "";
                String secondELabelName = "";
                if (ObjectUtils.isNotEmpty(secondELabelInfoResponseVO)) {
                    secondELabelImageAddr =
                            Objects.toString(secondELabelInfoResponseVO.getElabelImgPath(), "");
                    secondELabelName =
                            Objects.toString(secondELabelInfoResponseVO.getEnergyLabelName(), "");
                }
                productSmrResp.setSecondEnergyLabelImageAddr(secondELabelImageAddr);
                productSmrResp.setSecondEnergyLabelName(secondELabelName);
            } else if (CommonConstants.YES_FLAG.equals(bundleModelFlag)) {
                List<SystemPdsBundleElInfoResponseVO> bundleElInfoResponseVO =
                        systemPdsBundleClient.getBundleElInfo(
                                SystemPdsBundleElInfoRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .pdpId(productSmrResp.getModelId())
                                        .build());

                if (bundleElInfoResponseVO.size() > 0) {
                    for (int j = 0; j < bundleElInfoResponseVO.size(); j++) {
                        List<String> docTypeList;
                        SystemPdsBundleElInfoResponseVO bundleModel = bundleElInfoResponseVO.get(j);
                        String bundlePdpId = bundleModel.getBundlePdpId();

                        if (j == 0) {
                            if (!""
                                    .equals(
                                            StringUtils.defaultIfBlank(
                                                    bundleModel.getElDocTypeCode(), ""))) {
                                docTypeList = new ArrayList<String>();
                                docTypeList.add(bundleModel.getElDocTypeCode());
                                if (!""
                                        .equals(
                                                StringUtils.defaultIfBlank(
                                                        bundleModel.getFEnergyLabelCategory(),
                                                        ""))) {
                                    docTypeList.add(bundleModel.getFEnergyLabelCategory());
                                }

                                List<SystemPdsBundleGsriFileInfoResponseVO>
                                        sysPdsBundleEnergyLabelResp =
                                                systemPdsSupportClient.getBundleGsriFileInfo(
                                                        SystemPdsProductGsriFileInfoRequestVO
                                                                .builder()
                                                                .pdpId(bundlePdpId)
                                                                .siteCode(SITE_CODE)
                                                                .docTypeList(docTypeList)
                                                                .build());

                                if (sysPdsBundleEnergyLabelResp.size() > 0) {
                                    for (int k = 0; k < sysPdsBundleEnergyLabelResp.size(); k++) {
                                        SystemPdsBundleGsriFileInfoResponseVO bundleLabelVO =
                                                sysPdsBundleEnergyLabelResp.get(k);
                                        if (k == 0) {
                                            productSmrResp.setEnergyLabelDocId(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getDocId(), ""));
                                            productSmrResp.setEnergyLabelFileName(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getFileName(), ""));
                                            productSmrResp.setEnergyLabelOriginalName(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getOriginalName(), ""));
                                            productSmrResp.setEnergyLabelproductLeve1Code(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getProductLeve1Code(),
                                                            ""));
                                        }
                                        if (k == 1) {
                                            productSmrResp.setFEnergyLabelDocId(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getDocId(), ""));
                                            productSmrResp.setFEnergyLabelFileName(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getFileName(), ""));
                                            productSmrResp.setFEnergyLabelOriginalName(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getOriginalName(), ""));
                                            productSmrResp.setFenergyLabelproductLeve1Code(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getProductLeve1Code(),
                                                            ""));
                                        }
                                    }
                                }
                            }

                            if (!""
                                    .equals(
                                            StringUtils.defaultIfBlank(
                                                    bundleModel.getPisDocTypeCode(), ""))) {
                                docTypeList = new ArrayList<String>();
                                docTypeList.add(bundleModel.getPisDocTypeCode());

                                List<SystemPdsBundleGsriFileInfoResponseVO>
                                        sysPdsFicheEnergyLabelResp =
                                                systemPdsSupportClient.getBundleGsriFileInfo(
                                                        SystemPdsProductGsriFileInfoRequestVO
                                                                .builder()
                                                                .pdpId(bundlePdpId)
                                                                .siteCode(SITE_CODE)
                                                                .docTypeList(docTypeList)
                                                                .build());

                                if (sysPdsFicheEnergyLabelResp.size() > 0) {
                                    for (int k = 0; k < sysPdsFicheEnergyLabelResp.size(); k++) {
                                        SystemPdsBundleGsriFileInfoResponseVO bundleLabelVO =
                                                sysPdsFicheEnergyLabelResp.get(k);
                                        if (k == 0) {
                                            productSmrResp.setProductFicheDocId(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getDocId(), ""));
                                            productSmrResp.setProductFicheFileName(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getFileName(), ""));
                                            productSmrResp.setProductFicheOriginalName(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getOriginalName(), ""));
                                            productSmrResp.setProductFicheproductLeve1Code(
                                                    StringUtils.defaultIfBlank(
                                                            bundleLabelVO.getProductLeve1Code(),
                                                            ""));
                                        }
                                    }
                                }
                            }

                            /** 제품 에너지 라벨 조회 */
                            SystemPdsPdpEnergyLabelInfoResponseVO bundleELabelInfoResponseVO =
                                    systemPdsPdpInfoClient.getEnergyLabelInfo(
                                            SystemPdsPdpEnergyLabelInfoRequestVO.builder()
                                                    .siteCode(SITE_CODE)
                                                    .bizTypeCode(bizType)
                                                    .elabelClsCode(bundleModel.getElTypeCode())
                                                    .elabelGrdCode(bundleModel.getElabelGrdCode())
                                                    .build());

                            String bundleELabelImageAddr = "";
                            String bundleELabelName = "";
                            if (ObjectUtils.isNotEmpty(bundleELabelInfoResponseVO)) {
                                bundleELabelImageAddr =
                                        Objects.toString(
                                                bundleELabelInfoResponseVO.getElabelImgPath(), "");
                                bundleELabelName =
                                        Objects.toString(
                                                bundleELabelInfoResponseVO.getEnergyLabelName(),
                                                "");
                            }
                            productSmrResp.setEnergyLabelImageAddr(bundleELabelImageAddr);
                            productSmrResp.setEnergyLabelName(bundleELabelName);
                        }

                        if (j == 1) {
                            if (!""
                                    .equals(
                                            StringUtils.defaultIfBlank(
                                                    bundleModel.getElDocTypeCode(), ""))) {
                                docTypeList = new ArrayList<String>();
                                docTypeList.add(bundleModel.getElDocTypeCode());
                                if (!""
                                        .equals(
                                                StringUtils.defaultIfBlank(
                                                        bundleModel.getFEnergyLabelCategory(),
                                                        ""))) {
                                    docTypeList.add(bundleModel.getFEnergyLabelCategory());
                                }

                                List<SystemPdsBundleGsriFileInfoResponseVO>
                                        sysPdsBundleEnergyLabelResp =
                                                systemPdsSupportClient.getBundleGsriFileInfo(
                                                        SystemPdsProductGsriFileInfoRequestVO
                                                                .builder()
                                                                .pdpId(bundlePdpId)
                                                                .siteCode(SITE_CODE)
                                                                .docTypeList(docTypeList)
                                                                .build());

                                if (sysPdsBundleEnergyLabelResp.size() > 0) {
                                    if (""
                                            .equals(
                                                    Objects.toString(
                                                            productSmrResp.getEnergyLabelDocId(),
                                                            ""))) {
                                        for (int k = 0;
                                                k < sysPdsBundleEnergyLabelResp.size();
                                                k++) {
                                            SystemPdsBundleGsriFileInfoResponseVO bundleLabelVO =
                                                    sysPdsBundleEnergyLabelResp.get(k);
                                            if (k == 0) {
                                                productSmrResp.setEnergyLabelDocId(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getDocId(), ""));
                                                productSmrResp.setEnergyLabelFileName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getFileName(), ""));
                                                productSmrResp.setEnergyLabelOriginalName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getOriginalName(),
                                                                ""));
                                                productSmrResp.setEnergyLabelproductLeve1Code(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getProductLeve1Code(),
                                                                ""));
                                            }
                                            if (k == 1) {
                                                productSmrResp.setFEnergyLabelDocId(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getDocId(), ""));
                                                productSmrResp.setFEnergyLabelFileName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getFileName(), ""));
                                                productSmrResp.setFEnergyLabelOriginalName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getOriginalName(),
                                                                ""));
                                                productSmrResp.setFenergyLabelproductLeve1Code(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getProductLeve1Code(),
                                                                ""));
                                            }
                                        }

                                        /** 제품 에너지 라벨 조회 */
                                        SystemPdsPdpEnergyLabelInfoResponseVO
                                                bundleELabelInfoResponseVO =
                                                        systemPdsPdpInfoClient.getEnergyLabelInfo(
                                                                SystemPdsPdpEnergyLabelInfoRequestVO
                                                                        .builder()
                                                                        .siteCode(SITE_CODE)
                                                                        .bizTypeCode(bizType)
                                                                        .elabelClsCode(
                                                                                bundleModel
                                                                                        .getElTypeCode())
                                                                        .elabelGrdCode(
                                                                                bundleModel
                                                                                        .getElabelGrdCode())
                                                                        .build());

                                        String bundleELabelImageAddr = "";
                                        String bundleELabelName = "";
                                        if (ObjectUtils.isNotEmpty(bundleELabelInfoResponseVO)) {
                                            bundleELabelImageAddr =
                                                    Objects.toString(
                                                            bundleELabelInfoResponseVO
                                                                    .getElabelImgPath(),
                                                            "");
                                            bundleELabelName =
                                                    Objects.toString(
                                                            bundleELabelInfoResponseVO
                                                                    .getEnergyLabelName(),
                                                            "");
                                        }
                                        productSmrResp.setEnergyLabelImageAddr(
                                                bundleELabelImageAddr);
                                        productSmrResp.setEnergyLabelName(bundleELabelName);
                                    } else {
                                        for (int k = 0;
                                                k < sysPdsBundleEnergyLabelResp.size();
                                                k++) {
                                            SystemPdsBundleGsriFileInfoResponseVO bundleLabelVO =
                                                    sysPdsBundleEnergyLabelResp.get(k);
                                            if (k == 0) {
                                                productSmrResp.setSecondEnergyLabelDocId(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getDocId(), ""));
                                                productSmrResp.setSecondEnergyLabelFileName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getFileName(), ""));
                                                productSmrResp.setSecondEnergyLabelOriginalName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getOriginalName(),
                                                                ""));
                                                productSmrResp.setSecondEnergyLabelproductLeve1Code(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getProductLeve1Code(),
                                                                ""));
                                            }
                                            if (k == 1) {
                                                productSmrResp.setSecondFEnergyLabelDocId(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getDocId(), ""));
                                                productSmrResp.setSecondFEnergyLabelFileName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getFileName(), ""));
                                                productSmrResp.setSecondFEnergyLabelOriginalName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getOriginalName(),
                                                                ""));
                                                productSmrResp
                                                        .setSecondFEnergyLabelproductLeve1Code(
                                                                StringUtils.defaultIfBlank(
                                                                        bundleLabelVO
                                                                                .getProductLeve1Code(),
                                                                        ""));
                                            }
                                        }

                                        /** 제품 에너지 라벨 조회 */
                                        SystemPdsPdpEnergyLabelInfoResponseVO
                                                bundleELabelInfoResponseVO =
                                                        systemPdsPdpInfoClient.getEnergyLabelInfo(
                                                                SystemPdsPdpEnergyLabelInfoRequestVO
                                                                        .builder()
                                                                        .siteCode(SITE_CODE)
                                                                        .bizTypeCode(bizType)
                                                                        .elabelClsCode(
                                                                                bundleModel
                                                                                        .getElTypeCode())
                                                                        .elabelGrdCode(
                                                                                bundleModel
                                                                                        .getElabelGrdCode())
                                                                        .build());

                                        String bundleELabelImageAddr = "";
                                        String bundleELabelName = "";
                                        if (ObjectUtils.isNotEmpty(bundleELabelInfoResponseVO)) {
                                            bundleELabelImageAddr =
                                                    Objects.toString(
                                                            bundleELabelInfoResponseVO
                                                                    .getElabelImgPath(),
                                                            "");
                                            bundleELabelName =
                                                    Objects.toString(
                                                            bundleELabelInfoResponseVO
                                                                    .getEnergyLabelName(),
                                                            "");
                                        }
                                        productSmrResp.setSecondEnergyLabelImageAddr(
                                                bundleELabelImageAddr);
                                        productSmrResp.setSecondEnergyLabelName(bundleELabelName);
                                    }
                                }
                            }

                            if (!""
                                    .equals(
                                            StringUtils.defaultIfBlank(
                                                    bundleModel.getPisDocTypeCode(), ""))) {
                                docTypeList = new ArrayList<String>();
                                docTypeList.add(bundleModel.getPisDocTypeCode());

                                List<SystemPdsBundleGsriFileInfoResponseVO>
                                        sysPdsFicheEnergyLabelResp =
                                                systemPdsSupportClient.getBundleGsriFileInfo(
                                                        SystemPdsProductGsriFileInfoRequestVO
                                                                .builder()
                                                                .pdpId(bundlePdpId)
                                                                .siteCode(SITE_CODE)
                                                                .docTypeList(docTypeList)
                                                                .build());
                                if (sysPdsFicheEnergyLabelResp.size() > 0) {
                                    if (""
                                            .equals(
                                                    StringUtils.defaultIfBlank(
                                                            productSmrResp.getProductFicheDocId(),
                                                            ""))) {
                                        for (int k = 0;
                                                k < sysPdsFicheEnergyLabelResp.size();
                                                k++) {
                                            SystemPdsBundleGsriFileInfoResponseVO bundleLabelVO =
                                                    sysPdsFicheEnergyLabelResp.get(k);
                                            if (k == 0) {
                                                productSmrResp.setProductFicheDocId(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getDocId(), ""));
                                                productSmrResp.setProductFicheFileName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getFileName(), ""));
                                                productSmrResp.setProductFicheOriginalName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getOriginalName(),
                                                                ""));
                                                productSmrResp.setProductFicheproductLeve1Code(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getProductLeve1Code(),
                                                                ""));
                                            }
                                        }
                                    } else {
                                        for (int k = 0;
                                                k < sysPdsFicheEnergyLabelResp.size();
                                                k++) {
                                            SystemPdsBundleGsriFileInfoResponseVO bundleLabelVO =
                                                    sysPdsFicheEnergyLabelResp.get(k);
                                            if (k == 0) {
                                                productSmrResp.setSecondProductFicheDocId(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getDocId(), ""));
                                                productSmrResp.setSecondProductFicheFileName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getFileName(), ""));
                                                productSmrResp.setSecondProductFicheOriginalName(
                                                        StringUtils.defaultIfBlank(
                                                                bundleLabelVO.getOriginalName(),
                                                                ""));
                                                productSmrResp
                                                        .setSecondProductFicheproductLeve1Code(
                                                                StringUtils.defaultIfBlank(
                                                                        bundleLabelVO
                                                                                .getProductLeve1Code(),
                                                                        ""));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new D2xBusinessException(e);
        }
    }

    private SystemPdsProductResponseVO getProductInfo(String pimSkuId) {

        SystemPdsProductResponseVO systemPdsProductRespVO =
                systemPdsProductClient.getProductInfo(
                        SystemPdsProductRequestVO.builder()
                                .skuId(pimSkuId)
                                .localeCode(
                                        cachedDataUtil
                                                .getComLocaleCode(
                                                        CachedCodeRequestVO.builder().build())
                                                .getLocaleCode())
                                .build());

        if (ObjectUtils.isEmpty(systemPdsProductRespVO)) {
            throw new D2xBusinessException(CommonCodes.JSON_RESPONSE_NO_DATA_FOUND_MESSAGE);
        }

        return systemPdsProductRespVO;
    }

    private void setCodeInfo(ProductSummaryResponseVO productSmrRespVO, String bizType) {

        productSmrRespVO.setRepairComment(
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.builder().keyCode("repair-comment").build()));

        productSmrRespVO.setLoginUseFlag(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("LOGIN_USE_FLAG").build()),
                        CommonConstants.YES_FLAG));

        productSmrRespVO.setWishUseFlag(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("WISH_USE_FLAG").build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setAtcTrackEventFlag(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("atc_track_event_flag")
                                        .build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setBuyNowUseFlag(
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("BUY_NOW_FLAG").build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setBuyNowFlag(
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("BUY_NOW_FLAG").build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setBuyNowURL(
                cachedDataUtil.getComSystemConfigurationCode(
                        CachedCodeRequestVO.builder().keyCode("default_buy_now_url").build()));

        productSmrRespVO.setObsLoginFlag(
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("OBS_LOGIN_FLAG").build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setVipPriceFlag(
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("VIP_PRICE_USE_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setPdpSummaryImproveFlag(
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("PDP_SUMMARY_IMPROVE_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG));

        String thinqFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("THINQ_USE_FLAG").build()),
                        CommonConstants.YES_FLAG);

        if (CommonConstants.NO_FLAG.equals(thinqFlag)) {
            productSmrRespVO.setThinqFlag(CommonConstants.NO_FLAG);
        }

        String signatureFlag =
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("SIGNATURE_USE_FLAG")
                                        .build()),
                        CommonConstants.YES_FLAG);

        if (CommonConstants.NO_FLAG.equals(signatureFlag)) {
            productSmrRespVO.setSignatureFlag(CommonConstants.NO_FLAG);
        }

        productSmrRespVO.setThreeDUseFlag(
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("THREE_DIMENSION_USE_FLAG")
                                        .build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setArUseFlag(
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("AR_USE_FLAG").build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setVrUseFlag(
                StringUtils.defaultIfEmpty(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.builder().keyCode("VR_USE_FLAG").build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setB2bResourceFlag(
                ""
                                .equals(
                                        cachedDataUtil
                                                .getComSystemConfigurationCode(
                                                        CachedCodeRequestVO.builder()
                                                                .keyCode(
                                                                        "LG_B2B_PARTNER_PORTAL_LINK")
                                                                .build())
                                                .trim())
                        ? "N"
                        : "Y");

        productSmrRespVO.setPromotionHighlightUseFlag(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("promotion_highlight_use_flag")
                                        .build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setObsComTagShowFlag(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("obs_com_tag_show_flag")
                                        .build()),
                        CommonConstants.NO_FLAG));

        productSmrRespVO.setInquiryToBuyFlag(
                CommonConstants.BIZ_TYPE_B2B.equals(bizType)
                        ? CommonConstants.YES_FLAG
                        : CommonConstants.NO_FLAG);

        productSmrRespVO.setMktNameSeoUseFlag(
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemConfigurationCode(
                                CachedCodeRequestVO.builder()
                                        .keyCode("mkt_name_seo_use_flag")
                                        .build()),
                        CommonConstants.NO_FLAG));
    }

    private String getModelType(String modelTypeM) {
        return switch (modelTypeM) {
            case "A" -> "ADP";
            case "B" -> "BDP";
            case "O" -> "ODP";
            default -> "PDP";
        };
    }

    private void setModelPromotionMsg(ProductSummaryResponseVO productSmrRespVO, String today) {

        String siteCode = productSmrRespVO.getLocaleCode();
        String pdpId = productSmrRespVO.getModelId();

        SystemAdminProductPromotionMsgResponseVO sysAdminProductPromotionMsg =
                systemAdminPromotionClient.getModelPromotionMsg(
                        SystemAdminProductPromotionMsgRequestVO.builder()
                                .localeCode(siteCode)
                                .modelId(pdpId)
                                .today(today)
                                .build());

        if (ObjectUtils.isNotEmpty(sysAdminProductPromotionMsg)) {
            productSmrRespVO.setObsAdditionalDisclaimerText1Flag(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText1Flag());
            productSmrRespVO.setObsAdditionalDisclaimerText1Msg(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText1Msg());
            productSmrRespVO.setObsAdditionalDisclaimerText2Flag(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText2Flag());
            productSmrRespVO.setObsAdditionalDisclaimerText2Msg(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText2Msg());
            productSmrRespVO.setObsAdditionalDisclaimerText3Flag(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText3Flag());
            productSmrRespVO.setObsAdditionalDisclaimerText3Msg(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText3Msg());
            productSmrRespVO.setObsAdditionalDisclaimerText4Flag(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText4Flag());
            productSmrRespVO.setObsAdditionalDisclaimerText4Msg(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText4Msg());
            productSmrRespVO.setObsAdditionalDisclaimerText5Flag(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText5Flag());
            productSmrRespVO.setObsAdditionalDisclaimerText5Msg(
                    sysAdminProductPromotionMsg.getObsAdditionalDisclaimerText5Msg());
        }
    }

    private List<Map<String, Object>> getKeySpecsOfModel(
            ProductSummarySpecResponseVO detail, String siteCode) {
        List<Map<String, Object>> keySpec = new ArrayList<Map<String, Object>>();

        String productLevel3NotUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference(
                                        "PRODUCT_LEVE3_CODE_NOT_USE_FLAG")),
                        CommonConstants.NO_FLAG);
        String keySpecUseFlag =
                StringUtils.defaultIfBlank(
                        cachedDataUtil.getComSystemPreferenceCode(
                                CachedCodeRequestVO.ofComSysPreference("KEY_SPEC_USE_FLAG")),
                        CommonConstants.YES_FLAG);
        String seoSpecId =
                StringUtils.defaultIfBlank(detail.getSeoSpecId(), CommonConstants.NO_FLAG);

        if (CommonConstants.NO_FLAG.equals(keySpecUseFlag)
                && CommonConstants.NO_FLAG.equals(seoSpecId)) {
            return keySpec;
        }

        try {
            List<SystemPdsKeySpecResponseVO> sysPdsKeySpecRespList =
                    systemPdsSpecClient.getKeySpecs(
                            SystemPdsKeySpecRequestVO.builder()
                                    .skuId(detail.getPimSku())
                                    .siteCode(siteCode)
                                    .localeCode(
                                            cachedDataUtil
                                                    .getComLocaleCode(
                                                            CachedCodeRequestVO.ofComLocale())
                                                    .getLocaleCode())
                                    .lv3ProductCodeNotUseFlag(productLevel3NotUseFlag)
                                    .pdpId(detail.getModelId())
                                    .build());

            if (ObjectUtils.isNotEmpty(sysPdsKeySpecRespList)) {
                for (SystemPdsKeySpecResponseVO resp : sysPdsKeySpecRespList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("keySpecOrder", Integer.parseInt(resp.getDspSeq()));
                    map.put("specName", resp.getSpecNm());
                    map.put("specValue", resp.getSpecVal());
                    map.put("engSpecName", resp.getEngSpecName());
                    keySpec.add(map);
                }
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return keySpec;
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getTechSpecsOfModel(
            ProductSummarySpecResponseVO detail, String siteCode) {
        List<Map<String, Object>> techSpec = new ArrayList<Map<String, Object>>();
        try {
            Map<String, Object> spec = new HashMap<String, Object>();
            List<SpecItemVO> specList = new ArrayList<SpecItemVO>();

            List<Map<String, Object>> pList = new ArrayList<Map<String, Object>>();
            List<SystemPdsProductStandardSpecResponseVO> standardSpecList =
                    new ArrayList<SystemPdsProductStandardSpecResponseVO>();
            List<SystemPdsProductLgcomSpecResponseVO> lgcomSpecList =
                    new ArrayList<SystemPdsProductLgcomSpecResponseVO>();
            if (CommonConstants.YES_FLAG.equals(detail.getPdrUseFlag())) {
                standardSpecList =
                        systemPdsSpecClient.getProductStandardSpecs(
                                SystemPdsProductStandardSpecRequestVO.builder()
                                        .skuId(detail.getPimSku())
                                        .localeCode(
                                                cachedDataUtil
                                                        .getComLocaleCode(
                                                                CachedCodeRequestVO.ofComLocale())
                                                        .getLocaleCode())
                                        .pdpId(detail.getModelId())
                                        .build());
            } else {
                lgcomSpecList =
                        systemPdsSpecClient.getProductLgcomSpecs(
                                SystemPdsProductLgcomSpecRequestVO.builder()
                                        .pdpId(detail.getModelId())
                                        .siteCode(siteCode)
                                        .build());
            }

            ObjectMapper objectMapper = new ObjectMapper();
            if (ObjectUtils.isNotEmpty(standardSpecList)) {
                Map<String, Object> standardSpecMap = new HashMap<>();
                for (SystemPdsProductStandardSpecResponseVO standardSpecResp : standardSpecList) {
                    standardSpecMap = objectMapper.convertValue(standardSpecResp, Map.class);
                    pList.add(standardSpecMap);
                }
            }

            if (ObjectUtils.isNotEmpty(lgcomSpecList)) {
                Map<String, Object> lgcomSpecMap = new HashMap<>();
                for (SystemPdsProductLgcomSpecResponseVO lgcomSpecResp : lgcomSpecList) {
                    lgcomSpecMap = objectMapper.convertValue(lgcomSpecResp, Map.class);
                    pList.add(lgcomSpecMap);
                }
            }

            String lv1SpecId = "";
            String lv1SpecName = "";
            for (int i = 0; i < pList.size(); i++) {
                Map<String, Object> pItem = pList.get(i);
                if (!lv1SpecId.equals(pItem.get("lv1SpecCode").toString())) {
                    if (i != 0) {
                        spec.put("title", lv1SpecName);
                        spec.put("spec", specList);
                        techSpec.add(spec);

                        spec = new HashMap<String, Object>();
                        specList = new ArrayList<SpecItemVO>();
                    }

                    lv1SpecId = pItem.get("lv1SpecCode").toString();

                    if (ObjectUtils.isNotEmpty(pItem.get("lv1SpecLocalNm"))) {
                        lv1SpecName = pItem.get("lv1SpecLocalNm").toString();
                    } else if (ObjectUtils.isNotEmpty(pItem.get("lv1SpecNm"))) {
                        lv1SpecName = pItem.get("lv1SpecNm").toString();
                    }
                }

                SpecItemVO specItem = new SpecItemVO();
                String lv2SpecName = "";
                if (ObjectUtils.isNotEmpty(pItem.get("lv2SpecLocalNm"))) {
                    lv2SpecName = pItem.get("lv2SpecLocalNm").toString();
                } else if (ObjectUtils.isNotEmpty(pItem.get("lv2SpecNm"))) {
                    lv2SpecName = pItem.get("lv2SpecNm").toString();
                }
                specItem.setName(lv2SpecName);
                specItem.setValue(
                        ObjectUtils.isEmpty(pItem.get("specVal"))
                                ? ""
                                : pItem.get("specVal").toString());

                if (!CommonConstants.YES_FLAG.equals(detail.getPdrUseFlag())) {
                    specItem.setDescLinkAlt("");
                    specItem.setDescLinkFlag("");
                    specItem.setDescLinkTarget("");
                    specItem.setDescLinkUrl("");
                    specItem.setDescText("");
                    specItem.setIconPreviewUrlAddr("");
                }

                specItem.setIconPreviewUrlAddr(
                        ObjectUtils.isEmpty(pItem.get("iconPreviewUrlAddr"))
                                ? ""
                                : pItem.get("iconPreviewUrlAddr").toString());
                specItem.setDescLinkFlag(CommonConstants.NO_FLAG);
                specList.add(specItem);

                if (i == pList.size() - 1) {
                    spec.put("title", lv1SpecName);
                    spec.put("spec", specList);
                    techSpec.add(spec);
                }
            }
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return techSpec;
    }

    public List<AccessoryProductListResponseVO> getAccessoryProductList(
            AccessoryProductListRequestVO requestVO) {
        String SITE_CODE = RequestHeaderUtil.getSiteCode();
        String standardFlag = commonService.getCategoryStandardFlag();
        String pdpId = "";
        String bizType = requestVO.getBizTypeCode();
        List<AccessoryProductListResponseVO> respList = new ArrayList<>();

        List<String> pdpIdList =
                systemPdsProductListClient.getPdpIdListBySkuId(
                        SystemPdsPdpIdListBySkuRequestVO.builder()
                                .siteCode(SITE_CODE)
                                .standardFlag(standardFlag)
                                .skuIds(requestVO.getSku())
                                .build());

        if (ObjectUtils.isEmpty(pdpIdList)) {
            throw new D2xBusinessException(
                    CommonCodes.JSON_RESPONSE_MISSING_REQUIRED_PARAMETER_MESSAGE);
        }

        pdpId = pdpIdList.get(0);

        List<Map<String, Object>> compatibleStrList =
                systemPdsPdpInfoClient.getAccessoryProductList(
                        SystemPdsAccessoryProductRequestVO.builder()
                                .siteCode(SITE_CODE)
                                .pdpId(pdpId)
                                .bizTypeCode(bizType)
                                .functionName(requestVO.getFunctionName())
                                .searchModelNm(requestVO.getSearchModel())
                                .build());

        if (ObjectUtils.isNotEmpty(compatibleStrList)) {
            for (Map<String, Object> map : compatibleStrList) {
                AccessoryProductListResponseVO compatible = new AccessoryProductListResponseVO();
                compatible.setModel(Objects.toString(map.get("model"), ""));
                respList.add(compatible);
            }
        }

        return respList;
    }

    public ProductSupportInfoResponseVO getProductSupport(ProductSupportInfoRequestVO requestVO) {
        final String SITE_CODE = RequestHeaderUtil.getSiteCode();
        final String SKU = requestVO.getSku();
        ProductSupportInfoResponseVO responseVO = new ProductSupportInfoResponseVO();

        try {
            String LINK_COMMON_SUPPORT_PRODUCT =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("link-common_support_product")
                                            .build()),
                            "");

            String SUPPORT_LINK_LIVE_CHAT_OPEN_TYPE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("support_link_live_chat_open_type_flag")
                                            .build()),
                            "");

            String SUPPORT_LINK_LIVE_CHAT_OPEN_TYPE =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemConfigurationCode(
                                    CachedCodeRequestVO.builder()
                                            .keyCode("support_link_live_chat_open_type")
                                            .build()),
                            "");

            String CST_EMAIL_TO_CEO_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference(
                                            "CST_EMAIL_TO_CEO_USE_FLAG")),
                            CommonConstants.NO_FLAG);

            String MKT_PDP_EMAIL_TO_CEO_USE_FLAG =
                    StringUtils.defaultIfBlank(
                            cachedDataUtil.getComSystemPreferenceCode(
                                    CachedCodeRequestVO.ofComSysPreference(
                                            "MKT_PDP_EMAIL_TO_CEO_USE_FLAG")),
                            CommonConstants.NO_FLAG);

            /** 카테고리 표준화 국가 여부 조회 */
            String standardFlag = commonService.getCategoryStandardFlag();

            String localeCode =
                    StringUtils.defaultIfEmpty(
                            cachedDataUtil
                                    .getComLocaleCode(
                                            CachedCodeRequestVO.builder()
                                                    .siteCode(SITE_CODE)
                                                    .build())
                                    .getLocaleCode(),
                            "");

            SystemPdsPdpResponseVO systemPdspdpResponseVO =
                    systemPdsPdpInfoClient.getProductBasicInfo(
                            SystemPdsPdpRequestVO.builder()
                                    .lgcomSkuId(SKU)
                                    .siteCode(SITE_CODE)
                                    .localeCode(localeCode)
                                    .standardFlag(standardFlag)
                                    .build());

            SystemPdsProductResponseVO systemPdsProductRespVO =
                    getProductInfo(systemPdspdpResponseVO.getSkuId());

            String modelDisplayName = "";
            String csLinkCustModelCode = "";
            String pspLinkUrl = "";
            Map<String, Object> contactusBoxUrl = new HashMap<String, Object>();
            if (ObjectUtils.isNotEmpty(systemPdspdpResponseVO)
                    && ObjectUtils.isNotEmpty(systemPdsProductRespVO)) {
                modelDisplayName = systemPdspdpResponseVO.getProductNm();

                String defaultStr = "default";
                StringBuffer linkBuffer = new StringBuffer("/" + SITE_CODE.toLowerCase() + "/");
                linkBuffer.append(LINK_COMMON_SUPPORT_PRODUCT).append("/lg-");

                String salesModelCode = systemPdsProductRespVO.getSalesModelCode();
                String salesSuffixCode = systemPdsProductRespVO.getSalesModelSuffixCode();

                if (!"".equals(salesModelCode)) {
                    StringBuffer csModelBuffer = new StringBuffer();
                    csModelBuffer.append(salesModelCode);
                    if (!"".equals(salesSuffixCode)) {
                        csModelBuffer.append(".").append(salesSuffixCode);
                    }
                    csLinkCustModelCode = csModelBuffer.toString();
                    defaultStr = csLinkCustModelCode;
                }

                linkBuffer.append(defaultStr);
                pspLinkUrl = linkBuffer.toString();

                SystemPdsPdpCategoryResponseVO systemPdsPdpCategoryRespVO =
                        systemPdsPdpInfoClient.getPdpCategoryInfo(
                                SystemPdsPdpCategoryRequestVO.builder()
                                        .pdpId(systemPdspdpResponseVO.getPdpId())
                                        .standardFlag(standardFlag)
                                        .shopCode(systemPdspdpResponseVO.getShopCode())
                                        .build());
                String superCategoryId =
                        standardFlag.equals("Y")
                                ? systemPdsPdpCategoryRespVO.getLv2CategoryCode()
                                : systemPdsPdpCategoryRespVO.getLv1CategoryCode();
                String categoryId =
                        standardFlag.equals("Y")
                                ? systemPdsPdpCategoryRespVO.getLv3CategoryCode()
                                : systemPdsPdpCategoryRespVO.getLv2CategoryCode();

                List<SystemPdsConfigsResponseVO> systemPdsConfigRespVO =
                        systemPdsCommonClient.getSystemConfigs(
                                SystemPdsConfigsRequestVO.builder()
                                        .siteCode(SITE_CODE)
                                        .configCodeLike("support_url_")
                                        .build());

                for (SystemPdsConfigsResponseVO systemPdsConfig : systemPdsConfigRespVO) {
                    String key = systemPdsConfig.getConfigCode().replace("support_url_", "");
                    String value = systemPdsConfig.getConfigVal();
                    if (value.contains("{")) {
                        value = value.replace("{modelDisplayName}", modelDisplayName);
                        value = value.replace("{csLinkCustModelCode}", csLinkCustModelCode);
                        value = value.replace("{viewSalesCode}", csLinkCustModelCode);
                        value = value.replace("{superCategoryId}", superCategoryId);
                        value = value.replace("{categoryId}", categoryId);
                        value = value.replace("{none}", "");
                    }

                    contactusBoxUrl.put(key, value);
                }
            }

            responseVO.setModelDisplayName(modelDisplayName);
            responseVO.setSupportLinkUrl(pspLinkUrl);
            responseVO.setContactusBoxUrl(contactusBoxUrl);

            if ("Y".equals(SUPPORT_LINK_LIVE_CHAT_OPEN_TYPE_FLAG)) {
                if (SUPPORT_LINK_LIVE_CHAT_OPEN_TYPE == null
                        || SUPPORT_LINK_LIVE_CHAT_OPEN_TYPE.trim().length() <= 0) {
                    SUPPORT_LINK_LIVE_CHAT_OPEN_TYPE = "self";
                }

                responseVO.setLiveChatOpenTypeFlag("Y");
                responseVO.setLiveChatOpenType(SUPPORT_LINK_LIVE_CHAT_OPEN_TYPE);
            } else {
                responseVO.setLiveChatOpenTypeFlag("N");
                responseVO.setLiveChatOpenType("");
            }

            responseVO.setEmailToCeoUseFlag(CST_EMAIL_TO_CEO_USE_FLAG);
            responseVO.setMktPdpEmailToCeoUseFlag(MKT_PDP_EMAIL_TO_CEO_USE_FLAG);
        } catch (D2xBusinessException e) {
            throw new D2xBusinessException(e);
        }

        return responseVO;
    }
}
