package com.lge.d2x.domain.support.v1.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductGsriFileInfoDataVO {
    @Schema(description = "PDP아이디", example = "MD08718744")
    private String pdpId;

    @Schema(description = "문서아이디", example = "20170704059581")
    private String docId;

    @Schema(description = "파일 명", example = "HDP_UXC081BLMK1&2_MEZ64416601[20170704081957623].pdf")
    private String fileName;

    @Schema(description = "원본파일명", example = "HDP_UXC081BLMK1&2_MEZ64416601.pdf")
    private String originalName;

    @Schema(description = "제품 레벨1 코드", example = "AC")
    private String productLeve1Code;
}
